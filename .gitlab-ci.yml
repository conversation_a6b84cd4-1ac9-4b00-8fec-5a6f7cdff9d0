stages:
  - build and deploy

variables:
  SECURE_FILES_DOWNLOAD_PATH: 'secure_files'
  CACHE_FALLBACK_KEY: fallback-key

.node_modules-cache: &node_modules-cache
  key:
    files:
      - yarn.lock
  paths:
    - node_modules/
    - ios/Pods/
  policy: pull-push

.ruby-cache: &ruby-cache
  key:
    files:
      - Gemfile.lock
  paths:
    - vendor/
  policy: pull-push

.commit-cache: &commit-cache
  key: "${ENV_FILE}-last-commit-id"
  paths:
    - fastlane/commit/*_last_commit_id.txt
    - fastlane/version/
  policy: pull-push

default:
  tags:
    - node
    - fastlane
    - ios

before_script:
    - nvm install
    - nvm use


initialize:
  interruptible: true
  stage: build and deploy
  script:
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - cp "$SECURE_FILES_DOWNLOAD_PATH/${ENV_FILE}.env" .env
    - security import $SECURE_FILES_DOWNLOAD_PATH/${ENV_FILE}_certificate.p12   -k ~/Library/Keychains/login.keychain -P $IOS_CERTIFICATE_PASSWORD -T /usr/bin/codesign
    - mkdir -p fastlane/profiles
    - cp "$SECURE_FILES_DOWNLOAD_PATH/${ENV_FILE}_profile.mobileprovision" fastlane/profiles/profile.mobileprovision
    - eval "cp \$${ENV_FILE}_INAPP .env.${ENV_FILE}"
    - bundle install
    - yarn install
  rules:
    - if: '$CI_COMMIT_TAG =~ /^uat-release-.*$/'
      variables:
        ENV_FILE: 'uat'
    - if: '$CI_COMMIT_TAG =~ /^staging-release-.*$/'
      variables:
        ENV_FILE: 'staging'
    - if: '$CI_COMMIT_TAG =~ /^test-release-.*$/'
      variables:
        ENV_FILE: 'staging'
    - when: never
  cache:
    - <<: *node_modules-cache
    - <<: *ruby-cache

build_ios:
  stage: build and deploy
  needs: [initialize]
  script:
    - bundle exec fastlane ios build_and_distribute_by_tag
  artifacts:
    expose_as: 'iOS'
    paths:
      - build/ios
    expire_in: 7 days
  variables:
    GIT_STRATEGY: none
  only:
    - /^uat-release-.*$/
    - /^staging-release-.*$/
    - /^test-release-.*$/
  cache:
    <<: *commit-cache

build_android:
  stage: build and deploy
  needs: [initialize]
  script:
    - bundle exec fastlane android build_and_distribute_by_tag
  artifacts:
    expose_as: 'Android'
    paths:
      - build/android
    expire_in: 7 days
  variables:
    GIT_STRATEGY: none
  only:
    - /^uat-release-.*$/
    - /^staging-release-.*$/
    - /^test-release-.*$/
  cache:
    <<: *commit-cache
