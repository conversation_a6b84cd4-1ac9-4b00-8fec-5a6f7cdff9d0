<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(FACEBOOK_BUNDLE_URL_SCHEMES)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(GOOGLE_BUNDLE_URL_SCHEMES)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(APPSFLYER_DOMAIN)</string>
			</array>
  	</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<!-- FB SDK -->
	<key>FacebookAppID</key>
	<string>$(FACEBOOK_APP_ID)</string>
	<key>FacebookDisplayName</key>
	<string>$(APP_NAME)</string>
	<key>FacebookClientToken</key>
	<string>$(FACEBOOK_CLIENT_TOKEN)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<!-- End FB SDK -->
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSUserTrackingUsageDescription</key>
	<string>To help us optimise our Ads, this app needs to track some of your app-related activity while using the app</string>
	<key>NSCameraUsageDescription</key>
	<string>$(APP_NAME) would like to use your camera to scan QR code and upload avatar profile.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(APP_NAME) would love to know your location to make booking easier for you!😊</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>$(APP_NAME) would love to know your location to make booking easier for you!😊</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(APP_NAME) would love to know your location to make booking easier for you!😊</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>$(APP_NAME) would like to save photos to your photo gallery to upload and save photo onto your profile</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(APP_NAME) would like access to your photo gallery to upload and save photo onto your profile.</string>
	<key>UIAppFonts</key>
	<array>
		<string>smartos-v3.ttf</string>
		<string>icomoon.ttf</string>
		<string>Ionicons.ttf</string>
		<string>AntDesign.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>InterTight-Bold.ttf</string>
		<string>InterTight-SemiBold.ttf</string>
		<string>InterTight-Medium.ttf</string>
		<string>InterTight-Regular.ttf</string>
		<string>InterTight-Light.ttf</string>
		<string>InterTight-ExtraLight.ttf</string>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-Regular.ttf</string>
		<string>Montserrat-Medium.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
