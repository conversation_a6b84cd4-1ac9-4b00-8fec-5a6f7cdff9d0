<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_72" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="splash" translatesAutoresizingMaskIntoConstraints="NO" id="s7d-dl-XHg">
                                <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" red="0.92799156900000002" green="0.1102074012" blue="0.1437884569" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="s7d-dl-XHg" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="Eb2-To-uDT"/>
                            <constraint firstItem="s7d-dl-XHg" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="HGe-CP-q2D"/>
                            <constraint firstAttribute="trailing" secondItem="s7d-dl-XHg" secondAttribute="trailing" id="WzD-Ln-Yha"/>
                            <constraint firstAttribute="bottom" secondItem="s7d-dl-XHg" secondAttribute="bottom" id="rms-Zm-v6y"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="51.627906976744185" y="374.67811158798287"/>
        </scene>
    </scenes>
    <resources>
        <image name="splash" width="375" height="812"/>
    </resources>
</document>
