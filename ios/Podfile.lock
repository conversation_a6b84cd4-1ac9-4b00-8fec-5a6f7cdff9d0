PODS:
  - abseil/algorithm (1.20211102.0):
    - abseil/algorithm/algorithm (= 1.20211102.0)
    - abseil/algorithm/container (= 1.20211102.0)
  - abseil/algorithm/algorithm (1.20211102.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20211102.0):
    - abseil/base/atomic_hook (= 1.20211102.0)
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/base_internal (= 1.20211102.0)
    - abseil/base/config (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/base/dynamic_annotations (= 1.20211102.0)
    - abseil/base/endian (= 1.20211102.0)
    - abseil/base/errno_saver (= 1.20211102.0)
    - abseil/base/fast_type_id (= 1.20211102.0)
    - abseil/base/log_severity (= 1.20211102.0)
    - abseil/base/malloc_internal (= 1.20211102.0)
    - abseil/base/pretty_function (= 1.20211102.0)
    - abseil/base/raw_logging_internal (= 1.20211102.0)
    - abseil/base/spinlock_wait (= 1.20211102.0)
    - abseil/base/strerror (= 1.20211102.0)
    - abseil/base/throw_delegate (= 1.20211102.0)
  - abseil/base/atomic_hook (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20211102.0)
  - abseil/base/core_headers (1.20211102.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20211102.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20211102.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/pretty_function (1.20211102.0)
  - abseil/base/raw_logging_internal (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/container/common (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20211102.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20211102.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20211102.0):
    - abseil/algorithm/container
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20211102.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20211102.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20211102.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/container/have_sse
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/have_sse (1.20211102.0)
  - abseil/container/inlined_vector (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20211102.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/container/have_sse
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/functional/bind_front (1.20211102.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash/city (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20211102.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20211102.0):
    - abseil/memory/memory (= 1.20211102.0)
  - abseil/memory/memory (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20211102.0):
    - abseil/meta/type_traits (= 1.20211102.0)
  - abseil/meta/type_traits (1.20211102.0):
    - abseil/base/config
  - abseil/numeric/bits (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20211102.0):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/distributions (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20211102.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/random/internal/fastmath (1.20211102.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20211102.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20211102.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20211102.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20211102.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20211102.0):
    - abseil/base/config
  - abseil/random/internal/uniform_helper (1.20211102.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20211102.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20211102.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20211102.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20211102.0):
    - abseil/container/inlined_vector
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/cord (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/strings/cord_internal (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20211102.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20211102.0):
    - abseil/base/config
  - abseil/strings/internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20211102.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/strings (1.20211102.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (1.20211102.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20211102.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20211102.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20211102.0):
    - abseil/time/internal (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
  - abseil/time/internal (1.20211102.0):
    - abseil/time/internal/cctz (= 1.20211102.0)
  - abseil/time/internal/cctz (1.20211102.0):
    - abseil/time/internal/cctz/civil_time (= 1.20211102.0)
    - abseil/time/internal/cctz/time_zone (= 1.20211102.0)
  - abseil/time/internal/cctz/civil_time (1.20211102.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20211102.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20211102.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20211102.0):
    - abseil/types/any (= 1.20211102.0)
    - abseil/types/bad_any_cast (= 1.20211102.0)
    - abseil/types/bad_any_cast_impl (= 1.20211102.0)
    - abseil/types/bad_optional_access (= 1.20211102.0)
    - abseil/types/bad_variant_access (= 1.20211102.0)
    - abseil/types/compare (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/span (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
  - abseil/types/any (1.20211102.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20211102.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20211102.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20211102.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20211102.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20211102.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - AppsFlyerFramework (6.15.1):
    - AppsFlyerFramework/Main (= 6.15.1)
  - AppsFlyerFramework/Main (6.15.1)
  - boost (1.76.0)
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBAEMKit (17.4.0):
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBLazyVector (0.71.1)
  - FBReactNativeSpec (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.1)
    - RCTTypeSafety (= 0.71.1)
    - React-Core (= 0.71.1)
    - React-jsi (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - FBSDKCoreKit (17.4.0):
    - FBAEMKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit_Basics (17.4.0)
  - FBSDKGamingServicesKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
    - FBSDKShareKit (= 17.4.0)
  - FBSDKLoginKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
  - FBSDKShareKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
  - Firebase/CoreOnly (10.0.0):
    - FirebaseCore (= 10.0.0)
  - Firebase/Firestore (10.0.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.0.0)
  - Firebase/Messaging (10.0.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.0.0)
  - FirebaseCore (10.0.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.0.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.0.0):
    - abseil/algorithm (~> 1.20211102.0)
    - abseil/base (~> 1.20211102.0)
    - abseil/container/flat_hash_map (~> 1.20211102.0)
    - abseil/memory (~> 1.20211102.0)
    - abseil/meta (~> 1.20211102.0)
    - abseil/strings/strings (~> 1.20211102.0)
    - abseil/time (~> 1.20211102.0)
    - abseil/types (~> 1.20211102.0)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.44.0)"
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.0.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (7.0.0):
    - AppAuth (~> 1.5)
    - GTMAppAuth (< 3.0, >= 1.3)
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.44.0)":
    - "gRPC-C++/Implementation (= 1.44.0)"
    - "gRPC-C++/Interface (= 1.44.0)"
  - "gRPC-C++/Implementation (1.44.0)":
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - "gRPC-C++/Interface (= 1.44.0)"
    - gRPC-Core (= 1.44.0)
  - "gRPC-C++/Interface (1.44.0)"
  - gRPC-Core (1.44.0):
    - gRPC-Core/Implementation (= 1.44.0)
    - gRPC-Core/Interface (= 1.44.0)
  - gRPC-Core/Implementation (1.44.0):
    - abseil/base/base (= 1.20211102.0)
    - abseil/base/core_headers (= 1.20211102.0)
    - abseil/container/flat_hash_map (= 1.20211102.0)
    - abseil/container/inlined_vector (= 1.20211102.0)
    - abseil/functional/bind_front (= 1.20211102.0)
    - abseil/hash/hash (= 1.20211102.0)
    - abseil/memory/memory (= 1.20211102.0)
    - abseil/random/random (= 1.20211102.0)
    - abseil/status/status (= 1.20211102.0)
    - abseil/status/statusor (= 1.20211102.0)
    - abseil/strings/cord (= 1.20211102.0)
    - abseil/strings/str_format (= 1.20211102.0)
    - abseil/strings/strings (= 1.20211102.0)
    - abseil/synchronization/synchronization (= 1.20211102.0)
    - abseil/time/time (= 1.20211102.0)
    - abseil/types/optional (= 1.20211102.0)
    - abseil/types/variant (= 1.20211102.0)
    - abseil/utility/utility (= 1.20211102.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.44.0)
    - Libuv-gRPC (= 0.0.10)
  - gRPC-Core/Interface (1.44.0)
  - GTMAppAuth (2.0.0):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 4.0, >= 1.5)
  - GTMSessionFetcher/Core (3.5.0)
  - leveldb-library (1.22.6)
  - Libuv-gRPC (0.0.10):
    - Libuv-gRPC/Implementation (= 0.0.10)
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Implementation (0.0.10):
    - Libuv-gRPC/Interface (= 0.0.10)
  - Libuv-gRPC/Interface (0.0.10)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - lottie-ios (4.4.1)
  - lottie-react-native (6.7.2):
    - lottie-ios (= 4.4.1)
    - React-Core
  - Mixpanel-swift (4.3.0):
    - Mixpanel-swift/Complete (= 4.3.0)
  - Mixpanel-swift/Complete (4.3.0)
  - MixpanelReactNative (3.0.7):
    - Mixpanel-swift (= 4.3.0)
    - React-Core
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.71.1)
  - RCTTypeSafety (0.71.1):
    - FBLazyVector (= 0.71.1)
    - RCTRequired (= 0.71.1)
    - React-Core (= 0.71.1)
  - React (0.71.1):
    - React-Core (= 0.71.1)
    - React-Core/DevSupport (= 0.71.1)
    - React-Core/RCTWebSocket (= 0.71.1)
    - React-RCTActionSheet (= 0.71.1)
    - React-RCTAnimation (= 0.71.1)
    - React-RCTBlob (= 0.71.1)
    - React-RCTImage (= 0.71.1)
    - React-RCTLinking (= 0.71.1)
    - React-RCTNetwork (= 0.71.1)
    - React-RCTSettings (= 0.71.1)
    - React-RCTText (= 0.71.1)
    - React-RCTVibration (= 0.71.1)
  - React-callinvoker (0.71.1)
  - React-Codegen (0.71.1):
    - FBReactNativeSpec
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.1)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/Default (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/DevSupport (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.1)
    - React-Core/RCTWebSocket (= 0.71.1)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-jsinspector (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-Core/RCTWebSocket (0.71.1):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.1)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsiexecutor (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - Yoga
  - React-CoreModules (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.1)
    - React-Codegen (= 0.71.1)
    - React-Core/CoreModulesHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-RCTImage (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-cxxreact (0.71.1):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-jsinspector (= 0.71.1)
    - React-logger (= 0.71.1)
    - React-perflogger (= 0.71.1)
    - React-runtimeexecutor (= 0.71.1)
  - React-jsc (0.71.1):
    - React-jsc/Fabric (= 0.71.1)
    - React-jsi (= 0.71.1)
  - React-jsc/Fabric (0.71.1):
    - React-jsi (= 0.71.1)
  - React-jsi (0.71.1):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-perflogger (= 0.71.1)
  - React-jsinspector (0.71.1)
  - React-logger (0.71.1):
    - glog
  - react-native-appsflyer (6.15.1):
    - AppsFlyerFramework (= 6.15.1)
    - React
  - react-native-config (1.5.1):
    - react-native-config/App (= 1.5.1)
  - react-native-config/App (1.5.1):
    - React-Core
  - react-native-date-picker (4.3.5):
    - React-Core
  - react-native-document-picker (9.1.1):
    - React-Core
  - react-native-fbsdk-next (13.3.0):
    - React-Core
    - react-native-fbsdk-next/Core (= 13.3.0)
    - react-native-fbsdk-next/Login (= 13.3.0)
    - react-native-fbsdk-next/Share (= 13.3.0)
  - react-native-fbsdk-next/Core (13.3.0):
    - FBSDKCoreKit (~> 17.4)
    - React-Core
  - react-native-fbsdk-next/Login (13.3.0):
    - FBSDKLoginKit (~> 17.4)
    - React-Core
  - react-native-fbsdk-next/Share (13.3.0):
    - FBSDKGamingServicesKit (~> 17.4)
    - FBSDKShareKit (~> 17.4)
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-google-maps (1.9.0):
    - Google-Maps-iOS-Utils (= 4.2.2)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-html-to-pdf (0.12.0):
    - React-Core
  - react-native-image-resizer (3.0.11):
    - React-Core
  - react-native-maps (1.9.0):
    - React-Core
  - react-native-netinfo (9.3.7):
    - React-Core
  - react-native-pager-view (6.4.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.8.0):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-webview (13.12.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-perflogger (0.71.1)
  - React-RCTActionSheet (0.71.1):
    - React-Core/RCTActionSheetHeaders (= 0.71.1)
  - React-RCTAnimation (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.1)
    - React-Codegen (= 0.71.1)
    - React-Core/RCTAnimationHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-RCTAppDelegate (0.71.1):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.1)
    - React-Core/RCTBlobHeaders (= 0.71.1)
    - React-Core/RCTWebSocket (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-RCTNetwork (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-RCTImage (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.1)
    - React-Codegen (= 0.71.1)
    - React-Core/RCTImageHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-RCTNetwork (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-RCTLinking (0.71.1):
    - React-Codegen (= 0.71.1)
    - React-Core/RCTLinkingHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-RCTNetwork (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.1)
    - React-Codegen (= 0.71.1)
    - React-Core/RCTNetworkHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-RCTSettings (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.1)
    - React-Codegen (= 0.71.1)
    - React-Core/RCTSettingsHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-RCTText (0.71.1):
    - React-Core/RCTTextHeaders (= 0.71.1)
  - React-RCTVibration (0.71.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.1)
    - React-Core/RCTVibrationHeaders (= 0.71.1)
    - React-jsi (= 0.71.1)
    - ReactCommon/turbomodule/core (= 0.71.1)
  - React-runtimeexecutor (0.71.1):
    - React-jsi (= 0.71.1)
  - ReactCommon/turbomodule/bridging (0.71.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.1)
    - React-Core (= 0.71.1)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-logger (= 0.71.1)
    - React-perflogger (= 0.71.1)
  - ReactCommon/turbomodule/core (0.71.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.1)
    - React-Core (= 0.71.1)
    - React-cxxreact (= 0.71.1)
    - React-jsi (= 0.71.1)
    - React-logger (= 0.71.1)
    - React-perflogger (= 0.71.1)
  - ReactNativeGetLocation (4.0.1):
    - React-Core
  - RNAppleAuthentication (2.4.0):
    - React-Core
  - RNCAsyncStorage (1.21.0):
    - React-Core
  - RNCClipboard (1.11.1):
    - React-Core
  - RNDeviceInfo (10.12.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (16.0.0):
    - Firebase/CoreOnly (= 10.0.0)
    - React-Core
  - RNFBFirestore (16.0.0):
    - Firebase/Firestore (= 10.0.0)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (16.0.0):
    - Firebase/Messaging (= 10.0.0)
    - FirebaseCoreExtension (= 10.0.0)
    - React-Core
    - RNFBApp
  - RNFileViewer (2.1.5):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.14.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNGoogleSignin (11.0.1):
    - GoogleSignIn (~> 7.0.0)
    - React-Core
  - RNImageCropPicker (0.41.3):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.3)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.3):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNLocalize (3.0.4):
    - React-Core
  - RNNotifee (7.8.2):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.8.2)
  - RNNotifee/NotifeeCore (7.8.2):
    - React-Core
  - RNPermissions (4.0.4):
    - React-Core
  - RNQuickAction (0.3.13):
    - React
  - RNReanimated (3.7.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.29.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNSentry (6.2.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - Sentry/HybridSDK (= 8.40.1)
  - RNShare (12.0.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNSVG (12.0.0):
    - React
    - React-RCTImage
  - RNVectorIcons (10.0.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Sentry/HybridSDK (8.40.1)
  - Stripe (23.16.0):
    - StripeApplePay (= 23.16.0)
    - StripeCore (= 23.16.0)
    - StripePayments (= 23.16.0)
    - StripePaymentsUI (= 23.16.0)
    - StripeUICore (= 23.16.0)
  - stripe-react-native (0.33.0):
    - React-Core
    - Stripe (~> 23.16.0)
    - StripeApplePay (~> 23.16.0)
    - StripeFinancialConnections (~> 23.16.0)
    - StripePayments (~> 23.16.0)
    - StripePaymentSheet (~> 23.16.0)
    - StripePaymentsUI (~> 23.16.0)
  - StripeApplePay (23.16.0):
    - StripeCore (= 23.16.0)
  - StripeCore (23.16.0)
  - StripeFinancialConnections (23.16.0):
    - StripeCore (= 23.16.0)
    - StripeUICore (= 23.16.0)
  - StripePayments (23.16.0):
    - StripeCore (= 23.16.0)
    - StripePayments/Stripe3DS2 (= 23.16.0)
  - StripePayments/Stripe3DS2 (23.16.0):
    - StripeCore (= 23.16.0)
  - StripePaymentSheet (23.16.0):
    - StripeApplePay (= 23.16.0)
    - StripeCore (= 23.16.0)
    - StripePayments (= 23.16.0)
    - StripePaymentsUI (= 23.16.0)
  - StripePaymentsUI (23.16.0):
    - StripeCore (= 23.16.0)
    - StripePayments (= 23.16.0)
    - StripeUICore (= 23.16.0)
  - StripeUICore (23.16.0):
    - StripeCore (= 23.16.0)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - MixpanelReactNative (from `../node_modules/mixpanel-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-html-to-pdf (from `../node_modules/react-native-html-to-pdf`)
  - "react-native-image-resizer (from `../node_modules/@bam.tech/react-native-image-resizer`)"
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-tracking-transparency (from `../node_modules/react-native-tracking-transparency`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeGetLocation (from `../node_modules/react-native-get-location`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBFirestore (from `../node_modules/@react-native-firebase/firestore`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFileViewer (from `../node_modules/react-native-file-viewer`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNQuickAction (from `../node_modules/react-native-quick-actions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "stripe-react-native (from `../node_modules/@stripe/stripe-react-native`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - AppsFlyerFramework
    - BoringSSL-GRPC
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - Libuv-gRPC
    - libwebp
    - lottie-ios
    - Mixpanel-swift
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  MixpanelReactNative:
    :path: "../node_modules/mixpanel-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-html-to-pdf:
    :path: "../node_modules/react-native-html-to-pdf"
  react-native-image-resizer:
    :path: "../node_modules/@bam.tech/react-native-image-resizer"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-tracking-transparency:
    :path: "../node_modules/react-native-tracking-transparency"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeGetLocation:
    :path: "../node_modules/react-native-get-location"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBFirestore:
    :path: "../node_modules/@react-native-firebase/firestore"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFileViewer:
    :path: "../node_modules/react-native-file-viewer"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNQuickAction:
    :path: "../node_modules/react-native-quick-actions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  stripe-react-native:
    :path: "../node_modules/@stripe/stripe-react-native"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  abseil: ebe5b5529fb05d93a8bdb7951607be08b7fa71bc
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  AppsFlyerFramework: 375c63ff47da664031d2723947867caf0205058c
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBAEMKit: 58cb5f302cdd715a56d4c1d0dfdd2e423ac1421a
  FBLazyVector: ad72713385db5289b19f1ead07e8e4aa26dcb01d
  FBReactNativeSpec: df2602c11e33d310433496e28a48b4b2be652a61
  FBSDKCoreKit: 94d7461d0cecf441b1ba7c41acfff41daa8ccd41
  FBSDKCoreKit_Basics: 151b43db8b834d3f0e02f95d36a44ffd36265e45
  FBSDKGamingServicesKit: 55a13febe0cd117bf5f8f61f315d75aab9b7876e
  FBSDKLoginKit: 5c1cd53c91a2282b3a4fe6e6d3dcf2b8b0d33d55
  FBSDKShareKit: 00546a02dce72f37a4209cd68aaf5fb749185c3b
  Firebase: 1b810f3d0c0532e27a48f1961f8c0400a668a2cf
  FirebaseCore: 97f48a3a567a72b8d4daa0f03c3aadb78df4e995
  FirebaseCoreExtension: 449595a035812f16314ca88cebf959e3bb77dd67
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseFirestore: 5007583f3db2129de8e87f18ee63f4c86f07e7a3
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 8916bf5edb1dbfac74665a181e4d1ab3a78a08a2
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleSignIn: b232380cf495a429b8095d3178a8d5855b42e842
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  "gRPC-C++": 9675f953ace2b3de7c506039d77be1f2e77a8db2
  gRPC-Core: 943e491cb0d45598b0b0eb9e910c88080369290b
  GTMAppAuth: 99fb010047ba3973b7026e45393f51f27ab965ae
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  Libuv-gRPC: 55e51798e14ef436ad9bc45d12d43b77b49df378
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  lottie-react-native: b9b043670db02c7ae76d3c3a9cc43e44bf711537
  Mixpanel-swift: 2192b9a24cf41b870749e4e0e10fbc9822bb2b4a
  MixpanelReactNative: 2bc0ce12ec51fd5f3fafb1131b0ab44832ba3162
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: fd4d923b964658aa0c4091a32c8b2004c6d9e3a6
  RCTTypeSafety: c276d85975bde3d8448907235c70bf0da257adfd
  React: e481a67971af1ce9639c9f746b753dd0e84ca108
  React-callinvoker: 1051c04a94fa9d243786b86380606bad701a3b31
  React-Codegen: 5a9b86d168aa1ff48fa7b5f28d335a8f59f087c1
  React-Core: 5552f628b118e03d9b21d64ec8db5250cafbff58
  React-CoreModules: 59245305f41ff0adfeac334acc0594dea4585a7c
  React-cxxreact: a170a697a67e9fd5934314d1b5cac09083c75bfa
  React-jsc: b4246f1eed2b9addf2edc93b0462c1b4e55fc1d6
  React-jsi: 01fb00633bd7cde0b04b8c24aba62b8d0a534482
  React-jsiexecutor: 9877871b24929549a40272078b5f22735982631b
  React-jsinspector: ff56004b0c974b688a6548c156d5830ad751ae07
  React-logger: f12d82cd01a1e25d3623cf9b2b470677018c7e3d
  react-native-appsflyer: 24c078b1fec256fb6d9a9bf520c87e1c15f5368c
  react-native-config: 136f9755ccc991cc6438053a44363259ad4c7813
  react-native-date-picker: 4ca4a37b0c77c55af81b46983318af5c300b3595
  react-native-document-picker: a2e1577d26b9758ef3092e66f5d67f6e8c7454d9
  react-native-fbsdk-next: 8dc76b449a731540591366c7f2874df3701da8bd
  react-native-geolocation-service: 32b2c2a3b91e70ce2a8d0c684801aaeb0a07e0ec
  react-native-google-maps: 9da31254be2d8fd3936dfbe4f8e989b94d854509
  react-native-html-to-pdf: 7a49e6c58ac5221bcc093027b195f4b214f27a9d
  react-native-image-resizer: 24c5d06fae2176dc0caed4b6396e02befb44064a
  react-native-maps: 2173cbaddcef764af9a8ed56883b7672d6fc8337
  react-native-netinfo: be701059f57093572e5ba08cba14483d334b425d
  react-native-pager-view: 133c83c04d27ae21a73079a176465bd885ec052b
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: 416969a424e182df41785e36bc0a417deba794e0
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-tracking-transparency: 15eb319f2b982070eb9831582af27d87badfa624
  react-native-webview: 4908c3c69a592d1b52bf4e050032c423e2a09e7c
  React-perflogger: ec8eef2a8f03ecfa6361c2c5fb9197ef4a29cc85
  React-RCTActionSheet: a0c023b86cf4c862fa9c4eb0f6f91fbe878fb2de
  React-RCTAnimation: 168d53718c74153947c0109f55900faa64d79439
  React-RCTAppDelegate: eff606f35ef83cf67fbc614e5fb531de43d17b2e
  React-RCTBlob: 9bcbfc893bfda9f6b2eb016329d38c0f6366d31a
  React-RCTImage: 3fcd4570b4b0f1ac2f4b4b6308dba33ce66c5b50
  React-RCTLinking: 1edb8e1bb3fc39bf9e13c63d6aaaa3f0c3d18683
  React-RCTNetwork: 500a79e0e0f67678077df727fabba87a55c043e1
  React-RCTSettings: cc4414eb84ad756d619076c3999fecbf12896d6f
  React-RCTText: 2a34261f3da6e34f47a62154def657546ebfa5e1
  React-RCTVibration: 49d531ec8498e0afa2c9b22c2205784372e3d4f3
  React-runtimeexecutor: 311feb67600774723fe10eb8801d3138cae9ad67
  ReactCommon: 25caa7d7812ff4ed54a67fa5bdb6fce55d8990d8
  ReactNativeGetLocation: 490fcaab5a440dab40a7bff8e2e189b96657d23c
  RNAppleAuthentication: 8d313d93fe2238d6b7ff0a39c67ebcf298d96653
  RNCAsyncStorage: a03b770a50541a761447cea9c24536047832124d
  RNCClipboard: 5780fd039e547fc73a9dd14b5bcad2cd2d0315d9
  RNDeviceInfo: addb9b427c2822a2d8e94c87a136a224e0af738c
  RNFastImage: 462a183c4b0b6b26fdfd639e1ed6ba37536c3b87
  RNFBApp: 90861d83c0e990694e10611c8566042f02fed38a
  RNFBFirestore: fe8a35c19eb13db77bc668106977ae6c7453fbd3
  RNFBMessaging: 036b550b3ca8e7d98240d421e042db24edf6d80d
  RNFileViewer: 4b5d83358214347e4ab2d4ca8d5c1c90d869e251
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: dc1acdc779554be3aa733f4a9a19bb782ec3a48c
  RNGoogleSignin: b8f09e3ec56e09497e1e53b0ff66d5a45916c6b1
  RNImageCropPicker: 35c58e89a93062798ac902c96247e3cc9916d6eb
  RNLocalize: 88ef045a030828f406f1c92c2cab605c7df80a7e
  RNNotifee: 8768d065bf1e2f9f8f347b4bd79147431c7eacd6
  RNPermissions: 945b4e8921c81150aee2be973403d510c451029f
  RNQuickAction: c2c8f379e614428be0babe4d53a575739667744d
  RNReanimated: 0b02057c4bf3da30f49210f9a435ffcdf0de3f0d
  RNScreens: a425ae50ad66d024a6e936121bf5c9fbe6a5cdc6
  RNSentry: db596b30418b0ea09bcd31b1998960ba5dcd1cf4
  RNShare: b624747d82700c9033edbffb1b0b91d71105eaef
  RNSVG: 719a8eb24c92198ac726df5ac80c37032e3a6e65
  RNVectorIcons: e6ade3214d849feccb7ed564a97af1907a9c3ce6
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Sentry: e9215d7b17f7902692b4f8700e061e4f853e3521
  Stripe: fcab417424a45d995aa1e62a39bc942233b61ba3
  stripe-react-native: cb47fbba53c87c476025553b88a3bf593e6c435c
  StripeApplePay: 81de266a964b3ad02fb25e8b22fd8c235d2916a8
  StripeCore: ac141047bca25f5ae945a1cbdd00a90b0d08c616
  StripeFinancialConnections: d7a452e4bed1195d8978ee4f6c0ba2f82c89fdf4
  StripePayments: 5804cb9b02cca488042e5cf8cf57a86a5080006b
  StripePaymentSheet: 06ea6bbf49be869769c46bf31be4fbc3e23b5b19
  StripePaymentsUI: 24a0349ae8c7e8c7402086703720fe06c52ddea0
  StripeUICore: ce85e040a22bf0335061d14973d37757528b3d2d
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 921eb014669cf9c718ada68b08d362517d564e0c

PODFILE CHECKSUM: 2a47ac072d6b5946e7c50f2c58003270cdeae8dc

COCOAPODS: 1.16.2
