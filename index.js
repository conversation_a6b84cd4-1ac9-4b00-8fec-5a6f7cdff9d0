/**
 * @format
 */

import {AppRegistry} from 'react-native';
import {name as appName} from './app.json';
import App from './App';
import {initI18n} from './src/config/initI18n';
import 'react-native-gesture-handler';
import {startNetworkLogging} from 'react-native-network-logger';
import MixPanelSdk from './src/utils/mixPanelSdk';
// import AppsFlyer from './src/utils/appsFlyerSdk';
import * as Sentry from '@sentry/react-native';
import Config from 'react-native-config';

initI18n();
startNetworkLogging();
MixPanelSdk.initAppsFlyer();
// AppsFlyer.init();

if (__DEV__) {
  require('./src/config/ReactotronConfig');
}

if (!__DEV__) {
  Sentry.init({
    dsn: Config.SENTRY_DSN_KEY,
    tracesSampleRate: 0.1,
    environment: Config.APP_ENV_NAME,
  });
}

AppRegistry.registerComponent(appName, () => App);
