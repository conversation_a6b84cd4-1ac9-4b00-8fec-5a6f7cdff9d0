<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    xmlns:tools="http://schemas.android.com/tools" 
    package="com.taxiloyal.passenger.staging">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>

    <application
      android:name=".MainApplication"
      android:label="@string/APP_NAME"
      android:icon="@mipmap/ic_launcher_40"
      android:roundIcon="@mipmap/ic_launcher_40_round"
      android:allowBackup="false"
      tools:replace="android:allowBackup"
      android:theme="@style/AppTheme"
      android:requestLegacyExternalStorage="true">
      <meta-data android:name="com.google.android.geo.API_KEY" android:value="@string/GOOGLE_MAP_API_KEY_ANDROID"/>
      <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/FACEBOOK_APP_ID"/>
      <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/FACEBOOK_CLIENT_TOKEN"/>
      <activity
        android:name=".MainActivity"
        android:label="@string/APP_NAME"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:screenOrientation="portrait"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        <intent-filter  android:autoVerify="true">
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="@string/AUTH_RE_DIRECT_URL_SCHEME" />
          <data android:scheme="@string/FACEBOOK_BUNDLE_URL_SCHEMES" />
          <data android:scheme="https" android:host="@string/APPSFLYER_DOMAIN" />
        </intent-filter>
      </activity>
      <meta-data android:name="com.google.android.gms.wallet.api.enabled" android:value="true" />
    </application>
</manifest>
