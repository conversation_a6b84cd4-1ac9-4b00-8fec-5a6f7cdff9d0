apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'

import com.android.build.OutputFile

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

project.ext.envConfigFiles = [
    uat: "./.env.uat",
    staging: "./.env.staging",
    prod: "./.env.release",
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

project.ext.vectoricons = [
    iconFontNames: [
        'icomoon.ttf',
        'Ionicons.ttf',
        'AntDesign.ttf',
        'FontAwesome.ttf',
        'MaterialCommunityIcons.ttf',
        'InterTight-Bold.ttf',
        'InterTight-SemiBold.ttf',
        'InterTight-Medium.ttf',
        'InterTight-Regular.ttf',
        'InterTight-Light.ttf',
        'InterTight-ExtraLight.ttf',
        'Montserrat-Bold.ttf',
        'Montserrat-Regular.ttf',
        'Montserrat-Medium.ttf'
    ] // Name of the font files you want to copy
]

apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/react-native-codegen
    // codegenDir = file("../node_modules/react-native-codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Private function to get the list of Native Architectures you want to build.
 * This reads the value from reactNativeArchitectures in your gradle.properties
 * file and works together with the --active-arch-only flag of react-native run-android.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

android {
    ndkVersion rootProject.ext.ndkVersion

    compileSdkVersion rootProject.ext.compileSdkVersion
    flavorDimensions "default"
    namespace "com.taxiloyal.passenger.staging"
    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        multiDexEnabled true
        resValue "string", "build_config_package", "com.taxiloyal.passenger"
    }
    productFlavors {
        uat {
            applicationId "com.taxiloyal.enosta.passenger.uat"
            versionCode 234
            versionName "1.1.3"
        }
        staging {
            applicationId "com.taxiloyal.passenger.staging"
            versionCode 461
            versionName "1.1.3"
        }
        prod {
            applicationId "com.taxiloyal.passenger"
            versionCode 233
            versionName "1.3.4"
        }
    }

    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include (*reactNativeArchitectures())
        }
    }
    signingConfigs {
      debug {
            if (project.env.get('ANDROID_STORE_FILE')) {
                storeFile file(project.env.get("ANDROID_STORE_FILE"))
                storePassword project.env.get("ANDROID_STORE_PASSWORD")
                keyAlias project.env.get("ANDROID_KEY_ALIAS")
                keyPassword project.env.get("ANDROID_KEY_PASSWORD")
            }
        }
        release {
            if (project.env.get('ANDROID_STORE_FILE')) {
                storeFile file(project.env.get("ANDROID_STORE_FILE"))
                storePassword project.env.get("ANDROID_STORE_PASSWORD")
                keyAlias project.env.get("ANDROID_KEY_ALIAS")
                keyPassword project.env.get("ANDROID_KEY_PASSWORD")
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug']
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            matchingFallbacks = ['release']
            minifyEnabled enableProguardInReleaseBuilds
            shrinkResources false
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // https://developer.android.com/studio/build/configure-apk-splits.html
            // Example: versionCode 1 will generate 1001 for armeabi-v7a, 1002 for x86, etc.
            def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(OutputFile.ABI)
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        defaultConfig.versionCode * 1000 + versionCodes.get(abi)
            }

        }
    }
}

afterEvaluate {
    processUatDebugGoogleServices.dependsOn switchToReleaseUAT
    processUatReleaseGoogleServices.dependsOn switchToReleaseUAT
    processStagingDebugGoogleServices.dependsOn switchToReleaseStaging
    processStagingReleaseGoogleServices.dependsOn switchToReleaseStaging
    processProdReleaseGoogleServices.dependsOn switchToRelease
 }

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.0.0")

    implementation "com.google.android.gms:play-services-base:${rootProject.ext.playServicesVersion}"
    implementation "com.google.android.gms:play-services-maps:${rootProject.ext.playServicesVersion}"
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'org.jetbrains:annotations:16.0.2'

    debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}") {
        exclude group:'com.facebook.fbjni'
    }
    debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.facebook.flipper'
        exclude group:'com.squareup.okhttp3', module:'okhttp'
    }

    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}"){
        exclude group:'com.facebook.flipper'
    }
    implementation 'com.facebook.android:facebook-android-sdk:latest.release'

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}


apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)

task switchToRelease(type: Copy) {
    description = 'Switches to RELEASE google-services.json'
    from "src/prod"
    include "google-services.json"
    into "."
}

task switchToReleaseStaging(type: Copy) {
    description = 'Switches to STAGING google-services.json'
    from "src/staging"
    include "google-services.json"
    into "."
}

task switchToReleaseUAT(type: Copy) {
    description = 'Switches to UAT google-services.json'
    from "src/uat"
    include "google-services.json"
    into "."
}