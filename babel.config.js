module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['.'],
        alias: {
          src: './src',
          '@components': './src/components',
          '@navigation': './src/navigation',
          '@graphql': './src/graphql',
          '@screens': './src/screens',
          '@themes': './src/themes',
          '@utils': './src/utils',
          '@interface': './src/interface',
          '@react-query': './src/react-query',
          '@generated': './src/generated',
          '@api': './src/api',
          '@queries': './src/api/queries/index.ts',
          '@requests': './src/api/requests/index.ts',
          '@global': './global.d.ts',
        },
      },
    ],
    'react-native-reanimated/plugin',
  ],
};
