{"name": "taxiloyal", "version": "0.0.1", "private": true, "scripts": {"codegenSH": "./codegen.sh", "codegen": "eval $(grep '^VITE_API_SCHEMAS_URL' .env.release) && openapi-rq -i $VITE_API_SCHEMAS_URL -o src/api -c axios --indent 2 && eslint --ext .tsx,.ts,.js,.jsx src/api --fix", "android": "react-native run-android", "android-uat-debug": "react-native run-android --mode=uatDebug --appId com.taxiloyal.enosta.passenger.uat", "android-stg-debug": "react-native run-android --mode=stagingDebug --appId com.taxiloyal.passenger.staging", "android-release-debug": "npx react-native-rename '<PERSON><PERSON>' --androidBundleID 'com.taxiloyal.passenger' --skipGitStatusCheck && react-native run-android --mode=prodRelease--appId com.taxiloyal.passenger", "ios": "react-native run-ios", "check-types": "tsc --noEmit --pretty", "check-types:watch": "tsc --noEmit --watch", "lint": "eslint \"src/**/*.{tsx,ts}\" --fix", "lint-staged": "lint-staged", "lint-pass": "echo '\\033[4;32m♡ ' Format complete ♡' \\033[0m'", "start": "react-native start", "reset": "yarn start --reset-cache", "test": "jest", "pod": "yarn createIconFont && cd ios && pod install && cd ..", "podInstall": "cd ios && pod install && cd ..", "openXcode": "xed -b ios", "fix-xcode": "react-native-schemes-manager all", "removeCachedMetro": "npm cache clean --force && rm -rf ~/Library/Caches/com.facebook.ReactNativeBuild && yarn cache clean --verbose && rm -rf $TMPDIR/metro-bundler-cache-*", "removeCachedFiles": "rm -rf $TMPDIR/react-* && watchman watch-del-all && rm -rf ios/build/ModuleCache/* && rm -rf ios/build/* && rm -rf node_modules/* && rm -rf yarn.lock", "podNewCLear": "cd ios && rm -rf Pods/* && rm -rf Podfile.lock && cd ..", "generateIconFontComponent": "./node_modules/.bin/generate-icon ./src/assets/Fonts/style.css --componentName=icomoon --fontFamily=icomoon > ./node_modules/react-native-vector-icons/icomoon.js", "copyIconFontToNodeModules": "cp ./src/assets/Fonts/fonts/icomoon.ttf ./node_modules/react-native-vector-icons/Fonts/icomoon.ttf", "createFontToNodeModule": "cp ./src/assets/Fonts/*.ttf ./node_modules/react-native-vector-icons/Fonts", "createIconFont": "yarn run generateIconFontComponent && yarn run copyIconFontToNodeModules && yarn run createFontToNodeModule", "android:clean": "cd android && ./gradlew --stop && ./gradlew clean", "newclear": "yarn run removeCachedFiles && yarn run removeCachedMetro && yarn podNewCLear && yarn install --verbose && yarn android:clean && yarn run fix-xcode && yarn createIconFont && yarn run podInstall", "i18n": "ts-node script/generateTranslationTypes.ts", "prepare": "husky install", "postinstall": "patch-package && yarn createIconFont && bundle exec pod-install", "deleteDerivedData": "rm -rf ~/Library/Developer/Xcode/DerivedData/", "genAab": "npx react-native-rename 'Taxi Loyal' --androidBundleID 'com.taxiloyal.passenger' --skipGitStatusCheck && cd android && ./gradlew clean && ./gradlew bundleProdRelease && cd .. && open ./android", "genApk": "npx react-native-rename 'Taxi Loyal' --androidBundleID 'com.taxiloyal.passenger' --skipGitStatusCheck && cd android && ./gradlew clean && ./gradlew assembleProdRelease && cd .. && open ./android", "buildUAT": "npx react-native-rename '<PERSON><PERSON>' --androidBundleID 'com.taxiloyal.enosta.passenger.uat' --skipGitStatusCheck && cd android && ./gradlew clean && ./gradlew assembleReleaseUAT && cd ..", "runAndroidRelease": "npx react-native-rename '<PERSON><PERSON>' --androidBundleID 'com.taxiloyal.passenger' --skipGitStatusCheck && react-native run-android --variant=release", "runAndroidUAT": "npx react-native-rename '<PERSON><PERSON>' --androidBundleID 'com.taxiloyal.enosta.passenger.uat' --skipGitStatusCheck && react-native run-android --variant=releaseUAT"}, "xcodeSchemes": {"Debug": ["Debug"], "Release": ["Staging", "UAT", "Release"], "projectDirectory": "iOS"}, "dependencies": {"@bam.tech/react-native-image-resizer": "3.0.11", "@invertase/react-native-apple-authentication": "^2.3.0", "@notifee/react-native": "7.8.2", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-clipboard/clipboard": "1.11.1", "@react-native-community/netinfo": "9.3.7", "@react-native-firebase/app": "16.0.0", "@react-native-firebase/firestore": "16.0.0", "@react-native-firebase/messaging": "16.0.0", "@react-native-google-signin/google-signin": "11.0.1", "@react-navigation/bottom-tabs": "6.5.11", "@react-navigation/native": "6.1.9", "@react-navigation/native-stack": "6.9.17", "@sentry/react-native": "6.2.0", "@stripe/stripe-react-native": "0.33.0", "@tanstack/query-async-storage-persister": "5.17.9", "@tanstack/react-query": "5.14.6", "@tanstack/react-query-persist-client": "5.17.9", "axios": "1.6.2", "cheerio": "1.0.0-rc.12", "crypto-js": "3.3.0", "dayjs": "^1.11.10", "escape-string-regexp": "5.0.0", "formik": "2.4.5", "geolib": "3.3.4", "husky": "8.0.3", "i18next": "24.2.3", "lodash-es": "4.17.21", "lottie-react-native": "^6.7.0", "mixpanel-react-native": "3.0.7", "moment": "2.30.1", "moment-timezone": "0.5.48", "patch-package": "8.0.0", "pod-install": "^0.2.2", "react": "18.2.0", "react-i18next": "15.4.1", "react-native": "0.71.1", "react-native-appsflyer": "6.15.1", "react-native-base64": "0.2.1", "react-native-calendars": "^1.1304.1", "react-native-cn-quill": "^0.7.18", "react-native-config": "1.5.1", "react-native-date-picker": "4.3.5", "react-native-device-info": "10.12.0", "react-native-dialogs": "1.1.2", "react-native-document-picker": "9.1.1", "react-native-fast-image": "8.6.3", "react-native-fbsdk-next": "13.3.0", "react-native-file-viewer": "2.1.5", "react-native-fs": "2.20.0", "react-native-geolocation-service": "5.3.1", "react-native-gesture-handler": "2.14.0", "react-native-get-location": "4.0.1", "react-native-html-to-pdf": "0.12.0", "react-native-image-crop-picker": "0.41.3", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-linear-gradient": "2.8.3", "react-native-localize": "3.0.4", "react-native-maps": "1.9.0", "react-native-maps-directions": "1.9.0", "react-native-modal": "13.0.1", "react-native-modern-datepicker": "1.0.0-beta.91", "react-native-network-logger": "1.15.0", "react-native-pager-view": "6.4.1", "react-native-payment-icons": "1.0.11", "react-native-permissions": "4.0.4", "react-native-quick-actions": "0.3.13", "react-native-reanimated": "3.7.1", "react-native-reanimated-carousel": "3.5.1", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.8.0", "react-native-screens": "3.29.0", "react-native-share": "12.0.3", "react-native-splash-screen": "3.3.0", "react-native-svg": "12.0.0", "react-native-tab-view": "3.5.2", "react-native-toast-message": "2.2.0", "react-native-tracking-transparency": "0.1.2", "react-native-vector-icons": "10.0.2", "react-native-webview": "13.12.3", "reactotron-react-native": "5.1.12", "socket.io-client": "4.7.4", "yup": "1.3.3"}, "devDependencies": {"@7nohe/openapi-react-query-codegen": "0.5.3", "@babel/core": "7.21.0", "@babel/preset-env": "7.20.2", "@babel/runtime": "7.21.0", "@react-native-community/eslint-config": "3.0.0", "@reactdevjunn/eslint-config-react": "1.0.4", "@tanstack/eslint-plugin-query": "^5.14.6", "@tsconfig/react-native": "2.0.2", "@types/jest": "29.2.1", "@types/lodash-es": "^4.17.6", "@types/prettier": "2.4.1", "@types/react-native-modern-datepicker": "1.0.5", "@types/react-native-vector-icons": "6.4.18", "@types/react-test-renderer": "18.0.0", "babel-jest": "29.2.1", "babel-plugin-module-resolver": "5.0.0", "eslint": "^8.20.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "jest": "29.2.1", "metro-react-native-babel-preset": "0.73.7", "openapi-typescript-codegen": "^0.25.0", "prettier": "2.4.1", "react-native-schemes-manager": "2.0.0", "react-test-renderer": "18.2.0", "ts-node": "10.9.2", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}, "resolutions": {"@types/react": "18.0.24"}}