{"extends": ["@reactdevjunn/eslint-config-react", "plugin:react-native/all"], "plugins": ["react-native"], "rules": {"react-native/no-inline-styles": "error", "prettier/prettier": 1, "@typescript-eslint/no-unnecessary-condition": "off", "@typescript-eslint/no-use-before-define": ["error", {"functions": true, "classes": true, "variables": false}], "no-restricted-imports": ["warn", {"patterns": ["moment", "**/../..", "**/../../.."]}]}, "ignorePatterns": ["src/graphql"]}