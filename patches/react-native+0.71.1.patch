diff --git a/node_modules/react-native/scripts/.packager.env b/node_modules/react-native/scripts/.packager.env
new file mode 100644
index 0000000..361f5fb
--- /dev/null
+++ b/node_modules/react-native/scripts/.packager.env
@@ -0,0 +1 @@
+export RCT_METRO_PORT=8081
diff --git a/node_modules/react-native/scripts/cocoapods/codegen_utils.rb b/node_modules/react-native/scripts/cocoapods/codegen_utils.rb
index 3694e68..3fafa63 100644
--- a/node_modules/react-native/scripts/cocoapods/codegen_utils.rb
+++ b/node_modules/react-native/scripts/cocoapods/codegen_utils.rb
@@ -85,7 +85,7 @@ class CodegenUtils
           'source' => { :git => '' },
           'header_mappings_dir' => './',
           'platforms' => {
-            'ios' => '11.0',
+            'ios' => '12.0',
           },
           'source_files' => "**/*.{h,mm,cpp}",
           'pod_target_xcconfig' => { "HEADER_SEARCH_PATHS" =>
diff --git a/node_modules/react-native/third-party-podspecs/boost.podspec b/node_modules/react-native/third-party-podspecs/boost.podspec
index 3d9331c..8276eb1 100644
--- a/node_modules/react-native/third-party-podspecs/boost.podspec
+++ b/node_modules/react-native/third-party-podspecs/boost.podspec
@@ -10,8 +10,8 @@ Pod::Spec.new do |spec|
   spec.homepage = 'http://www.boost.org'
   spec.summary = 'Boost provides free peer-reviewed portable C++ source libraries.'
   spec.authors = 'Rene Rivera'
-  spec.source = { :http => 'https://boostorg.jfrog.io/artifactory/main/release/1.76.0/source/boost_1_76_0.tar.bz2',
-                  :sha256 => '****************************************************************' }
+  spec.source = { :http => 'https://sourceforge.net/projects/boost/files/boost/1.83.0/boost_1_83_0.tar.bz2',
+                  :sha256 => '****************************************************************' }
 
   # Pinning to the same version as React.podspec.
   spec.platforms = { :ios => '11.0' }
