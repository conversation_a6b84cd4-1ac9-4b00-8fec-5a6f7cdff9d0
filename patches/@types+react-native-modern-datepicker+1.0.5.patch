diff --git a/node_modules/@types/react-native-modern-datepicker/index.d.ts b/node_modules/@types/react-native-modern-datepicker/index.d.ts
index a0529bb..4bf5a6c 100644
--- a/node_modules/@types/react-native-modern-datepicker/index.d.ts
+++ b/node_modules/@types/react-native-modern-datepicker/index.d.ts
@@ -32,6 +32,9 @@ export interface ModernDatepickerProps {
         headerAnimationDistance?: number;
         daysAnimationDistance?: number;
     };
+    isVietnam?: boolean;
+    onDone?: () => void;
+    onClose?: () => void;
 }
 
 export default function(props: ModernDatepickerProps): JSX.Element;
