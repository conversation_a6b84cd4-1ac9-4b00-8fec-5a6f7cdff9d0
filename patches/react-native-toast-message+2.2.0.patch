diff --git a/node_modules/react-native-toast-message/lib/src/hooks/useSlideAnimation.js b/node_modules/react-native-toast-message/lib/src/hooks/useSlideAnimation.js
index 33f0b87..9bef62f 100644
--- a/node_modules/react-native-toast-message/lib/src/hooks/useSlideAnimation.js
+++ b/node_modules/react-native-toast-message/lib/src/hooks/useSlideAnimation.js
@@ -1,5 +1,5 @@
 import React from 'react';
-import { Animated, Platform } from 'react-native';
+import { Animated, Dimensions, Platform } from 'react-native';
 import { additiveInverseArray } from '../utils/array';
 import { useKeyboard } from './useKeyboard';
 export function translateYOutputRangeFor({ position, height, topOffset, bottomOffset, keyboardHeight, keyboardOffset }) {
@@ -13,6 +13,8 @@ const useNativeDriver = Platform.select({ native: true, default: false });
 export function useSlideAnimation({ position, height, topOffset, bottomOffset, keyboardOffset }) {
     const animatedValue = React.useRef(new Animated.Value(0));
     const { keyboardHeight } = useKeyboard();
+const {heightFix, width} = Dimensions.get('window');
+
     const animate = React.useCallback((toValue) => {
         Animated.spring(animatedValue.current, {
             toValue,
@@ -22,15 +24,17 @@ export function useSlideAnimation({ position, height, topOffset, bottomOffset, k
     }, []);
     const translateY = React.useMemo(() => animatedValue.current.interpolate({
         inputRange: [0, 1],
-        outputRange: translateYOutputRangeFor({
-            position,
-            height,
-            topOffset,
-            bottomOffset,
-            keyboardHeight,
-            keyboardOffset
-        })
-    }), [position, height, topOffset, bottomOffset, keyboardHeight, keyboardOffset]);
+        // outputRange: translateYOutputRangeFor({
+        //     position,
+        //     height,
+        //     topOffset,
+        //     bottomOffset,
+        //     keyboardHeight,
+        //     keyboardOffset
+        // })
+        outputRange:[heightFix||0,heightFix||0]
+    }), [heightFix,position, height, topOffset, bottomOffset, keyboardHeight, keyboardOffset]);
+    
     const opacity = animatedValue.current.interpolate({
         inputRange: [0, 0.7, 1],
         outputRange: [0, 1, 1]
diff --git a/node_modules/react-native-toast-message/lib/src/useToast.js b/node_modules/react-native-toast-message/lib/src/useToast.js
index 12a44a0..a193dd9 100644
--- a/node_modules/react-native-toast-message/lib/src/useToast.js
+++ b/node_modules/react-native-toast-message/lib/src/useToast.js
@@ -7,6 +7,7 @@ export const DEFAULT_DATA = {
     text1: undefined,
     text2: undefined
 };
+
 export const DEFAULT_OPTIONS = {
     type: 'success',
     text1Style: null,
