diff --git a/node_modules/react-native-modern-datepicker/src/datePicker/DatePicker.js b/node_modules/react-native-modern-datepicker/src/datePicker/DatePicker.js
index 7fb2842..8e76b00 100644
--- a/node_modules/react-native-modern-datepicker/src/datePicker/DatePicker.js
+++ b/node_modules/react-native-modern-datepicker/src/datePicker/DatePicker.js
@@ -89,7 +89,7 @@ const DatePicker = props => {
     <CalendarContext.Provider value={contextValue}>
       <View
         style={[style.container, {minHeight}, props.style]}
-        onLayout={({nativeEvent}) => setMinHeight(nativeEvent.layout.width * 0.9 + 55)}>
+        onLayout={({nativeEvent}) => setMinHeight(nativeEvent.layout.width * 0.9 + 100)}>
         {renderBody()}
       </View>
     </CalendarContext.Provider>
diff --git a/node_modules/react-native-modern-datepicker/src/datePicker/components/Calendar.js b/node_modules/react-native-modern-datepicker/src/datePicker/components/Calendar.js
index 868c302..c7049ee 100644
--- a/node_modules/react-native-modern-datepicker/src/datePicker/components/Calendar.js
+++ b/node_modules/react-native-modern-datepicker/src/datePicker/components/Calendar.js
@@ -1,7 +1,8 @@
 import React, {useEffect} from 'react';
-import {View, StyleSheet, Text, Animated} from 'react-native';
+import {View, StyleSheet, Text, Animated, TouchableOpacity} from 'react-native';
 
-import {Header, Days} from '.';
+import { Header } from './Header';
+import { Days } from './Days';
 import {useCalendar} from '../DatePicker';
 
 const Calendar = () => {
@@ -32,7 +33,26 @@ const Calendar = () => {
           <Days />
         </Animated.View>
       </View>
+      <View style={style.footer}>
+          <TouchableOpacity
+            style={[style.button, style.cancelButton]}
+            onPress={() =>{
+                utils?.data?.onClose?.()
+              }
+            }
+            activeOpacity={0.8}>
+            <Text style={style.btnText}>{utils.config.timeClose}</Text>
+          </TouchableOpacity>
+        <TouchableOpacity style={style.button} activeOpacity={0.8} 
+          onPress={() =>{
+              utils?.data?.onDone?.()
+            }
+          }>
+          <Text style={style.btnText}>{utils.config.timeSelect}</Text>
+        </TouchableOpacity>
+      </View>
     </View>
+    
   );
 };
 
@@ -41,6 +61,7 @@ const styles = theme =>
     container: {
       flexDirection: 'column',
       flex: 1,
+      position:'relative',
     },
     daysName: {
       paddingBottom: 10,
@@ -71,6 +92,26 @@ const styles = theme =>
       top: 0,
       right: 0,
     },
+    footer: {
+      paddingBottom: 4,
+      flexDirection: 'row',
+      justifyContent: 'center',
+    },
+    button: {
+      paddingVertical: 10,
+      paddingHorizontal: 25,
+      borderRadius: 8,
+      backgroundColor: theme.mainColor,
+      margin: 8,
+    },
+    btnText: {
+      fontSize: theme.textFontSize,
+      color: theme.selectedTextColor,
+      fontFamily: theme.defaultFont,
+    },
+    cancelButton: {
+      backgroundColor: theme.textSecondaryColor,
+    },
   });
 
 export {Calendar};
diff --git a/node_modules/react-native-modern-datepicker/src/datePicker/components/Days.js b/node_modules/react-native-modern-datepicker/src/datePicker/components/Days.js
index 05de512..33a94a6 100644
--- a/node_modules/react-native-modern-datepicker/src/datePicker/components/Days.js
+++ b/node_modules/react-native-modern-datepicker/src/datePicker/components/Days.js
@@ -31,6 +31,7 @@ const Days = () => {
           style={{
             width: itemSize,
             height: itemSize,
+            padding: 5
           }}>
           {day && (
             <TouchableOpacity
diff --git a/node_modules/react-native-modern-datepicker/src/datePicker/components/Header.js b/node_modules/react-native-modern-datepicker/src/datePicker/components/Header.js
index 06f0db1..933c400 100644
--- a/node_modules/react-native-modern-datepicker/src/datePicker/components/Header.js
+++ b/node_modules/react-native-modern-datepicker/src/datePicker/components/Header.js
@@ -13,6 +13,7 @@ const Header = ({changeMonth}) => {
     minimumDate,
     maximumDate,
     isGregorian,
+    isVietnam,
     mode,
   } = useCalendar();
   const [mainState, setMainState] = state;
@@ -79,6 +80,9 @@ const Header = ({changeMonth}) => {
             <Text style={[style.headerText, style.monthText]}>
               {utils.getMonthYearText(mainState.activeDate).split(' ')[1]}
             </Text>
+            {isVietnam && (<Text style={[style.headerText, style.monthText]}>
+              {utils.getMonthYearText(mainState.activeDate).split(' ')[2]}
+            </Text>)}
           </TouchableOpacity>
           {mode === 'datepicker' && (
             <TouchableOpacity
diff --git a/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectMonth.js b/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectMonth.js
index 6ccfa19..b15b95c 100644
--- a/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectMonth.js
+++ b/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectMonth.js
@@ -31,7 +31,8 @@ const SelectMonth = () => {
   const style = styles(options);
   const [year, setYear] = useState(utils.getMonthYearText(mainState.activeDate).split(' ')[1]);
   const openAnimation = useRef(new Animated.Value(0)).current;
-  const currentMonth = Number(mainState.activeDate.split('/')[1]);
+
+  const currentMonth = Number(mainState.activeDate.split(/[-/]/)[1]);
   const prevDisable = maximumDate && utils.checkYearDisabled(Number(utils.toEnglish(year)), true);
   const nextDisable = minimumDate && utils.checkYearDisabled(Number(utils.toEnglish(year)), false);
 
@@ -48,7 +49,7 @@ const SelectMonth = () => {
   }, [mainState.monthOpen, openAnimation]);
 
   useEffect(() => {
-    show && setYear(utils.getMonthYearText(mainState.activeDate).split(' ')[1]);
+    show && setYear(utils.getMonthYearText(mainState.activeDate).split(' ').reverse()?.[0]);
   }, [mainState.activeDate, utils, show]);
 
   const onSelectMonth = month => {
diff --git a/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectTime.js b/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectTime.js
index 5decffe..4d8d9e2 100644
--- a/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectTime.js
+++ b/node_modules/react-native-modern-datepicker/src/datePicker/components/SelectTime.js
@@ -8,94 +8,138 @@ import {
   Easing,
   TouchableOpacity,
   I18nManager,
+  Platform,
+  LayoutAnimation,
 } from 'react-native';
 
 import {useCalendar} from '../DatePicker';
+import moment from 'moment-jalaali';
 
-const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);
-
-const TimeScroller = ({title, data, onChange}) => {
+const TimeScroller = ({title, data, onChange, defaultItem, minuteInterval}) => {
   const {options, utils} = useCalendar();
   const [itemSize, setItemSize] = useState(0);
   const style = styles(options);
   const scrollAnimatedValue = useRef(new Animated.Value(0)).current;
-  const scrollListener = useRef(null);
-  const active = useRef(0);
+  const active = useRef(null);
   data = ['', '', ...data, '', ''];
 
-  useEffect(() => {
-    scrollListener.current && clearInterval(scrollListener.current);
-    scrollListener.current = scrollAnimatedValue.addListener(({value}) => (active.current = value));
-
-    return () => {
-      clearInterval(scrollListener.current);
-    };
-  }, [scrollAnimatedValue]);
-
   const changeItemWidth = ({nativeEvent}) => {
     const {width} = nativeEvent.layout;
-    !itemSize && setItemSize(width / 5);
+    !itemSize && setItemSize(Math.round(width / 5));
   };
 
   const renderItem = ({item, index}) => {
-    const makeAnimated = (a, b, c) => {
-      return {
-        inputRange: [...data.map((_, i) => i * itemSize)],
-        outputRange: [
-          ...data.map((_, i) => {
-            const center = i + 2;
-            if (center === index) {
-              return a;
-            } else if (center + 1 === index || center - 1 === index) {
-              return b;
-            } else {
-              return c;
-            }
-          }),
-        ],
+    
+    if(Platform.OS==='ios'){
+      const makeAnimated = (a, b, c) => {
+
+        return {
+          inputRange: [...data.map((_, i) => i * itemSize)],
+          outputRange: [
+            ...data.map((_, i) => {
+              const center = i + 2;
+              if (center === index) {
+                return a;
+              } else if (center + 1 === index || center - 1 === index) {
+                return b;
+              } else {
+                return c;
+              }
+            }),
+          ],
+        };
       };
-    };
 
+      return (
+        <Animated.View
+          style={[
+            {
+              width: itemSize,
+              opacity: scrollAnimatedValue.interpolate(makeAnimated(1, 0.6, 0.3)),
+              transform: [
+                {
+                  scale: scrollAnimatedValue.interpolate(makeAnimated(1.2, 0.9, 0.8)),
+                },
+                {
+                  scaleX: I18nManager.isRTL ? -1 : 1,
+                },
+              ],
+            },
+            style.listItem,
+          ]}>
+          <Text style={style.listItemText}>
+            {utils.toPersianNumber(String(item).length === 1 ? '0' + item : item)}
+          </Text>
+        </Animated.View>
+      )
+    }
+    const isSelected = active.current !==null ? Math.round(active.current / itemSize) === item : defaultItem === item;
     return (
       <Animated.View
         style={[
           {
             width: itemSize,
-            opacity: scrollAnimatedValue.interpolate(makeAnimated(1, 0.6, 0.3)),
+            opacity: isSelected ? 1 : 0.6,
             transform: [
-              {
-                scale: scrollAnimatedValue.interpolate(makeAnimated(1.2, 0.9, 0.8)),
+               {
+                scale: isSelected ? 1.2 : 0.8,
               },
               {
-                scaleX: I18nManager.isRTL ? -1 : 1,
+                 scaleX: I18nManager.isRTL ? -1 : 1,
               },
             ],
           },
-          style.listItem,
+           style.listItem,
         ]}>
         <Text style={style.listItemText}>
           {utils.toPersianNumber(String(item).length === 1 ? '0' + item : item)}
         </Text>
       </Animated.View>
-    );
+    )
   };
 
+  const flatListRef = useRef(null)
+
+  const [retryScroll, setRetryScroll] = useState(false)
+
+  useEffect(() => {
+      setTimeout(() => {
+        if(defaultItem){
+          const roundData = Math.round(defaultItem/minuteInterval) * minuteInterval
+          const selectedIndex = data.findIndex((ele)=>roundData===ele)
+          if(selectedIndex>=0){
+            flatListRef?.current?.scrollToIndex({index: selectedIndex - 2 , animated:true})
+          }
+        }
+      }, 950);
+  }, [retryScroll])
+
   return (
     <View style={style.row} onLayout={changeItemWidth}>
       <Text style={style.title}>{title}</Text>
-      <AnimatedFlatList
+      <FlatList
+        onScrollToIndexFailed={()=>{
+          setRetryScroll(true)
+        }}
+        initialNumToRender={60}
+        ref={flatListRef}
         pagingEnabled
         showsHorizontalScrollIndicator={false}
         horizontal
         snapToInterval={itemSize}
         decelerationRate={'fast'}
         onScroll={Animated.event([{nativeEvent: {contentOffset: {x: scrollAnimatedValue}}}], {
-          useNativeDriver: true,
+          useNativeDriver: false,
         })}
         data={I18nManager.isRTL ? data.reverse() : data}
-        onMomentumScrollEnd={() => {
-          const index = Math.round(active.current / itemSize);
-          onChange(data[index + 2]);
+        onMomentumScrollEnd={(e) => {
+          LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
+          const { x } = e.nativeEvent.contentOffset;
+          if(active.current !== x){
+            active.current = x;
+            const index = Math.round(x / itemSize);
+            onChange(data[index + 2]);
+          }
         }}
         keyExtractor={(_, i) => String(i)}
         renderItem={renderItem}
@@ -124,12 +168,15 @@ const SelectTime = () => {
   });
   const style = styles(options);
   const openAnimation = useRef(new Animated.Value(0)).current;
+  const convertDate = moment(mainState.activeDate)
+  const hour = convertDate.hour();
+  const minues = convertDate.minute();
 
   useEffect(() => {
     show &&
       setTime({
-        minute: 0,
-        hour: 0,
+        minute: minues,
+        hour: hour,
       });
   }, [show]);
 
@@ -137,15 +184,15 @@ const SelectTime = () => {
     mainState.timeOpen && setShow(true);
     Animated.timing(openAnimation, {
       toValue: mainState.timeOpen ? 1 : 0,
-      duration: 350,
-      useNativeDriver: true,
+      duration: 150,
+      useNativeDriver: false,
       easing: Easing.bezier(0.17, 0.67, 0.46, 1),
     }).start(() => {
       !mainState.timeOpen && setShow(false);
     });
   }, [mainState.timeOpen, openAnimation]);
 
-  const selectTime = () => {
+  const onSelectTime = () => {
     const newTime = utils.getDate(mainState.activeDate);
     newTime.hour(time.hour).minute(time.minute);
     setMainState({
@@ -188,16 +235,17 @@ const SelectTime = () => {
         title={utils.config.hour}
         data={Array.from({length: 24}, (x, i) => i)}
         onChange={hour => setTime({...time, hour})}
+        defaultItem={hour}
+        minuteInterval={minuteInterval}
       />
       <TimeScroller
         title={utils.config.minute}
         data={Array.from({length: 60 / minuteInterval}, (x, i) => i * minuteInterval)}
         onChange={minute => setTime({...time, minute})}
+        defaultItem={minues}
+        minuteInterval={minuteInterval}
       />
       <View style={style.footer}>
-        <TouchableOpacity style={style.button} activeOpacity={0.8} onPress={selectTime}>
-          <Text style={style.btnText}>{utils.config.timeSelect}</Text>
-        </TouchableOpacity>
         {mode !== 'time' && (
           <TouchableOpacity
             style={[style.button, style.cancelButton]}
@@ -210,6 +258,9 @@ const SelectTime = () => {
             <Text style={style.btnText}>{utils.config.timeClose}</Text>
           </TouchableOpacity>
         )}
+        <TouchableOpacity style={style.button} activeOpacity={0.8} onPress={onSelectTime}>
+          <Text style={style.btnText}>{utils.config.timeSelect}</Text>
+        </TouchableOpacity>
       </View>
     </Animated.View>
   ) : null;
diff --git a/node_modules/react-native-modern-datepicker/src/utils.js b/node_modules/react-native-modern-datepicker/src/utils.js
index c9f0cce..2a138fc 100644
--- a/node_modules/react-native-modern-datepicker/src/utils.js
+++ b/node_modules/react-native-modern-datepicker/src/utils.js
@@ -4,30 +4,30 @@ import moment from 'moment-jalaali';
 
 const m = moment();
 const jalaaliConfigs = {
-  dayNames: ['شنبه', 'یکشنبه', 'دوشنبه', 'سه شنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه'],
-  dayNamesShort: ['ش', 'ی', 'د', 'س', 'چ', 'پ', 'ج'],
+  dayNames: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
+  dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
   monthNames: [
-    'فروردین',
-    'اردیبهشت',
-    'خرداد',
-    'تیر',
-    'مرداد',
-    'شهریور',
-    'مهر',
-    'آبان',
-    'آذر',
-    'دی',
-    'بهمن',
-    'اسفند',
+    'Tháng 1',
+    'Tháng 2',
+    'Tháng 3',
+    'Tháng 4',
+    'Tháng 5',
+    'Tháng 6',
+    'Tháng 7',
+    'Tháng 8',
+    'Tháng 9',
+    'Tháng 10',
+    'Tháng 11',
+    'Tháng 12',
   ],
-  selectedFormat: 'jYYYY/jMM/jDD',
-  dateFormat: 'jYYYY/jMM/jDD',
-  monthYearFormat: 'jYYYY jMM',
-  timeFormat: 'HH:mm ',
-  hour: 'ساعت',
-  minute: 'دقیقه',
-  timeSelect: 'انتخاب',
-  timeClose: 'بستن',
+  selectedFormat: 'YYYY/MM/DD',
+  dateFormat: 'YYYY/MM/DD',
+  monthYearFormat: 'YYYY MM',
+  timeFormat: 'HH:mm',
+  hour: 'Giờ',
+  minute: 'Phút',
+  timeSelect: 'Chọn',
+  timeClose: 'Đóng',
 };
 const gregorianConfigs = {
   dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
@@ -57,14 +57,17 @@ const gregorianConfigs = {
 };
 
 class utils {
-  constructor({minimumDate, maximumDate, isGregorian, mode, reverse, configs}) {
+  constructor({minimumDate, maximumDate, isGregorian, mode, reverse, configs, isVietnam, onDone, onClose}) {
     this.data = {
       minimumDate,
       maximumDate,
       isGregorian,
       reverse: reverse === 'unset' ? !isGregorian : reverse,
+      onDone,
+      onClose
     };
-    this.config = isGregorian ? gregorianConfigs : jalaaliConfigs;
+
+    this.config = isVietnam ?jalaaliConfigs: gregorianConfigs ;
     this.config = {...this.config, ...configs};
     if (mode === 'time' || mode === 'datepicker') {
       this.config.selectedFormat = this.config.dateFormat + ' ' + this.config.timeFormat;
@@ -99,7 +102,9 @@ class utils {
     return value.replace(/[۰-۹]/g, (w) => w.charCodeAt(0) - charCodeZero);
   };
 
-  getDate = (time) => moment(time, this.config.selectedFormat);
+  getDate = (time) => {
+    return moment(time, this.config.selectedFormat)
+  };
 
   getMonthYearText = (time) => {
     const {isGregorian} = this.data;
