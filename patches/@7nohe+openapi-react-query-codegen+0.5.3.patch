diff --git a/node_modules/@7nohe/openapi-react-query-codegen/dist/src/createUseQuery.js b/node_modules/@7nohe/openapi-react-query-codegen/dist/src/createUseQuery.js
index 9efddcb..ce682cf 100644
--- a/node_modules/@7nohe/openapi-react-query-codegen/dist/src/createUseQuery.js
+++ b/node_modules/@7nohe/openapi-react-query-codegen/dist/src/createUseQuery.js
@@ -63,7 +63,7 @@ const createUseQuery = (node, className, method, jsDoc = [], deprecated = false)
                 typescript_1.default.factory.createUnionTypeNode([
                     typescript_1.default.factory.createLiteralTypeNode(typescript_1.default.factory.createStringLiteral("queryKey")),
                     typescript_1.default.factory.createLiteralTypeNode(typescript_1.default.factory.createStringLiteral("queryFn")),
-                    typescript_1.default.factory.createLiteralTypeNode(typescript_1.default.factory.createStringLiteral("initialData")),
+                    // typescript_1.default.factory.createLiteralTypeNode(typescript_1.default.factory.createStringLiteral("initialData")),
                 ]),
             ])),
         ], undefined, typescript_1.default.factory.createToken(typescript_1.default.SyntaxKind.EqualsGreaterThanToken), typescript_1.default.factory.createCallExpression(typescript_1.default.factory.createIdentifier("useQuery"), [
