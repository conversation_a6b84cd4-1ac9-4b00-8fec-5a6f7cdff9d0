# Config iOS
IOS_TARGET_NAME  = ENV["IOS_TARGET_NAME"]
IOS_WORKSPACE_PATH = "./ios/#{IOS_TARGET_NAME}.xcworkspace"
IOS_XCODEPROJ_PATH = "./ios/#{IOS_TARGET_NAME}.xcodeproj"
IOS_PLIST_PATH = "#{IOS_TARGET_NAME}/Info.plist"

# Config Android
ANDROID_APP_PATH = 'android/app'
BUILD_GRADLE_PATH = 'android/app/build.gradle'
IOS_VERSION_INFO_PATH = "./version/ios_version_info_#{ENV["ENVIRONMENT"]}.txt"
ANDROID_VERSION_INFO_PATH = "./version/android_version_info_#{ENV["ENVIRONMENT"]}.txt"

# Common
desc "Create release notes"
private_lane :create_release_notes do |options|
  last_commit_file = "./commit/#{ENV["ENVIRONMENT"]}_last_commit_id.txt"
  latest_commit = `git log -1 --pretty=format:%h`.strip

  if File.exist?(last_commit_file) && !File.zero?(last_commit_file)
    last_git_commit = File.read(last_commit_file).strip
  else
    # If the file does not exist or is empty, get the last commit hash
    last_git_commit = latest_commit
  end

  if last_git_commit == latest_commit
    last_git_commit = `git log -2 --pretty=format:%h | tail -n 1`.strip
  end

  # Generate release notes
  ENV['RELEASE_NOTES'] = "Uploaded from Gitlab CI server\n" + sh("git log #{last_git_commit}..HEAD --pretty=tformat:'- (%h) %s'").strip

  # Update the last commit file
  sh "mkdir -p commit"
  sh "git log -1 --pretty=tformat:%h> #{last_commit_file}"
end

def generate_checks_url()
  commit_id = sh("git rev-parse HEAD").strip
  repo_url = sh("git remote get-url origin").strip
  repo_name = repo_url.split('/').last.gsub('.git', '')
  repo_owner = repo_url.split('/')[-2]
  checks_url = "https://github.com/#{repo_owner}/#{repo_name}/commit/#{commit_id}/checks"
  return checks_url
end

def get_release_notes()
  return ENV['RELEASE_NOTES']
end

def get_versions_from_file(file_path, default)
  if File.exist?(file_path) && !File.zero?(file_path)
    version_info = File.read(file_path).split("\n")
    current_version_code = version_info[0].split("=").last.to_i
    version_code = current_version_code >= default.to_i ? (current_version_code + 1) : (default.to_i + 1)
    UI.message("Version is: #{version_code}")
    return version_code
  else
    puts file_path
    return default
  end
end

def save_android_versions(version_code)
  sh "mkdir -p version"
  sh "echo 'versionCode=#{version_code}' > #{ANDROID_VERSION_INFO_PATH}"
end

def save_ios_versions(version_code)
  sh "mkdir -p version"
  sh "echo 'versionCode=#{version_code}' > #{IOS_VERSION_INFO_PATH}"
end

lane :build_and_distribute_by_tag do
  Fastlane::LaneManager.cruise_lane("android", "build_and_distribute_by_tag")
  Fastlane::LaneManager.cruise_lane("ios", "build_and_distribute_by_tag")
end

platform :android do
  private_lane :cache_artifact_file do
    sh "mkdir -p ../build/android"
    sh "cp ../android/app/build/outputs/apk/#{ENV["ENVIRONMENT"]}/release/* \"../build/android\""
  end

  private_lane :distribute_firebase do |options|
    apk_path = options[:apk_path]
    notes = options[:notes]
    result = firebase_app_distribution(
        app: ENV["FIREBASE_ANDROID_APP_ID"],
        groups: ENV["FIREBASE_APP_DISTRIBUTION_GROUPS"],
        release_notes: notes,
        apk_path: apk_path,
        firebase_cli_token: ENV["FIREBASE_TOKEN"]
    )
  end

  private_lane :setup_setting do |options|
    version_code = options[:version_code]
    increment_version_code(
      app_project_dir: ANDROID_APP_PATH,
      flavor: ENV["ENVIRONMENT"],
      version_code: version_code
    )
  end

  private_lane :build_and_distribute do |options|
    task = options[:task]
    version_code = get_versions_from_file(ANDROID_VERSION_INFO_PATH, ENV["ANDROID_DEFAULT_VERSION_CODE"])

    setup_setting(version_code: version_code)
    begin
      notes = get_release_notes()
      gradle(task: task, project_dir: 'android/')
      distribute_firebase(apk_path: lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH], notes: notes)
      cache_artifact_file
      save_android_versions(version_code)
    rescue => exception
      raise exception
    end
  end

  lane :build_and_distribute_by_tag do |options|
    create_release_notes
    tag = ENV["ENVIRONMENT"]
    case tag
    when "uat"
      build_and_distribute(task: "assembleUatRelease")
    when "staging"
      build_and_distribute(task: "assembleStagingRelease")
    when "prod"
      build_and_distribute(task: "bundleProdRelease")
    else
      UI.error "Unknown tag: #{tag}"
    end
  end 

end


platform :ios do
  
  private_lane :setup_setting do |options|
    version_code = options[:version_code]
    profile_name = options[:profile_name]
    bundle_identifier = options[:bundle_identifier]
    update_code_signing_settings(
      path: IOS_XCODEPROJ_PATH, 
      use_automatic_signing: false,
      profile_name: profile_name,
      bundle_identifier: bundle_identifier,
      code_sign_identity: "iPhone Developer",
      targets:[IOS_TARGET_NAME],
      team_id: ENV["APP_TEAM_ID"],
    )
    increment_build_number(
      xcodeproj: IOS_XCODEPROJ_PATH,
      build_number: version_code
    )
    install_provisioning_profile(path: "fastlane/profiles/profile.mobileprovision")
  end

  private_lane :distribute_test_flight do |options|
    notes = get_release_notes()
    ipa_path = options[:ipa]
    upload_to_testflight(
      skip_submission: true,
      skip_waiting_for_build_processing: true,
      ipa: ipa_path,
      changelog: notes
    )
  end

  private_lane :distribute do |options|
    kind = options[:kind]
    ipa_path = options[:ipa_path]
    notes = get_release_notes()
    case kind
    when "testflight"
      distribute_test_flight(ipa_path: ipa_path)
    when "firebase"
      distribute_firebase(ipa_path: ipa_path, notes: notes)
    else
      UI.error "Unknown tag: #{tag}"
    end
  end

  private_lane :build_and_distribute do |options|
    configuration = options[:configuration]
    scheme_name = options[:scheme_name]
    profile_name = options[:profile_name]
    bundle_identifier = options[:bundle_identifier]
    version_code = get_versions_from_file(IOS_VERSION_INFO_PATH, ENV["IOS_DEFAULT_VERSION_CODE"])
    version_number = get_version_number(xcodeproj: IOS_XCODEPROJ_PATH, target: IOS_TARGET_NAME, configuration: configuration)

    begin
      setup_setting(
        profile_name: profile_name,
        bundle_identifier: bundle_identifier,
        version_code: version_code
      )
      ipa_path = gym(
        scheme: scheme_name,
        workspace: IOS_WORKSPACE_PATH,
        configuration: configuration,
        export_method: "development",
        output_directory: "build/ios",
        output_name: "app-#{ENV["ENVIRONMENT"]}.ipa",
        clean: true,
        include_bitcode: false,
        silent: true
      )
      distribute(ipa_path: ipa_path, kind: "firebase")
      save_ios_versions(version_code)
    rescue => exception
      raise exception
    end
  end

  private_lane :distribute_firebase do |options|
    ipa_path = options[:ipa_path]
    notes = options[:notes]
    result = firebase_app_distribution(
      app: ENV["FIREBASE_IOS_APP_ID"],
      groups: ENV["FIREBASE_APP_DISTRIBUTION_GROUPS"],
      release_notes: notes,
      ipa_path: ipa_path,
      firebase_cli_token: ENV["FIREBASE_TOKEN"]
    )
  end

  lane :build_and_distribute_by_tag do |options|
    create_release_notes
    tag = ENV["ENVIRONMENT"]
    profile_name = ENV["IOS_PROFILE_NAME"]
    bundle_identifier = ENV["IOS_BUNDLE_ID"]
    case tag
    when "uat"
      build_and_distribute(
        configuration: "UAT", 
        scheme_name: "UAT",
        profile_name: profile_name,
        bundle_identifier: bundle_identifier
      )
    when "staging"
      build_and_distribute(
        configuration: "Staging",
        scheme_name: "Staging",
        profile_name: profile_name,
        bundle_identifier: bundle_identifier
      )
    when "prod"
      build_and_distribute(
        configuration: "Release",
        scheme_name: "Release",
        profile_name: profile_name,
        bundle_identifier: bundle_identifier
      )
    else
      UI.error "Unknown tag: #{tag}"
    end
  end 

end
