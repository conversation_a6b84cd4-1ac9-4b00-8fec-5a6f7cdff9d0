## 🔥 Summary  
<!-- Briefly describe the purpose and main changes in this Merge Request. -->

## ✅ Key Changes  
<!-- Check all that apply and describe as needed. -->
- [ ] ✨ New Feature: `<feature name>`
- [ ] 🐛 Bug Fix: `<describe fix>`
- [ ] 🧹 Refactor / Code Cleanup
- [ ] 📝 Documentation Update

## 📸 Screenshots / Screen Recordings  
<!-- Add images or videos to show relevant UI/UX changes. -->

## 🧪 Self-Test Checklist (React Native)  
<!-- Confirm that basic testing has been done across platforms. -->
- [ ] I ran `yarn install && cd ios && pod install` successfully  
- [ ] I ran the app on Android  
- [ ] I ran the app on iOS  
- [ ] I tested on both emulator/simulator and physical device (if available)  
- [ ] I checked for UI issues on different screen sizes and resolutions  
- [ ] I verified navigation, gestures, and key features behave as expected  
- [ ] No critical crashes or layout breaks were observed during manual testing  

## 🧭 How to Test This MR  
<!-- Optional: Add steps or notes to help reviewers verify the changes. -->
1. Pull this branch  
2. Run:  
   ```bash  
   yarn install
   cd ios && pod install
