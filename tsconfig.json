// prettier-ignore
{
  "compilerOptions": {
    "target": "esnext",                                  /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
    "lib": ["es2017"],                                   /* Specify a set of bundled library declaration files that describe the target runtime environment. */
    "jsx": "react-native",                               /* Specify what JSX code is generated. */
    "module": "commonjs",                                /* Specify what module code is generated. */
    "moduleResolution": "node",                          /* Specify how TypeScript looks up a file from a given module specifier. */
    "baseUrl": ".",                                  /* Specify the base directory to resolve non-relative module names. */
    "paths": {
      "@themes/*":["./src/themes/*"],
      "@themes":["./src/themes/index.ts"],
      "@utils/*":["./src/utils/*"],
      "@graphql/*":["./src/graphql/*"],
      "@navigation/*":["./src/navigation/*"],
      "@components/*": ["./src/components/*"],
      "@components":["./src/components/index.ts"],
      "@screens/*": ["./src/screens/*"],
      "@interface/*": ["./src/interface/*"],
      "@react-query/*": ["./src/react-query/*"],
      "@generated/*":["./src/generated/*"],
      "@api": ["./src/api/index.ts"],
      "@queries": ["./src/api/queries/index.ts"],
      "@requests":["./src/api/requests/index.ts"],
      "@global":["./global.d.ts"],
    },                                      /* Specify a set of entries that re-map imports to additional lookup locations. */
    "resolveJsonModule": true,                           /* Enable importing .json files */
    "allowJs": true,                                     /* Allow JavaScript files to be a part of your program. Use the checkJS option to get errors from these files. */
    "checkJs": true,                                  /* Enable error reporting in type-checked JavaScript files. */
    "noEmit": true,                                      /* Disable emitting files from a compilation. */
    "isolatedModules": true,                             /* Ensure that each file can be safely transpiled without relying on other imports. */
    "allowSyntheticDefaultImports": true,                /* Allow 'import x from y' when a module doesn't have a default export. */
    "esModuleInterop": true,                             /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables allowSyntheticDefaultImports for type compatibility. */
    "forceConsistentCasingInFileNames": true,            /* Ensure that casing is correct in imports. */
    "strict": true,                                      /* Enable all strict type-checking options. */
    "useUnknownInCatchVariables": false,               /* Type catch clause variables as 'unknown' instead of 'any'. */
    "skipLibCheck": true                                 /* Skip type checking all .d.ts files. */
  },
  "include": ["src", "**/*.ts", "**/*.tsx", "./index.d.ts"],
  "exclude": [
    "node_modules", "babel.config.js", "metro.config.js", "jest.config.js"
  ]
}
