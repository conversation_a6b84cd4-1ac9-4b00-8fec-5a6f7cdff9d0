/* eslint-disable @typescript-eslint/prefer-optional-chain */
/* eslint-disable react-hooks/exhaustive-deps */
import moment from 'moment';
import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
  useRef,
} from 'react';
import firestore from '@react-native-firebase/firestore';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';
import NetInfo from '@react-native-community/netinfo';
import {io, Socket} from 'socket.io-client';
import Config from 'react-native-config';
import {QueryObserverResult, RefetchOptions} from '@tanstack/react-query';
import {Vibration} from 'react-native';
import {useGlobalState} from '@react-query/clientStateManage';
import '@react-native-firebase/app';
import {
  useBookingsServiceBookingControllerGetBooking,
  useBookingsServiceBookingControllerGetBookingOfMeKey,
  useBookingsServiceBookingControllerGetCurrentBooking,
  useBookingsServiceBookingControllerGetCurrentBookingKey,
} from '@queries';
import {BookingEntity, IBaseResponse} from '@requests';
import {useAppState} from '@utils/hooks/useAppState';
import {useTipBooking} from '@utils/hooks/useTipBooking';
import {queryClient} from '@react-query/queryClient';
import {IRealTime} from '@global';
import {SOCKET_EMIT_EVENT} from '@themes/Constants';
import {EKeyTabActivity} from '@themes/Enums';

interface BookingContextType {
  refetchAndUpdate: () => void;
  currentUserBooking: BookingEntity;
  showCurrentBooking: boolean;
  setShowCurrentBooking: React.Dispatch<React.SetStateAction<boolean>>;
  currentStatus: BookingEntity.status;
  setCurrentStatus: React.Dispatch<React.SetStateAction<BookingEntity.status>>;
  setBookingId: React.Dispatch<React.SetStateAction<string>>;
  handleClearBooking: () => void;
  setDataBooking: React.Dispatch<React.SetStateAction<BookingEntity | null>>;
  isFocusActivities: boolean;
  setIsFocusActivities: React.Dispatch<React.SetStateAction<boolean>>;
  refetchCurrentBooking: (
    options?: RefetchOptions,
  ) => Promise<QueryObserverResult<IBaseResponse, unknown>>;
}

const BookingContext = createContext<BookingContextType | null>(null);

interface BookingProviderProps {
  children: ReactNode;
}

export function BookingProvider({children}: BookingProviderProps) {
  const {t} = useTranslation();
  const navigation = useNavigation();
  const [loyalPointConfig] = useGlobalState('configData');
  const [userData] = useGlobalState('userData');
  const [isShareRoute] = useGlobalState('isShareRoute');

  const [bookingId, setBookingId] = useState<string>('');
  const [showCurrentBooking, setShowCurrentBooking] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<BookingEntity.status>(
    BookingEntity.status.CONFIRMED_ADDRESS,
  );
  const [dataBooking, setDataBooking] = useState<BookingEntity | null>(null);
  const [isFocusActivities, setIsFocusActivities] = useState(false);
  const [socketState, setSocketState] = useState<Socket>();
  const [socketConnected, setSocketConnected] = useState(false);
  const hasShownAutoCancelModal = useRef(false);

  const handleClearBooking = useCallback(() => {
    setDataBooking(null);
    setShowCurrentBooking(false);
    setBookingId('');
    setCurrentStatus(BookingEntity.status.CONFIRMED_ADDRESS);
  }, []);

  const {handleRateAndTip} = useTipBooking({
    bookingId,
    handleClear: handleClearBooking,
  });

  const {data: currentUserBooking, refetch} =
    useBookingsServiceBookingControllerGetCurrentBooking(
      [useBookingsServiceBookingControllerGetCurrentBookingKey],
      {
        enabled: !!userData?.user?.id && !!userData?.accessToken,
      },
    );

  const {refetch: refetchDetail} =
    useBookingsServiceBookingControllerGetBooking(
      {
        id: bookingId,
      },
      undefined,
      {
        enabled: false,
      },
    );

  const refetchAndUpdate = useCallback(() => {
    refetch()?.then(res => {
      if (res?.data?.data !== null) {
        setDataBooking(res?.data?.data as BookingEntity);
      }
    });
  }, []);

  const refetchActivitiesList = () => {
    if (!isFocusActivities) return;
    setTimeout(() => {
      queryClient.refetchQueries({
        queryKey: [
          useBookingsServiceBookingControllerGetBookingOfMeKey,
          EKeyTabActivity.Recent,
        ],
        type: 'all',
      });
    }, 500);
  };

  const showAutoCancelBookingModal = useCallback(() => {
    navigation.navigate('ActionModal', {
      confirmLabel: t('button.gotIt'),
      content: t('booking.noDriversNearbyContent2'),
      enableCloseOnMask: false,
      hasCancelBtn: false,
      onApply: () => {
        refetchAndUpdate();
        handleClearBooking();
        navigation.navigate('Main');
      },
      title: t('booking.noDriversNearby'),
    });
  }, []);

  const showWarningCancelBookingModal = useCallback(() => {
    navigation.navigate('ActionModal', {
      autoConfirm: true,
      cancelLabel: t('button.cancelBooking'),
      confirmLabel: t('button.stillWait'),
      content: t('booking.noDriversNearbyContent'),
      enableCloseOnMask: false,
      onApply: () => navigation.goBack(),
      onCancel: () => onCancelBooking(),
      title: t('booking.noDriversNearby'),
    });
  }, []);

  const onCancelBooking = useCallback(() => {
    navigation.goBack();
    navigation.navigate('CancelReasonList', {
      bookingId,
      isTopBarEnable: false,
    });
  }, [bookingId]);

  useEffect(() => {
    if (!dataBooking) return;
    const interval = setInterval(() => {
      const currentWaitingTime = moment().diff(
        moment(dataBooking?.createdAt),
        'seconds',
      );

      if (
        Number(currentWaitingTime) >=
          Number(loyalPointConfig?.autoCancelTime || 600) &&
        [
          BookingEntity.status.CONFIRMED_ADDRESS,
          BookingEntity.status.DEMAND_CREATION,
          BookingEntity.status.AWAITING_CONFIRMED_VEHICLE,
        ].includes(currentStatus) &&
        !hasShownAutoCancelModal.current
      ) {
        hasShownAutoCancelModal.current = true;
        showAutoCancelBookingModal();
        clearInterval(interval);
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [dataBooking]);

  useEffect(() => {
    if (currentUserBooking?.data != null) {
      setShowCurrentBooking(true);
      setBookingId(currentUserBooking.data.id);
      setCurrentStatus(currentUserBooking.data.status);
      setDataBooking(currentUserBooking.data as BookingEntity);
    }
  }, [currentUserBooking?.data]);

  useEffect(() => {
    if (
      currentStatus &&
      bookingId &&
      isFocusActivities &&
      userData?.accessToken &&
      dataBooking?.id
    ) {
      refetchActivitiesList();
    }
  }, [isFocusActivities, bookingId, currentStatus, userData, dataBooking?.id]);

  const handleBookingUpdate = (data: IRealTime) => {
    const {status, isWarningCancel} = data;

    refetch();
    setCurrentStatus(status);
    Vibration.vibrate();

    if (status === BookingEntity.status.CANCELLED) {
      handleClearBooking();
      setShowCurrentBooking(false);
    }
    if (isWarningCancel) {
      showWarningCancelBookingModal();
    }
  };

  useEffect(() => {
    if (currentStatus === BookingEntity.status.COMPLETED) {
      setShowCurrentBooking(false);
      handleRateAndTip();
    }
  }, [currentStatus]);

  useEffect(() => {
    if (!bookingId || !userData?.accessToken || isShareRoute) {
      if (socketState) {
        socketState.disconnect();
        setSocketState(undefined);
        setSocketConnected(false);
      }
      return;
    }

    const socket: Socket = io(`${Config.SOCKET_URL}/bookings`, {
      auth: {
        token: `Bearer ${userData?.accessToken}`,
      },
      reconnectionAttempts: 1,
      transports: ['websocket'],
    });

    setSocketState(socket);

    socket.on('connect', () => {
      setSocketConnected(true);
    });

    socket.on('disconnect', () => {
      setSocketConnected(false);
    });

    socket.on(SOCKET_EMIT_EVENT.CHANGE_STATUS, handleBookingUpdate);

    return () => {
      socket.off(SOCKET_EMIT_EVENT.CHANGE_STATUS);
      socket.disconnect();
      setSocketState(undefined);
      setSocketConnected(false);
    };
  }, [bookingId, userData?.accessToken, isShareRoute]);

  useEffect(() => {
    if (!userData?.user?.id || !bookingId || socketConnected || isShareRoute)
      return;

    const unsubscribe = firestore()
      .collection('users')
      .doc(userData.user.id)
      .collection('bookings')
      .doc(bookingId)
      .onSnapshot(docSnapshot => {
        if (!docSnapshot || !docSnapshot?.exists) return;
        const data = docSnapshot.data() as IRealTime;
        handleBookingUpdate(data);
      });

    return () => unsubscribe();
  }, [userData?.user?.id, bookingId, socketConnected, isShareRoute]);

  useAppState({
    appActiveHandler: () => {
      if (userData?.accessToken && bookingId) {
        refetchActivitiesList();
        refetchDetail()?.then(res => {
          // Clear data when background active booking completed or cancel
          if (
            [
              BookingEntity.status.COMPLETED,
              BookingEntity.status.CANCELLED,
            ].includes(res?.data?.data?.status)
          ) {
            navigation.navigate('Main');
            setDataBooking(null);
            setShowCurrentBooking(false);
            if (res?.data?.data?.status === BookingEntity.status.COMPLETED) {
              setCurrentStatus(BookingEntity.status.COMPLETED);
            } else {
              setCurrentStatus(BookingEntity.status.CONFIRMED_ADDRESS);
            }
          } else {
            setDataBooking(res?.data?.data as BookingEntity);
          }
        });
      }
    },
  });

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      if (state.isConnected && bookingId && socketState) {
        socketState.connect();
      }
    });

    return () => unsubscribe();
  }, [bookingId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (!socketConnected && userData?.accessToken && bookingId) {
      interval = setInterval(() => {
        refetch();
      }, 10000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [socketConnected, userData?.accessToken, bookingId]);

  return (
    <BookingContext.Provider
      value={{
        currentStatus,
        currentUserBooking: dataBooking as BookingEntity,
        handleClearBooking,
        isFocusActivities,
        refetchAndUpdate,
        refetchCurrentBooking: refetch,
        setBookingId,
        setCurrentStatus,
        setDataBooking,
        setIsFocusActivities,
        setShowCurrentBooking,
        showCurrentBooking,
      }}>
      {children}
    </BookingContext.Provider>
  );
}

export const useBookingGlobalStatus = (): BookingContextType => {
  const context = useContext(BookingContext);
  if (!context) {
    if (!context) {
      return {
        currentStatus: BookingEntity.status.CONFIRMED_ADDRESS,
        currentUserBooking: null as unknown as BookingEntity,
        handleClearBooking: () => undefined,
        isFocusActivities: false,
        refetchAndUpdate: () => undefined,
        refetchCurrentBooking: () =>
          Promise.resolve({} as QueryObserverResult<IBaseResponse, unknown>),
        setBookingId: () => undefined,
        setCurrentStatus: () => undefined,
        setDataBooking: () => undefined,
        setIsFocusActivities: () => undefined,
        setShowCurrentBooking: () => undefined,
        showCurrentBooking: false,
      };
    }
  }
  return context;
};
