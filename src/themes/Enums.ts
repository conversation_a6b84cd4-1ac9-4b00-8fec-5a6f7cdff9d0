export enum EDaySpecial {
  Noel = 'Noel',
  NewYear = 'NewYear',
  Normal = 'Normal',
  <PERSON> = 'Valentine',
}

export enum EScreenDeepLink {
  ShareRoute = 'ShareRoute',
  Subscription = 'subscription',
}

export enum EStatusMixpanel {
  NonOrganic = 'Non-organic',
}

export enum ETypeQuickAction {
  BookARide = 'BookARide',
}

export enum EKeyTabActivity {
  Recent = 'Recent',
  History = 'History',
}

export enum EKeyLoyalPoint {
  ReferNewFriendPoint = 'referNewFriendPoint',
  DropRate = 'dropRate',
  FinishFirstRidePoint = 'finishFirstRidePoint',
}
