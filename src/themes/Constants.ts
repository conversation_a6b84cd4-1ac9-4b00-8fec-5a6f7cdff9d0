import {Dimensions, Platform} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import Config from 'react-native-config';
import {ReasonListProps} from '@global';
import {
  CancelBookingDto,
  LocationDto,
  SavedLocationDto,
  UpdateUserDto,
} from '../api/requests';
import Colors from './Colors';
import Images from './Images';
import {EDaySpecial, EScreenDeepLink, ETypeQuickAction} from './Enums';
import {LOTTIE} from './Lottie';

const {height, width} = Dimensions.get('window');

export const DEFAULT_LANGUAGE = 'en';
export const LANGUAGE = Object.freeze({
  EN: 'en',
  FR: 'fr',
});

export const DEFAULT_FORMAT_DATE = 'YYYY-MM-DD';
export const STR_TIME_FORMAT = 'HH:mm';
export const STR_DATE_FORMAT = 'DD/MM/YYYY';
export const STR_DATETIME_FORMAT = 'DD/MM/YYYY, HH:mm';
export const MONTH_YEAR_FORMAT = 'MM/YYYY';
export const FORMAT_DATE_TIME_PICKER = 'YYYY-MM-DD HH:mm';
export const FORMAT_DOB = 'MMM DD, YYYY';
export const FORMAT_BOOKING_DATE = 'YYYY-MM-DDTHH:mm:ss.SSS[Z]';
export const FORMAT_NOTIFICATION_DATE = 'dddd, D MMM, YYYY';
export const TIME_NOT_24H_FORMAT = 'hh:mm A';
export const HISTORY_POINT_FORMAT = 'MMM DD YYYY • hh:mm A';
export const FORMAT_COUPON = 'DD MMM YYYY';
export const FORMAT_DATE_MONTH = 'MMM DD';
export const DATE_MONTH_HOUR_FORMAT = 'MMM DD, hh:mm a';

export const TZ_OTTAWA = `America/Toronto`;

export const SCREEN_WIDTH = width;
export const SCREEN_HEIGHT = height;

export const GOOGLE_MAPS_API_KEY =
  Platform.OS === 'ios'
    ? Config.GOOGLE_MAP_API_KEY_IOS
    : Config.GOOGLE_MAP_API_KEY_ANDROID;
export const DISTANCE = 5000;

export const PASSWORD_REGEX =
  /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+{}|:;,<>?~\-./]).{8,}$/;

export const LOWER_CASE_REGEX = /[a-z]/;
export const UPPER_CASE_REGEX = /[A-Z]/;
export const NUMBER_CASE_REGEX = /\d/;
export const SPECIAL_CHARACTER_REGEX = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/;

export const PHONE_REGEX = /^\d{10}$/;

export const NUMBER_ONLY_REGEX = /^[0-9]*$/;

export const REFERRAL_CODE_REGEX = /^[A-Z0-9]{8}$/;

export const HAS_NOTCH = DeviceInfo.hasNotch();

export const IS_ANDROID = Platform.OS === 'android';
export const IS_IOS = Platform.OS === 'ios';
export const APP_VERSION = DeviceInfo.getVersion();
export const MAIL_SUPPORT = '<EMAIL>';
export const PHONE_SUPPORT = '******-484-4444';
interface BookingStatusProps {
  [key: string]: {
    backgroundColor?: string;
    id: string;
    textColor: string;
    title: string;
    dotColor?: string;
  };
}

export type CardViewType =
  | 'alipay'
  | 'amex'
  | 'american-express'
  | 'code'
  | 'cvv'
  | 'diners-club'
  | 'diners'
  | 'discover'
  | 'elo'
  | 'generic'
  | 'hiper'
  | 'hipercard'
  | 'jcb'
  | 'maestro'
  | 'mastercard'
  | 'master'
  | 'mir'
  | 'paypal'
  | 'unionpay'
  | 'visa';

export const BOOKING_STATUS: BookingStatusProps = {
  ARRIVAL_AT_CLIENT: {
    id: 'ARRIVAL_AT_CLIENT',
    textColor: Colors.blue700,
    title: 'booking.status.arrivalAtClient',
  },
  AWAITING_CONFIRMED_VEHICLE: {
    id: 'AWAITING_CONFIRMED_VEHICLE',
    textColor: Colors.blue700,
    title: 'booking.connectDrivers',
  },
  CANCELLED: {
    backgroundColor: Colors.danger100,
    dotColor: Colors.danger700,
    id: 'CANCELLED',
    textColor: Colors.danger700,
    title: 'activities.cancelled',
  },
  CLIENT_ON_BOARD: {
    id: 'CLIENT_ON_BOARD',
    textColor: Colors.blue700,
    title: 'booking.status.clientOnBoard',
  },
  COMPLETED: {
    backgroundColor: Colors.success100,
    dotColor: Colors.success500,
    id: 'COMPLETED',
    textColor: Colors.success,
    title: 'activities.completed',
  },
  DEMAND_CREATION: {
    id: 'DEMAND_CREATION',
    textColor: Colors.blue700,
    title: 'booking.connectDrivers',
  },
  ONGOING: {
    backgroundColor: Colors.blue100,
    dotColor: Colors.blue500,
    id: 'ONGOING',
    textColor: Colors.blue700,
    title: 'activities.onGoing',
  },
  SCHEDULED: {
    backgroundColor: Colors.purple100,
    dotColor: Colors.purple500,
    id: 'SCHEDULED',
    textColor: Colors.purple900,
    title: 'activities.scheduled',
  },
  VEHICLE_CONFIRMED: {
    id: 'VEHICLE_CONFIRMED',
    textColor: Colors.blue700,
    title: 'booking.status.vehicleConfirm',
  },
};

export const INPUT_ACCESSORY_VIEW_ID = 'uniqueID';
export const INPUT_ACCESSORY_SAVED_LOCATION_ID = 'savedLocationID';

export const DEFAULT_SAVED_LOCATION_TYPE = {
  HOME: 'home',
  WORK: 'work',
};

export const SEARCH_TYPE = {
  DESTINATION: 'DESTINATION',
  PICKUP: 'PICKUP',
  SAVED_LOCATION: 'SAVED_LOCATION',
};

export const ACTIVE_OPACITY = 0.45;

export const SOCKET_EMIT_EVENT = {
  CHANGE_STATUS: 'change_status',
};

export const CARS = [
  {
    amount: 0,
    id: 'ANY_CAR',
    image: Images.carTypical,
    name: 'booking.anyCar',
    seat: 'booking.46seaters',
    vehicleType: 'ANY_CAR',
  },
  {
    amount: 0,
    id: 'TYPICAL_CAR',
    image: Images.carTypical,
    name: 'booking.typicalCar',
    seat: 'booking.4seaters',
    vehicleType: 'TYPICAL_CAR',
  },
  {
    amount: 0,
    id: 'ELECTRIC_CAR',
    image: Images.carElectric,
    name: 'booking.electricCar',
    seat: 'booking.46seaters',
    vehicleType: 'ELECTRIC_CAR',
  },
  {
    id: 'VAN',
    image: Images.van,
    name: 'booking.van',
    seat: 'booking.6seaters',
    vehicleType: 'VAN',
  },
  {
    amount: 0,
    id: 'ACCESSIBLE_VAN',
    image: Images.accessibleVan,
    name: 'booking.accessibleVan',
    seat: 'booking.6seaters',
    vehicleType: 'ACCESSIBLE_VAN',
  },
];

export const BOOKING_TYPE = {
  BUSINESS: 'BUSINESS',
  PERSONAL: 'PERSONAL',
};

export const LIMIT_PLACES = 5;
export const MIN_SEARCH_CHARACTER = 3;

export const DEFAULT_SAVED_LOCATIONS = [
  {
    address: '',
    coordinates: null,
    icon: 'Outline-HouseLine',
    id: SavedLocationDto.type.HOME,
    name: 'savedLocation.home',
    title: 'savedLocation.title.home',
    type: SavedLocationDto.type.HOME,
  },
  {
    address: '',
    coordinates: null,
    icon: 'Outline-Briefcase',
    id: SavedLocationDto.type.WORK,
    name: 'savedLocation.work',
    title: 'savedLocation.title.work',
    type: SavedLocationDto.type.WORK,
  },
];

export const GENDER_DATA = [
  {
    id: UpdateUserDto.gender.MALE,
    title: 'profile.male',
  },
  {
    id: UpdateUserDto.gender.FEMALE,
    title: 'profile.female',
  },
  {
    id: UpdateUserDto.gender.OTHER,
    title: 'profile.other',
  },
];

export const REASON_LIST: ReasonListProps[] = [
  {
    icon: 'Outline-Timer',
    id: CancelBookingDto.reason.DRIVER_ARRIVED_EARLY,
    label: 'cancelBooking.reason1',
  },
  {
    icon: 'Outline-UserMinus',
    id: CancelBookingDto.reason.DRIVER_ASKED_CANCEL,
    label: 'cancelBooking.reason2',
  },
  {
    icon: 'Outline-ClockAfternoon',
    id: CancelBookingDto.reason.DRIVER_NOT_GETTING_CLOSER,
    label: 'cancelBooking.reason3',
  },
  {
    icon: 'Outline-UserCircleGear',
    id: CancelBookingDto.reason.COULD_NOT_FIND_DRIVER,
    label: 'cancelBooking.reason4',
  },
  {
    icon: 'Outline-Clock',
    id: CancelBookingDto.reason.WAIT_TIME_TOO_LONG,
    label: 'cancelBooking.reason5',
  },
  {
    icon: 'Outline-ArrowsOutLineHorizontal',
    id: CancelBookingDto.reason.DRIVER_DELAYED,
    label: 'cancelBooking.reason6',
  },
  {
    icon: 'Outline-ArrowsIn',
    id: CancelBookingDto.reason.DRIVER_NOT_MOVING,
    label: 'cancelBooking.reason7',
  },
  {
    icon: 'Outline-ArrowsCounterClockwise',
    id: CancelBookingDto.reason.INCORRECT_ADDRESS,
    label: 'cancelBooking.reason8',
  },
  {
    icon: 'Outline-DotsSix',
    id: CancelBookingDto.reason.OTHER,
    label: 'cancelBooking.reason9',
  },
];

export enum EKeyTabNotification {
  GENERAL = 'GENERAL',
  PROMOTION = 'PROMOTION',
}

export enum EFilterStatus {
  CurrentBooking = 'CURRENT_BOOKING',
  Completed = 'COMPLETED',
  Cancelled = 'CANCELLED',
}

export const CURRENT_USER_LOCATION: LocationDto = {
  address: {
    address: '331 Rue Main, Gatineau, QC J8P 5P5, Canada',
    name: '331 Rue Main, Gatineau, QC J8P 5K5, Canada',
  },
  geo: {
    latitude: 45.**************,
    longitude: -75.6519366,
  },
};

export const CASH = {
  id: 'cash',
  isDefault: true,
  name: 'payment.cash',
};

export const WALLET = {
  id: 'wallet',
  isDefault: false,
  name: '',
};

export const MESSAGE_CODE = Object.freeze({
  DE_ACTIVE_ACCOUNT: 'USER_IS_DISABLED',
});

export const GLOBAL_STATE_KEY_PREFIX = 'globalState';

export const isReleaseMode = Config.APP_ENV_NAME === 'RELEASE';

export const WAIT_TIME_POSITION_THEME = {
  ['bottom-left']: {
    anchor: {x: 0.82, y: 0.8},
    container: {
      alignItems: 'flex-end',
      height: 80,
      justifyContent: 'flex-end',
      width: 160,
    },
    waitTimeContainer: {bottom: 0, left: 0, position: 'absolute', zIndex: 99},
  },
  ['bottom-right']: {
    anchor: {x: 0.18, y: 0.4},
    container: {
      alignItems: 'flex-start',
      height: 90,
      justifyContent: 'flex-start',
      width: 160,
    },
    waitTimeContainer: {
      bottom: 0,
      position: 'absolute',
      right: 0,
      zIndex: 99,
    },
  },
  ['top-left']: {
    anchor: {x: 0.84, y: 0.75},
    container: {
      alignItems: 'flex-end',
      height: 80,
      justifyContent: 'flex-end',
      width: 160,
    },
    waitTimeContainer: {left: 0, position: 'absolute', top: 0, zIndex: 99},
  },
  ['top-right']: {
    anchor: {x: 0.185, y: 0.75},
    container: {
      alignItems: 'flex-start',
      height: 80,
      justifyContent: 'flex-end',
      width: 160,
    },
    waitTimeContainer: {position: 'absolute', right: 0, top: 0, zIndex: 99},
  },
};

export const DISPATCH_CENTER_NUMBER = '+18196635252';
export enum SOCIAL {
  APPLE = 'apple',
  GOOGLE = 'google',
}

export enum ERide {
  NOW = 'NOW',
  SCHEDULE = 'SCHEDULE',
}

export const MIN_HEIGHT_MODAL_PICKUP = 210;
export const MAX_HEIGHT_MODAL_PICKUP = SCREEN_HEIGHT * 0.5;

export const INITIAL_REGION = {
  latitude: 45.267870606955704,
  latitudeDelta: 3.578196971751211,
  longitude: -75.21365392953157,
  longitudeDelta: 2.35107421875,
};

export const ITEM_SHOW_SUB = 2;

export const HTTPS = 'https://';
export const HTTP = 'http://';

export const SHORT_CUT_ITEMS = [
  {
    icon: 'taxi',
    titleEn: 'Book a ride',
    titleFr: 'Réserver une course',
    type: ETypeQuickAction.BookARide,
    userInfo: {
      url: 'url',
    },
  },
];

export enum EStepVerify {
  PhoneNumber = 'PHONE_NUMBER',
  VerificationCode = 'VERIFICATION_CODE',
}

export const CANADA_PHONE_CODE = '+1';
export const REGION = 'VN';

export const POLICY_VERIFY_PHONE: Record<'en' | 'fr', string> = {
  en: 'https://taxiloyal.com/en/policies/privacy-policy',
  fr: 'https://taxiloyal.com/policies/privacy-policy',
};

export const FILE_NAME_RECEIPT = 'TaxiLoyal_Receipt';
export const PATH_URL = {
  BookingReceipt: '/receipt',
  BookingShare: 'booking-sharing/encrypted',
};
