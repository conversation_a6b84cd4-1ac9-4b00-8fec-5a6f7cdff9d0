import {Platform, StyleSheet} from 'react-native';
import {hasNotch} from 'react-native-device-info';
import Colors from './Colors';

export const PADDING = 20;
export const BORDER_RADIUS = 10;

export const GlobalStyle = StyleSheet.create({
  center: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    backgroundColor: Colors.white,
    flex: 1,
  },
  flex1: {
    flex: 1,
  },
  hitSlop: {
    bottom: 10,
    left: 10,
    right: 10,
    top: 10,
  },
  marginButton: {
    marginBottom: Platform.select({
      android: PADDING,
      ios: hasNotch() ? 0 : PADDING,
    }),
  },
  shadow: {
    elevation: 14,
    shadowColor: '#D4D3D0',
    shadowOffset: {
      height: 12,
      width: 0,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
  shadowButton: {
    elevation: 21,
    shadowColor: '#000',
    shadowOffset: {
      height: 10,
      width: 0,
    },
    shadowOpacity: 0.53,
    shadowRadius: 13.97,
  },
  shadowCar: {
    elevation: 24,
    shadowColor: '#000',
    shadowOffset: {
      height: 12,
      width: 2,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
  },
  shadowCard: {
    elevation: 21,
    shadowColor: '#242525',
    shadowOffset: {
      height: 10,
      width: 0,
    },
    shadowOpacity: 0.53,
    shadowRadius: 13.97,
  },
  shadowSearchInput: {
    elevation: 7,
    shadowColor: '#ADACAA',
    shadowOffset: {
      height: 3,
      width: 0,
    },
    shadowOpacity: 0.29,

    shadowRadius: 4.65,
  },
  shadowSoft: {
    elevation: 5,
    shadowColor: '#D4D3D0',
    shadowOffset: {
      height: 2,
      width: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});
