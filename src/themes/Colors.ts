const defaultColors = {
  alertDefault: '#FFCD36',
  background: '#FAFAF7',
  backgroundSearch: '#FAF9F7',
  blue100: '#E5F1FF',
  blue200: '#B4CFFF',
  blue500: '#6AA1FF',
  blue700: '#007AFF',
  borderColor: '#e6e3e2',
  colorLink: '#1570EF',
  danger100: '#FEF1F2',
  danger200: '#FEE4E2',
  danger50: '#FEF1F2',
  danger500: '#F8727D',
  danger600: '#EF4352',

  danger700: '#E02D3C',
  defaultSecondaryBg: '#F5F5F5',
  error: '#FF3B30',
  fadeBackground: 'rgba(0, 0, 0, 0.50)',
  fadeBackground2: 'rgba(0, 0, 0, 0.40)',
  fadeBackground3: 'rgba(0, 0, 0, 0.10)',
  gold: '#FEDC34',
  grey1: '#1E2328',
  grey2: '#34383D',
  grey3: '#616568',
  grey4: '#8E9193',
  grey5: '#BBBDBE',
  grey6: '#E8E9E9',
  neutral100: '#FAF9F7',
  neutral150: '#F4F3F2',
  neutral200: '#EEEDEC',
  neutral300: '#E5E4E3',
  neutral400: '#D4D3D0',
  neutral50: '#FCFCFC',
  neutral500: '#ADACAA',
  neutral600: '#848281',
  neutral700: '#4C4D4D',
  neutral800: '#343433',
  neutral850: '#2C2C2B',
  neutral875: '#242525',
  neutral900: '#141518',
  neutralDefault: '#16330014',
  pink: '#FDC8CA',
  primary: '#ED1C24',
  primary100: '#FFF1F2',
  primary200: '#FFDFE0',
  primary300: '#FFC5C7',
  primary400: '#FF9DA1',
  primary600: '#FE353D',
  primary900: '#A41016',
  purple100: '#F8F5FF',
  purple500: '#AF89FA',
  purple900: '#5221B5',
  starYellow: '#FABC41',
  success: '#088752',
  success100: '#EDFDF6',
  success500: '#62C49B',
  success600: '#0EA466',
  successDefault: '#34C759',
  successSoft: '#EAF9EE',
  transparent: 'transparent',
  warning100: '#FFF8EB',
  warning600: '#DB7D1F',
  warning700: '#B25E09',
  warningBorderHover: '#F38744',
  white: '#fff',
};

export default defaultColors;
