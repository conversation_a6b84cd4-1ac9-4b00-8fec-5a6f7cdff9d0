import {AppSubscriptionEntity, SubscriptionPlanEntity} from '@requests';

export interface ISubscription extends AppSubscriptionEntity {
  isSubscribe?: boolean;
  plan?: SubscriptionPlanEntity;
  renewDate?: string;
  endDate?: string;
  nextPlan: SubscriptionPlanEntity;
  isChangePlan?: boolean;
  subscriptionId?: string;
  appliedDiscounts?: string[];
}

export interface PackageCardProps {
  onPress?: () => void;
  data?: ISubscription;
  isDetail?: boolean;
  mySubscription?: ISubscription;
}

export interface StatusBadgeProps {
  // isSubscribed?: boolean;
  color: string;
  label: string;
}

export interface PackageInfoProps {
  name?: string;
  planName?: string;
  date?: string;
  dateLabel: string;
  statusColor: string;
  statusLabel: string;
  isSubscribed?: boolean;
  isShowChangePlan?: boolean;
  subscription?: ISubscription;
  nextPlan?: SubscriptionPlanEntity;
  isChangePlan?: boolean;
  isDetail?: boolean;
}
