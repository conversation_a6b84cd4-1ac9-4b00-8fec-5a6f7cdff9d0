@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?vfrqtm');
  src:  url('fonts/icomoon.eot?vfrqtm#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?vfrqtm') format('truetype'),
    url('fonts/icomoon.woff?vfrqtm') format('woff'),
    url('fonts/icomoon.svg?vfrqtm#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Fill-AddressBook:before {
  content: "\e900";
}
.icon-Fill-Airplane:before {
  content: "\e901";
}
.icon-Fill-AirplaneInFlight:before {
  content: "\e902";
}
.icon-Fill-AirplaneLanding:before {
  content: "\e903";
}
.icon-Fill-AirplaneTakeoff:before {
  content: "\e904";
}
.icon-Fill-AirplaneTilt:before {
  content: "\e905";
}
.icon-Fill-Airplay:before {
  content: "\e906";
}
.icon-Fill-AirTrafficControl:before {
  content: "\e907";
}
.icon-Fill-Alarm:before {
  content: "\e908";
}
.icon-Fill-Alien:before {
  content: "\e909";
}
.icon-Fill-AlignBottom:before {
  content: "\e90a";
}
.icon-Fill-AlignBottomSimple:before {
  content: "\e90b";
}
.icon-Fill-AlignCenterHorizontal:before {
  content: "\e90c";
}
.icon-Fill-AlignCenterHorizontalSimple:before {
  content: "\e90d";
}
.icon-Fill-AlignCenterVertical:before {
  content: "\e90e";
}
.icon-Fill-AlignCenterVerticalSimple:before {
  content: "\e90f";
}
.icon-Fill-AlignLeft:before {
  content: "\e910";
}
.icon-Fill-AlignLeftSimple:before {
  content: "\e911";
}
.icon-Fill-AlignRight:before {
  content: "\e912";
}
.icon-Fill-AlignRightSimple:before {
  content: "\e913";
}
.icon-Fill-AlignTop:before {
  content: "\e914";
}
.icon-Fill-AlignTopSimple:before {
  content: "\e915";
}
.icon-Fill-AmazonLogo:before {
  content: "\e916";
}
.icon-Fill-Anchor:before {
  content: "\e917";
}
.icon-Fill-AnchorSimple:before {
  content: "\e918";
}
.icon-Fill-AndroidLogo:before {
  content: "\e919";
}
.icon-Fill-AngularLogo:before {
  content: "\e91a";
}
.icon-Fill-Aperture:before {
  content: "\e91b";
}
.icon-Fill-AppleLogo:before {
  content: "\e91c";
}
.icon-Fill-ApplePodcastsLogo:before {
  content: "\e91d";
}
.icon-Fill-AppStoreLogo:before {
  content: "\e91e";
}
.icon-Fill-AppWindow:before {
  content: "\e91f";
}
.icon-Fill-Archive:before {
  content: "\e920";
}
.icon-Fill-ArchiveBox:before {
  content: "\e921";
}
.icon-Fill-ArchiveTray:before {
  content: "\e922";
}
.icon-Fill-Armchair:before {
  content: "\e923";
}
.icon-Fill-ArrowArcLeft:before {
  content: "\e924";
}
.icon-Fill-ArrowArcRight:before {
  content: "\e925";
}
.icon-Fill-ArrowBendDoubleUpLeft:before {
  content: "\e926";
}
.icon-Fill-ArrowBendDoubleUpRight:before {
  content: "\e927";
}
.icon-Fill-ArrowBendDownLeft:before {
  content: "\e928";
}
.icon-Fill-ArrowBendDownRight:before {
  content: "\e929";
}
.icon-Fill-ArrowBendLeftDown:before {
  content: "\e92a";
}
.icon-Fill-ArrowBendLeftUp:before {
  content: "\e92b";
}
.icon-Fill-ArrowBendRightDown:before {
  content: "\e92c";
}
.icon-Fill-ArrowBendRightUp:before {
  content: "\e92d";
}
.icon-Fill-ArrowBendUpLeft:before {
  content: "\e92e";
}
.icon-Fill-ArrowBendUpRight:before {
  content: "\e92f";
}
.icon-Fill-ArrowCircleDown:before {
  content: "\e930";
}
.icon-Fill-ArrowCircleDownLeft:before {
  content: "\e931";
}
.icon-Fill-ArrowCircleDownRight:before {
  content: "\e932";
}
.icon-Fill-ArrowCircleLeft:before {
  content: "\e933";
}
.icon-Fill-ArrowCircleRight:before {
  content: "\e934";
}
.icon-Fill-ArrowCircleUp:before {
  content: "\e935";
}
.icon-Fill-ArrowCircleUpLeft:before {
  content: "\e936";
}
.icon-Fill-ArrowCircleUpRight:before {
  content: "\e937";
}
.icon-Fill-ArrowClockwise:before {
  content: "\e938";
}
.icon-Fill-ArrowCounterClockwise:before {
  content: "\e939";
}
.icon-Fill-ArrowDown:before {
  content: "\e93a";
}
.icon-Fill-ArrowDownLeft:before {
  content: "\e93b";
}
.icon-Fill-ArrowDownRight:before {
  content: "\e93c";
}
.icon-Fill-ArrowElbowDownLeft:before {
  content: "\e93d";
}
.icon-Fill-ArrowElbowDownRight:before {
  content: "\e93e";
}
.icon-Fill-ArrowElbowLeft:before {
  content: "\e93f";
}
.icon-Fill-ArrowElbowLeftDown:before {
  content: "\e940";
}
.icon-Fill-ArrowElbowLeftUp:before {
  content: "\e941";
}
.icon-Fill-ArrowElbowRight:before {
  content: "\e942";
}
.icon-Fill-ArrowElbowRightDown:before {
  content: "\e943";
}
.icon-Fill-ArrowElbowRightUp:before {
  content: "\e944";
}
.icon-Fill-ArrowElbowUpLeft:before {
  content: "\e945";
}
.icon-Fill-ArrowElbowUpRight:before {
  content: "\e946";
}
.icon-Fill-ArrowFatDown:before {
  content: "\e947";
}
.icon-Fill-ArrowFatLeft:before {
  content: "\e948";
}
.icon-Fill-ArrowFatLineDown:before {
  content: "\e949";
}
.icon-Fill-ArrowFatLineLeft:before {
  content: "\e94a";
}
.icon-Fill-ArrowFatLineRight:before {
  content: "\e94b";
}
.icon-Fill-ArrowFatLinesDown:before {
  content: "\e94c";
}
.icon-Fill-ArrowFatLinesLeft:before {
  content: "\e94d";
}
.icon-Fill-ArrowFatLinesRight:before {
  content: "\e94e";
}
.icon-Fill-ArrowFatLinesUp:before {
  content: "\e94f";
}
.icon-Fill-ArrowFatLineUp:before {
  content: "\e950";
}
.icon-Fill-ArrowFatRight:before {
  content: "\e951";
}
.icon-Fill-ArrowFatUp:before {
  content: "\e952";
}
.icon-Fill-ArrowLeft:before {
  content: "\e953";
}
.icon-Fill-ArrowLineDown:before {
  content: "\e954";
}
.icon-Fill-ArrowLineDownLeft:before {
  content: "\e955";
}
.icon-Fill-ArrowLineDownRight:before {
  content: "\e956";
}
.icon-Fill-ArrowLineLeft:before {
  content: "\e957";
}
.icon-Fill-ArrowLineRight:before {
  content: "\e958";
}
.icon-Fill-ArrowLineUp:before {
  content: "\e959";
}
.icon-Fill-ArrowLineUpLeft:before {
  content: "\e95a";
}
.icon-Fill-ArrowLineUpRight:before {
  content: "\e95b";
}
.icon-Fill-ArrowRight:before {
  content: "\e95c";
}
.icon-Fill-ArrowsClockwise:before {
  content: "\e95d";
}
.icon-Fill-ArrowsCounterClockwise:before {
  content: "\e95e";
}
.icon-Fill-ArrowsDownUp:before {
  content: "\e95f";
}
.icon-Fill-ArrowsHorizontal:before {
  content: "\e960";
}
.icon-Fill-ArrowsIn:before {
  content: "\e961";
}
.icon-Fill-ArrowsInCardinal:before {
  content: "\e962";
}
.icon-Fill-ArrowsInLineHorizontal:before {
  content: "\e963";
}
.icon-Fill-ArrowsInLineVertical:before {
  content: "\e964";
}
.icon-Fill-ArrowsInSimple:before {
  content: "\e965";
}
.icon-Fill-ArrowsLeftRight:before {
  content: "\e966";
}
.icon-Fill-ArrowsMerge:before {
  content: "\e967";
}
.icon-Fill-ArrowsOut:before {
  content: "\e968";
}
.icon-Fill-ArrowsOutCardinal:before {
  content: "\e969";
}
.icon-Fill-ArrowsOutLineHorizontal:before {
  content: "\e96a";
}
.icon-Fill-ArrowsOutLineVertical:before {
  content: "\e96b";
}
.icon-Fill-ArrowsOutSimple:before {
  content: "\e96c";
}
.icon-Fill-ArrowSquareDown:before {
  content: "\e96d";
}
.icon-Fill-ArrowSquareDownLeft:before {
  content: "\e96e";
}
.icon-Fill-ArrowSquareDownRight:before {
  content: "\e96f";
}
.icon-Fill-ArrowSquareIn:before {
  content: "\e970";
}
.icon-Fill-ArrowSquareLeft:before {
  content: "\e971";
}
.icon-Fill-ArrowSquareOut:before {
  content: "\e972";
}
.icon-Fill-ArrowSquareRight:before {
  content: "\e973";
}
.icon-Fill-ArrowSquareUp:before {
  content: "\e974";
}
.icon-Fill-ArrowSquareUpLeft:before {
  content: "\e975";
}
.icon-Fill-ArrowSquareUpRight:before {
  content: "\e976";
}
.icon-Fill-ArrowsSplit:before {
  content: "\e977";
}
.icon-Fill-ArrowsVertical:before {
  content: "\e978";
}
.icon-Fill-ArrowUDownLeft:before {
  content: "\e979";
}
.icon-Fill-ArrowUDownRight:before {
  content: "\e97a";
}
.icon-Fill-ArrowULeftDown:before {
  content: "\e97b";
}
.icon-Fill-ArrowULeftUp:before {
  content: "\e97c";
}
.icon-Fill-ArrowUp:before {
  content: "\e97d";
}
.icon-Fill-ArrowUpLeft:before {
  content: "\e97e";
}
.icon-Fill-ArrowUpRight:before {
  content: "\e97f";
}
.icon-Fill-ArrowURightDown:before {
  content: "\e980";
}
.icon-Fill-ArrowURightUp:before {
  content: "\e981";
}
.icon-Fill-ArrowUUpLeft:before {
  content: "\e982";
}
.icon-Fill-ArrowUUpRight:before {
  content: "\e983";
}
.icon-Fill-Article:before {
  content: "\e984";
}
.icon-Fill-ArticleMedium:before {
  content: "\e985";
}
.icon-Fill-ArticleNyTimes:before {
  content: "\e986";
}
.icon-Fill-Asterisk:before {
  content: "\e987";
}
.icon-Fill-AsteriskSimple:before {
  content: "\e988";
}
.icon-Fill-At:before {
  content: "\e989";
}
.icon-Fill-Atom:before {
  content: "\e98a";
}
.icon-Fill-Baby:before {
  content: "\e98b";
}
.icon-Fill-Backpack:before {
  content: "\e98c";
}
.icon-Fill-Backspace:before {
  content: "\e98d";
}
.icon-Fill-Bag:before {
  content: "\e98e";
}
.icon-Fill-BagSimple:before {
  content: "\e98f";
}
.icon-Fill-Balloon:before {
  content: "\e990";
}
.icon-Fill-Bandaids:before {
  content: "\e991";
}
.icon-Fill-Bank:before {
  content: "\e992";
}
.icon-Fill-Barbell:before {
  content: "\e993";
}
.icon-Fill-Barcode:before {
  content: "\e994";
}
.icon-Fill-Barricade:before {
  content: "\e995";
}
.icon-Fill-Baseball:before {
  content: "\e996";
}
.icon-Fill-BaseballCap:before {
  content: "\e997";
}
.icon-Fill-Basket:before {
  content: "\e998";
}
.icon-Fill-Basketball:before {
  content: "\e999";
}
.icon-Fill-Bathtub:before {
  content: "\e99a";
}
.icon-Fill-BatteryCharging:before {
  content: "\e99b";
}
.icon-Fill-BatteryChargingVertical:before {
  content: "\e99c";
}
.icon-Fill-BatteryEmpty:before {
  content: "\e99d";
}
.icon-Fill-BatteryFull:before {
  content: "\e99e";
}
.icon-Fill-BatteryHigh:before {
  content: "\e99f";
}
.icon-Fill-BatteryLow:before {
  content: "\e9a0";
}
.icon-Fill-BatteryMedium:before {
  content: "\e9a1";
}
.icon-Fill-BatteryPlus:before {
  content: "\e9a2";
}
.icon-Fill-BatteryPlusVertical:before {
  content: "\e9a3";
}
.icon-Fill-BatteryVerticalEmpty:before {
  content: "\e9a4";
}
.icon-Fill-BatteryVerticalFull:before {
  content: "\e9a5";
}
.icon-Fill-BatteryVerticalHigh:before {
  content: "\e9a6";
}
.icon-Fill-BatteryVerticalLow:before {
  content: "\e9a7";
}
.icon-Fill-BatteryVerticalMedium:before {
  content: "\e9a8";
}
.icon-Fill-BatteryWarning:before {
  content: "\e9a9";
}
.icon-Fill-BatteryWarningVertical:before {
  content: "\e9aa";
}
.icon-Fill-Bed:before {
  content: "\e9ab";
}
.icon-Fill-BeerBottle:before {
  content: "\e9ac";
}
.icon-Fill-BeerStein:before {
  content: "\e9ad";
}
.icon-Fill-BehanceLogo:before {
  content: "\e9ae";
}
.icon-Fill-Bell:before {
  content: "\e9af";
}
.icon-Fill-BellRinging:before {
  content: "\e9b0";
}
.icon-Fill-BellSimple:before {
  content: "\e9b1";
}
.icon-Fill-BellSimpleRinging:before {
  content: "\e9b2";
}
.icon-Fill-BellSimpleSlash:before {
  content: "\e9b3";
}
.icon-Fill-BellSimpleZ:before {
  content: "\e9b4";
}
.icon-Fill-BellSlash:before {
  content: "\e9b5";
}
.icon-Fill-BellZ:before {
  content: "\e9b6";
}
.icon-Fill-BezierCurve:before {
  content: "\e9b7";
}
.icon-Fill-Bicycle:before {
  content: "\e9b8";
}
.icon-Fill-Binoculars:before {
  content: "\e9b9";
}
.icon-Fill-Bird:before {
  content: "\e9ba";
}
.icon-Fill-Bluetooth:before {
  content: "\e9bb";
}
.icon-Fill-BluetoothConnected:before {
  content: "\e9bc";
}
.icon-Fill-BluetoothSlash:before {
  content: "\e9bd";
}
.icon-Fill-BluetoothX:before {
  content: "\e9be";
}
.icon-Fill-Boat:before {
  content: "\e9bf";
}
.icon-Fill-Bone:before {
  content: "\e9c0";
}
.icon-Fill-Book:before {
  content: "\e9c1";
}
.icon-Fill-BookBookmark:before {
  content: "\e9c2";
}
.icon-Fill-Bookmark:before {
  content: "\e9c3";
}
.icon-Fill-Bookmarks:before {
  content: "\e9c4";
}
.icon-Fill-BookmarkSimple:before {
  content: "\e9c5";
}
.icon-Fill-BookmarksSimple:before {
  content: "\e9c6";
}
.icon-Fill-BookOpen:before {
  content: "\e9c7";
}
.icon-Fill-BookOpenText:before {
  content: "\e9c8";
}
.icon-Fill-Books:before {
  content: "\e9c9";
}
.icon-Fill-Boot:before {
  content: "\e9ca";
}
.icon-Fill-BoundingBox:before {
  content: "\e9cb";
}
.icon-Fill-BowlFood:before {
  content: "\e9cc";
}
.icon-Fill-BracketsAngle:before {
  content: "\e9cd";
}
.icon-Fill-BracketsCurly:before {
  content: "\e9ce";
}
.icon-Fill-BracketsRound:before {
  content: "\e9cf";
}
.icon-Fill-BracketsSquare:before {
  content: "\e9d0";
}
.icon-Fill-Brain:before {
  content: "\e9d1";
}
.icon-Fill-Brandy:before {
  content: "\e9d2";
}
.icon-Fill-Bridge:before {
  content: "\e9d3";
}
.icon-Fill-Briefcase:before {
  content: "\e9d4";
}
.icon-Fill-BriefcaseMetal:before {
  content: "\e9d5";
}
.icon-Fill-Broadcast:before {
  content: "\e9d6";
}
.icon-Fill-Broom:before {
  content: "\e9d7";
}
.icon-Fill-Browser:before {
  content: "\e9d8";
}
.icon-Fill-Browsers:before {
  content: "\e9d9";
}
.icon-Fill-Bug:before {
  content: "\e9da";
}
.icon-Fill-BugBeetle:before {
  content: "\e9db";
}
.icon-Fill-BugDroid:before {
  content: "\e9dc";
}
.icon-Fill-Buildings:before {
  content: "\e9dd";
}
.icon-Fill-Bus:before {
  content: "\e9de";
}
.icon-Fill-Butterfly:before {
  content: "\e9df";
}
.icon-Fill-Cactus:before {
  content: "\e9e0";
}
.icon-Fill-Cake:before {
  content: "\e9e1";
}
.icon-Fill-Calculator:before {
  content: "\e9e2";
}
.icon-Fill-Calendar:before {
  content: "\e9e3";
}
.icon-Fill-CalendarBlank:before {
  content: "\e9e4";
}
.icon-Fill-CalendarCheck:before {
  content: "\e9e5";
}
.icon-Fill-CalendarPlus:before {
  content: "\e9e6";
}
.icon-Fill-CalendarX:before {
  content: "\e9e7";
}
.icon-Fill-CallBell:before {
  content: "\e9e8";
}
.icon-Fill-Camera:before {
  content: "\e9e9";
}
.icon-Fill-CameraPlus:before {
  content: "\e9ea";
}
.icon-Fill-CameraRotate:before {
  content: "\e9eb";
}
.icon-Fill-CameraSlash:before {
  content: "\e9ec";
}
.icon-Fill-Campfire:before {
  content: "\e9ed";
}
.icon-Fill-Car:before {
  content: "\e9ee";
}
.icon-Fill-Cardholder:before {
  content: "\e9ef";
}
.icon-Fill-Cards:before {
  content: "\e9f0";
}
.icon-Fill-CaretCircleDoubleDown:before {
  content: "\e9f1";
}
.icon-Fill-CaretCircleDoubleLeft:before {
  content: "\e9f2";
}
.icon-Fill-CaretCircleDoubleRight:before {
  content: "\e9f3";
}
.icon-Fill-CaretCircleDoubleUp:before {
  content: "\e9f4";
}
.icon-Fill-CaretCircleDown:before {
  content: "\e9f5";
}
.icon-Fill-CaretCircleLeft:before {
  content: "\e9f6";
}
.icon-Fill-CaretCircleRight:before {
  content: "\e9f7";
}
.icon-Fill-CaretCircleUp:before {
  content: "\e9f8";
}
.icon-Fill-CaretCircleUpDown:before {
  content: "\e9f9";
}
.icon-Fill-CaretDoubleDown:before {
  content: "\e9fa";
}
.icon-Fill-CaretDoubleLeft:before {
  content: "\e9fb";
}
.icon-Fill-CaretDoubleRight:before {
  content: "\e9fc";
}
.icon-Fill-CaretDoubleUp:before {
  content: "\e9fd";
}
.icon-Fill-CaretDown:before {
  content: "\e9fe";
}
.icon-Fill-CaretLeft:before {
  content: "\e9ff";
}
.icon-Fill-CaretRight:before {
  content: "\ea00";
}
.icon-Fill-CaretUp:before {
  content: "\ea01";
}
.icon-Fill-CaretUpDown:before {
  content: "\ea02";
}
.icon-Fill-CarProfile:before {
  content: "\ea03";
}
.icon-Fill-Carrot:before {
  content: "\ea04";
}
.icon-Fill-CarSimple:before {
  content: "\ea05";
}
.icon-Fill-CassetteTape:before {
  content: "\ea06";
}
.icon-Fill-CastleTurret:before {
  content: "\ea07";
}
.icon-Fill-Cat:before {
  content: "\ea08";
}
.icon-Fill-CellSignalFull:before {
  content: "\ea09";
}
.icon-Fill-CellSignalHigh:before {
  content: "\ea0a";
}
.icon-Fill-CellSignalLow:before {
  content: "\ea0b";
}
.icon-Fill-CellSignalMedium:before {
  content: "\ea0c";
}
.icon-Fill-CellSignalNone:before {
  content: "\ea0d";
}
.icon-Fill-CellSignalSlash:before {
  content: "\ea0e";
}
.icon-Fill-CellSignalX:before {
  content: "\ea0f";
}
.icon-Fill-Certificate:before {
  content: "\ea10";
}
.icon-Fill-Chair:before {
  content: "\ea11";
}
.icon-Fill-Chalkboard:before {
  content: "\ea12";
}
.icon-Fill-ChalkboardSimple:before {
  content: "\ea13";
}
.icon-Fill-ChalkboardTeacher:before {
  content: "\ea14";
}
.icon-Fill-Champagne:before {
  content: "\ea15";
}
.icon-Fill-ChargingStation:before {
  content: "\ea16";
}
.icon-Fill-ChartBar:before {
  content: "\ea17";
}
.icon-Fill-ChartBarHorizontal:before {
  content: "\ea18";
}
.icon-Fill-ChartDonut:before {
  content: "\ea19";
}
.icon-Fill-ChartLine:before {
  content: "\ea1a";
}
.icon-Fill-ChartLineDown:before {
  content: "\ea1b";
}
.icon-Fill-ChartLineUp:before {
  content: "\ea1c";
}
.icon-Fill-ChartPie:before {
  content: "\ea1d";
}
.icon-Fill-ChartPieSlice:before {
  content: "\ea1e";
}
.icon-Fill-ChartPolar:before {
  content: "\ea1f";
}
.icon-Fill-ChartScatter:before {
  content: "\ea20";
}
.icon-Fill-Chat:before {
  content: "\ea21";
}
.icon-Fill-ChatCentered:before {
  content: "\ea22";
}
.icon-Fill-ChatCenteredDots:before {
  content: "\ea23";
}
.icon-Fill-ChatCenteredText:before {
  content: "\ea24";
}
.icon-Fill-ChatCircle:before {
  content: "\ea25";
}
.icon-Fill-ChatCircleDots:before {
  content: "\ea26";
}
.icon-Fill-ChatCircleText:before {
  content: "\ea27";
}
.icon-Fill-ChatDots:before {
  content: "\ea28";
}
.icon-Fill-Chats:before {
  content: "\ea29";
}
.icon-Fill-ChatsCircle:before {
  content: "\ea2a";
}
.icon-Fill-ChatsTeardrop:before {
  content: "\ea2b";
}
.icon-Fill-ChatTeardrop:before {
  content: "\ea2c";
}
.icon-Fill-ChatTeardropDots:before {
  content: "\ea2d";
}
.icon-Fill-ChatTeardropText:before {
  content: "\ea2e";
}
.icon-Fill-ChatText:before {
  content: "\ea2f";
}
.icon-Fill-Check:before {
  content: "\ea30";
}
.icon-Fill-CheckCircle:before {
  content: "\ea31";
}
.icon-Fill-CheckFat:before {
  content: "\ea32";
}
.icon-Fill-Checks:before {
  content: "\ea33";
}
.icon-Fill-CheckSquare:before {
  content: "\ea34";
}
.icon-Fill-CheckSquareOffset:before {
  content: "\ea35";
}
.icon-Fill-Church:before {
  content: "\ea36";
}
.icon-Fill-Circle:before {
  content: "\ea37";
}
.icon-Fill-CircleDashed:before {
  content: "\ea38";
}
.icon-Fill-CircleHalf:before {
  content: "\ea39";
}
.icon-Fill-CircleHalfTilt:before {
  content: "\ea3a";
}
.icon-Fill-CircleNotch:before {
  content: "\ea3b";
}
.icon-Fill-CirclesFour:before {
  content: "\ea3c";
}
.icon-Fill-CirclesThree:before {
  content: "\ea3d";
}
.icon-Fill-CirclesThreePlus:before {
  content: "\ea3e";
}
.icon-Fill-Circuitry:before {
  content: "\ea3f";
}
.icon-Fill-Clipboard:before {
  content: "\ea40";
}
.icon-Fill-ClipboardText:before {
  content: "\ea41";
}
.icon-Fill-Clock:before {
  content: "\ea42";
}
.icon-Fill-ClockAfternoon:before {
  content: "\ea43";
}
.icon-Fill-ClockClockwise:before {
  content: "\ea44";
}
.icon-Fill-ClockCountdown:before {
  content: "\ea45";
}
.icon-Fill-ClockCounterClockwise:before {
  content: "\ea46";
}
.icon-Fill-ClosedCaptioning:before {
  content: "\ea47";
}
.icon-Fill-Cloud:before {
  content: "\ea48";
}
.icon-Fill-CloudArrowDown:before {
  content: "\ea49";
}
.icon-Fill-CloudArrowUp:before {
  content: "\ea4a";
}
.icon-Fill-CloudCheck:before {
  content: "\ea4b";
}
.icon-Fill-CloudFog:before {
  content: "\ea4c";
}
.icon-Fill-CloudLightning:before {
  content: "\ea4d";
}
.icon-Fill-CloudMoon:before {
  content: "\ea4e";
}
.icon-Fill-CloudRain:before {
  content: "\ea4f";
}
.icon-Fill-CloudSlash:before {
  content: "\ea50";
}
.icon-Fill-CloudSnow:before {
  content: "\ea51";
}
.icon-Fill-CloudSun:before {
  content: "\ea52";
}
.icon-Fill-CloudWarning:before {
  content: "\ea53";
}
.icon-Fill-CloudX:before {
  content: "\ea54";
}
.icon-Fill-Club:before {
  content: "\ea55";
}
.icon-Fill-CoatHanger:before {
  content: "\ea56";
}
.icon-Fill-CodaLogo:before {
  content: "\ea57";
}
.icon-Fill-Code:before {
  content: "\ea58";
}
.icon-Fill-CodeBlock:before {
  content: "\ea59";
}
.icon-Fill-CodepenLogo:before {
  content: "\ea5a";
}
.icon-Fill-CodesandboxLogo:before {
  content: "\ea5b";
}
.icon-Fill-CodeSimple:before {
  content: "\ea5c";
}
.icon-Fill-Coffee:before {
  content: "\ea5d";
}
.icon-Fill-Coin:before {
  content: "\ea5e";
}
.icon-Fill-Coins:before {
  content: "\ea5f";
}
.icon-Fill-CoinVertical:before {
  content: "\ea60";
}
.icon-Fill-Columns:before {
  content: "\ea61";
}
.icon-Fill-Command:before {
  content: "\ea62";
}
.icon-Fill-Compass:before {
  content: "\ea63";
}
.icon-Fill-CompassTool:before {
  content: "\ea64";
}
.icon-Fill-ComputerTower:before {
  content: "\ea65";
}
.icon-Fill-Confetti:before {
  content: "\ea66";
}
.icon-Fill-ContactlessPayment:before {
  content: "\ea67";
}
.icon-Fill-Control:before {
  content: "\ea68";
}
.icon-Fill-Cookie:before {
  content: "\ea69";
}
.icon-Fill-CookingPot:before {
  content: "\ea6a";
}
.icon-Fill-Copy:before {
  content: "\ea6b";
}
.icon-Fill-Copyleft:before {
  content: "\ea6c";
}
.icon-Fill-Copyright:before {
  content: "\ea6d";
}
.icon-Fill-CopySimple:before {
  content: "\ea6e";
}
.icon-Fill-CornersIn:before {
  content: "\ea6f";
}
.icon-Fill-CornersOut:before {
  content: "\ea70";
}
.icon-Fill-Couch:before {
  content: "\ea71";
}
.icon-Fill-Cpu:before {
  content: "\ea72";
}
.icon-Fill-CreditCard:before {
  content: "\ea73";
}
.icon-Fill-Crop:before {
  content: "\ea74";
}
.icon-Fill-Cross:before {
  content: "\ea75";
}
.icon-Fill-Crosshair:before {
  content: "\ea76";
}
.icon-Fill-CrosshairSimple:before {
  content: "\ea77";
}
.icon-Fill-Crown:before {
  content: "\ea78";
}
.icon-Fill-CrownSimple:before {
  content: "\ea79";
}
.icon-Fill-Cube:before {
  content: "\ea7a";
}
.icon-Fill-CubeFocus:before {
  content: "\ea7b";
}
.icon-Fill-CubeTransparent:before {
  content: "\ea7c";
}
.icon-Fill-CurrencyBtc:before {
  content: "\ea7d";
}
.icon-Fill-CurrencyCircleDollar:before {
  content: "\ea7e";
}
.icon-Fill-CurrencyCny:before {
  content: "\ea7f";
}
.icon-Fill-CurrencyDollar:before {
  content: "\ea80";
}
.icon-Fill-CurrencyDollarSimple:before {
  content: "\ea81";
}
.icon-Fill-CurrencyEth:before {
  content: "\ea82";
}
.icon-Fill-CurrencyEur:before {
  content: "\ea83";
}
.icon-Fill-CurrencyGbp:before {
  content: "\ea84";
}
.icon-Fill-CurrencyInr:before {
  content: "\ea85";
}
.icon-Fill-CurrencyJpy:before {
  content: "\ea86";
}
.icon-Fill-CurrencyKrw:before {
  content: "\ea87";
}
.icon-Fill-CurrencyKzt:before {
  content: "\ea88";
}
.icon-Fill-CurrencyNgn:before {
  content: "\ea89";
}
.icon-Fill-CurrencyRub:before {
  content: "\ea8a";
}
.icon-Fill-Cursor:before {
  content: "\ea8b";
}
.icon-Fill-CursorClick:before {
  content: "\ea8c";
}
.icon-Fill-CursorText:before {
  content: "\ea8d";
}
.icon-Fill-Cylinder:before {
  content: "\ea8e";
}
.icon-Fill-Database:before {
  content: "\ea8f";
}
.icon-Fill-Desktop:before {
  content: "\ea90";
}
.icon-Fill-DesktopTower:before {
  content: "\ea91";
}
.icon-Fill-Detective:before {
  content: "\ea92";
}
.icon-Fill-DeviceMobile:before {
  content: "\ea93";
}
.icon-Fill-DeviceMobileCamera:before {
  content: "\ea94";
}
.icon-Fill-DeviceMobileSpeaker:before {
  content: "\ea95";
}
.icon-Fill-Devices:before {
  content: "\ea96";
}
.icon-Fill-DeviceTablet:before {
  content: "\ea97";
}
.icon-Fill-DeviceTabletCamera:before {
  content: "\ea98";
}
.icon-Fill-DeviceTabletSpeaker:before {
  content: "\ea99";
}
.icon-Fill-DevToLogo:before {
  content: "\ea9a";
}
.icon-Fill-Diamond:before {
  content: "\ea9b";
}
.icon-Fill-DiamondsFour:before {
  content: "\ea9c";
}
.icon-Fill-DiceFive:before {
  content: "\ea9d";
}
.icon-Fill-DiceFour:before {
  content: "\ea9e";
}
.icon-Fill-DiceOne:before {
  content: "\ea9f";
}
.icon-Fill-DiceSix:before {
  content: "\eaa0";
}
.icon-Fill-DiceThree:before {
  content: "\eaa1";
}
.icon-Fill-DiceTwo:before {
  content: "\eaa2";
}
.icon-Fill-Disc:before {
  content: "\eaa3";
}
.icon-Fill-DiscordLogo:before {
  content: "\eaa4";
}
.icon-Fill-Divide:before {
  content: "\eaa5";
}
.icon-Fill-Dna:before {
  content: "\eaa6";
}
.icon-Fill-Dog:before {
  content: "\eaa7";
}
.icon-Fill-Door:before {
  content: "\eaa8";
}
.icon-Fill-DoorOpen:before {
  content: "\eaa9";
}
.icon-Fill-Dot:before {
  content: "\eaaa";
}
.icon-Fill-DotOutline:before {
  content: "\eaab";
}
.icon-Fill-DotsNine:before {
  content: "\eaac";
}
.icon-Fill-DotsSix:before {
  content: "\eaad";
}
.icon-Fill-DotsSixVertical:before {
  content: "\eaae";
}
.icon-Fill-DotsThree:before {
  content: "\eaaf";
}
.icon-Fill-DotsThreeCircle:before {
  content: "\eab0";
}
.icon-Fill-DotsThreeCircleVertical:before {
  content: "\eab1";
}
.icon-Fill-DotsThreeOutline:before {
  content: "\eab2";
}
.icon-Fill-DotsThreeOutlineVertical:before {
  content: "\eab3";
}
.icon-Fill-DotsThreeVertical:before {
  content: "\eab4";
}
.icon-Fill-Download:before {
  content: "\eab5";
}
.icon-Fill-DownloadSimple:before {
  content: "\eab6";
}
.icon-Fill-Dress:before {
  content: "\eab7";
}
.icon-Fill-DribbbleLogo:before {
  content: "\eab8";
}
.icon-Fill-Drop:before {
  content: "\eab9";
}
.icon-Fill-DropboxLogo:before {
  content: "\eaba";
}
.icon-Fill-DropHalf:before {
  content: "\eabb";
}
.icon-Fill-DropHalfBottom:before {
  content: "\eabc";
}
.icon-Fill-Ear:before {
  content: "\eabd";
}
.icon-Fill-EarSlash:before {
  content: "\eabe";
}
.icon-Fill-Egg:before {
  content: "\eabf";
}
.icon-Fill-EggCrack:before {
  content: "\eac0";
}
.icon-Fill-Eject:before {
  content: "\eac1";
}
.icon-Fill-EjectSimple:before {
  content: "\eac2";
}
.icon-Fill-Elevator:before {
  content: "\eac3";
}
.icon-Fill-Engine:before {
  content: "\eac4";
}
.icon-Fill-Envelope:before {
  content: "\eac5";
}
.icon-Fill-EnvelopeOpen:before {
  content: "\eac6";
}
.icon-Fill-EnvelopeSimple:before {
  content: "\eac7";
}
.icon-Fill-EnvelopeSimpleOpen:before {
  content: "\eac8";
}
.icon-Fill-Equalizer:before {
  content: "\eac9";
}
.icon-Fill-Equals:before {
  content: "\eaca";
}
.icon-Fill-Eraser:before {
  content: "\eacb";
}
.icon-Fill-EscalatorDown:before {
  content: "\eacc";
}
.icon-Fill-EscalatorUp:before {
  content: "\eacd";
}
.icon-Fill-Exam:before {
  content: "\eace";
}
.icon-Fill-Exclude:before {
  content: "\eacf";
}
.icon-Fill-ExcludeSquare:before {
  content: "\ead0";
}
.icon-Fill-Export:before {
  content: "\ead1";
}
.icon-Fill-Eye:before {
  content: "\ead2";
}
.icon-Fill-EyeClosed:before {
  content: "\ead3";
}
.icon-Fill-Eyedropper:before {
  content: "\ead4";
}
.icon-Fill-EyedropperSample:before {
  content: "\ead5";
}
.icon-Fill-Eyeglasses:before {
  content: "\ead6";
}
.icon-Fill-EyeSlash:before {
  content: "\ead7";
}
.icon-Fill-FacebookLogo:before {
  content: "\ead8";
}
.icon-Fill-FaceMask:before {
  content: "\ead9";
}
.icon-Fill-Factory:before {
  content: "\eada";
}
.icon-Fill-Faders:before {
  content: "\eadb";
}
.icon-Fill-FadersHorizontal:before {
  content: "\eadc";
}
.icon-Fill-Fan:before {
  content: "\eadd";
}
.icon-Fill-FastForward:before {
  content: "\eade";
}
.icon-Fill-FastForwardCircle:before {
  content: "\eadf";
}
.icon-Fill-Feather:before {
  content: "\eae0";
}
.icon-Fill-FigmaLogo:before {
  content: "\eae1";
}
.icon-Fill-File:before {
  content: "\eae2";
}
.icon-Fill-FileArchive:before {
  content: "\eae3";
}
.icon-Fill-FileArrowDown:before {
  content: "\eae4";
}
.icon-Fill-FileArrowUp:before {
  content: "\eae5";
}
.icon-Fill-FileAudio:before {
  content: "\eae6";
}
.icon-Fill-FileCloud:before {
  content: "\eae7";
}
.icon-Fill-FileCode:before {
  content: "\eae8";
}
.icon-Fill-FileCss:before {
  content: "\eae9";
}
.icon-Fill-FileCsv:before {
  content: "\eaea";
}
.icon-Fill-FileDashed:before {
  content: "\eaeb";
}
.icon-Fill-FileDoc:before {
  content: "\eaec";
}
.icon-Fill-FileHtml:before {
  content: "\eaed";
}
.icon-Fill-FileImage:before {
  content: "\eaee";
}
.icon-Fill-FileJpg:before {
  content: "\eaef";
}
.icon-Fill-FileJs:before {
  content: "\eaf0";
}
.icon-Fill-FileJsx:before {
  content: "\eaf1";
}
.icon-Fill-FileLock:before {
  content: "\eaf2";
}
.icon-Fill-FileMinus:before {
  content: "\eaf3";
}
.icon-Fill-FilePdf:before {
  content: "\eaf4";
}
.icon-Fill-FilePlus:before {
  content: "\eaf5";
}
.icon-Fill-FilePng:before {
  content: "\eaf6";
}
.icon-Fill-FilePpt:before {
  content: "\eaf7";
}
.icon-Fill-FileRs:before {
  content: "\eaf8";
}
.icon-Fill-Files:before {
  content: "\eaf9";
}
.icon-Fill-FileSearch:before {
  content: "\eafa";
}
.icon-Fill-FileSql:before {
  content: "\eafb";
}
.icon-Fill-FileSvg:before {
  content: "\eafc";
}
.icon-Fill-FileText:before {
  content: "\eafd";
}
.icon-Fill-FileTs:before {
  content: "\eafe";
}
.icon-Fill-FileTsx:before {
  content: "\eaff";
}
.icon-Fill-FileVideo:before {
  content: "\eb00";
}
.icon-Fill-FileVue:before {
  content: "\eb01";
}
.icon-Fill-FileX:before {
  content: "\eb02";
}
.icon-Fill-FileXls:before {
  content: "\eb03";
}
.icon-Fill-FileZip:before {
  content: "\eb04";
}
.icon-Fill-FilmReel:before {
  content: "\eb05";
}
.icon-Fill-FilmScript:before {
  content: "\eb06";
}
.icon-Fill-FilmSlate:before {
  content: "\eb07";
}
.icon-Fill-FilmStrip:before {
  content: "\eb08";
}
.icon-Fill-Fingerprint:before {
  content: "\eb09";
}
.icon-Fill-FingerprintSimple:before {
  content: "\eb0a";
}
.icon-Fill-FinnTheHuman:before {
  content: "\eb0b";
}
.icon-Fill-Fire:before {
  content: "\eb0c";
}
.icon-Fill-FireExtinguisher:before {
  content: "\eb0d";
}
.icon-Fill-FireSimple:before {
  content: "\eb0e";
}
.icon-Fill-FirstAid:before {
  content: "\eb0f";
}
.icon-Fill-FirstAidKit:before {
  content: "\eb10";
}
.icon-Fill-Fish:before {
  content: "\eb11";
}
.icon-Fill-FishSimple:before {
  content: "\eb12";
}
.icon-Fill-Flag:before {
  content: "\eb13";
}
.icon-Fill-FlagBanner:before {
  content: "\eb14";
}
.icon-Fill-FlagCheckered:before {
  content: "\eb15";
}
.icon-Fill-FlagPennant:before {
  content: "\eb16";
}
.icon-Fill-Flame:before {
  content: "\eb17";
}
.icon-Fill-Flashlight:before {
  content: "\eb18";
}
.icon-Fill-Flask:before {
  content: "\eb19";
}
.icon-Fill-FloppyDisk:before {
  content: "\eb1a";
}
.icon-Fill-FloppyDiskBack:before {
  content: "\eb1b";
}
.icon-Fill-FlowArrow:before {
  content: "\eb1c";
}
.icon-Fill-Flower:before {
  content: "\eb1d";
}
.icon-Fill-FlowerLotus:before {
  content: "\eb1e";
}
.icon-Fill-FlowerTulip:before {
  content: "\eb1f";
}
.icon-Fill-FlyingSaucer:before {
  content: "\eb20";
}
.icon-Fill-Folder:before {
  content: "\eb21";
}
.icon-Fill-FolderDashed:before {
  content: "\eb22";
}
.icon-Fill-FolderLock:before {
  content: "\eb23";
}
.icon-Fill-FolderMinus:before {
  content: "\eb24";
}
.icon-Fill-FolderNotch:before {
  content: "\eb25";
}
.icon-Fill-FolderNotchMinus:before {
  content: "\eb26";
}
.icon-Fill-FolderNotchOpen:before {
  content: "\eb27";
}
.icon-Fill-FolderNotchPlus:before {
  content: "\eb28";
}
.icon-Fill-FolderOpen:before {
  content: "\eb29";
}
.icon-Fill-FolderPlus:before {
  content: "\eb2a";
}
.icon-Fill-Folders:before {
  content: "\eb2b";
}
.icon-Fill-FolderSimple:before {
  content: "\eb2c";
}
.icon-Fill-FolderSimpleDashed:before {
  content: "\eb2d";
}
.icon-Fill-FolderSimpleLock:before {
  content: "\eb2e";
}
.icon-Fill-FolderSimpleMinus:before {
  content: "\eb2f";
}
.icon-Fill-FolderSimplePlus:before {
  content: "\eb30";
}
.icon-Fill-FolderSimpleStar:before {
  content: "\eb31";
}
.icon-Fill-FolderSimpleUser:before {
  content: "\eb32";
}
.icon-Fill-FolderStar:before {
  content: "\eb33";
}
.icon-Fill-FolderUser:before {
  content: "\eb34";
}
.icon-Fill-Football:before {
  content: "\eb35";
}
.icon-Fill-Footprints:before {
  content: "\eb36";
}
.icon-Fill-ForkKnife:before {
  content: "\eb37";
}
.icon-Fill-FrameCorners:before {
  content: "\eb38";
}
.icon-Fill-FramerLogo:before {
  content: "\eb39";
}
.icon-Fill-Function:before {
  content: "\eb3a";
}
.icon-Fill-Funnel:before {
  content: "\eb3b";
}
.icon-Fill-FunnelSimple:before {
  content: "\eb3c";
}
.icon-Fill-GameController:before {
  content: "\eb3d";
}
.icon-Fill-Garage:before {
  content: "\eb3e";
}
.icon-Fill-GasCan:before {
  content: "\eb3f";
}
.icon-Fill-GasPump:before {
  content: "\eb40";
}
.icon-Fill-Gauge:before {
  content: "\eb41";
}
.icon-Fill-Gavel:before {
  content: "\eb42";
}
.icon-Fill-Gear:before {
  content: "\eb43";
}
.icon-Fill-GearFine:before {
  content: "\eb44";
}
.icon-Fill-GearSix:before {
  content: "\eb45";
}
.icon-Fill-GenderFemale:before {
  content: "\eb46";
}
.icon-Fill-GenderIntersex:before {
  content: "\eb47";
}
.icon-Fill-GenderMale:before {
  content: "\eb48";
}
.icon-Fill-GenderNeuter:before {
  content: "\eb49";
}
.icon-Fill-GenderNonbinary:before {
  content: "\eb4a";
}
.icon-Fill-GenderTransgender:before {
  content: "\eb4b";
}
.icon-Fill-Ghost:before {
  content: "\eb4c";
}
.icon-Fill-Gif:before {
  content: "\eb4d";
}
.icon-Fill-Gift:before {
  content: "\eb4e";
}
.icon-Fill-GitBranch:before {
  content: "\eb4f";
}
.icon-Fill-GitCommit:before {
  content: "\eb50";
}
.icon-Fill-GitDiff:before {
  content: "\eb51";
}
.icon-Fill-GitFork:before {
  content: "\eb52";
}
.icon-Fill-GithubLogo:before {
  content: "\eb53";
}
.icon-Fill-GitlabLogo:before {
  content: "\eb54";
}
.icon-Fill-GitlabLogoSimple:before {
  content: "\eb55";
}
.icon-Fill-GitMerge:before {
  content: "\eb56";
}
.icon-Fill-GitPullRequest:before {
  content: "\eb57";
}
.icon-Fill-Globe:before {
  content: "\eb58";
}
.icon-Fill-GlobeHemisphereEast:before {
  content: "\eb59";
}
.icon-Fill-GlobeHemisphereWest:before {
  content: "\eb5a";
}
.icon-Fill-GlobeSimple:before {
  content: "\eb5b";
}
.icon-Fill-GlobeStand:before {
  content: "\eb5c";
}
.icon-Fill-Goggles:before {
  content: "\eb5d";
}
.icon-Fill-GoodreadsLogo:before {
  content: "\eb5e";
}
.icon-Fill-GoogleCardboardLogo:before {
  content: "\eb5f";
}
.icon-Fill-GoogleChromeLogo:before {
  content: "\eb60";
}
.icon-Fill-GoogleDriveLogo:before {
  content: "\eb61";
}
.icon-Fill-GoogleLogo:before {
  content: "\eb62";
}
.icon-Fill-GooglePhotosLogo:before {
  content: "\eb63";
}
.icon-Fill-GooglePlayLogo:before {
  content: "\eb64";
}
.icon-Fill-GooglePodcastsLogo:before {
  content: "\eb65";
}
.icon-Fill-Gradient:before {
  content: "\eb66";
}
.icon-Fill-GraduationCap:before {
  content: "\eb67";
}
.icon-Fill-Grains:before {
  content: "\eb68";
}
.icon-Fill-GrainsSlash:before {
  content: "\eb69";
}
.icon-Fill-Graph:before {
  content: "\eb6a";
}
.icon-Fill-GridFour:before {
  content: "\eb6b";
}
.icon-Fill-GridNine:before {
  content: "\eb6c";
}
.icon-Fill-Guitar:before {
  content: "\eb6d";
}
.icon-Fill-Hamburger:before {
  content: "\eb6e";
}
.icon-Fill-Hammer:before {
  content: "\eb6f";
}
.icon-Fill-Hand:before {
  content: "\eb70";
}
.icon-Fill-Handbag:before {
  content: "\eb71";
}
.icon-Fill-HandbagSimple:before {
  content: "\eb72";
}
.icon-Fill-HandCoins:before {
  content: "\eb73";
}
.icon-Fill-HandEye:before {
  content: "\eb74";
}
.icon-Fill-HandFist:before {
  content: "\eb75";
}
.icon-Fill-HandGrabbing:before {
  content: "\eb76";
}
.icon-Fill-HandHeart:before {
  content: "\eb77";
}
.icon-Fill-HandPalm:before {
  content: "\eb78";
}
.icon-Fill-HandPointing:before {
  content: "\eb79";
}
.icon-Fill-HandsClapping:before {
  content: "\eb7a";
}
.icon-Fill-Handshake:before {
  content: "\eb7b";
}
.icon-Fill-HandSoap:before {
  content: "\eb7c";
}
.icon-Fill-HandsPraying:before {
  content: "\eb7d";
}
.icon-Fill-HandSwipeLeft:before {
  content: "\eb7e";
}
.icon-Fill-HandSwipeRight:before {
  content: "\eb7f";
}
.icon-Fill-HandTap:before {
  content: "\eb80";
}
.icon-Fill-HandWaving:before {
  content: "\eb81";
}
.icon-Fill-HardDrive:before {
  content: "\eb82";
}
.icon-Fill-HardDrives:before {
  content: "\eb83";
}
.icon-Fill-Hash:before {
  content: "\eb84";
}
.icon-Fill-HashStraight:before {
  content: "\eb85";
}
.icon-Fill-Headlights:before {
  content: "\eb86";
}
.icon-Fill-Headphones:before {
  content: "\eb87";
}
.icon-Fill-Headset:before {
  content: "\eb88";
}
.icon-Fill-Heart:before {
  content: "\eb89";
}
.icon-Fill-Heartbeat:before {
  content: "\eb8a";
}
.icon-Fill-HeartBreak:before {
  content: "\eb8b";
}
.icon-Fill-HeartHalf:before {
  content: "\eb8c";
}
.icon-Fill-HeartStraight:before {
  content: "\eb8d";
}
.icon-Fill-HeartStraightBreak:before {
  content: "\eb8e";
}
.icon-Fill-Hexagon:before {
  content: "\eb8f";
}
.icon-Fill-HighHeel:before {
  content: "\eb90";
}
.icon-Fill-HighlighterCircle:before {
  content: "\eb91";
}
.icon-Fill-Hoodie:before {
  content: "\eb92";
}
.icon-Fill-Horse:before {
  content: "\eb93";
}
.icon-Fill-Hourglass:before {
  content: "\eb94";
}
.icon-Fill-HourglassHigh:before {
  content: "\eb95";
}
.icon-Fill-HourglassLow:before {
  content: "\eb96";
}
.icon-Fill-HourglassMedium:before {
  content: "\eb97";
}
.icon-Fill-HourglassSimple:before {
  content: "\eb98";
}
.icon-Fill-HourglassSimpleHigh:before {
  content: "\eb99";
}
.icon-Fill-HourglassSimpleLow:before {
  content: "\eb9a";
}
.icon-Fill-HourglassSimpleMedium:before {
  content: "\eb9b";
}
.icon-Fill-House:before {
  content: "\eb9c";
}
.icon-Fill-HouseLine:before {
  content: "\eb9d";
}
.icon-Fill-HouseSimple:before {
  content: "\eb9e";
}
.icon-Fill-IceCream:before {
  content: "\eb9f";
}
.icon-Fill-IdentificationBadge:before {
  content: "\eba0";
}
.icon-Fill-IdentificationCard:before {
  content: "\eba1";
}
.icon-Fill-Image:before {
  content: "\eba2";
}
.icon-Fill-Images:before {
  content: "\eba3";
}
.icon-Fill-ImageSquare:before {
  content: "\eba4";
}
.icon-Fill-ImagesSquare:before {
  content: "\eba5";
}
.icon-Fill-Infinity:before {
  content: "\eba6";
}
.icon-Fill-Info:before {
  content: "\eba7";
}
.icon-Fill-InstagramLogo:before {
  content: "\eba8";
}
.icon-Fill-Intersect:before {
  content: "\eba9";
}
.icon-Fill-IntersectSquare:before {
  content: "\ebaa";
}
.icon-Fill-IntersectThree:before {
  content: "\ebab";
}
.icon-Fill-Jeep:before {
  content: "\ebac";
}
.icon-Fill-Kanban:before {
  content: "\ebad";
}
.icon-Fill-Key:before {
  content: "\ebae";
}
.icon-Fill-Keyboard:before {
  content: "\ebaf";
}
.icon-Fill-Keyhole:before {
  content: "\ebb0";
}
.icon-Fill-KeyReturn:before {
  content: "\ebb1";
}
.icon-Fill-Knife:before {
  content: "\ebb2";
}
.icon-Fill-Ladder:before {
  content: "\ebb3";
}
.icon-Fill-LadderSimple:before {
  content: "\ebb4";
}
.icon-Fill-Lamp:before {
  content: "\ebb5";
}
.icon-Fill-Laptop:before {
  content: "\ebb6";
}
.icon-Fill-Layout:before {
  content: "\ebb7";
}
.icon-Fill-Leaf:before {
  content: "\ebb8";
}
.icon-Fill-Lifebuoy:before {
  content: "\ebb9";
}
.icon-Fill-Lightbulb:before {
  content: "\ebba";
}
.icon-Fill-LightbulbFilament:before {
  content: "\ebbb";
}
.icon-Fill-Lighthouse:before {
  content: "\ebbc";
}
.icon-Fill-Lightning:before {
  content: "\ebbd";
}
.icon-Fill-LightningA:before {
  content: "\ebbe";
}
.icon-Fill-LightningSlash:before {
  content: "\ebbf";
}
.icon-Fill-LineSegment:before {
  content: "\ebc0";
}
.icon-Fill-LineSegments:before {
  content: "\ebc1";
}
.icon-Fill-Link:before {
  content: "\ebc2";
}
.icon-Fill-LinkBreak:before {
  content: "\ebc3";
}
.icon-Fill-LinkedinLogo:before {
  content: "\ebc4";
}
.icon-Fill-LinkSimple:before {
  content: "\ebc5";
}
.icon-Fill-LinkSimpleBreak:before {
  content: "\ebc6";
}
.icon-Fill-LinkSimpleHorizontal:before {
  content: "\ebc7";
}
.icon-Fill-LinkSimpleHorizontalBreak:before {
  content: "\ebc8";
}
.icon-Fill-LinuxLogo:before {
  content: "\ebc9";
}
.icon-Fill-List:before {
  content: "\ebca";
}
.icon-Fill-ListBullets:before {
  content: "\ebcb";
}
.icon-Fill-ListChecks:before {
  content: "\ebcc";
}
.icon-Fill-ListDashes:before {
  content: "\ebcd";
}
.icon-Fill-ListMagnifyingGlass:before {
  content: "\ebce";
}
.icon-Fill-ListNumbers:before {
  content: "\ebcf";
}
.icon-Fill-ListPlus:before {
  content: "\ebd0";
}
.icon-Fill-Lock:before {
  content: "\ebd1";
}
.icon-Fill-Lockers:before {
  content: "\ebd2";
}
.icon-Fill-LockKey:before {
  content: "\ebd3";
}
.icon-Fill-LockKeyOpen:before {
  content: "\ebd4";
}
.icon-Fill-LockLaminated:before {
  content: "\ebd5";
}
.icon-Fill-LockLaminatedOpen:before {
  content: "\ebd6";
}
.icon-Fill-LockOpen:before {
  content: "\ebd7";
}
.icon-Fill-LockSimple:before {
  content: "\ebd8";
}
.icon-Fill-LockSimpleOpen:before {
  content: "\ebd9";
}
.icon-Fill-MagicWand:before {
  content: "\ebda";
}
.icon-Fill-Magnet:before {
  content: "\ebdb";
}
.icon-Fill-MagnetStraight:before {
  content: "\ebdc";
}
.icon-Fill-MagnifyingGlass:before {
  content: "\ebdd";
}
.icon-Fill-MagnifyingGlassMinus:before {
  content: "\ebde";
}
.icon-Fill-MagnifyingGlassPlus:before {
  content: "\ebdf";
}
.icon-Fill-MapPin:before {
  content: "\ebe0";
}
.icon-Fill-MapPinLine:before {
  content: "\ebe1";
}
.icon-Fill-MapTrifold:before {
  content: "\ebe2";
}
.icon-Fill-MarkerCircle:before {
  content: "\ebe3";
}
.icon-Fill-Martini:before {
  content: "\ebe4";
}
.icon-Fill-MaskHappy:before {
  content: "\ebe5";
}
.icon-Fill-MaskSad:before {
  content: "\ebe6";
}
.icon-Fill-MathOperations:before {
  content: "\ebe7";
}
.icon-Fill-Medal:before {
  content: "\ebe8";
}
.icon-Fill-MedalMilitary:before {
  content: "\ebe9";
}
.icon-Fill-MediumLogo:before {
  content: "\ebea";
}
.icon-Fill-Megaphone:before {
  content: "\ebeb";
}
.icon-Fill-MegaphoneSimple:before {
  content: "\ebec";
}
.icon-Fill-MessengerLogo:before {
  content: "\ebed";
}
.icon-Fill-MetaLogo:before {
  content: "\ebee";
}
.icon-Fill-Metronome:before {
  content: "\ebef";
}
.icon-Fill-Microphone:before {
  content: "\ebf0";
}
.icon-Fill-MicrophoneSlash:before {
  content: "\ebf1";
}
.icon-Fill-MicrophoneStage:before {
  content: "\ebf2";
}
.icon-Fill-MicrosoftExcelLogo:before {
  content: "\ebf3";
}
.icon-Fill-MicrosoftOutlookLogo:before {
  content: "\ebf4";
}
.icon-Fill-MicrosoftPowerpointLogo:before {
  content: "\ebf5";
}
.icon-Fill-MicrosoftTeamsLogo:before {
  content: "\ebf6";
}
.icon-Fill-MicrosoftWordLogo:before {
  content: "\ebf7";
}
.icon-Fill-Minus:before {
  content: "\ebf8";
}
.icon-Fill-MinusCircle:before {
  content: "\ebf9";
}
.icon-Fill-MinusSquare:before {
  content: "\ebfa";
}
.icon-Fill-Money:before {
  content: "\ebfb";
}
.icon-Fill-Monitor:before {
  content: "\ebfc";
}
.icon-Fill-MonitorPlay:before {
  content: "\ebfd";
}
.icon-Fill-Moon:before {
  content: "\ebfe";
}
.icon-Fill-MoonStars:before {
  content: "\ebff";
}
.icon-Fill-Moped:before {
  content: "\ec00";
}
.icon-Fill-MopedFront:before {
  content: "\ec01";
}
.icon-Fill-Mosque:before {
  content: "\ec02";
}
.icon-Fill-Motorcycle:before {
  content: "\ec03";
}
.icon-Fill-Mountains:before {
  content: "\ec04";
}
.icon-Fill-Mouse:before {
  content: "\ec05";
}
.icon-Fill-MouseSimple:before {
  content: "\ec06";
}
.icon-Fill-MusicNote:before {
  content: "\ec07";
}
.icon-Fill-MusicNotes:before {
  content: "\ec08";
}
.icon-Fill-MusicNoteSimple:before {
  content: "\ec09";
}
.icon-Fill-MusicNotesPlus:before {
  content: "\ec0a";
}
.icon-Fill-MusicNotesSimple:before {
  content: "\ec0b";
}
.icon-Fill-NavigationArrow:before {
  content: "\ec0c";
}
.icon-Fill-Needle:before {
  content: "\ec0d";
}
.icon-Fill-Newspaper:before {
  content: "\ec0e";
}
.icon-Fill-NewspaperClipping:before {
  content: "\ec0f";
}
.icon-Fill-Notches:before {
  content: "\ec10";
}
.icon-Fill-Note:before {
  content: "\ec11";
}
.icon-Fill-NoteBlank:before {
  content: "\ec12";
}
.icon-Fill-Notebook:before {
  content: "\ec13";
}
.icon-Fill-Notepad:before {
  content: "\ec14";
}
.icon-Fill-NotePencil:before {
  content: "\ec15";
}
.icon-Fill-Notification:before {
  content: "\ec16";
}
.icon-Fill-NotionLogo:before {
  content: "\ec17";
}
.icon-Fill-NumberCircleEight:before {
  content: "\ec18";
}
.icon-Fill-NumberCircleFive:before {
  content: "\ec19";
}
.icon-Fill-NumberCircleFour:before {
  content: "\ec1a";
}
.icon-Fill-NumberCircleNine:before {
  content: "\ec1b";
}
.icon-Fill-NumberCircleOne:before {
  content: "\ec1c";
}
.icon-Fill-NumberCircleSeven:before {
  content: "\ec1d";
}
.icon-Fill-NumberCircleSix:before {
  content: "\ec1e";
}
.icon-Fill-NumberCircleThree:before {
  content: "\ec1f";
}
.icon-Fill-NumberCircleTwo:before {
  content: "\ec20";
}
.icon-Fill-NumberCircleZero:before {
  content: "\ec21";
}
.icon-Fill-NumberEight:before {
  content: "\ec22";
}
.icon-Fill-NumberFive:before {
  content: "\ec23";
}
.icon-Fill-NumberFour:before {
  content: "\ec24";
}
.icon-Fill-NumberNine:before {
  content: "\ec25";
}
.icon-Fill-NumberOne:before {
  content: "\ec26";
}
.icon-Fill-NumberSeven:before {
  content: "\ec27";
}
.icon-Fill-NumberSix:before {
  content: "\ec28";
}
.icon-Fill-NumberSquareEight:before {
  content: "\ec29";
}
.icon-Fill-NumberSquareFive:before {
  content: "\ec2a";
}
.icon-Fill-NumberSquareFour:before {
  content: "\ec2b";
}
.icon-Fill-NumberSquareNine:before {
  content: "\ec2c";
}
.icon-Fill-NumberSquareOne:before {
  content: "\ec2d";
}
.icon-Fill-NumberSquareSeven:before {
  content: "\ec2e";
}
.icon-Fill-NumberSquareSix:before {
  content: "\ec2f";
}
.icon-Fill-NumberSquareThree:before {
  content: "\ec30";
}
.icon-Fill-NumberSquareTwo:before {
  content: "\ec31";
}
.icon-Fill-NumberSquareZero:before {
  content: "\ec32";
}
.icon-Fill-NumberThree:before {
  content: "\ec33";
}
.icon-Fill-NumberTwo:before {
  content: "\ec34";
}
.icon-Fill-NumberZero:before {
  content: "\ec35";
}
.icon-Fill-Nut:before {
  content: "\ec36";
}
.icon-Fill-NyTimesLogo:before {
  content: "\ec37";
}
.icon-Fill-Octagon:before {
  content: "\ec38";
}
.icon-Fill-OfficeChair:before {
  content: "\ec39";
}
.icon-Fill-Option:before {
  content: "\ec3a";
}
.icon-Fill-OrangeSlice:before {
  content: "\ec3b";
}
.icon-Fill-Package:before {
  content: "\ec3c";
}
.icon-Fill-PaintBrush:before {
  content: "\ec3d";
}
.icon-Fill-PaintBrushBroad:before {
  content: "\ec3e";
}
.icon-Fill-PaintBrushHousehold:before {
  content: "\ec3f";
}
.icon-Fill-PaintBucket:before {
  content: "\ec40";
}
.icon-Fill-PaintRoller:before {
  content: "\ec41";
}
.icon-Fill-Palette:before {
  content: "\ec42";
}
.icon-Fill-Pants:before {
  content: "\ec43";
}
.icon-Fill-Paperclip:before {
  content: "\ec44";
}
.icon-Fill-PaperclipHorizontal:before {
  content: "\ec45";
}
.icon-Fill-PaperPlane:before {
  content: "\ec46";
}
.icon-Fill-PaperPlaneRight:before {
  content: "\ec47";
}
.icon-Fill-PaperPlaneTilt:before {
  content: "\ec48";
}
.icon-Fill-Parachute:before {
  content: "\ec49";
}
.icon-Fill-Paragraph:before {
  content: "\ec4a";
}
.icon-Fill-Parallelogram:before {
  content: "\ec4b";
}
.icon-Fill-Park:before {
  content: "\ec4c";
}
.icon-Fill-Password:before {
  content: "\ec4d";
}
.icon-Fill-Path:before {
  content: "\ec4e";
}
.icon-Fill-PatreonLogo:before {
  content: "\ec4f";
}
.icon-Fill-Pause:before {
  content: "\ec50";
}
.icon-Fill-PauseCircle:before {
  content: "\ec51";
}
.icon-Fill-PawPrint:before {
  content: "\ec52";
}
.icon-Fill-PaypalLogo:before {
  content: "\ec53";
}
.icon-Fill-Peace:before {
  content: "\ec54";
}
.icon-Fill-Pen:before {
  content: "\ec55";
}
.icon-Fill-Pencil:before {
  content: "\ec56";
}
.icon-Fill-PencilCircle:before {
  content: "\ec57";
}
.icon-Fill-PencilLine:before {
  content: "\ec58";
}
.icon-Fill-PencilSimple:before {
  content: "\ec59";
}
.icon-Fill-PencilSimpleLine:before {
  content: "\ec5a";
}
.icon-Fill-PencilSimpleSlash:before {
  content: "\ec5b";
}
.icon-Fill-PencilSlash:before {
  content: "\ec5c";
}
.icon-Fill-PenNib:before {
  content: "\ec5d";
}
.icon-Fill-PenNibStraight:before {
  content: "\ec5e";
}
.icon-Fill-Pentagram:before {
  content: "\ec5f";
}
.icon-Fill-Pepper:before {
  content: "\ec60";
}
.icon-Fill-Percent:before {
  content: "\ec61";
}
.icon-Fill-Person:before {
  content: "\ec62";
}
.icon-Fill-PersonArmsSpread:before {
  content: "\ec63";
}
.icon-Fill-PersonSimple:before {
  content: "\ec64";
}
.icon-Fill-PersonSimpleBike:before {
  content: "\ec65";
}
.icon-Fill-PersonSimpleRun:before {
  content: "\ec66";
}
.icon-Fill-PersonSimpleThrow:before {
  content: "\ec67";
}
.icon-Fill-PersonSimpleWalk:before {
  content: "\ec68";
}
.icon-Fill-Perspective:before {
  content: "\ec69";
}
.icon-Fill-Phone:before {
  content: "\ec6a";
}
.icon-Fill-PhoneCall:before {
  content: "\ec6b";
}
.icon-Fill-PhoneDisconnect:before {
  content: "\ec6c";
}
.icon-Fill-PhoneIncoming:before {
  content: "\ec6d";
}
.icon-Fill-PhoneOutgoing:before {
  content: "\ec6e";
}
.icon-Fill-PhonePlus:before {
  content: "\ec6f";
}
.icon-Fill-PhoneSlash:before {
  content: "\ec70";
}
.icon-Fill-PhoneX:before {
  content: "\ec71";
}
.icon-Fill-PhosphorLogo:before {
  content: "\ec72";
}
.icon-Fill-Pi:before {
  content: "\ec73";
}
.icon-Fill-PianoKeys:before {
  content: "\ec74";
}
.icon-Fill-PictureInPicture:before {
  content: "\ec75";
}
.icon-Fill-PiggyBank:before {
  content: "\ec76";
}
.icon-Fill-Pill:before {
  content: "\ec77";
}
.icon-Fill-PinterestLogo:before {
  content: "\ec78";
}
.icon-Fill-Pinwheel:before {
  content: "\ec79";
}
.icon-Fill-Pizza:before {
  content: "\ec7a";
}
.icon-Fill-Placeholder:before {
  content: "\ec7b";
}
.icon-Fill-Planet:before {
  content: "\ec7c";
}
.icon-Fill-Plant:before {
  content: "\ec7d";
}
.icon-Fill-Play:before {
  content: "\ec7e";
}
.icon-Fill-PlayCircle:before {
  content: "\ec7f";
}
.icon-Fill-Playlist:before {
  content: "\ec80";
}
.icon-Fill-PlayPause:before {
  content: "\ec81";
}
.icon-Fill-Plug:before {
  content: "\ec82";
}
.icon-Fill-PlugCharging:before {
  content: "\ec83";
}
.icon-Fill-Plugs:before {
  content: "\ec84";
}
.icon-Fill-PlugsConnected:before {
  content: "\ec85";
}
.icon-Fill-Plus:before {
  content: "\ec86";
}
.icon-Fill-PlusCircle:before {
  content: "\ec87";
}
.icon-Fill-PlusMinus:before {
  content: "\ec88";
}
.icon-Fill-PlusSquare:before {
  content: "\ec89";
}
.icon-Fill-PokerChip:before {
  content: "\ec8a";
}
.icon-Fill-PoliceCar:before {
  content: "\ec8b";
}
.icon-Fill-Polygon:before {
  content: "\ec8c";
}
.icon-Fill-Popcorn:before {
  content: "\ec8d";
}
.icon-Fill-PottedPlant:before {
  content: "\ec8e";
}
.icon-Fill-Power:before {
  content: "\ec8f";
}
.icon-Fill-Prescription:before {
  content: "\ec90";
}
.icon-Fill-Presentation:before {
  content: "\ec91";
}
.icon-Fill-PresentationChart:before {
  content: "\ec92";
}
.icon-Fill-Printer:before {
  content: "\ec93";
}
.icon-Fill-Prohibit:before {
  content: "\ec94";
}
.icon-Fill-ProhibitInset:before {
  content: "\ec95";
}
.icon-Fill-ProjectorScreen:before {
  content: "\ec96";
}
.icon-Fill-ProjectorScreenChart:before {
  content: "\ec97";
}
.icon-Fill-Pulse:before {
  content: "\ec98";
}
.icon-Fill-PushPin:before {
  content: "\ec99";
}
.icon-Fill-PushPinSimple:before {
  content: "\ec9a";
}
.icon-Fill-PushPinSimpleSlash:before {
  content: "\ec9b";
}
.icon-Fill-PushPinSlash:before {
  content: "\ec9c";
}
.icon-Fill-PuzzlePiece:before {
  content: "\ec9d";
}
.icon-Fill-QrCode:before {
  content: "\ec9e";
}
.icon-Fill-Question:before {
  content: "\ec9f";
}
.icon-Fill-Queue:before {
  content: "\eca0";
}
.icon-Fill-Quotes:before {
  content: "\eca1";
}
.icon-Fill-Radical:before {
  content: "\eca2";
}
.icon-Fill-Radio:before {
  content: "\eca3";
}
.icon-Fill-Radioactive:before {
  content: "\eca4";
}
.icon-Fill-RadioButton:before {
  content: "\eca5";
}
.icon-Fill-Rainbow:before {
  content: "\eca6";
}
.icon-Fill-RainbowCloud:before {
  content: "\eca7";
}
.icon-Fill-ReadCvLogo:before {
  content: "\eca8";
}
.icon-Fill-Receipt:before {
  content: "\eca9";
}
.icon-Fill-ReceiptX:before {
  content: "\ecaa";
}
.icon-Fill-Record:before {
  content: "\ecab";
}
.icon-Fill-Rectangle:before {
  content: "\ecac";
}
.icon-Fill-Recycle:before {
  content: "\ecad";
}
.icon-Fill-RedditLogo:before {
  content: "\ecae";
}
.icon-Fill-Repeat:before {
  content: "\ecaf";
}
.icon-Fill-RepeatOnce:before {
  content: "\ecb0";
}
.icon-Fill-Rewind:before {
  content: "\ecb1";
}
.icon-Fill-RewindCircle:before {
  content: "\ecb2";
}
.icon-Fill-RoadHorizon:before {
  content: "\ecb3";
}
.icon-Fill-Robot:before {
  content: "\ecb4";
}
.icon-Fill-Rocket:before {
  content: "\ecb5";
}
.icon-Fill-RocketLaunch:before {
  content: "\ecb6";
}
.icon-Fill-Rows:before {
  content: "\ecb7";
}
.icon-Fill-Rss:before {
  content: "\ecb8";
}
.icon-Fill-RssSimple:before {
  content: "\ecb9";
}
.icon-Fill-Rug:before {
  content: "\ecba";
}
.icon-Fill-Ruler:before {
  content: "\ecbb";
}
.icon-Fill-Scales:before {
  content: "\ecbc";
}
.icon-Fill-Scan:before {
  content: "\ecbd";
}
.icon-Fill-Scissors:before {
  content: "\ecbe";
}
.icon-Fill-Scooter:before {
  content: "\ecbf";
}
.icon-Fill-Screencast:before {
  content: "\ecc0";
}
.icon-Fill-ScribbleLoop:before {
  content: "\ecc1";
}
.icon-Fill-Scroll:before {
  content: "\ecc2";
}
.icon-Fill-Seal:before {
  content: "\ecc3";
}
.icon-Fill-SealCheck:before {
  content: "\ecc4";
}
.icon-Fill-SealQuestion:before {
  content: "\ecc5";
}
.icon-Fill-SealWarning:before {
  content: "\ecc6";
}
.icon-Fill-Selection:before {
  content: "\ecc7";
}
.icon-Fill-SelectionAll:before {
  content: "\ecc8";
}
.icon-Fill-SelectionBackground:before {
  content: "\ecc9";
}
.icon-Fill-SelectionForeground:before {
  content: "\ecca";
}
.icon-Fill-SelectionInverse:before {
  content: "\eccb";
}
.icon-Fill-SelectionPlus:before {
  content: "\eccc";
}
.icon-Fill-SelectionSlash:before {
  content: "\eccd";
}
.icon-Fill-Shapes:before {
  content: "\ecce";
}
.icon-Fill-Share:before {
  content: "\eccf";
}
.icon-Fill-ShareFat:before {
  content: "\ecd0";
}
.icon-Fill-ShareNetwork:before {
  content: "\ecd1";
}
.icon-Fill-Shield:before {
  content: "\ecd2";
}
.icon-Fill-ShieldCheck:before {
  content: "\ecd3";
}
.icon-Fill-ShieldCheckered:before {
  content: "\ecd4";
}
.icon-Fill-ShieldChevron:before {
  content: "\ecd5";
}
.icon-Fill-ShieldPlus:before {
  content: "\ecd6";
}
.icon-Fill-ShieldSlash:before {
  content: "\ecd7";
}
.icon-Fill-ShieldStar:before {
  content: "\ecd8";
}
.icon-Fill-ShieldWarning:before {
  content: "\ecd9";
}
.icon-Fill-ShirtFolded:before {
  content: "\ecda";
}
.icon-Fill-ShootingStar:before {
  content: "\ecdb";
}
.icon-Fill-ShoppingBag:before {
  content: "\ecdc";
}
.icon-Fill-ShoppingBagOpen:before {
  content: "\ecdd";
}
.icon-Fill-ShoppingCart:before {
  content: "\ecde";
}
.icon-Fill-ShoppingCartSimple:before {
  content: "\ecdf";
}
.icon-Fill-Shower:before {
  content: "\ece0";
}
.icon-Fill-Shrimp:before {
  content: "\ece1";
}
.icon-Fill-Shuffle:before {
  content: "\ece2";
}
.icon-Fill-ShuffleAngular:before {
  content: "\ece3";
}
.icon-Fill-ShuffleSimple:before {
  content: "\ece4";
}
.icon-Fill-Sidebar:before {
  content: "\ece5";
}
.icon-Fill-SidebarSimple:before {
  content: "\ece6";
}
.icon-Fill-Sigma:before {
  content: "\ece7";
}
.icon-Fill-Signature:before {
  content: "\ece8";
}
.icon-Fill-SignIn:before {
  content: "\ece9";
}
.icon-Fill-SignOut:before {
  content: "\ecea";
}
.icon-Fill-Signpost:before {
  content: "\eceb";
}
.icon-Fill-SimCard:before {
  content: "\ecec";
}
.icon-Fill-Siren:before {
  content: "\eced";
}
.icon-Fill-SketchLogo:before {
  content: "\ecee";
}
.icon-Fill-SkipBack:before {
  content: "\ecef";
}
.icon-Fill-SkipBackCircle:before {
  content: "\ecf0";
}
.icon-Fill-SkipForward:before {
  content: "\ecf1";
}
.icon-Fill-SkipForwardCircle:before {
  content: "\ecf2";
}
.icon-Fill-Skull:before {
  content: "\ecf3";
}
.icon-Fill-SlackLogo:before {
  content: "\ecf4";
}
.icon-Fill-Sliders:before {
  content: "\ecf5";
}
.icon-Fill-SlidersHorizontal:before {
  content: "\ecf6";
}
.icon-Fill-Slideshow:before {
  content: "\ecf7";
}
.icon-Fill-Smiley:before {
  content: "\ecf8";
}
.icon-Fill-SmileyAngry:before {
  content: "\ecf9";
}
.icon-Fill-SmileyBlank:before {
  content: "\ecfa";
}
.icon-Fill-SmileyMeh:before {
  content: "\ecfb";
}
.icon-Fill-SmileyNervous:before {
  content: "\ecfc";
}
.icon-Fill-SmileySad:before {
  content: "\ecfd";
}
.icon-Fill-SmileySticker:before {
  content: "\ecfe";
}
.icon-Fill-SmileyWink:before {
  content: "\ecff";
}
.icon-Fill-SmileyXEyes:before {
  content: "\ed00";
}
.icon-Fill-SnapchatLogo:before {
  content: "\ed01";
}
.icon-Fill-Sneaker:before {
  content: "\ed02";
}
.icon-Fill-SneakerMove:before {
  content: "\ed03";
}
.icon-Fill-Snowflake:before {
  content: "\ed04";
}
.icon-Fill-SoccerBall:before {
  content: "\ed05";
}
.icon-Fill-SortAscending:before {
  content: "\ed06";
}
.icon-Fill-SortDescending:before {
  content: "\ed07";
}
.icon-Fill-SoundcloudLogo:before {
  content: "\ed08";
}
.icon-Fill-Spade:before {
  content: "\ed09";
}
.icon-Fill-Sparkle:before {
  content: "\ed0a";
}
.icon-Fill-SpeakerHifi:before {
  content: "\ed0b";
}
.icon-Fill-SpeakerHigh:before {
  content: "\ed0c";
}
.icon-Fill-SpeakerLow:before {
  content: "\ed0d";
}
.icon-Fill-SpeakerNone:before {
  content: "\ed0e";
}
.icon-Fill-SpeakerSimpleHigh:before {
  content: "\ed0f";
}
.icon-Fill-SpeakerSimpleLow:before {
  content: "\ed10";
}
.icon-Fill-SpeakerSimpleNone:before {
  content: "\ed11";
}
.icon-Fill-SpeakerSimpleSlash:before {
  content: "\ed12";
}
.icon-Fill-SpeakerSimpleX:before {
  content: "\ed13";
}
.icon-Fill-SpeakerSlash:before {
  content: "\ed14";
}
.icon-Fill-SpeakerX:before {
  content: "\ed15";
}
.icon-Fill-Spinner:before {
  content: "\ed16";
}
.icon-Fill-SpinnerGap:before {
  content: "\ed17";
}
.icon-Fill-Spiral:before {
  content: "\ed18";
}
.icon-Fill-SplitHorizontal:before {
  content: "\ed19";
}
.icon-Fill-SplitVertical:before {
  content: "\ed1a";
}
.icon-Fill-SpotifyLogo:before {
  content: "\ed1b";
}
.icon-Fill-Square:before {
  content: "\ed1c";
}
.icon-Fill-SquareHalf:before {
  content: "\ed1d";
}
.icon-Fill-SquareHalfBottom:before {
  content: "\ed1e";
}
.icon-Fill-SquareLogo:before {
  content: "\ed1f";
}
.icon-Fill-SquaresFour:before {
  content: "\ed20";
}
.icon-Fill-SquareSplitHorizontal:before {
  content: "\ed21";
}
.icon-Fill-SquareSplitVertical:before {
  content: "\ed22";
}
.icon-Fill-Stack:before {
  content: "\ed23";
}
.icon-Fill-StackOverflowLogo:before {
  content: "\ed24";
}
.icon-Fill-StackSimple:before {
  content: "\ed25";
}
.icon-Fill-Stairs:before {
  content: "\ed26";
}
.icon-Fill-Stamp:before {
  content: "\ed27";
}
.icon-Fill-Star:before {
  content: "\ed28";
}
.icon-Fill-StarAndCrescent:before {
  content: "\ed29";
}
.icon-Fill-StarFour:before {
  content: "\ed2a";
}
.icon-Fill-StarHalf:before {
  content: "\ed2b";
}
.icon-Fill-StarOfDavid:before {
  content: "\ed2c";
}
.icon-Fill-SteeringWheel:before {
  content: "\ed2d";
}
.icon-Fill-Steps:before {
  content: "\ed2e";
}
.icon-Fill-Stethoscope:before {
  content: "\ed2f";
}
.icon-Fill-Sticker:before {
  content: "\ed30";
}
.icon-Fill-Stool:before {
  content: "\ed31";
}
.icon-Fill-Stop:before {
  content: "\ed32";
}
.icon-Fill-StopCircle:before {
  content: "\ed33";
}
.icon-Fill-Storefront:before {
  content: "\ed34";
}
.icon-Fill-Strategy:before {
  content: "\ed35";
}
.icon-Fill-StripeLogo:before {
  content: "\ed36";
}
.icon-Fill-Student:before {
  content: "\ed37";
}
.icon-Fill-Subtitles:before {
  content: "\ed38";
}
.icon-Fill-Subtract:before {
  content: "\ed39";
}
.icon-Fill-SubtractSquare:before {
  content: "\ed3a";
}
.icon-Fill-Suitcase:before {
  content: "\ed3b";
}
.icon-Fill-SuitcaseRolling:before {
  content: "\ed3c";
}
.icon-Fill-SuitcaseSimple:before {
  content: "\ed3d";
}
.icon-Fill-Sun:before {
  content: "\ed3e";
}
.icon-Fill-SunDim:before {
  content: "\ed3f";
}
.icon-Fill-Sunglasses:before {
  content: "\ed40";
}
.icon-Fill-SunHorizon:before {
  content: "\ed41";
}
.icon-Fill-Swap:before {
  content: "\ed42";
}
.icon-Fill-Swatches:before {
  content: "\ed43";
}
.icon-Fill-SwimmingPool:before {
  content: "\ed44";
}
.icon-Fill-Sword:before {
  content: "\ed45";
}
.icon-Fill-Synagogue:before {
  content: "\ed46";
}
.icon-Fill-Syringe:before {
  content: "\ed47";
}
.icon-Fill-Table:before {
  content: "\ed48";
}
.icon-Fill-Tabs:before {
  content: "\ed49";
}
.icon-Fill-Tag:before {
  content: "\ed4a";
}
.icon-Fill-TagChevron:before {
  content: "\ed4b";
}
.icon-Fill-TagSimple:before {
  content: "\ed4c";
}
.icon-Fill-Target:before {
  content: "\ed4d";
}
.icon-Fill-Taxi:before {
  content: "\ed4e";
}
.icon-Fill-TelegramLogo:before {
  content: "\ed4f";
}
.icon-Fill-Television:before {
  content: "\ed50";
}
.icon-Fill-TelevisionSimple:before {
  content: "\ed51";
}
.icon-Fill-TennisBall:before {
  content: "\ed52";
}
.icon-Fill-Tent:before {
  content: "\ed53";
}
.icon-Fill-Terminal:before {
  content: "\ed54";
}
.icon-Fill-TerminalWindow:before {
  content: "\ed55";
}
.icon-Fill-TestTube:before {
  content: "\ed56";
}
.icon-Fill-TextAa:before {
  content: "\ed57";
}
.icon-Fill-TextAlignCenter:before {
  content: "\ed58";
}
.icon-Fill-TextAlignJustify:before {
  content: "\ed59";
}
.icon-Fill-TextAlignLeft:before {
  content: "\ed5a";
}
.icon-Fill-TextAlignRight:before {
  content: "\ed5b";
}
.icon-Fill-TextAUnderline:before {
  content: "\ed5c";
}
.icon-Fill-TextB:before {
  content: "\ed5d";
}
.icon-Fill-Textbox:before {
  content: "\ed5e";
}
.icon-Fill-TextColumns:before {
  content: "\ed5f";
}
.icon-Fill-TextH:before {
  content: "\ed60";
}
.icon-Fill-TextHFive:before {
  content: "\ed61";
}
.icon-Fill-TextHFour:before {
  content: "\ed62";
}
.icon-Fill-TextHOne:before {
  content: "\ed63";
}
.icon-Fill-TextHSix:before {
  content: "\ed64";
}
.icon-Fill-TextHThree:before {
  content: "\ed65";
}
.icon-Fill-TextHTwo:before {
  content: "\ed66";
}
.icon-Fill-TextIndent:before {
  content: "\ed67";
}
.icon-Fill-TextItalic:before {
  content: "\ed68";
}
.icon-Fill-TextOutdent:before {
  content: "\ed69";
}
.icon-Fill-TextStrikethrough:before {
  content: "\ed6a";
}
.icon-Fill-TextT:before {
  content: "\ed6b";
}
.icon-Fill-TextUnderline:before {
  content: "\ed6c";
}
.icon-Fill-Thermometer:before {
  content: "\ed6d";
}
.icon-Fill-ThermometerCold:before {
  content: "\ed6e";
}
.icon-Fill-ThermometerHot:before {
  content: "\ed6f";
}
.icon-Fill-ThermometerSimple:before {
  content: "\ed70";
}
.icon-Fill-ThumbsDown:before {
  content: "\ed71";
}
.icon-Fill-ThumbsUp:before {
  content: "\ed72";
}
.icon-Fill-Ticket:before {
  content: "\ed73";
}
.icon-Fill-TidalLogo:before {
  content: "\ed74";
}
.icon-Fill-TiktokLogo:before {
  content: "\ed75";
}
.icon-Fill-Timer:before {
  content: "\ed76";
}
.icon-Fill-Tipi:before {
  content: "\ed77";
}
.icon-Fill-ToggleLeft:before {
  content: "\ed78";
}
.icon-Fill-ToggleRight:before {
  content: "\ed79";
}
.icon-Fill-Toilet:before {
  content: "\ed7a";
}
.icon-Fill-ToiletPaper:before {
  content: "\ed7b";
}
.icon-Fill-Toolbox:before {
  content: "\ed7c";
}
.icon-Fill-Tooth:before {
  content: "\ed7d";
}
.icon-Fill-Tote:before {
  content: "\ed7e";
}
.icon-Fill-ToteSimple:before {
  content: "\ed7f";
}
.icon-Fill-Trademark:before {
  content: "\ed80";
}
.icon-Fill-TrademarkRegistered:before {
  content: "\ed81";
}
.icon-Fill-TrafficCone:before {
  content: "\ed82";
}
.icon-Fill-TrafficSign:before {
  content: "\ed83";
}
.icon-Fill-TrafficSignal:before {
  content: "\ed84";
}
.icon-Fill-Train:before {
  content: "\ed85";
}
.icon-Fill-TrainRegional:before {
  content: "\ed86";
}
.icon-Fill-TrainSimple:before {
  content: "\ed87";
}
.icon-Fill-Tram:before {
  content: "\ed88";
}
.icon-Fill-Translate:before {
  content: "\ed89";
}
.icon-Fill-Trash:before {
  content: "\ed8a";
}
.icon-Fill-TrashSimple:before {
  content: "\ed8b";
}
.icon-Fill-Tray:before {
  content: "\ed8c";
}
.icon-Fill-Tree:before {
  content: "\ed8d";
}
.icon-Fill-TreeEvergreen:before {
  content: "\ed8e";
}
.icon-Fill-TreePalm:before {
  content: "\ed8f";
}
.icon-Fill-TreeStructure:before {
  content: "\ed90";
}
.icon-Fill-TrendDown:before {
  content: "\ed91";
}
.icon-Fill-TrendUp:before {
  content: "\ed92";
}
.icon-Fill-Triangle:before {
  content: "\ed93";
}
.icon-Fill-Trophy:before {
  content: "\ed94";
}
.icon-Fill-Truck:before {
  content: "\ed95";
}
.icon-Fill-TShirt:before {
  content: "\ed96";
}
.icon-Fill-TwitchLogo:before {
  content: "\ed97";
}
.icon-Fill-TwitterLogo:before {
  content: "\ed98";
}
.icon-Fill-Umbrella:before {
  content: "\ed99";
}
.icon-Fill-UmbrellaSimple:before {
  content: "\ed9a";
}
.icon-Fill-Unite:before {
  content: "\ed9b";
}
.icon-Fill-UniteSquare:before {
  content: "\ed9c";
}
.icon-Fill-Upload:before {
  content: "\ed9d";
}
.icon-Fill-UploadSimple:before {
  content: "\ed9e";
}
.icon-Fill-Usb:before {
  content: "\ed9f";
}
.icon-Fill-User:before {
  content: "\eda0";
}
.icon-Fill-UserCircle:before {
  content: "\eda1";
}
.icon-Fill-UserCircleGear:before {
  content: "\eda2";
}
.icon-Fill-UserCircleMinus:before {
  content: "\eda3";
}
.icon-Fill-UserCirclePlus:before {
  content: "\eda4";
}
.icon-Fill-UserFocus:before {
  content: "\eda5";
}
.icon-Fill-UserGear:before {
  content: "\eda6";
}
.icon-Fill-UserList:before {
  content: "\eda7";
}
.icon-Fill-UserMinus:before {
  content: "\eda8";
}
.icon-Fill-UserPlus:before {
  content: "\eda9";
}
.icon-Fill-UserRectangle:before {
  content: "\edaa";
}
.icon-Fill-Users:before {
  content: "\edab";
}
.icon-Fill-UsersFour:before {
  content: "\edac";
}
.icon-Fill-UserSquare:before {
  content: "\edad";
}
.icon-Fill-UsersThree:before {
  content: "\edae";
}
.icon-Fill-UserSwitch:before {
  content: "\edaf";
}
.icon-Fill-Van:before {
  content: "\edb0";
}
.icon-Fill-Vault:before {
  content: "\edb1";
}
.icon-Fill-Vibrate:before {
  content: "\edb2";
}
.icon-Fill-Video:before {
  content: "\edb3";
}
.icon-Fill-VideoCamera:before {
  content: "\edb4";
}
.icon-Fill-VideoCameraSlash:before {
  content: "\edb5";
}
.icon-Fill-Vignette:before {
  content: "\edb6";
}
.icon-Fill-VinylRecord:before {
  content: "\edb7";
}
.icon-Fill-VirtualReality:before {
  content: "\edb8";
}
.icon-Fill-Virus:before {
  content: "\edb9";
}
.icon-Fill-Voicemail:before {
  content: "\edba";
}
.icon-Fill-Volleyball:before {
  content: "\edbb";
}
.icon-Fill-Wall:before {
  content: "\edbc";
}
.icon-Fill-Wallet:before {
  content: "\edbd";
}
.icon-Fill-Warehouse:before {
  content: "\edbe";
}
.icon-Fill-Warning:before {
  content: "\edbf";
}
.icon-Fill-WarningCircle:before {
  content: "\edc0";
}
.icon-Fill-WarningDiamond:before {
  content: "\edc1";
}
.icon-Fill-WarningOctagon:before {
  content: "\edc2";
}
.icon-Fill-Watch:before {
  content: "\edc3";
}
.icon-Fill-Waveform:before {
  content: "\edc4";
}
.icon-Fill-Waves:before {
  content: "\edc5";
}
.icon-Fill-WaveSawtooth:before {
  content: "\edc6";
}
.icon-Fill-WaveSine:before {
  content: "\edc7";
}
.icon-Fill-WaveSquare:before {
  content: "\edc8";
}
.icon-Fill-WaveTriangle:before {
  content: "\edc9";
}
.icon-Fill-Webcam:before {
  content: "\edca";
}
.icon-Fill-WebcamSlash:before {
  content: "\edcb";
}
.icon-Fill-WebhooksLogo:before {
  content: "\edcc";
}
.icon-Fill-WechatLogo:before {
  content: "\edcd";
}
.icon-Fill-WhatsappLogo:before {
  content: "\edce";
}
.icon-Fill-Wheelchair:before {
  content: "\edcf";
}
.icon-Fill-WheelchairMotion:before {
  content: "\edd0";
}
.icon-Fill-WifiHigh:before {
  content: "\edd1";
}
.icon-Fill-WifiLow:before {
  content: "\edd2";
}
.icon-Fill-WifiMedium:before {
  content: "\edd3";
}
.icon-Fill-WifiNone:before {
  content: "\edd4";
}
.icon-Fill-WifiSlash:before {
  content: "\edd5";
}
.icon-Fill-WifiX:before {
  content: "\edd6";
}
.icon-Fill-Wind:before {
  content: "\edd7";
}
.icon-Fill-WindowsLogo:before {
  content: "\edd8";
}
.icon-Fill-Wine:before {
  content: "\edd9";
}
.icon-Fill-Wrench:before {
  content: "\edda";
}
.icon-Fill-X:before {
  content: "\eddb";
}
.icon-Fill-XCircle:before {
  content: "\eddc";
}
.icon-Fill-XSquare:before {
  content: "\eddd";
}
.icon-Fill-YinYang:before {
  content: "\edde";
}
.icon-Fill-YoutubeLogo:before {
  content: "\eddf";
}
.icon-Outline-AddressBook:before {
  content: "\ede0";
}
.icon-Outline-Airplane:before {
  content: "\ede1";
}
.icon-Outline-AirplaneInFlight:before {
  content: "\ede2";
}
.icon-Outline-AirplaneLanding:before {
  content: "\ede3";
}
.icon-Outline-AirplaneTakeoff:before {
  content: "\ede4";
}
.icon-Outline-AirplaneTilt:before {
  content: "\ede5";
}
.icon-Outline-Airplay:before {
  content: "\ede6";
}
.icon-Outline-AirTrafficControl:before {
  content: "\ede7";
}
.icon-Outline-Alarm:before {
  content: "\ede8";
}
.icon-Outline-Alien:before {
  content: "\ede9";
}
.icon-Outline-AlignBottom:before {
  content: "\edea";
}
.icon-Outline-AlignBottomSimple:before {
  content: "\edeb";
}
.icon-Outline-AlignCenterHorizontal:before {
  content: "\edec";
}
.icon-Outline-AlignCenterHorizontalSimple:before {
  content: "\eded";
}
.icon-Outline-AlignCenterVertical:before {
  content: "\edee";
}
.icon-Outline-AlignCenterVerticalSimple:before {
  content: "\edef";
}
.icon-Outline-AlignLeft:before {
  content: "\edf0";
}
.icon-Outline-AlignLeftSimple:before {
  content: "\edf1";
}
.icon-Outline-AlignRight:before {
  content: "\edf2";
}
.icon-Outline-AlignRightSimple:before {
  content: "\edf3";
}
.icon-Outline-AlignTop:before {
  content: "\edf4";
}
.icon-Outline-AlignTopSimple:before {
  content: "\edf5";
}
.icon-Outline-AmazonLogo:before {
  content: "\edf6";
}
.icon-Outline-Anchor:before {
  content: "\edf7";
}
.icon-Outline-AnchorSimple:before {
  content: "\edf8";
}
.icon-Outline-AndroidLogo:before {
  content: "\edf9";
}
.icon-Outline-AngularLogo:before {
  content: "\edfa";
}
.icon-Outline-Aperture:before {
  content: "\edfb";
}
.icon-Outline-AppleLogo:before {
  content: "\edfc";
}
.icon-Outline-ApplePodcastsLogo:before {
  content: "\edfd";
}
.icon-Outline-AppStoreLogo:before {
  content: "\edfe";
}
.icon-Outline-AppWindow:before {
  content: "\edff";
}
.icon-Outline-Archive:before {
  content: "\ee00";
}
.icon-Outline-ArchiveBox:before {
  content: "\ee01";
}
.icon-Outline-ArchiveTray:before {
  content: "\ee02";
}
.icon-Outline-Armchair:before {
  content: "\ee03";
}
.icon-Outline-ArrowArcLeft:before {
  content: "\ee04";
}
.icon-Outline-ArrowArcRight:before {
  content: "\ee05";
}
.icon-Outline-ArrowBendDoubleUpLeft:before {
  content: "\ee06";
}
.icon-Outline-ArrowBendDoubleUpRight:before {
  content: "\ee07";
}
.icon-Outline-ArrowBendDownLeft:before {
  content: "\ee08";
}
.icon-Outline-ArrowBendDownRight:before {
  content: "\ee09";
}
.icon-Outline-ArrowBendLeftDown:before {
  content: "\ee0a";
}
.icon-Outline-ArrowBendLeftUp:before {
  content: "\ee0b";
}
.icon-Outline-ArrowBendRightDown:before {
  content: "\ee0c";
}
.icon-Outline-ArrowBendRightUp:before {
  content: "\ee0d";
}
.icon-Outline-ArrowBendUpLeft:before {
  content: "\ee0e";
}
.icon-Outline-ArrowBendUpRight:before {
  content: "\ee0f";
}
.icon-Outline-ArrowCircleDown:before {
  content: "\ee10";
}
.icon-Outline-ArrowCircleDownLeft:before {
  content: "\ee11";
}
.icon-Outline-ArrowCircleDownRight:before {
  content: "\ee12";
}
.icon-Outline-ArrowCircleLeft:before {
  content: "\ee13";
}
.icon-Outline-ArrowCircleRight:before {
  content: "\ee14";
}
.icon-Outline-ArrowCircleUp:before {
  content: "\ee15";
}
.icon-Outline-ArrowCircleUpLeft:before {
  content: "\ee16";
}
.icon-Outline-ArrowCircleUpRight:before {
  content: "\ee17";
}
.icon-Outline-ArrowClockwise:before {
  content: "\ee18";
}
.icon-Outline-ArrowCounterClockwise:before {
  content: "\ee19";
}
.icon-Outline-ArrowDown:before {
  content: "\ee1a";
}
.icon-Outline-ArrowDownLeft:before {
  content: "\ee1b";
}
.icon-Outline-ArrowDownRight:before {
  content: "\ee1c";
}
.icon-Outline-ArrowElbowDownLeft:before {
  content: "\ee1d";
}
.icon-Outline-ArrowElbowDownRight:before {
  content: "\ee1e";
}
.icon-Outline-ArrowElbowLeft:before {
  content: "\ee1f";
}
.icon-Outline-ArrowElbowLeftDown:before {
  content: "\ee20";
}
.icon-Outline-ArrowElbowLeftUp:before {
  content: "\ee21";
}
.icon-Outline-ArrowElbowRight:before {
  content: "\ee22";
}
.icon-Outline-ArrowElbowRightDown:before {
  content: "\ee23";
}
.icon-Outline-ArrowElbowRightUp:before {
  content: "\ee24";
}
.icon-Outline-ArrowElbowUpLeft:before {
  content: "\ee25";
}
.icon-Outline-ArrowElbowUpRight:before {
  content: "\ee26";
}
.icon-Outline-ArrowFatDown:before {
  content: "\ee27";
}
.icon-Outline-ArrowFatLeft:before {
  content: "\ee28";
}
.icon-Outline-ArrowFatLineDown:before {
  content: "\ee29";
}
.icon-Outline-ArrowFatLineLeft:before {
  content: "\ee2a";
}
.icon-Outline-ArrowFatLineRight:before {
  content: "\ee2b";
}
.icon-Outline-ArrowFatLinesDown:before {
  content: "\ee2c";
}
.icon-Outline-ArrowFatLinesLeft:before {
  content: "\ee2d";
}
.icon-Outline-ArrowFatLinesRight:before {
  content: "\ee2e";
}
.icon-Outline-ArrowFatLinesUp:before {
  content: "\ee2f";
}
.icon-Outline-ArrowFatLineUp:before {
  content: "\ee30";
}
.icon-Outline-ArrowFatRight:before {
  content: "\ee31";
}
.icon-Outline-ArrowFatUp:before {
  content: "\ee32";
}
.icon-Outline-ArrowLeft:before {
  content: "\ee33";
}
.icon-Outline-ArrowLineDown:before {
  content: "\ee34";
}
.icon-Outline-ArrowLineDownLeft:before {
  content: "\ee35";
}
.icon-Outline-ArrowLineDownRight:before {
  content: "\ee36";
}
.icon-Outline-ArrowLineLeft:before {
  content: "\ee37";
}
.icon-Outline-ArrowLineRight:before {
  content: "\ee38";
}
.icon-Outline-ArrowLineUp:before {
  content: "\ee39";
}
.icon-Outline-ArrowLineUpLeft:before {
  content: "\ee3a";
}
.icon-Outline-ArrowLineUpRight:before {
  content: "\ee3b";
}
.icon-Outline-ArrowRight:before {
  content: "\ee3c";
}
.icon-Outline-ArrowsClockwise:before {
  content: "\ee3d";
}
.icon-Outline-ArrowsCounterClockwise:before {
  content: "\ee3e";
}
.icon-Outline-ArrowsDownUp:before {
  content: "\ee3f";
}
.icon-Outline-ArrowsHorizontal:before {
  content: "\ee40";
}
.icon-Outline-ArrowsIn:before {
  content: "\ee41";
}
.icon-Outline-ArrowsInCardinal:before {
  content: "\ee42";
}
.icon-Outline-ArrowsInLineHorizontal:before {
  content: "\ee43";
}
.icon-Outline-ArrowsInLineVertical:before {
  content: "\ee44";
}
.icon-Outline-ArrowsInSimple:before {
  content: "\ee45";
}
.icon-Outline-ArrowsLeftRight:before {
  content: "\ee46";
}
.icon-Outline-ArrowsMerge:before {
  content: "\ee47";
}
.icon-Outline-ArrowsOut:before {
  content: "\ee48";
}
.icon-Outline-ArrowsOutCardinal:before {
  content: "\ee49";
}
.icon-Outline-ArrowsOutLineHorizontal:before {
  content: "\ee4a";
}
.icon-Outline-ArrowsOutLineVertical:before {
  content: "\ee4b";
}
.icon-Outline-ArrowsOutSimple:before {
  content: "\ee4c";
}
.icon-Outline-ArrowSquareDown:before {
  content: "\ee4d";
}
.icon-Outline-ArrowSquareDownLeft:before {
  content: "\ee4e";
}
.icon-Outline-ArrowSquareDownRight:before {
  content: "\ee4f";
}
.icon-Outline-ArrowSquareIn:before {
  content: "\ee50";
}
.icon-Outline-ArrowSquareLeft:before {
  content: "\ee51";
}
.icon-Outline-ArrowSquareOut:before {
  content: "\ee52";
}
.icon-Outline-ArrowSquareRight:before {
  content: "\ee53";
}
.icon-Outline-ArrowSquareUp:before {
  content: "\ee54";
}
.icon-Outline-ArrowSquareUpLeft:before {
  content: "\ee55";
}
.icon-Outline-ArrowSquareUpRight:before {
  content: "\ee56";
}
.icon-Outline-ArrowsSplit:before {
  content: "\ee57";
}
.icon-Outline-ArrowsVertical:before {
  content: "\ee58";
}
.icon-Outline-ArrowUDownLeft:before {
  content: "\ee59";
}
.icon-Outline-ArrowUDownRight:before {
  content: "\ee5a";
}
.icon-Outline-ArrowULeftDown:before {
  content: "\ee5b";
}
.icon-Outline-ArrowULeftUp:before {
  content: "\ee5c";
}
.icon-Outline-ArrowUp:before {
  content: "\ee5d";
}
.icon-Outline-ArrowUpLeft:before {
  content: "\ee5e";
}
.icon-Outline-ArrowUpRight:before {
  content: "\ee5f";
}
.icon-Outline-ArrowURightDown:before {
  content: "\ee60";
}
.icon-Outline-ArrowURightUp:before {
  content: "\ee61";
}
.icon-Outline-ArrowUUpLeft:before {
  content: "\ee62";
}
.icon-Outline-ArrowUUpRight:before {
  content: "\ee63";
}
.icon-Outline-Article:before {
  content: "\ee64";
}
.icon-Outline-ArticleMedium:before {
  content: "\ee65";
}
.icon-Outline-ArticleNyTimes:before {
  content: "\ee66";
}
.icon-Outline-Asterisk:before {
  content: "\ee67";
}
.icon-Outline-AsteriskSimple:before {
  content: "\ee68";
}
.icon-Outline-At:before {
  content: "\ee69";
}
.icon-Outline-Atom:before {
  content: "\ee6a";
}
.icon-Outline-Baby:before {
  content: "\ee6b";
}
.icon-Outline-Backpack:before {
  content: "\ee6c";
}
.icon-Outline-Backspace:before {
  content: "\ee6d";
}
.icon-Outline-Bag:before {
  content: "\ee6e";
}
.icon-Outline-BagSimple:before {
  content: "\ee6f";
}
.icon-Outline-Balloon:before {
  content: "\ee70";
}
.icon-Outline-Bandaids:before {
  content: "\ee71";
}
.icon-Outline-Bank:before {
  content: "\ee72";
}
.icon-Outline-Barbell:before {
  content: "\ee73";
}
.icon-Outline-Barcode:before {
  content: "\ee74";
}
.icon-Outline-Barricade:before {
  content: "\ee75";
}
.icon-Outline-Baseball:before {
  content: "\ee76";
}
.icon-Outline-BaseballCap:before {
  content: "\ee77";
}
.icon-Outline-Basket:before {
  content: "\ee78";
}
.icon-Outline-Basketball:before {
  content: "\ee79";
}
.icon-Outline-Bathtub:before {
  content: "\ee7a";
}
.icon-Outline-BatteryCharging:before {
  content: "\ee7b";
}
.icon-Outline-BatteryChargingVertical:before {
  content: "\ee7c";
}
.icon-Outline-BatteryEmpty:before {
  content: "\ee7d";
}
.icon-Outline-BatteryFull:before {
  content: "\ee7e";
}
.icon-Outline-BatteryHigh:before {
  content: "\ee7f";
}
.icon-Outline-BatteryLow:before {
  content: "\ee80";
}
.icon-Outline-BatteryMedium:before {
  content: "\ee81";
}
.icon-Outline-BatteryPlus:before {
  content: "\ee82";
}
.icon-Outline-BatteryPlusVertical:before {
  content: "\ee83";
}
.icon-Outline-BatteryVerticalEmpty:before {
  content: "\ee84";
}
.icon-Outline-BatteryVerticalFull:before {
  content: "\ee85";
}
.icon-Outline-BatteryVerticalHigh:before {
  content: "\ee86";
}
.icon-Outline-BatteryVerticalLow:before {
  content: "\ee87";
}
.icon-Outline-BatteryVerticalMedium:before {
  content: "\ee88";
}
.icon-Outline-BatteryWarning:before {
  content: "\ee89";
}
.icon-Outline-BatteryWarningVertical:before {
  content: "\ee8a";
}
.icon-Outline-Bed:before {
  content: "\ee8b";
}
.icon-Outline-BeerBottle:before {
  content: "\ee8c";
}
.icon-Outline-BeerStein:before {
  content: "\ee8d";
}
.icon-Outline-BehanceLogo:before {
  content: "\ee8e";
}
.icon-Outline-Bell:before {
  content: "\ee8f";
}
.icon-Outline-BellRinging:before {
  content: "\ee90";
}
.icon-Outline-BellSimple:before {
  content: "\ee91";
}
.icon-Outline-BellSimpleRinging:before {
  content: "\ee92";
}
.icon-Outline-BellSimpleSlash:before {
  content: "\ee93";
}
.icon-Outline-BellSimpleZ:before {
  content: "\ee94";
}
.icon-Outline-BellSlash:before {
  content: "\ee95";
}
.icon-Outline-BellZ:before {
  content: "\ee96";
}
.icon-Outline-BezierCurve:before {
  content: "\ee97";
}
.icon-Outline-Bicycle:before {
  content: "\ee98";
}
.icon-Outline-Binoculars:before {
  content: "\ee99";
}
.icon-Outline-Bird:before {
  content: "\ee9a";
}
.icon-Outline-Bluetooth:before {
  content: "\ee9b";
}
.icon-Outline-BluetoothConnected:before {
  content: "\ee9c";
}
.icon-Outline-BluetoothSlash:before {
  content: "\ee9d";
}
.icon-Outline-BluetoothX:before {
  content: "\ee9e";
}
.icon-Outline-Boat:before {
  content: "\ee9f";
}
.icon-Outline-Bone:before {
  content: "\eea0";
}
.icon-Outline-Book:before {
  content: "\eea1";
}
.icon-Outline-BookBookmark:before {
  content: "\eea2";
}
.icon-Outline-Bookmark:before {
  content: "\eea3";
}
.icon-Outline-Bookmarks:before {
  content: "\eea4";
}
.icon-Outline-BookmarkSimple:before {
  content: "\eea5";
}
.icon-Outline-BookmarksSimple:before {
  content: "\eea6";
}
.icon-Outline-BookOpen:before {
  content: "\eea7";
}
.icon-Outline-BookOpenText:before {
  content: "\eea8";
}
.icon-Outline-Books:before {
  content: "\eea9";
}
.icon-Outline-Boot:before {
  content: "\eeaa";
}
.icon-Outline-BoundingBox:before {
  content: "\eeab";
}
.icon-Outline-BowlFood:before {
  content: "\eeac";
}
.icon-Outline-BracketsAngle:before {
  content: "\eead";
}
.icon-Outline-BracketsCurly:before {
  content: "\eeae";
}
.icon-Outline-BracketsRound:before {
  content: "\eeaf";
}
.icon-Outline-BracketsSquare:before {
  content: "\eeb0";
}
.icon-Outline-Brain:before {
  content: "\eeb1";
}
.icon-Outline-Brandy:before {
  content: "\eeb2";
}
.icon-Outline-Bridge:before {
  content: "\eeb3";
}
.icon-Outline-Briefcase:before {
  content: "\eeb4";
}
.icon-Outline-BriefcaseMetal:before {
  content: "\eeb5";
}
.icon-Outline-Broadcast:before {
  content: "\eeb6";
}
.icon-Outline-Broom:before {
  content: "\eeb7";
}
.icon-Outline-Browser:before {
  content: "\eeb8";
}
.icon-Outline-Browsers:before {
  content: "\eeb9";
}
.icon-Outline-Bug:before {
  content: "\eeba";
}
.icon-Outline-BugBeetle:before {
  content: "\eebb";
}
.icon-Outline-BugDroid:before {
  content: "\eebc";
}
.icon-Outline-Buildings:before {
  content: "\eebd";
}
.icon-Outline-Bus:before {
  content: "\eebe";
}
.icon-Outline-Butterfly:before {
  content: "\eebf";
}
.icon-Outline-Cactus:before {
  content: "\eec0";
}
.icon-Outline-Cake:before {
  content: "\eec1";
}
.icon-Outline-Calculator:before {
  content: "\eec2";
}
.icon-Outline-Calendar:before {
  content: "\eec3";
}
.icon-Outline-CalendarBlank:before {
  content: "\eec4";
}
.icon-Outline-CalendarCheck:before {
  content: "\eec5";
}
.icon-Outline-CalendarPlus:before {
  content: "\eec6";
}
.icon-Outline-CalendarX:before {
  content: "\eec7";
}
.icon-Outline-CallBell:before {
  content: "\eec8";
}
.icon-Outline-Camera:before {
  content: "\eec9";
}
.icon-Outline-CameraPlus:before {
  content: "\eeca";
}
.icon-Outline-CameraRotate:before {
  content: "\eecb";
}
.icon-Outline-CameraSlash:before {
  content: "\eecc";
}
.icon-Outline-Campfire:before {
  content: "\eecd";
}
.icon-Outline-Car:before {
  content: "\eece";
}
.icon-Outline-Cardholder:before {
  content: "\eecf";
}
.icon-Outline-Cards:before {
  content: "\eed0";
}
.icon-Outline-CaretCircleDoubleDown:before {
  content: "\eed1";
}
.icon-Outline-CaretCircleDoubleLeft:before {
  content: "\eed2";
}
.icon-Outline-CaretCircleDoubleRight:before {
  content: "\eed3";
}
.icon-Outline-CaretCircleDoubleUp:before {
  content: "\eed4";
}
.icon-Outline-CaretCircleDown:before {
  content: "\eed5";
}
.icon-Outline-CaretCircleLeft:before {
  content: "\eed6";
}
.icon-Outline-CaretCircleRight:before {
  content: "\eed7";
}
.icon-Outline-CaretCircleUp:before {
  content: "\eed8";
}
.icon-Outline-CaretCircleUpDown:before {
  content: "\eed9";
}
.icon-Outline-CaretDoubleDown:before {
  content: "\eeda";
}
.icon-Outline-CaretDoubleLeft:before {
  content: "\eedb";
}
.icon-Outline-CaretDoubleRight:before {
  content: "\eedc";
}
.icon-Outline-CaretDoubleUp:before {
  content: "\eedd";
}
.icon-Outline-CaretDown:before {
  content: "\eede";
}
.icon-Outline-CaretLeft:before {
  content: "\eedf";
}
.icon-Outline-CaretRight:before {
  content: "\eee0";
}
.icon-Outline-CaretUp:before {
  content: "\eee1";
}
.icon-Outline-CaretUpDown:before {
  content: "\eee2";
}
.icon-Outline-CarProfile:before {
  content: "\eee3";
}
.icon-Outline-Carrot:before {
  content: "\eee4";
}
.icon-Outline-CarSimple:before {
  content: "\eee5";
}
.icon-Outline-CassetteTape:before {
  content: "\eee6";
}
.icon-Outline-CastleTurret:before {
  content: "\eee7";
}
.icon-Outline-Cat:before {
  content: "\eee8";
}
.icon-Outline-CellSignalFull:before {
  content: "\eee9";
}
.icon-Outline-CellSignalHigh:before {
  content: "\eeea";
}
.icon-Outline-CellSignalLow:before {
  content: "\eeeb";
}
.icon-Outline-CellSignalMedium:before {
  content: "\eeec";
}
.icon-Outline-CellSignalNone:before {
  content: "\eeed";
}
.icon-Outline-CellSignalSlash:before {
  content: "\eeee";
}
.icon-Outline-CellSignalX:before {
  content: "\eeef";
}
.icon-Outline-Certificate:before {
  content: "\eef0";
}
.icon-Outline-Chair:before {
  content: "\eef1";
}
.icon-Outline-Chalkboard:before {
  content: "\eef2";
}
.icon-Outline-ChalkboardSimple:before {
  content: "\eef3";
}
.icon-Outline-ChalkboardTeacher:before {
  content: "\eef4";
}
.icon-Outline-Champagne:before {
  content: "\eef5";
}
.icon-Outline-ChargingStation:before {
  content: "\eef6";
}
.icon-Outline-ChartBar:before {
  content: "\eef7";
}
.icon-Outline-ChartBarHorizontal:before {
  content: "\eef8";
}
.icon-Outline-ChartDonut:before {
  content: "\eef9";
}
.icon-Outline-ChartLine:before {
  content: "\eefa";
}
.icon-Outline-ChartLineDown:before {
  content: "\eefb";
}
.icon-Outline-ChartLineUp:before {
  content: "\eefc";
}
.icon-Outline-ChartPie:before {
  content: "\eefd";
}
.icon-Outline-ChartPieSlice:before {
  content: "\eefe";
}
.icon-Outline-ChartPolar:before {
  content: "\eeff";
}
.icon-Outline-ChartScatter:before {
  content: "\ef00";
}
.icon-Outline-Chat:before {
  content: "\ef01";
}
.icon-Outline-ChatCentered:before {
  content: "\ef02";
}
.icon-Outline-ChatCenteredDots:before {
  content: "\ef03";
}
.icon-Outline-ChatCenteredText:before {
  content: "\ef04";
}
.icon-Outline-ChatCircle:before {
  content: "\ef05";
}
.icon-Outline-ChatCircleDots:before {
  content: "\ef06";
}
.icon-Outline-ChatCircleText:before {
  content: "\ef07";
}
.icon-Outline-ChatDots:before {
  content: "\ef08";
}
.icon-Outline-Chats:before {
  content: "\ef09";
}
.icon-Outline-ChatsCircle:before {
  content: "\ef0a";
}
.icon-Outline-ChatsTeardrop:before {
  content: "\ef0b";
}
.icon-Outline-ChatTeardrop:before {
  content: "\ef0c";
}
.icon-Outline-ChatTeardropDots:before {
  content: "\ef0d";
}
.icon-Outline-ChatTeardropText:before {
  content: "\ef0e";
}
.icon-Outline-ChatText:before {
  content: "\ef0f";
}
.icon-Outline-Check:before {
  content: "\ef10";
}
.icon-Outline-CheckCircle:before {
  content: "\ef11";
}
.icon-Outline-CheckFat:before {
  content: "\ef12";
}
.icon-Outline-Checks:before {
  content: "\ef13";
}
.icon-Outline-CheckSquare:before {
  content: "\ef14";
}
.icon-Outline-CheckSquareOffset:before {
  content: "\ef15";
}
.icon-Outline-Church:before {
  content: "\ef16";
}
.icon-Outline-Circle:before {
  content: "\ef17";
}
.icon-Outline-CircleDashed:before {
  content: "\ef18";
}
.icon-Outline-CircleHalf:before {
  content: "\ef19";
}
.icon-Outline-CircleHalfTilt:before {
  content: "\ef1a";
}
.icon-Outline-CircleNotch:before {
  content: "\ef1b";
}
.icon-Outline-CirclesFour:before {
  content: "\ef1c";
}
.icon-Outline-CirclesThree:before {
  content: "\ef1d";
}
.icon-Outline-CirclesThreePlus:before {
  content: "\ef1e";
}
.icon-Outline-Circuitry:before {
  content: "\ef1f";
}
.icon-Outline-Clipboard:before {
  content: "\ef20";
}
.icon-Outline-ClipboardText:before {
  content: "\ef21";
}
.icon-Outline-Clock:before {
  content: "\ef22";
}
.icon-Outline-ClockAfternoon:before {
  content: "\ef23";
}
.icon-Outline-ClockClockwise:before {
  content: "\ef24";
}
.icon-Outline-ClockCountdown:before {
  content: "\ef25";
}
.icon-Outline-ClockCounterClockwise:before {
  content: "\ef26";
}
.icon-Outline-ClosedCaptioning:before {
  content: "\ef27";
}
.icon-Outline-Cloud:before {
  content: "\ef28";
}
.icon-Outline-CloudArrowDown:before {
  content: "\ef29";
}
.icon-Outline-CloudArrowUp:before {
  content: "\ef2a";
}
.icon-Outline-CloudCheck:before {
  content: "\ef2b";
}
.icon-Outline-CloudFog:before {
  content: "\ef2c";
}
.icon-Outline-CloudLightning:before {
  content: "\ef2d";
}
.icon-Outline-CloudMoon:before {
  content: "\ef2e";
}
.icon-Outline-CloudRain:before {
  content: "\ef2f";
}
.icon-Outline-CloudSlash:before {
  content: "\ef30";
}
.icon-Outline-CloudSnow:before {
  content: "\ef31";
}
.icon-Outline-CloudSun:before {
  content: "\ef32";
}
.icon-Outline-CloudWarning:before {
  content: "\ef33";
}
.icon-Outline-CloudX:before {
  content: "\ef34";
}
.icon-Outline-Club:before {
  content: "\ef35";
}
.icon-Outline-CoatHanger:before {
  content: "\ef36";
}
.icon-Outline-CodaLogo:before {
  content: "\ef37";
}
.icon-Outline-Code:before {
  content: "\ef38";
}
.icon-Outline-CodeBlock:before {
  content: "\ef39";
}
.icon-Outline-CodepenLogo:before {
  content: "\ef3a";
}
.icon-Outline-CodesandboxLogo:before {
  content: "\ef3b";
}
.icon-Outline-CodeSimple:before {
  content: "\ef3c";
}
.icon-Outline-Coffee:before {
  content: "\ef3d";
}
.icon-Outline-Coin:before {
  content: "\ef3e";
}
.icon-Outline-Coins:before {
  content: "\ef3f";
}
.icon-Outline-CoinVertical:before {
  content: "\ef40";
}
.icon-Outline-Columns:before {
  content: "\ef41";
}
.icon-Outline-Command:before {
  content: "\ef42";
}
.icon-Outline-Compass:before {
  content: "\ef43";
}
.icon-Outline-CompassTool:before {
  content: "\ef44";
}
.icon-Outline-ComputerTower:before {
  content: "\ef45";
}
.icon-Outline-Confetti:before {
  content: "\ef46";
}
.icon-Outline-ContactlessPayment:before {
  content: "\ef47";
}
.icon-Outline-Control:before {
  content: "\ef48";
}
.icon-Outline-Cookie:before {
  content: "\ef49";
}
.icon-Outline-CookingPot:before {
  content: "\ef4a";
}
.icon-Outline-Copy:before {
  content: "\ef4b";
}
.icon-Outline-Copyleft:before {
  content: "\ef4c";
}
.icon-Outline-Copyright:before {
  content: "\ef4d";
}
.icon-Outline-CopySimple:before {
  content: "\ef4e";
}
.icon-Outline-CornersIn:before {
  content: "\ef4f";
}
.icon-Outline-CornersOut:before {
  content: "\ef50";
}
.icon-Outline-Couch:before {
  content: "\ef51";
}
.icon-Outline-Cpu:before {
  content: "\ef52";
}
.icon-Outline-CreditCard:before {
  content: "\ef53";
}
.icon-Outline-Crop:before {
  content: "\ef54";
}
.icon-Outline-Cross:before {
  content: "\ef55";
}
.icon-Outline-Crosshair:before {
  content: "\ef56";
}
.icon-Outline-CrosshairSimple:before {
  content: "\ef57";
}
.icon-Outline-Crown:before {
  content: "\ef58";
}
.icon-Outline-CrownSimple:before {
  content: "\ef59";
}
.icon-Outline-Cube:before {
  content: "\ef5a";
}
.icon-Outline-CubeFocus:before {
  content: "\ef5b";
}
.icon-Outline-CubeTransparent:before {
  content: "\ef5c";
}
.icon-Outline-CurrencyBtc:before {
  content: "\ef5d";
}
.icon-Outline-CurrencyCircleDollar:before {
  content: "\ef5e";
}
.icon-Outline-CurrencyCny:before {
  content: "\ef5f";
}
.icon-Outline-CurrencyDollar:before {
  content: "\ef60";
}
.icon-Outline-CurrencyDollarSimple:before {
  content: "\ef61";
}
.icon-Outline-CurrencyEth:before {
  content: "\ef62";
}
.icon-Outline-CurrencyEur:before {
  content: "\ef63";
}
.icon-Outline-CurrencyGbp:before {
  content: "\ef64";
}
.icon-Outline-CurrencyInr:before {
  content: "\ef65";
}
.icon-Outline-CurrencyJpy:before {
  content: "\ef66";
}
.icon-Outline-CurrencyKrw:before {
  content: "\ef67";
}
.icon-Outline-CurrencyKzt:before {
  content: "\ef68";
}
.icon-Outline-CurrencyNgn:before {
  content: "\ef69";
}
.icon-Outline-CurrencyRub:before {
  content: "\ef6a";
}
.icon-Outline-Cursor:before {
  content: "\ef6b";
}
.icon-Outline-CursorClick:before {
  content: "\ef6c";
}
.icon-Outline-CursorText:before {
  content: "\ef6d";
}
.icon-Outline-Cylinder:before {
  content: "\ef6e";
}
.icon-Outline-Database:before {
  content: "\ef6f";
}
.icon-Outline-Desktop:before {
  content: "\ef70";
}
.icon-Outline-DesktopTower:before {
  content: "\ef71";
}
.icon-Outline-Detective:before {
  content: "\ef72";
}
.icon-Outline-DeviceMobile:before {
  content: "\ef73";
}
.icon-Outline-DeviceMobileCamera:before {
  content: "\ef74";
}
.icon-Outline-DeviceMobileSpeaker:before {
  content: "\ef75";
}
.icon-Outline-Devices:before {
  content: "\ef76";
}
.icon-Outline-DeviceTablet:before {
  content: "\ef77";
}
.icon-Outline-DeviceTabletCamera:before {
  content: "\ef78";
}
.icon-Outline-DeviceTabletSpeaker:before {
  content: "\ef79";
}
.icon-Outline-DevToLogo:before {
  content: "\ef7a";
}
.icon-Outline-Diamond:before {
  content: "\ef7b";
}
.icon-Outline-DiamondsFour:before {
  content: "\ef7c";
}
.icon-Outline-DiceFive:before {
  content: "\ef7d";
}
.icon-Outline-DiceFour:before {
  content: "\ef7e";
}
.icon-Outline-DiceOne:before {
  content: "\ef7f";
}
.icon-Outline-DiceSix:before {
  content: "\ef80";
}
.icon-Outline-DiceThree:before {
  content: "\ef81";
}
.icon-Outline-DiceTwo:before {
  content: "\ef82";
}
.icon-Outline-Disc:before {
  content: "\ef83";
}
.icon-Outline-DiscordLogo:before {
  content: "\ef84";
}
.icon-Outline-Divide:before {
  content: "\ef85";
}
.icon-Outline-Dna:before {
  content: "\ef86";
}
.icon-Outline-Dog:before {
  content: "\ef87";
}
.icon-Outline-Door:before {
  content: "\ef88";
}
.icon-Outline-DoorOpen:before {
  content: "\ef89";
}
.icon-Outline-Dot:before {
  content: "\ef8a";
}
.icon-Outline-DotOutline:before {
  content: "\ef8b";
}
.icon-Outline-DotsNine:before {
  content: "\ef8c";
}
.icon-Outline-DotsSix:before {
  content: "\ef8d";
}
.icon-Outline-DotsSixVertical:before {
  content: "\ef8e";
}
.icon-Outline-DotsThree:before {
  content: "\ef8f";
}
.icon-Outline-DotsThreeCircle:before {
  content: "\ef90";
}
.icon-Outline-DotsThreeCircleVertical:before {
  content: "\ef91";
}
.icon-Outline-DotsThreeOutline:before {
  content: "\ef92";
}
.icon-Outline-DotsThreeOutlineVertical:before {
  content: "\ef93";
}
.icon-Outline-DotsThreeVertical:before {
  content: "\ef94";
}
.icon-Outline-Download:before {
  content: "\ef95";
}
.icon-Outline-DownloadSimple:before {
  content: "\ef96";
}
.icon-Outline-Dress:before {
  content: "\ef97";
}
.icon-Outline-DribbbleLogo:before {
  content: "\ef98";
}
.icon-Outline-Drop:before {
  content: "\ef99";
}
.icon-Outline-DropboxLogo:before {
  content: "\ef9a";
}
.icon-Outline-DropHalf:before {
  content: "\ef9b";
}
.icon-Outline-DropHalfBottom:before {
  content: "\ef9c";
}
.icon-Outline-Ear:before {
  content: "\ef9d";
}
.icon-Outline-EarSlash:before {
  content: "\ef9e";
}
.icon-Outline-Egg:before {
  content: "\ef9f";
}
.icon-Outline-EggCrack:before {
  content: "\efa0";
}
.icon-Outline-Eject:before {
  content: "\efa1";
}
.icon-Outline-EjectSimple:before {
  content: "\efa2";
}
.icon-Outline-Elevator:before {
  content: "\efa3";
}
.icon-Outline-Engine:before {
  content: "\efa4";
}
.icon-Outline-Envelope:before {
  content: "\efa5";
}
.icon-Outline-EnvelopeOpen:before {
  content: "\efa6";
}
.icon-Outline-EnvelopeSimple:before {
  content: "\efa7";
}
.icon-Outline-EnvelopeSimpleOpen:before {
  content: "\efa8";
}
.icon-Outline-Equalizer:before {
  content: "\efa9";
}
.icon-Outline-Equals:before {
  content: "\efaa";
}
.icon-Outline-Eraser:before {
  content: "\efab";
}
.icon-Outline-EscalatorDown:before {
  content: "\efac";
}
.icon-Outline-EscalatorUp:before {
  content: "\efad";
}
.icon-Outline-Exam:before {
  content: "\efae";
}
.icon-Outline-Exclude:before {
  content: "\efaf";
}
.icon-Outline-ExcludeSquare:before {
  content: "\efb0";
}
.icon-Outline-Export:before {
  content: "\efb1";
}
.icon-Outline-Eye:before {
  content: "\efb2";
}
.icon-Outline-EyeClosed:before {
  content: "\efb3";
}
.icon-Outline-Eyedropper:before {
  content: "\efb4";
}
.icon-Outline-EyedropperSample:before {
  content: "\efb5";
}
.icon-Outline-Eyeglasses:before {
  content: "\efb6";
}
.icon-Outline-EyeSlash:before {
  content: "\efb7";
}
.icon-Outline-FacebookLogo:before {
  content: "\efb8";
}
.icon-Outline-FaceMask:before {
  content: "\efb9";
}
.icon-Outline-Factory:before {
  content: "\efba";
}
.icon-Outline-Faders:before {
  content: "\efbb";
}
.icon-Outline-FadersHorizontal:before {
  content: "\efbc";
}
.icon-Outline-Fan:before {
  content: "\efbd";
}
.icon-Outline-FastForward:before {
  content: "\efbe";
}
.icon-Outline-FastForwardCircle:before {
  content: "\efbf";
}
.icon-Outline-Feather:before {
  content: "\efc0";
}
.icon-Outline-FigmaLogo:before {
  content: "\efc1";
}
.icon-Outline-File:before {
  content: "\efc2";
}
.icon-Outline-FileArchive:before {
  content: "\efc3";
}
.icon-Outline-FileArrowDown:before {
  content: "\efc4";
}
.icon-Outline-FileArrowUp:before {
  content: "\efc5";
}
.icon-Outline-FileAudio:before {
  content: "\efc6";
}
.icon-Outline-FileCloud:before {
  content: "\efc7";
}
.icon-Outline-FileCode:before {
  content: "\efc8";
}
.icon-Outline-FileCss:before {
  content: "\efc9";
}
.icon-Outline-FileCsv:before {
  content: "\efca";
}
.icon-Outline-FileDashed:before {
  content: "\efcb";
}
.icon-Outline-FileDoc:before {
  content: "\efcc";
}
.icon-Outline-FileHtml:before {
  content: "\efcd";
}
.icon-Outline-FileImage:before {
  content: "\efce";
}
.icon-Outline-FileJpg:before {
  content: "\efcf";
}
.icon-Outline-FileJs:before {
  content: "\efd0";
}
.icon-Outline-FileJsx:before {
  content: "\efd1";
}
.icon-Outline-FileLock:before {
  content: "\efd2";
}
.icon-Outline-FileMinus:before {
  content: "\efd3";
}
.icon-Outline-FilePdf:before {
  content: "\efd4";
}
.icon-Outline-FilePlus:before {
  content: "\efd5";
}
.icon-Outline-FilePng:before {
  content: "\efd6";
}
.icon-Outline-FilePpt:before {
  content: "\efd7";
}
.icon-Outline-FileRs:before {
  content: "\efd8";
}
.icon-Outline-Files:before {
  content: "\efd9";
}
.icon-Outline-FileSearch:before {
  content: "\efda";
}
.icon-Outline-FileSql:before {
  content: "\efdb";
}
.icon-Outline-FileSvg:before {
  content: "\efdc";
}
.icon-Outline-FileText:before {
  content: "\efdd";
}
.icon-Outline-FileTs:before {
  content: "\efde";
}
.icon-Outline-FileTsx:before {
  content: "\efdf";
}
.icon-Outline-FileVideo:before {
  content: "\efe0";
}
.icon-Outline-FileVue:before {
  content: "\efe1";
}
.icon-Outline-FileX:before {
  content: "\efe2";
}
.icon-Outline-FileXls:before {
  content: "\efe3";
}
.icon-Outline-FileZip:before {
  content: "\efe4";
}
.icon-Outline-FilmReel:before {
  content: "\efe5";
}
.icon-Outline-FilmScript:before {
  content: "\efe6";
}
.icon-Outline-FilmSlate:before {
  content: "\efe7";
}
.icon-Outline-FilmStrip:before {
  content: "\efe8";
}
.icon-Outline-Fingerprint:before {
  content: "\efe9";
}
.icon-Outline-FingerprintSimple:before {
  content: "\efea";
}
.icon-Outline-FinnTheHuman:before {
  content: "\efeb";
}
.icon-Outline-Fire:before {
  content: "\efec";
}
.icon-Outline-FireExtinguisher:before {
  content: "\efed";
}
.icon-Outline-FireSimple:before {
  content: "\efee";
}
.icon-Outline-FirstAid:before {
  content: "\efef";
}
.icon-Outline-FirstAidKit:before {
  content: "\eff0";
}
.icon-Outline-Fish:before {
  content: "\eff1";
}
.icon-Outline-FishSimple:before {
  content: "\eff2";
}
.icon-Outline-Flag:before {
  content: "\eff3";
}
.icon-Outline-FlagBanner:before {
  content: "\eff4";
}
.icon-Outline-FlagCheckered:before {
  content: "\eff5";
}
.icon-Outline-FlagPennant:before {
  content: "\eff6";
}
.icon-Outline-Flame:before {
  content: "\eff7";
}
.icon-Outline-Flashlight:before {
  content: "\eff8";
}
.icon-Outline-Flask:before {
  content: "\eff9";
}
.icon-Outline-FloppyDisk:before {
  content: "\effa";
}
.icon-Outline-FloppyDiskBack:before {
  content: "\effb";
}
.icon-Outline-FlowArrow:before {
  content: "\effc";
}
.icon-Outline-Flower:before {
  content: "\effd";
}
.icon-Outline-FlowerLotus:before {
  content: "\effe";
}
.icon-Outline-FlowerTulip:before {
  content: "\efff";
}
.icon-Outline-FlyingSaucer:before {
  content: "\f000";
}
.icon-Outline-Folder:before {
  content: "\f001";
}
.icon-Outline-FolderDashed:before {
  content: "\f002";
}
.icon-Outline-FolderLock:before {
  content: "\f003";
}
.icon-Outline-FolderMinus:before {
  content: "\f004";
}
.icon-Outline-FolderNotch:before {
  content: "\f005";
}
.icon-Outline-FolderNotchMinus:before {
  content: "\f006";
}
.icon-Outline-FolderNotchOpen:before {
  content: "\f007";
}
.icon-Outline-FolderNotchPlus:before {
  content: "\f008";
}
.icon-Outline-FolderOpen:before {
  content: "\f009";
}
.icon-Outline-FolderPlus:before {
  content: "\f00a";
}
.icon-Outline-Folders:before {
  content: "\f00b";
}
.icon-Outline-FolderSimple:before {
  content: "\f00c";
}
.icon-Outline-FolderSimpleDashed:before {
  content: "\f00d";
}
.icon-Outline-FolderSimpleLock:before {
  content: "\f00e";
}
.icon-Outline-FolderSimpleMinus:before {
  content: "\f00f";
}
.icon-Outline-FolderSimplePlus:before {
  content: "\f010";
}
.icon-Outline-FolderSimpleStar:before {
  content: "\f011";
}
.icon-Outline-FolderSimpleUser:before {
  content: "\f012";
}
.icon-Outline-FolderStar:before {
  content: "\f013";
}
.icon-Outline-FolderUser:before {
  content: "\f014";
}
.icon-Outline-Football:before {
  content: "\f015";
}
.icon-Outline-Footprints:before {
  content: "\f016";
}
.icon-Outline-ForkKnife:before {
  content: "\f017";
}
.icon-Outline-FrameCorners:before {
  content: "\f018";
}
.icon-Outline-FramerLogo:before {
  content: "\f019";
}
.icon-Outline-Function:before {
  content: "\f01a";
}
.icon-Outline-Funnel:before {
  content: "\f01b";
}
.icon-Outline-FunnelSimple:before {
  content: "\f01c";
}
.icon-Outline-GameController:before {
  content: "\f01d";
}
.icon-Outline-Garage:before {
  content: "\f01e";
}
.icon-Outline-GasCan:before {
  content: "\f01f";
}
.icon-Outline-GasPump:before {
  content: "\f020";
}
.icon-Outline-Gauge:before {
  content: "\f021";
}
.icon-Outline-Gavel:before {
  content: "\f022";
}
.icon-Outline-Gear:before {
  content: "\f023";
}
.icon-Outline-GearFine:before {
  content: "\f024";
}
.icon-Outline-GearSix:before {
  content: "\f025";
}
.icon-Outline-GenderFemale:before {
  content: "\f026";
}
.icon-Outline-GenderIntersex:before {
  content: "\f027";
}
.icon-Outline-GenderMale:before {
  content: "\f028";
}
.icon-Outline-GenderNeuter:before {
  content: "\f029";
}
.icon-Outline-GenderNonbinary:before {
  content: "\f02a";
}
.icon-Outline-GenderTransgender:before {
  content: "\f02b";
}
.icon-Outline-Ghost:before {
  content: "\f02c";
}
.icon-Outline-Gif:before {
  content: "\f02d";
}
.icon-Outline-Gift:before {
  content: "\f02e";
}
.icon-Outline-GitBranch:before {
  content: "\f02f";
}
.icon-Outline-GitCommit:before {
  content: "\f030";
}
.icon-Outline-GitDiff:before {
  content: "\f031";
}
.icon-Outline-GitFork:before {
  content: "\f032";
}
.icon-Outline-GithubLogo:before {
  content: "\f033";
}
.icon-Outline-GitlabLogo:before {
  content: "\f034";
}
.icon-Outline-GitlabLogoSimple:before {
  content: "\f035";
}
.icon-Outline-GitMerge:before {
  content: "\f036";
}
.icon-Outline-GitPullRequest:before {
  content: "\f037";
}
.icon-Outline-Globe:before {
  content: "\f038";
}
.icon-Outline-GlobeHemisphereEast:before {
  content: "\f039";
}
.icon-Outline-GlobeHemisphereWest:before {
  content: "\f03a";
}
.icon-Outline-GlobeSimple:before {
  content: "\f03b";
}
.icon-Outline-GlobeStand:before {
  content: "\f03c";
}
.icon-Outline-Goggles:before {
  content: "\f03d";
}
.icon-Outline-GoodreadsLogo:before {
  content: "\f03e";
}
.icon-Outline-GoogleCardboardLogo:before {
  content: "\f03f";
}
.icon-Outline-GoogleChromeLogo:before {
  content: "\f040";
}
.icon-Outline-GoogleDriveLogo:before {
  content: "\f041";
}
.icon-Outline-GoogleLogo:before {
  content: "\f042";
}
.icon-Outline-GooglePhotosLogo:before {
  content: "\f043";
}
.icon-Outline-GooglePlayLogo:before {
  content: "\f044";
}
.icon-Outline-GooglePodcastsLogo:before {
  content: "\f045";
}
.icon-Outline-Gradient:before {
  content: "\f046";
}
.icon-Outline-GraduationCap:before {
  content: "\f047";
}
.icon-Outline-Grains:before {
  content: "\f048";
}
.icon-Outline-GrainsSlash:before {
  content: "\f049";
}
.icon-Outline-Graph:before {
  content: "\f04a";
}
.icon-Outline-GridFour:before {
  content: "\f04b";
}
.icon-Outline-GridNine:before {
  content: "\f04c";
}
.icon-Outline-Guitar:before {
  content: "\f04d";
}
.icon-Outline-Hamburger:before {
  content: "\f04e";
}
.icon-Outline-Hammer:before {
  content: "\f04f";
}
.icon-Outline-Hand:before {
  content: "\f050";
}
.icon-Outline-Handbag:before {
  content: "\f051";
}
.icon-Outline-HandbagSimple:before {
  content: "\f052";
}
.icon-Outline-HandCoins:before {
  content: "\f053";
}
.icon-Outline-HandEye:before {
  content: "\f054";
}
.icon-Outline-HandFist:before {
  content: "\f055";
}
.icon-Outline-HandGrabbing:before {
  content: "\f056";
}
.icon-Outline-HandHeart:before {
  content: "\f057";
}
.icon-Outline-HandPalm:before {
  content: "\f058";
}
.icon-Outline-HandPointing:before {
  content: "\f059";
}
.icon-Outline-HandsClapping:before {
  content: "\f05a";
}
.icon-Outline-Handshake:before {
  content: "\f05b";
}
.icon-Outline-HandSoap:before {
  content: "\f05c";
}
.icon-Outline-HandsPraying:before {
  content: "\f05d";
}
.icon-Outline-HandSwipeLeft:before {
  content: "\f05e";
}
.icon-Outline-HandSwipeRight:before {
  content: "\f05f";
}
.icon-Outline-HandTap:before {
  content: "\f060";
}
.icon-Outline-HandWaving:before {
  content: "\f061";
}
.icon-Outline-HardDrive:before {
  content: "\f062";
}
.icon-Outline-HardDrives:before {
  content: "\f063";
}
.icon-Outline-Hash:before {
  content: "\f064";
}
.icon-Outline-HashStraight:before {
  content: "\f065";
}
.icon-Outline-Headlights:before {
  content: "\f066";
}
.icon-Outline-Headphones:before {
  content: "\f067";
}
.icon-Outline-Headset:before {
  content: "\f068";
}
.icon-Outline-Heart:before {
  content: "\f069";
}
.icon-Outline-Heartbeat:before {
  content: "\f06a";
}
.icon-Outline-HeartBreak:before {
  content: "\f06b";
}
.icon-Outline-HeartHalf:before {
  content: "\f06c";
}
.icon-Outline-HeartStraight:before {
  content: "\f06d";
}
.icon-Outline-HeartStraightBreak:before {
  content: "\f06e";
}
.icon-Outline-Hexagon:before {
  content: "\f06f";
}
.icon-Outline-HighHeel:before {
  content: "\f070";
}
.icon-Outline-HighlighterCircle:before {
  content: "\f071";
}
.icon-Outline-Hoodie:before {
  content: "\f072";
}
.icon-Outline-Horse:before {
  content: "\f073";
}
.icon-Outline-Hourglass:before {
  content: "\f074";
}
.icon-Outline-HourglassHigh:before {
  content: "\f075";
}
.icon-Outline-HourglassLow:before {
  content: "\f076";
}
.icon-Outline-HourglassMedium:before {
  content: "\f077";
}
.icon-Outline-HourglassSimple:before {
  content: "\f078";
}
.icon-Outline-HourglassSimpleHigh:before {
  content: "\f079";
}
.icon-Outline-HourglassSimpleLow:before {
  content: "\f07a";
}
.icon-Outline-HourglassSimpleMedium:before {
  content: "\f07b";
}
.icon-Outline-House:before {
  content: "\f07c";
}
.icon-Outline-HouseLine:before {
  content: "\f07d";
}
.icon-Outline-HouseSimple:before {
  content: "\f07e";
}
.icon-Outline-IceCream:before {
  content: "\f07f";
}
.icon-Outline-IdentificationBadge:before {
  content: "\f080";
}
.icon-Outline-IdentificationCard:before {
  content: "\f081";
}
.icon-Outline-Image:before {
  content: "\f082";
}
.icon-Outline-Images:before {
  content: "\f083";
}
.icon-Outline-ImageSquare:before {
  content: "\f084";
}
.icon-Outline-ImagesSquare:before {
  content: "\f085";
}
.icon-Outline-Infinity:before {
  content: "\f086";
}
.icon-Outline-Info:before {
  content: "\f087";
}
.icon-Outline-InstagramLogo:before {
  content: "\f088";
}
.icon-Outline-Intersect:before {
  content: "\f089";
}
.icon-Outline-IntersectSquare:before {
  content: "\f08a";
}
.icon-Outline-IntersectThree:before {
  content: "\f08b";
}
.icon-Outline-Jeep:before {
  content: "\f08c";
}
.icon-Outline-Kanban:before {
  content: "\f08d";
}
.icon-Outline-Key:before {
  content: "\f08e";
}
.icon-Outline-Keyboard:before {
  content: "\f08f";
}
.icon-Outline-Keyhole:before {
  content: "\f090";
}
.icon-Outline-KeyReturn:before {
  content: "\f091";
}
.icon-Outline-Knife:before {
  content: "\f092";
}
.icon-Outline-Ladder:before {
  content: "\f093";
}
.icon-Outline-LadderSimple:before {
  content: "\f094";
}
.icon-Outline-Lamp:before {
  content: "\f095";
}
.icon-Outline-Laptop:before {
  content: "\f096";
}
.icon-Outline-Layout:before {
  content: "\f097";
}
.icon-Outline-Leaf:before {
  content: "\f098";
}
.icon-Outline-Lifebuoy:before {
  content: "\f099";
}
.icon-Outline-Lightbulb:before {
  content: "\f09a";
}
.icon-Outline-LightbulbFilament:before {
  content: "\f09b";
}
.icon-Outline-Lighthouse:before {
  content: "\f09c";
}
.icon-Outline-Lightning:before {
  content: "\f09d";
}
.icon-Outline-LightningA:before {
  content: "\f09e";
}
.icon-Outline-LightningSlash:before {
  content: "\f09f";
}
.icon-Outline-LineSegment:before {
  content: "\f0a0";
}
.icon-Outline-LineSegments:before {
  content: "\f0a1";
}
.icon-Outline-Link:before {
  content: "\f0a2";
}
.icon-Outline-LinkBreak:before {
  content: "\f0a3";
}
.icon-Outline-LinkedinLogo:before {
  content: "\f0a4";
}
.icon-Outline-LinkSimple:before {
  content: "\f0a5";
}
.icon-Outline-LinkSimpleBreak:before {
  content: "\f0a6";
}
.icon-Outline-LinkSimpleHorizontal:before {
  content: "\f0a7";
}
.icon-Outline-LinkSimpleHorizontalBreak:before {
  content: "\f0a8";
}
.icon-Outline-LinuxLogo:before {
  content: "\f0a9";
}
.icon-Outline-List:before {
  content: "\f0aa";
}
.icon-Outline-ListBullets:before {
  content: "\f0ab";
}
.icon-Outline-ListChecks:before {
  content: "\f0ac";
}
.icon-Outline-ListDashes:before {
  content: "\f0ad";
}
.icon-Outline-ListMagnifyingGlass:before {
  content: "\f0ae";
}
.icon-Outline-ListNumbers:before {
  content: "\f0af";
}
.icon-Outline-ListPlus:before {
  content: "\f0b0";
}
.icon-Outline-Lock:before {
  content: "\f0b1";
}
.icon-Outline-Lockers:before {
  content: "\f0b2";
}
.icon-Outline-LockKey:before {
  content: "\f0b3";
}
.icon-Outline-LockKeyOpen:before {
  content: "\f0b4";
}
.icon-Outline-LockLaminated:before {
  content: "\f0b5";
}
.icon-Outline-LockLaminatedOpen:before {
  content: "\f0b6";
}
.icon-Outline-LockOpen:before {
  content: "\f0b7";
}
.icon-Outline-LockSimple:before {
  content: "\f0b8";
}
.icon-Outline-LockSimpleOpen:before {
  content: "\f0b9";
}
.icon-Outline-MagicWand:before {
  content: "\f0ba";
}
.icon-Outline-Magnet:before {
  content: "\f0bb";
}
.icon-Outline-MagnetStraight:before {
  content: "\f0bc";
}
.icon-Outline-MagnifyingGlass:before {
  content: "\f0bd";
}
.icon-Outline-MagnifyingGlassMinus:before {
  content: "\f0be";
}
.icon-Outline-MagnifyingGlassPlus:before {
  content: "\f0bf";
}
.icon-Outline-MapPin:before {
  content: "\f0c0";
}
.icon-Outline-MapPinLine:before {
  content: "\f0c1";
}
.icon-Outline-MapTrifold:before {
  content: "\f0c2";
}
.icon-Outline-MarkerCircle:before {
  content: "\f0c3";
}
.icon-Outline-Martini:before {
  content: "\f0c4";
}
.icon-Outline-MaskHappy:before {
  content: "\f0c5";
}
.icon-Outline-MaskSad:before {
  content: "\f0c6";
}
.icon-Outline-MathOperations:before {
  content: "\f0c7";
}
.icon-Outline-Medal:before {
  content: "\f0c8";
}
.icon-Outline-MedalMilitary:before {
  content: "\f0c9";
}
.icon-Outline-MediumLogo:before {
  content: "\f0ca";
}
.icon-Outline-Megaphone:before {
  content: "\f0cb";
}
.icon-Outline-MegaphoneSimple:before {
  content: "\f0cc";
}
.icon-Outline-MessengerLogo:before {
  content: "\f0cd";
}
.icon-Outline-MetaLogo:before {
  content: "\f0ce";
}
.icon-Outline-Metronome:before {
  content: "\f0cf";
}
.icon-Outline-Microphone:before {
  content: "\f0d0";
}
.icon-Outline-MicrophoneSlash:before {
  content: "\f0d1";
}
.icon-Outline-MicrophoneStage:before {
  content: "\f0d2";
}
.icon-Outline-MicrosoftExcelLogo:before {
  content: "\f0d3";
}
.icon-Outline-MicrosoftOutlookLogo:before {
  content: "\f0d4";
}
.icon-Outline-MicrosoftPowerpointLogo:before {
  content: "\f0d5";
}
.icon-Outline-MicrosoftTeamsLogo:before {
  content: "\f0d6";
}
.icon-Outline-MicrosoftWordLogo:before {
  content: "\f0d7";
}
.icon-Outline-Minus:before {
  content: "\f0d8";
}
.icon-Outline-MinusCircle:before {
  content: "\f0d9";
}
.icon-Outline-MinusSquare:before {
  content: "\f0da";
}
.icon-Outline-Money:before {
  content: "\f0db";
}
.icon-Outline-Monitor:before {
  content: "\f0dc";
}
.icon-Outline-MonitorPlay:before {
  content: "\f0dd";
}
.icon-Outline-Moon:before {
  content: "\f0de";
}
.icon-Outline-MoonStars:before {
  content: "\f0df";
}
.icon-Outline-Moped:before {
  content: "\f0e0";
}
.icon-Outline-MopedFront:before {
  content: "\f0e1";
}
.icon-Outline-Mosque:before {
  content: "\f0e2";
}
.icon-Outline-Motorcycle:before {
  content: "\f0e3";
}
.icon-Outline-Mountains:before {
  content: "\f0e4";
}
.icon-Outline-Mouse:before {
  content: "\f0e5";
}
.icon-Outline-MouseSimple:before {
  content: "\f0e6";
}
.icon-Outline-MusicNote:before {
  content: "\f0e7";
}
.icon-Outline-MusicNotes:before {
  content: "\f0e8";
}
.icon-Outline-MusicNoteSimple:before {
  content: "\f0e9";
}
.icon-Outline-MusicNotesPlus:before {
  content: "\f0ea";
}
.icon-Outline-MusicNotesSimple:before {
  content: "\f0eb";
}
.icon-Outline-NavigationArrow:before {
  content: "\f0ec";
}
.icon-Outline-Needle:before {
  content: "\f0ed";
}
.icon-Outline-Newspaper:before {
  content: "\f0ee";
}
.icon-Outline-NewspaperClipping:before {
  content: "\f0ef";
}
.icon-Outline-Notches:before {
  content: "\f0f0";
}
.icon-Outline-Note:before {
  content: "\f0f1";
}
.icon-Outline-NoteBlank:before {
  content: "\f0f2";
}
.icon-Outline-Notebook:before {
  content: "\f0f3";
}
.icon-Outline-Notepad:before {
  content: "\f0f4";
}
.icon-Outline-NotePencil:before {
  content: "\f0f5";
}
.icon-Outline-Notification:before {
  content: "\f0f6";
}
.icon-Outline-NotionLogo:before {
  content: "\f0f7";
}
.icon-Outline-NumberCircleEight:before {
  content: "\f0f8";
}
.icon-Outline-NumberCircleFive:before {
  content: "\f0f9";
}
.icon-Outline-NumberCircleFour:before {
  content: "\f0fa";
}
.icon-Outline-NumberCircleNine:before {
  content: "\f0fb";
}
.icon-Outline-NumberCircleOne:before {
  content: "\f0fc";
}
.icon-Outline-NumberCircleSeven:before {
  content: "\f0fd";
}
.icon-Outline-NumberCircleSix:before {
  content: "\f0fe";
}
.icon-Outline-NumberCircleThree:before {
  content: "\f0ff";
}
.icon-Outline-NumberCircleTwo:before {
  content: "\f100";
}
.icon-Outline-NumberCircleZero:before {
  content: "\f101";
}
.icon-Outline-NumberEight:before {
  content: "\f102";
}
.icon-Outline-NumberFive:before {
  content: "\f103";
}
.icon-Outline-NumberFour:before {
  content: "\f104";
}
.icon-Outline-NumberNine:before {
  content: "\f105";
}
.icon-Outline-NumberOne:before {
  content: "\f106";
}
.icon-Outline-NumberSeven:before {
  content: "\f107";
}
.icon-Outline-NumberSix:before {
  content: "\f108";
}
.icon-Outline-NumberSquareEight:before {
  content: "\f109";
}
.icon-Outline-NumberSquareFive:before {
  content: "\f10a";
}
.icon-Outline-NumberSquareFour:before {
  content: "\f10b";
}
.icon-Outline-NumberSquareNine:before {
  content: "\f10c";
}
.icon-Outline-NumberSquareOne:before {
  content: "\f10d";
}
.icon-Outline-NumberSquareSeven:before {
  content: "\f10e";
}
.icon-Outline-NumberSquareSix:before {
  content: "\f10f";
}
.icon-Outline-NumberSquareThree:before {
  content: "\f110";
}
.icon-Outline-NumberSquareTwo:before {
  content: "\f111";
}
.icon-Outline-NumberSquareZero:before {
  content: "\f112";
}
.icon-Outline-NumberThree:before {
  content: "\f113";
}
.icon-Outline-NumberTwo:before {
  content: "\f114";
}
.icon-Outline-NumberZero:before {
  content: "\f115";
}
.icon-Outline-Nut:before {
  content: "\f116";
}
.icon-Outline-NyTimesLogo:before {
  content: "\f117";
}
.icon-Outline-Octagon:before {
  content: "\f118";
}
.icon-Outline-OfficeChair:before {
  content: "\f119";
}
.icon-Outline-Option:before {
  content: "\f11a";
}
.icon-Outline-OrangeSlice:before {
  content: "\f11b";
}
.icon-Outline-Package:before {
  content: "\f11c";
}
.icon-Outline-PaintBrush:before {
  content: "\f11d";
}
.icon-Outline-PaintBrushBroad:before {
  content: "\f11e";
}
.icon-Outline-PaintBrushHousehold:before {
  content: "\f11f";
}
.icon-Outline-PaintBucket:before {
  content: "\f120";
}
.icon-Outline-PaintRoller:before {
  content: "\f121";
}
.icon-Outline-Palette:before {
  content: "\f122";
}
.icon-Outline-Pants:before {
  content: "\f123";
}
.icon-Outline-Paperclip:before {
  content: "\f124";
}
.icon-Outline-PaperclipHorizontal:before {
  content: "\f125";
}
.icon-Outline-PaperPlane:before {
  content: "\f126";
}
.icon-Outline-PaperPlaneRight:before {
  content: "\f127";
}
.icon-Outline-PaperPlaneTilt:before {
  content: "\f128";
}
.icon-Outline-Parachute:before {
  content: "\f129";
}
.icon-Outline-Paragraph:before {
  content: "\f12a";
}
.icon-Outline-Parallelogram:before {
  content: "\f12b";
}
.icon-Outline-Park:before {
  content: "\f12c";
}
.icon-Outline-Password:before {
  content: "\f12d";
}
.icon-Outline-Path:before {
  content: "\f12e";
}
.icon-Outline-PatreonLogo:before {
  content: "\f12f";
}
.icon-Outline-Pause:before {
  content: "\f130";
}
.icon-Outline-PauseCircle:before {
  content: "\f131";
}
.icon-Outline-PawPrint:before {
  content: "\f132";
}
.icon-Outline-PaypalLogo:before {
  content: "\f133";
}
.icon-Outline-Peace:before {
  content: "\f134";
}
.icon-Outline-Pen:before {
  content: "\f135";
}
.icon-Outline-Pencil:before {
  content: "\f136";
}
.icon-Outline-PencilCircle:before {
  content: "\f137";
}
.icon-Outline-PencilLine:before {
  content: "\f138";
}
.icon-Outline-PencilSimple:before {
  content: "\f139";
}
.icon-Outline-PencilSimpleLine:before {
  content: "\f13a";
}
.icon-Outline-PencilSimpleSlash:before {
  content: "\f13b";
}
.icon-Outline-PencilSlash:before {
  content: "\f13c";
}
.icon-Outline-PenNib:before {
  content: "\f13d";
}
.icon-Outline-PenNibStraight:before {
  content: "\f13e";
}
.icon-Outline-Pentagram:before {
  content: "\f13f";
}
.icon-Outline-Pepper:before {
  content: "\f140";
}
.icon-Outline-Percent:before {
  content: "\f141";
}
.icon-Outline-Person:before {
  content: "\f142";
}
.icon-Outline-PersonArmsSpread:before {
  content: "\f143";
}
.icon-Outline-PersonSimple:before {
  content: "\f144";
}
.icon-Outline-PersonSimpleBike:before {
  content: "\f145";
}
.icon-Outline-PersonSimpleRun:before {
  content: "\f146";
}
.icon-Outline-PersonSimpleThrow:before {
  content: "\f147";
}
.icon-Outline-PersonSimpleWalk:before {
  content: "\f148";
}
.icon-Outline-Perspective:before {
  content: "\f149";
}
.icon-Outline-Phone:before {
  content: "\f14a";
}
.icon-Outline-PhoneCall:before {
  content: "\f14b";
}
.icon-Outline-PhoneDisconnect:before {
  content: "\f14c";
}
.icon-Outline-PhoneIncoming:before {
  content: "\f14d";
}
.icon-Outline-PhoneOutgoing:before {
  content: "\f14e";
}
.icon-Outline-PhonePlus:before {
  content: "\f14f";
}
.icon-Outline-PhoneSlash:before {
  content: "\f150";
}
.icon-Outline-PhoneX:before {
  content: "\f151";
}
.icon-Outline-PhosphorLogo:before {
  content: "\f152";
}
.icon-Outline-Pi:before {
  content: "\f153";
}
.icon-Outline-PianoKeys:before {
  content: "\f154";
}
.icon-Outline-PictureInPicture:before {
  content: "\f155";
}
.icon-Outline-PiggyBank:before {
  content: "\f156";
}
.icon-Outline-Pill:before {
  content: "\f157";
}
.icon-Outline-PinterestLogo:before {
  content: "\f158";
}
.icon-Outline-Pinwheel:before {
  content: "\f159";
}
.icon-Outline-Pizza:before {
  content: "\f15a";
}
.icon-Outline-Placeholder:before {
  content: "\f15b";
}
.icon-Outline-Planet:before {
  content: "\f15c";
}
.icon-Outline-Plant:before {
  content: "\f15d";
}
.icon-Outline-Play:before {
  content: "\f15e";
}
.icon-Outline-PlayCircle:before {
  content: "\f15f";
}
.icon-Outline-Playlist:before {
  content: "\f160";
}
.icon-Outline-PlayPause:before {
  content: "\f161";
}
.icon-Outline-Plug:before {
  content: "\f162";
}
.icon-Outline-PlugCharging:before {
  content: "\f163";
}
.icon-Outline-Plugs:before {
  content: "\f164";
}
.icon-Outline-PlugsConnected:before {
  content: "\f165";
}
.icon-Outline-Plus:before {
  content: "\f166";
}
.icon-Outline-PlusCircle:before {
  content: "\f167";
}
.icon-Outline-PlusMinus:before {
  content: "\f168";
}
.icon-Outline-PlusSquare:before {
  content: "\f169";
}
.icon-Outline-PokerChip:before {
  content: "\f16a";
}
.icon-Outline-PoliceCar:before {
  content: "\f16b";
}
.icon-Outline-Polygon:before {
  content: "\f16c";
}
.icon-Outline-Popcorn:before {
  content: "\f16d";
}
.icon-Outline-PottedPlant:before {
  content: "\f16e";
}
.icon-Outline-Power:before {
  content: "\f16f";
}
.icon-Outline-Prescription:before {
  content: "\f170";
}
.icon-Outline-Presentation:before {
  content: "\f171";
}
.icon-Outline-PresentationChart:before {
  content: "\f172";
}
.icon-Outline-Printer:before {
  content: "\f173";
}
.icon-Outline-Prohibit:before {
  content: "\f174";
}
.icon-Outline-ProhibitInset:before {
  content: "\f175";
}
.icon-Outline-ProjectorScreen:before {
  content: "\f176";
}
.icon-Outline-ProjectorScreenChart:before {
  content: "\f177";
}
.icon-Outline-Pulse:before {
  content: "\f178";
}
.icon-Outline-PushPin:before {
  content: "\f179";
}
.icon-Outline-PushPinSimple:before {
  content: "\f17a";
}
.icon-Outline-PushPinSimpleSlash:before {
  content: "\f17b";
}
.icon-Outline-PushPinSlash:before {
  content: "\f17c";
}
.icon-Outline-PuzzlePiece:before {
  content: "\f17d";
}
.icon-Outline-QrCode:before {
  content: "\f17e";
}
.icon-Outline-Question:before {
  content: "\f17f";
}
.icon-Outline-Queue:before {
  content: "\f180";
}
.icon-Outline-Quotes:before {
  content: "\f181";
}
.icon-Outline-Radical:before {
  content: "\f182";
}
.icon-Outline-Radio:before {
  content: "\f183";
}
.icon-Outline-Radioactive:before {
  content: "\f184";
}
.icon-Outline-RadioButton:before {
  content: "\f185";
}
.icon-Outline-Rainbow:before {
  content: "\f186";
}
.icon-Outline-RainbowCloud:before {
  content: "\f187";
}
.icon-Outline-ReadCvLogo:before {
  content: "\f188";
}
.icon-Outline-Receipt:before {
  content: "\f189";
}
.icon-Outline-ReceiptX:before {
  content: "\f18a";
}
.icon-Outline-Record:before {
  content: "\f18b";
}
.icon-Outline-Rectangle:before {
  content: "\f18c";
}
.icon-Outline-Recycle:before {
  content: "\f18d";
}
.icon-Outline-RedditLogo:before {
  content: "\f18e";
}
.icon-Outline-Repeat:before {
  content: "\f18f";
}
.icon-Outline-RepeatOnce:before {
  content: "\f190";
}
.icon-Outline-Rewind:before {
  content: "\f191";
}
.icon-Outline-RewindCircle:before {
  content: "\f192";
}
.icon-Outline-RoadHorizon:before {
  content: "\f193";
}
.icon-Outline-Robot:before {
  content: "\f194";
}
.icon-Outline-Rocket:before {
  content: "\f195";
}
.icon-Outline-RocketLaunch:before {
  content: "\f196";
}
.icon-Outline-Rows:before {
  content: "\f197";
}
.icon-Outline-Rss:before {
  content: "\f198";
}
.icon-Outline-RssSimple:before {
  content: "\f199";
}
.icon-Outline-Rug:before {
  content: "\f19a";
}
.icon-Outline-Ruler:before {
  content: "\f19b";
}
.icon-Outline-Scales:before {
  content: "\f19c";
}
.icon-Outline-Scan:before {
  content: "\f19d";
}
.icon-Outline-Scissors:before {
  content: "\f19e";
}
.icon-Outline-Scooter:before {
  content: "\f19f";
}
.icon-Outline-Screencast:before {
  content: "\f1a0";
}
.icon-Outline-ScribbleLoop:before {
  content: "\f1a1";
}
.icon-Outline-Scroll:before {
  content: "\f1a2";
}
.icon-Outline-Seal:before {
  content: "\f1a3";
}
.icon-Outline-SealCheck:before {
  content: "\f1a4";
}
.icon-Outline-SealQuestion:before {
  content: "\f1a5";
}
.icon-Outline-SealWarning:before {
  content: "\f1a6";
}
.icon-Outline-Selection:before {
  content: "\f1a7";
}
.icon-Outline-SelectionAll:before {
  content: "\f1a8";
}
.icon-Outline-SelectionBackground:before {
  content: "\f1a9";
}
.icon-Outline-SelectionForeground:before {
  content: "\f1aa";
}
.icon-Outline-SelectionInverse:before {
  content: "\f1ab";
}
.icon-Outline-SelectionPlus:before {
  content: "\f1ac";
}
.icon-Outline-SelectionSlash:before {
  content: "\f1ad";
}
.icon-Outline-Shapes:before {
  content: "\f1ae";
}
.icon-Outline-Share:before {
  content: "\f1af";
}
.icon-Outline-ShareFat:before {
  content: "\f1b0";
}
.icon-Outline-ShareNetwork:before {
  content: "\f1b1";
}
.icon-Outline-Shield:before {
  content: "\f1b2";
}
.icon-Outline-ShieldCheck:before {
  content: "\f1b3";
}
.icon-Outline-ShieldCheckered:before {
  content: "\f1b4";
}
.icon-Outline-ShieldChevron:before {
  content: "\f1b5";
}
.icon-Outline-ShieldPlus:before {
  content: "\f1b6";
}
.icon-Outline-ShieldSlash:before {
  content: "\f1b7";
}
.icon-Outline-ShieldStar:before {
  content: "\f1b8";
}
.icon-Outline-ShieldWarning:before {
  content: "\f1b9";
}
.icon-Outline-ShirtFolded:before {
  content: "\f1ba";
}
.icon-Outline-ShootingStar:before {
  content: "\f1bb";
}
.icon-Outline-ShoppingBag:before {
  content: "\f1bc";
}
.icon-Outline-ShoppingBagOpen:before {
  content: "\f1bd";
}
.icon-Outline-ShoppingCart:before {
  content: "\f1be";
}
.icon-Outline-ShoppingCartSimple:before {
  content: "\f1bf";
}
.icon-Outline-Shower:before {
  content: "\f1c0";
}
.icon-Outline-Shrimp:before {
  content: "\f1c1";
}
.icon-Outline-Shuffle:before {
  content: "\f1c2";
}
.icon-Outline-ShuffleAngular:before {
  content: "\f1c3";
}
.icon-Outline-ShuffleSimple:before {
  content: "\f1c4";
}
.icon-Outline-Sidebar:before {
  content: "\f1c5";
}
.icon-Outline-SidebarSimple:before {
  content: "\f1c6";
}
.icon-Outline-Sigma:before {
  content: "\f1c7";
}
.icon-Outline-Signature:before {
  content: "\f1c8";
}
.icon-Outline-SignIn:before {
  content: "\f1c9";
}
.icon-Outline-SignOut:before {
  content: "\f1ca";
}
.icon-Outline-Signpost:before {
  content: "\f1cb";
}
.icon-Outline-SimCard:before {
  content: "\f1cc";
}
.icon-Outline-Siren:before {
  content: "\f1cd";
}
.icon-Outline-SketchLogo:before {
  content: "\f1ce";
}
.icon-Outline-SkipBack:before {
  content: "\f1cf";
}
.icon-Outline-SkipBackCircle:before {
  content: "\f1d0";
}
.icon-Outline-SkipForward:before {
  content: "\f1d1";
}
.icon-Outline-SkipForwardCircle:before {
  content: "\f1d2";
}
.icon-Outline-Skull:before {
  content: "\f1d3";
}
.icon-Outline-SlackLogo:before {
  content: "\f1d4";
}
.icon-Outline-Sliders:before {
  content: "\f1d5";
}
.icon-Outline-SlidersHorizontal:before {
  content: "\f1d6";
}
.icon-Outline-Slideshow:before {
  content: "\f1d7";
}
.icon-Outline-Smiley:before {
  content: "\f1d8";
}
.icon-Outline-SmileyAngry:before {
  content: "\f1d9";
}
.icon-Outline-SmileyBlank:before {
  content: "\f1da";
}
.icon-Outline-SmileyMeh:before {
  content: "\f1db";
}
.icon-Outline-SmileyNervous:before {
  content: "\f1dc";
}
.icon-Outline-SmileySad:before {
  content: "\f1dd";
}
.icon-Outline-SmileySticker:before {
  content: "\f1de";
}
.icon-Outline-SmileyWink:before {
  content: "\f1df";
}
.icon-Outline-SmileyXEyes:before {
  content: "\f1e0";
}
.icon-Outline-SnapchatLogo:before {
  content: "\f1e1";
}
.icon-Outline-Sneaker:before {
  content: "\f1e2";
}
.icon-Outline-SneakerMove:before {
  content: "\f1e3";
}
.icon-Outline-Snowflake:before {
  content: "\f1e4";
}
.icon-Outline-SoccerBall:before {
  content: "\f1e5";
}
.icon-Outline-SortAscending:before {
  content: "\f1e6";
}
.icon-Outline-SortDescending:before {
  content: "\f1e7";
}
.icon-Outline-SoundcloudLogo:before {
  content: "\f1e8";
}
.icon-Outline-Spade:before {
  content: "\f1e9";
}
.icon-Outline-Sparkle:before {
  content: "\f1ea";
}
.icon-Outline-SpeakerHifi:before {
  content: "\f1eb";
}
.icon-Outline-SpeakerHigh:before {
  content: "\f1ec";
}
.icon-Outline-SpeakerLow:before {
  content: "\f1ed";
}
.icon-Outline-SpeakerNone:before {
  content: "\f1ee";
}
.icon-Outline-SpeakerSimpleHigh:before {
  content: "\f1ef";
}
.icon-Outline-SpeakerSimpleLow:before {
  content: "\f1f0";
}
.icon-Outline-SpeakerSimpleNone:before {
  content: "\f1f1";
}
.icon-Outline-SpeakerSimpleSlash:before {
  content: "\f1f2";
}
.icon-Outline-SpeakerSimpleX:before {
  content: "\f1f3";
}
.icon-Outline-SpeakerSlash:before {
  content: "\f1f4";
}
.icon-Outline-SpeakerX:before {
  content: "\f1f5";
}
.icon-Outline-Spinner:before {
  content: "\f1f6";
}
.icon-Outline-SpinnerGap:before {
  content: "\f1f7";
}
.icon-Outline-Spiral:before {
  content: "\f1f8";
}
.icon-Outline-SplitHorizontal:before {
  content: "\f1f9";
}
.icon-Outline-SplitVertical:before {
  content: "\f1fa";
}
.icon-Outline-SpotifyLogo:before {
  content: "\f1fb";
}
.icon-Outline-Square:before {
  content: "\f1fc";
}
.icon-Outline-SquareHalf:before {
  content: "\f1fd";
}
.icon-Outline-SquareHalfBottom:before {
  content: "\f1fe";
}
.icon-Outline-SquareLogo:before {
  content: "\f1ff";
}
.icon-Outline-SquaresFour:before {
  content: "\f200";
}
.icon-Outline-SquareSplitHorizontal:before {
  content: "\f201";
}
.icon-Outline-SquareSplitVertical:before {
  content: "\f202";
}
.icon-Outline-Stack:before {
  content: "\f203";
}
.icon-Outline-StackOverflowLogo:before {
  content: "\f204";
}
.icon-Outline-StackSimple:before {
  content: "\f205";
}
.icon-Outline-Stairs:before {
  content: "\f206";
}
.icon-Outline-Stamp:before {
  content: "\f207";
}
.icon-Outline-Star:before {
  content: "\f208";
}
.icon-Outline-StarAndCrescent:before {
  content: "\f209";
}
.icon-Outline-StarFour:before {
  content: "\f20a";
}
.icon-Outline-StarHalf:before {
  content: "\f20b";
}
.icon-Outline-StarOfDavid:before {
  content: "\f20c";
}
.icon-Outline-SteeringWheel:before {
  content: "\f20d";
}
.icon-Outline-Steps:before {
  content: "\f20e";
}
.icon-Outline-Stethoscope:before {
  content: "\f20f";
}
.icon-Outline-Sticker:before {
  content: "\f210";
}
.icon-Outline-Stool:before {
  content: "\f211";
}
.icon-Outline-Stop:before {
  content: "\f212";
}
.icon-Outline-StopCircle:before {
  content: "\f213";
}
.icon-Outline-Storefront:before {
  content: "\f214";
}
.icon-Outline-Strategy:before {
  content: "\f215";
}
.icon-Outline-StripeLogo:before {
  content: "\f216";
}
.icon-Outline-Student:before {
  content: "\f217";
}
.icon-Outline-Subtitles:before {
  content: "\f218";
}
.icon-Outline-Subtract:before {
  content: "\f219";
}
.icon-Outline-SubtractSquare:before {
  content: "\f21a";
}
.icon-Outline-Suitcase:before {
  content: "\f21b";
}
.icon-Outline-SuitcaseRolling:before {
  content: "\f21c";
}
.icon-Outline-SuitcaseSimple:before {
  content: "\f21d";
}
.icon-Outline-Sun:before {
  content: "\f21e";
}
.icon-Outline-SunDim:before {
  content: "\f21f";
}
.icon-Outline-Sunglasses:before {
  content: "\f220";
}
.icon-Outline-SunHorizon:before {
  content: "\f221";
}
.icon-Outline-Swap:before {
  content: "\f222";
}
.icon-Outline-Swatches:before {
  content: "\f223";
}
.icon-Outline-SwimmingPool:before {
  content: "\f224";
}
.icon-Outline-Sword:before {
  content: "\f225";
}
.icon-Outline-Synagogue:before {
  content: "\f226";
}
.icon-Outline-Syringe:before {
  content: "\f227";
}
.icon-Outline-Table:before {
  content: "\f228";
}
.icon-Outline-Tabs:before {
  content: "\f229";
}
.icon-Outline-Tag:before {
  content: "\f22a";
}
.icon-Outline-TagChevron:before {
  content: "\f22b";
}
.icon-Outline-TagSimple:before {
  content: "\f22c";
}
.icon-Outline-Target:before {
  content: "\f22d";
}
.icon-Outline-Taxi:before {
  content: "\f22e";
}
.icon-Outline-TelegramLogo:before {
  content: "\f22f";
}
.icon-Outline-Television:before {
  content: "\f230";
}
.icon-Outline-TelevisionSimple:before {
  content: "\f231";
}
.icon-Outline-TennisBall:before {
  content: "\f232";
}
.icon-Outline-Tent:before {
  content: "\f233";
}
.icon-Outline-Terminal:before {
  content: "\f234";
}
.icon-Outline-TerminalWindow:before {
  content: "\f235";
}
.icon-Outline-TestTube:before {
  content: "\f236";
}
.icon-Outline-TextAa:before {
  content: "\f237";
}
.icon-Outline-TextAlignCenter:before {
  content: "\f238";
}
.icon-Outline-TextAlignJustify:before {
  content: "\f239";
}
.icon-Outline-TextAlignLeft:before {
  content: "\f23a";
}
.icon-Outline-TextAlignRight:before {
  content: "\f23b";
}
.icon-Outline-TextAUnderline:before {
  content: "\f23c";
}
.icon-Outline-TextB:before {
  content: "\f23d";
}
.icon-Outline-Textbox:before {
  content: "\f23e";
}
.icon-Outline-TextColumns:before {
  content: "\f23f";
}
.icon-Outline-TextH:before {
  content: "\f240";
}
.icon-Outline-TextHFive:before {
  content: "\f241";
}
.icon-Outline-TextHFour:before {
  content: "\f242";
}
.icon-Outline-TextHOne:before {
  content: "\f243";
}
.icon-Outline-TextHSix:before {
  content: "\f244";
}
.icon-Outline-TextHThree:before {
  content: "\f245";
}
.icon-Outline-TextHTwo:before {
  content: "\f246";
}
.icon-Outline-TextIndent:before {
  content: "\f247";
}
.icon-Outline-TextItalic:before {
  content: "\f248";
}
.icon-Outline-TextOutdent:before {
  content: "\f249";
}
.icon-Outline-TextStrikethrough:before {
  content: "\f24a";
}
.icon-Outline-TextT:before {
  content: "\f24b";
}
.icon-Outline-TextUnderline:before {
  content: "\f24c";
}
.icon-Outline-Thermometer:before {
  content: "\f24d";
}
.icon-Outline-ThermometerCold:before {
  content: "\f24e";
}
.icon-Outline-ThermometerHot:before {
  content: "\f24f";
}
.icon-Outline-ThermometerSimple:before {
  content: "\f250";
}
.icon-Outline-ThumbsDown:before {
  content: "\f251";
}
.icon-Outline-ThumbsUp:before {
  content: "\f252";
}
.icon-Outline-Ticket:before {
  content: "\f253";
}
.icon-Outline-TidalLogo:before {
  content: "\f254";
}
.icon-Outline-TiktokLogo:before {
  content: "\f255";
}
.icon-Outline-Timer:before {
  content: "\f256";
}
.icon-Outline-Tipi:before {
  content: "\f257";
}
.icon-Outline-ToggleLeft:before {
  content: "\f258";
}
.icon-Outline-ToggleRight:before {
  content: "\f259";
}
.icon-Outline-Toilet:before {
  content: "\f25a";
}
.icon-Outline-ToiletPaper:before {
  content: "\f25b";
}
.icon-Outline-Toolbox:before {
  content: "\f25c";
}
.icon-Outline-Tooth:before {
  content: "\f25d";
}
.icon-Outline-Tote:before {
  content: "\f25e";
}
.icon-Outline-ToteSimple:before {
  content: "\f25f";
}
.icon-Outline-Trademark:before {
  content: "\f260";
}
.icon-Outline-TrademarkRegistered:before {
  content: "\f261";
}
.icon-Outline-TrafficCone:before {
  content: "\f262";
}
.icon-Outline-TrafficSign:before {
  content: "\f263";
}
.icon-Outline-TrafficSignal:before {
  content: "\f264";
}
.icon-Outline-Train:before {
  content: "\f265";
}
.icon-Outline-TrainRegional:before {
  content: "\f266";
}
.icon-Outline-TrainSimple:before {
  content: "\f267";
}
.icon-Outline-Tram:before {
  content: "\f268";
}
.icon-Outline-Translate:before {
  content: "\f269";
}
.icon-Outline-Trash:before {
  content: "\f26a";
}
.icon-Outline-TrashSimple:before {
  content: "\f26b";
}
.icon-Outline-Tray:before {
  content: "\f26c";
}
.icon-Outline-Tree:before {
  content: "\f26d";
}
.icon-Outline-TreeEvergreen:before {
  content: "\f26e";
}
.icon-Outline-TreePalm:before {
  content: "\f26f";
}
.icon-Outline-TreeStructure:before {
  content: "\f270";
}
.icon-Outline-TrendDown:before {
  content: "\f271";
}
.icon-Outline-TrendUp:before {
  content: "\f272";
}
.icon-Outline-Triangle:before {
  content: "\f273";
}
.icon-Outline-Trophy:before {
  content: "\f274";
}
.icon-Outline-Truck:before {
  content: "\f275";
}
.icon-Outline-TShirt:before {
  content: "\f276";
}
.icon-Outline-TwitchLogo:before {
  content: "\f277";
}
.icon-Outline-TwitterLogo:before {
  content: "\f278";
}
.icon-Outline-Umbrella:before {
  content: "\f279";
}
.icon-Outline-UmbrellaSimple:before {
  content: "\f27a";
}
.icon-Outline-Unite:before {
  content: "\f27b";
}
.icon-Outline-UniteSquare:before {
  content: "\f27c";
}
.icon-Outline-Upload:before {
  content: "\f27d";
}
.icon-Outline-UploadSimple:before {
  content: "\f27e";
}
.icon-Outline-Usb:before {
  content: "\f27f";
}
.icon-Outline-User:before {
  content: "\f280";
}
.icon-Outline-UserCircle:before {
  content: "\f281";
}
.icon-Outline-UserCircleGear:before {
  content: "\f282";
}
.icon-Outline-UserCircleMinus:before {
  content: "\f283";
}
.icon-Outline-UserCirclePlus:before {
  content: "\f284";
}
.icon-Outline-UserFocus:before {
  content: "\f285";
}
.icon-Outline-UserGear:before {
  content: "\f286";
}
.icon-Outline-UserList:before {
  content: "\f287";
}
.icon-Outline-UserMinus:before {
  content: "\f288";
}
.icon-Outline-UserPlus:before {
  content: "\f289";
}
.icon-Outline-UserRectangle:before {
  content: "\f28a";
}
.icon-Outline-Users:before {
  content: "\f28b";
}
.icon-Outline-UsersFour:before {
  content: "\f28c";
}
.icon-Outline-UserSquare:before {
  content: "\f28d";
}
.icon-Outline-UsersThree:before {
  content: "\f28e";
}
.icon-Outline-UserSwitch:before {
  content: "\f28f";
}
.icon-Outline-Van:before {
  content: "\f290";
}
.icon-Outline-Vault:before {
  content: "\f291";
}
.icon-Outline-Vibrate:before {
  content: "\f292";
}
.icon-Outline-Video:before {
  content: "\f293";
}
.icon-Outline-VideoCamera:before {
  content: "\f294";
}
.icon-Outline-VideoCameraSlash:before {
  content: "\f295";
}
.icon-Outline-Vignette:before {
  content: "\f296";
}
.icon-Outline-VinylRecord:before {
  content: "\f297";
}
.icon-Outline-VirtualReality:before {
  content: "\f298";
}
.icon-Outline-Virus:before {
  content: "\f299";
}
.icon-Outline-Voicemail:before {
  content: "\f29a";
}
.icon-Outline-Volleyball:before {
  content: "\f29b";
}
.icon-Outline-Wall:before {
  content: "\f29c";
}
.icon-Outline-Wallet:before {
  content: "\f29d";
}
.icon-Outline-Warehouse:before {
  content: "\f29e";
}
.icon-Outline-Warning:before {
  content: "\f29f";
}
.icon-Outline-WarningCircle:before {
  content: "\f2a0";
}
.icon-Outline-WarningDiamond:before {
  content: "\f2a1";
}
.icon-Outline-WarningOctagon:before {
  content: "\f2a2";
}
.icon-Outline-Watch:before {
  content: "\f2a3";
}
.icon-Outline-Waveform:before {
  content: "\f2a4";
}
.icon-Outline-Waves:before {
  content: "\f2a5";
}
.icon-Outline-WaveSawtooth:before {
  content: "\f2a6";
}
.icon-Outline-WaveSine:before {
  content: "\f2a7";
}
.icon-Outline-WaveSquare:before {
  content: "\f2a8";
}
.icon-Outline-WaveTriangle:before {
  content: "\f2a9";
}
.icon-Outline-Webcam:before {
  content: "\f2aa";
}
.icon-Outline-WebcamSlash:before {
  content: "\f2ab";
}
.icon-Outline-WebhooksLogo:before {
  content: "\f2ac";
}
.icon-Outline-WechatLogo:before {
  content: "\f2ad";
}
.icon-Outline-WhatsappLogo:before {
  content: "\f2ae";
}
.icon-Outline-Wheelchair:before {
  content: "\f2af";
}
.icon-Outline-WheelchairMotion:before {
  content: "\f2b0";
}
.icon-Outline-WifiHigh:before {
  content: "\f2b1";
}
.icon-Outline-WifiLow:before {
  content: "\f2b2";
}
.icon-Outline-WifiMedium:before {
  content: "\f2b3";
}
.icon-Outline-WifiNone:before {
  content: "\f2b4";
}
.icon-Outline-WifiSlash:before {
  content: "\f2b5";
}
.icon-Outline-WifiX:before {
  content: "\f2b6";
}
.icon-Outline-Wind:before {
  content: "\f2b7";
}
.icon-Outline-WindowsLogo:before {
  content: "\f2b8";
}
.icon-Outline-Wine:before {
  content: "\f2b9";
}
.icon-Outline-Wrench:before {
  content: "\f2ba";
}
.icon-Outline-X:before {
  content: "\f2bb";
}
.icon-Outline-XCircle:before {
  content: "\f2bc";
}
.icon-Outline-XSquare:before {
  content: "\f2bd";
}
.icon-Outline-YinYang:before {
  content: "\f2be";
}
.icon-Outline-YoutubeLogo:before {
  content: "\f2bf";
}
