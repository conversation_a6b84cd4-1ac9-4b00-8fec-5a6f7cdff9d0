// This is autogenerated by running `npm run gen:i18:keys`
export type TranslationKeys =
  | 'validate.email.invalid'
  | 'validate.email.required'
  | 'validate.password.invalid'
  | 'validate.password.required'
  | 'validate.password.min'
  | 'validate.password.shouldContain'
  | 'validate.password.lowerCase'
  | 'validate.password.upperCase'
  | 'validate.password.number'
  | 'validate.password.specialCharacters'
  | 'validate.fullName.invalid'
  | 'validate.fullName.required'
  | 'validate.phoneNumber.invalid'
  | 'validate.phoneNumber.required'
  | 'validate.code.required'
  | 'validate.pin.required'
  | 'validate.pin.numbersOnly'
  | 'validate.referralCode.invalid'
  | 'validate.reason.required'
  | 'validate.yourIssue.required'
  | 'button.confirm'
  | 'button.resendCode'
  | 'button.confirmCancelBooking'
  | 'button.bookCar'
  | 'button.cancel'
  | 'button.discard'
  | 'button.addLocation'
  | 'button.apply'
  | 'button.cancelSchedule'
  | 'button.saveChange'
  | 'button.edit'
  | 'button.addMethod'
  | 'button.addCard'
  | 'button.unlink'
  | 'button.addCodePin'
  | 'button.ok'
  | 'button.login'
  | 'button.help'
  | 'button.confirmDestination'
  | 'button.confirmPickUp'
  | 'button.addNewBusiness'
  | 'button.submit'
  | 'button.later'
  | 'button.save'
  | 'button.addPromoCode'
  | 'button.cancelBooking'
  | 'button.saveLocation'
  | 'button.stillWait'
  | 'button.removeCoupon'
  | 'button.applyCoupon'
  | 'button.remove'
  | 'button.changePaymentMethod'
  | 'button.addBusiness'
  | 'button.bookACar'
  | 'button.updateNow'
  | 'button.getStarted'
  | 'button.addPaymentMethod'
  | 'button.logout'
  | 'button.add'
  | 'button.skipAndCancel'
  | 'button.keepMyRide'
  | 'button.confirmDelete'
  | 'button.delete'
  | 'button.bookingNow'
  | 'button.subscribe'
  | 'button.resubscribe'
  | 'button.subscribeNow'
  | 'button.change'
  | 'button.cancelPlan'
  | 'button.proceedCancel'
  | 'button.gotIt'
  | 'button.skip'
  | 'button.shareLink'
  | 'button.view'
  | 'button.useNow'
  | 'button.useLater'
  | 'button.use'
  | 'button.useLater2Line'
  | 'button.seeOtherCoupon'
  | 'button.continueWithoutCoupon'
  | 'button.viewLoyalPoints'
  | 'button.dismiss'
  | 'button.redeemCoupon'
  | 'button.redeemCouponPoint'
  | 'button.keepPlan'
  | 'button.viewDetail'
  | 'button.continue'
  | 'button.redeem'
  | 'button.redeemAndUse'
  | 'button.next'
  | 'button.selectPickupTime'
  | 'button.reschedule'
  | 'button.bookNow'
  | 'button.reserveBooking'
  | 'button.pickUpNow'
  | 'button.backToHomepage'
  | 'button.scheduleBooking'
  | 'button.back'
  | 'button.verify'
  | 'button.resendCodeIn'
  | 'button.verifyNow'
  | 'button.verified'
  | 'button.applyChange'
  | 'button.done'
  | 'button.resend'
  | 'login.title'
  | 'login.emailTitle'
  | 'login.passwordTitle'
  | 'login.troubleLogin'
  | 'login.signUpWith'
  | 'login.loginWith'
  | 'login.loginSession'
  | 'login.signUpNow'
  | 'login.doNotHaveAccount'
  | 'login.or'
  | 'login.getStarted'
  | 'login.createAccount'
  | 'login.continuePhoneNumber'
  | 'login.loginInWithEmail'
  | 'login.toCreateANew'
  | 'login.enterPhoneNumber'
  | 'login.otpVerification'
  | 'login.enterOtp'
  | 'login.didNotReceiveCode'
  | 'signUp.title'
  | 'signUp.nameTitle'
  | 'signUp.phoneNumberTitle'
  | 'signUp.addressTitle'
  | 'signUp.referralCode'
  | 'verifyCode.title'
  | 'verifyCode.content'
  | 'verifyCode.expiredCode'
  | 'forgotPassword.title'
  | 'forgotPassword.newPassword'
  | 'forgotPassword.content'
  | 'changePassword.title'
  | 'changePassword.currentPassword'
  | 'changePassword.newPassword'
  | 'booking.cancelRide'
  | 'booking.driverComing'
  | 'booking.confirmCancelNoCharge'
  | 'booking.confirmCancelChargeLate'
  | 'booking.bookingConfirmation'
  | 'booking.distance'
  | 'booking.estimatedTime'
  | 'booking.personal'
  | 'booking.business'
  | 'booking.paymentMethod'
  | 'booking.coupon'
  | 'booking.estArrivedTime'
  | 'booking.waitingTime'
  | 'booking.price'
  | 'booking.year'
  | 'booking.year_plural'
  | 'booking.month'
  | 'booking.month_plural'
  | 'booking.day'
  | 'booking.day_plural'
  | 'booking.hour'
  | 'booking.hour_plural'
  | 'booking.minute'
  | 'booking.minute_plural'
  | 'booking.second'
  | 'booking.second_plural'
  | 'booking.connectDrivers'
  | 'booking.distanceTime'
  | 'booking.noCarTitle'
  | 'booking.scheduleConfirmation'
  | 'booking.yourLocation'
  | 'booking.type'
  | 'booking.arrivedTime'
  | 'booking.totalIncludingTax'
  | 'booking.confirmToSchedule'
  | 'booking.noDriversNearby'
  | 'booking.noDriversNearbyContent'
  | 'booking.noDriversNearbyContent2'
  | 'booking.noDriversNearbyInAppNotification'
  | 'booking.noDriversNearbyInAppNotification2'
  | 'booking.couponNotApplied'
  | 'booking.couponNotAppliedContent'
  | 'booking.businessAdditionRequired'
  | 'booking.businessAdditionRequiredContent'
  | 'booking.paymentType'
  | 'booking.transportationTime'
  | 'booking.estArrivalTime'
  | 'booking.cardPaymentShouldUsed'
  | 'booking.cardPaymentShouldUsedContent'
  | 'booking.typeOfCar'
  | 'booking.anyCar'
  | 'booking.typicalCar'
  | 'booking.electricCar'
  | 'booking.van'
  | 'booking.accessibleVan'
  | 'booking.4seaters'
  | 'booking.46seaters'
  | 'booking.6seaters'
  | 'booking.bookingInProcessing'
  | 'booking.bookingOnGoing'
  | 'booking.status.completed'
  | 'booking.status.arrivalAtClient'
  | 'booking.status.clientOnBoard'
  | 'booking.status.vehicleConfirm'
  | 'booking.bookingID'
  | 'booking.updatePhoneTitle'
  | 'booking.phonePlaceholder'
  | 'booking.couponCannotUsed'
  | 'booking.couponCannotUsedContent'
  | 'booking.updatePhone'
  | 'booking.updatePhoneContent'
  | 'booking.updatePhonePlaceholder'
  | 'booking.timeExceeded'
  | 'booking.timeExceededMessage'
  | 'booking.chooseATime'
  | 'booking.pickUpAt'
  | 'booking.selectLocation'
  | 'booking.placeholderSelect'
  | 'booking.viewAll'
  | 'booking.priceUpdateTitle'
  | 'booking.priceUpdateDesc'
  | 'booking.priceUpdated'
  | 'booking.couponFixedFare'
  | 'booking.rideUpdated'
  | 'selectDestination.title'
  | 'selectDestination.schedule'
  | 'selectDestination.chooseOnMap'
  | 'selectDestination.whereTo'
  | 'bottomTab.home'
  | 'bottomTab.activities'
  | 'bottomTab.notification'
  | 'bottomTab.setting'
  | 'savedLocation.title.default'
  | 'savedLocation.title.home'
  | 'savedLocation.title.work'
  | 'savedLocation.title.add'
  | 'savedLocation.title.editLocation'
  | 'savedLocation.home'
  | 'savedLocation.work'
  | 'savedLocation.recent'
  | 'savedLocation.noResult'
  | 'savedLocation.enterLocation'
  | 'business.available'
  | 'business.unavailable'
  | 'business.codeAndPin'
  | 'business.pin'
  | 'business.maxSpendingLimit'
  | 'business.maxNumberUsage'
  | 'business.distanceLimit'
  | 'business.expiredDate'
  | 'business.newBusiness'
  | 'business.taxiLoyalBusinessHub'
  | 'business.businessConfiguration'
  | 'business.viewDetailInformation'
  | 'business.removeBusiness'
  | 'business.selectYourBusiness'
  | 'business.yourBusiness'
  | 'business.detailInformation'
  | 'business.removeBusinessContent'
  | 'business.yourCompanyName'
  | 'business.yourDepartment'
  | 'business.yourGroup'
  | 'business.yourRole'
  | 'business.status'
  | 'business.repeat'
  | 'business.noRepeat'
  | 'business.monthly'
  | 'business.weekly'
  | 'business.yourBusinessEmail'
  | 'business.mainInfoTitle'
  | 'business.infoTitle1'
  | 'business.infoContent1'
  | 'business.infoTitle2'
  | 'business.infoContent2'
  | 'business.infoTitle3'
  | 'business.infoContent3'
  | 'cancelBooking.title'
  | 'cancelBooking.whyCancel'
  | 'cancelBooking.optionalChoice'
  | 'cancelBooking.reasonBooking'
  | 'cancelBooking.enterReason'
  | 'cancelBooking.reason1'
  | 'cancelBooking.reason2'
  | 'cancelBooking.reason3'
  | 'cancelBooking.reason4'
  | 'cancelBooking.reason5'
  | 'cancelBooking.reason6'
  | 'cancelBooking.reason7'
  | 'cancelBooking.reason8'
  | 'cancelBooking.reason9'
  | 'cancelBooking.autoCancellation'
  | 'cancelBooking.cancelledByCenter'
  | 'rating.title'
  | 'rating.content'
  | 'rating.enterNote'
  | 'rating.earnPoint'
  | 'rating.earnPointFirstRide'
  | 'rating.earnPointFirstRideTitle'
  | 'rating.addTip'
  | 'rating.tripPrice'
  | 'rating.enterCustomAmount'
  | 'rating.payment'
  | 'rating.noTip'
  | 'rating.insufficientBalance'
  | 'rating.tipYourDriver'
  | 'rating.titleFinish'
  | 'rating.contentFinish'
  | 'rating.tipAmount'
  | 'rating.tipTheDriver'
  | 'rating.noteTip'
  | 'rating.noteTip2'
  | 'rating.extraTip'
  | 'rating.noCard'
  | 'rating.inclTip'
  | 'activities.index'
  | 'activities.history'
  | 'activities.cancelTitle'
  | 'activities.cancelContent'
  | 'activities.away'
  | 'activities.cancelledReason'
  | 'activities.allActivities'
  | 'activities.completed'
  | 'activities.cancelled'
  | 'activities.onGoing'
  | 'activities.scheduled'
  | 'activities.currentBooking'
  | 'activities.rebook'
  | 'activities.rateOrTip'
  | 'activities.rebookThisRide'
  | 'activities.rateOrTipDriver'
  | 'activities.rideConfirmed'
  | 'activities.rideConfirmedDesc'
  | 'activities.recent'
  | 'activities.route'
  | 'activities.viewHistory'
  | 'activities.exportInvoice'
  | 'activities.pdfOption'
  | 'activities.chooseOption'
  | 'activities.downloadPdf'
  | 'activities.previewPdf'
  | 'activities.sharePdf'
  | 'activities.downLoadSuccess'
  | 'activities.tipPaymentType'
  | 'activities.fullyRefund'
  | 'activities.partialRefund'
  | 'activities.refundTitleFull'
  | 'activities.refundTitlePartial'
  | 'activities.refundDesc'
  | 'home.welcome'
  | 'home.search'
  | 'home.setupPayment'
  | 'home.hello'
  | 'home.there'
  | 'home.readMore'
  | 'home.happyHoliday'
  | 'home.tapHere'
  | 'home.days'
  | 'home.hours'
  | 'home.mins'
  | 'home.secs'
  | 'scheduleTime.title'
  | 'scheduleTime.time'
  | 'empty.title'
  | 'empty.notification'
  | 'empty.coupon'
  | 'empty.business'
  | 'empty.booking'
  | 'empty.pointHistory'
  | 'empty.couponManual'
  | 'empty.subscription'
  | 'deleteAccount.title'
  | 'deleteAccount.reason'
  | 'deleteAccount.description'
  | 'deleteAccount.content1'
  | 'deleteAccount.content2'
  | 'deleteAccount.content3'
  | 'deleteAccount.content4'
  | 'deleteAccount.content5'
  | 'deleteAccount.content6'
  | 'deleteAccount.confirm1'
  | 'deleteAccount.confirm2'
  | 'deleteAccount.enterReason'
  | 'profile.avatar'
  | 'profile.generalInformation'
  | 'profile.editPersonalInformation'
  | 'profile.name'
  | 'profile.email'
  | 'profile.phoneNumber'
  | 'profile.gender'
  | 'profile.dob'
  | 'profile.selectDob'
  | 'profile.selectGender'
  | 'profile.male'
  | 'profile.female'
  | 'profile.other'
  | 'payment.title'
  | 'payment.setDefault'
  | 'payment.cardNumber'
  | 'payment.unlinkMethod'
  | 'payment.default'
  | 'payment.unlinkPayment'
  | 'payment.unlinkPaymentContent'
  | 'payment.cash'
  | 'payment.cashWaring'
  | 'payment.promoCodeApplied'
  | 'payment.date'
  | 'payment.applePay'
  | 'payment.googlePay'
  | 'payment.clickToSetUp'
  | 'payment.payInCard'
  | 'locationPicker.title'
  | 'locationPicker.pickUpConfirmation'
  | 'locationPicker.soFarAway'
  | 'help.index'
  | 'help.title'
  | 'help.callPolice'
  | 'help.callDispatchCenter'
  | 'help.shareLocation'
  | 'help.contactSupport'
  | 'help.supportDescription'
  | 'help.paymentIssue'
  | 'help.lostItems'
  | 'help.other'
  | 'help.selectReason'
  | 'help.shareYourIssue'
  | 'help.reasonTitle'
  | 'help.thankYou'
  | 'help.submitSuccess'
  | 'tooltip.distanceAndTime'
  | 'tooltip.estimatedDestination'
  | 'picker.openImages'
  | 'picker.openCamera'
  | 'about.rental'
  | 'about.contactUs'
  | 'about.businessHours'
  | 'about.taxiLoyalPolicies'
  | 'about.privacyPolicy'
  | 'about.protectionPolicy'
  | 'about.workingTime'
  | 'birthday40Year.title'
  | 'birthday40Year.date'
  | 'birthday40Year.time'
  | 'birthday40Year.mainTitle'
  | 'birthday40Year.mainDescription'
  | 'birthday40Year.discountTitle'
  | 'birthday40Year.discountDescription'
  | 'birthday40Year.bookNowButton'
  | 'birthday40Year.subscriptionTitle'
  | 'birthday40Year.subscriptionOriginalPrice'
  | 'birthday40Year.subscriptionDescription'
  | 'birthday40Year.subscriptionBenefit1'
  | 'birthday40Year.subscriptionBenefit2'
  | 'birthday40Year.subscriptionBenefit3'
  | 'birthday40Year.subscriptionBenefit4'
  | 'birthday40Year.tryNowButton'
  | 'birthday40Year.referralTitle'
  | 'birthday40Year.referralDescription'
  | 'birthday40Year.referralDescription2'
  | 'birthday40Year.referralDescription3'
  | 'birthday40Year.earnPointsButton'
  | 'birthday40Year.hurryMessage'
  | 'notification.title'
  | 'notification.general'
  | 'notification.promotion'
  | 'notification.readPromotion'
  | 'notification.readGeneral'
  | 'coupon.title'
  | 'coupon.promoCode'
  | 'coupon.loyalPoint'
  | 'coupon.comingSoon'
  | 'coupon.loyalPointContent'
  | 'coupon.searchCoupon'
  | 'coupon.discountPercent'
  | 'coupon.discountAmount'
  | 'coupon.upTo'
  | 'coupon.redeemCoupon'
  | 'coupon.redeemThisCoupon'
  | 'coupon.unavailable'
  | 'coupon.myCoupon'
  | 'coupon.couponStore'
  | 'coupon.redeemCouponSuccess'
  | 'updateAppVersion.title'
  | 'updateAppVersion.content'
  | 'intro.introTitle1'
  | 'intro.introContent1'
  | 'intro.introTitle2'
  | 'intro.introContent2'
  | 'intro.introTitle3'
  | 'intro.introContent3'
  | 'intro.introTitle4'
  | 'intro.introContent4'
  | 'language.title'
  | 'language.changeLanguage'
  | 'setting.general'
  | 'setting.profileDetails'
  | 'setting.privacyCentre'
  | 'setting.about'
  | 'setting.others'
  | 'setting.version'
  | 'setting.aboutTaxiLoyal'
  | 'setting.referFriends'
  | 'error.500'
  | 'error.inAnotherBooking'
  | 'error.invalidLink'
  | 'error.codeSend'
  | 'error.closeDestination'
  | 'error.notSupportedLocation'
  | 'permissions.requestPhoto'
  | 'permissions.requestCamera'
  | 'permissions.titleAlertLimited'
  | 'permissions.selectMore'
  | 'permissions.selectAll'
  | 'permissions.keepPhotos'
  | 'permissions.saveFile'
  | 'permissions.fileDownload'
  | 'subscription.description'
  | 'subscription.paidMonthly'
  | 'subscription.subscribed'
  | 'subscription.canceled'
  | 'subscription.endOn'
  | 'subscription.renewOn'
  | 'subscription.unlimitedBenefits'
  | 'subscription.history'
  | 'subscription.title'
  | 'subscription.benefits'
  | 'subscription.managePlan'
  | 'subscription.paymentMethod'
  | 'subscription.termOfUse'
  | 'subscription.usageInstruction'
  | 'subscription.renewTitle'
  | 'subscription.renewContent'
  | 'subscription.monthlyCharge'
  | 'subscription.billingStarts'
  | 'subscription.billing'
  | 'subscription.planPrice'
  | 'subscription.totalPrice'
  | 'subscription.includingTax'
  | 'subscription.billNote'
  | 'subscription.purchased'
  | 'subscription.planHistory'
  | 'subscription.transactionDetails'
  | 'subscription.servicePeriod'
  | 'subscription.paymentID'
  | 'subscription.cancelTitle'
  | 'subscription.cancelPlan'
  | 'subscription.cancelSuccessContent'
  | 'subscription.cancelSuccessTitle'
  | 'subscription.welcome'
  | 'subscription.welcomeContent'
  | 'subscription.cancelConfirm'
  | 'subscription.cancelConfirmContent'
  | 'subscription.detail'
  | 'subscription.chooseAPlan'
  | 'subscription.currentPlan'
  | 'subscription.changePlan'
  | 'subscription.unsubscribe'
  | 'subscription.noteUnsubscribe'
  | 'subscription.unsubscribeModal.confirm.title'
  | 'subscription.unsubscribeModal.confirm.content'
  | 'subscription.unsubscribeModal.success.title'
  | 'subscription.unsubscribeModal.success.description'
  | 'subscription.endsIn'
  | 'subscription.subscribedTo'
  | 'subscription.renewalOn'
  | 'subscription.cancelledPackage'
  | 'subscription.expireOn'
  | 'subscription.from'
  | 'subscription.month'
  | 'subscription.year'
  | 'subscription.noteApplyChange'
  | 'subscription.yearly'
  | 'subscription.monthly'
  | 'subscription.equivalentToOnly'
  | 'subscription.off'
  | 'subscription.yearlyCharge'
  | 'subscription.noteChangePlan'
  | 'subscription.saveValue'
  | 'subscription.getDiscount'
  | 'subscription.onPlan'
  | 'subscription.cancelPackage'
  | 'subscription.resubscribe'
  | 'subscription.keepActive'
  | 'subscription.yourAllSet'
  | 'subscription.changePlanDescription'
  | 'subscription.priorityPickup'
  | 'subscription.unsubscribePackage'
  | 'subscription.success'
  | 'subscription.failed'
  | 'subscription.plan'
  | 'subscription.planEnds'
  | 'subscription.planStarts'
  | 'subscription.switchPlan'
  | 'subscription.processedDate'
  | 'subscription.chooseYourPlan'
  | 'subscription.chooseYourPlanDesc'
  | 'subscription.changePlanNote'
  | 'referral.title'
  | 'referral.content'
  | 'referral.successfulReferral'
  | 'referral.totalPointsEarned'
  | 'referral.myReferralCode'
  | 'referral.invitedList'
  | 'referral.points'
  | 'referral.copy'
  | 'referral.socialContent'
  | 'referral.enterReferralCode'
  | 'referral.placeHolder'
  | 'referral.referralBonus'
  | 'referral.referralBonusUnlocked'
  | 'referral.referralBonusUnlockedContent'
  | 'loyalPoints.title'
  | 'loyalPoints.totalLoyalPoints'
  | 'loyalPoints.howGetPoints'
  | 'loyalPoints.howUsePoints'
  | 'loyalPoints.redeemYourPoints'
  | 'loyalPoints.redeem'
  | 'loyalPoints.yourPoints'
  | 'historyPoint.title'
  | 'historyPoint.completedRide'
  | 'historyPoint.completedFirstRide'
  | 'accessibleVan.content'
  | 'accessibleVan.contentModal'
  | 'note.noteToDriver'
  | 'note.title'
  | 'note.placeHolderNote'
  | 'note.travelWithCat'
  | 'note.travelWithDog'
  | 'note.babySeat'
  | 'note.heavyPackage'
  | 'note.addedPickupDetails'
  | 'deactivated.title'
  | 'deactivated.description'
  | 'deactivated.cannotOpenURL'
  | 'modalRide.now'
  | 'modalRide.schedule'
  | 'modalRide.nowDesc'
  | 'modalRide.scheduleDesc'
  | 'modalRide.title'
  | 'shareRoute.lookingForDriver'
  | 'shareRoute.waitingForPickup'
  | 'shareRoute.driverArrived'
  | 'shareRoute.onTheWay'
  | 'shareRoute.arrived'
  | 'shareRoute.linkShare'
  | 'shareRoute.titleSuccess'
  | 'shareRoute.titleFailed'
  | 'shareRoute.descriptionSuccess'
  | 'shareRoute.descriptionFailed'
  | 'shareRoute.bookingCancel'
  | 'verifyPhone.title'
  | 'verifyPhone.descriptionPhone'
  | 'verifyPhone.descriptionOtp'
  | 'verifyPhone.otpInCorrect'
  | 'verifyPhone.titleSuccess'
  | 'verifyPhone.descriptionSuccess'
  | 'verifyPhone.policy'
  | 'verifyPhone.here'
  | 'errorMessage.500.message'
  | 'errorMessage.500.description'
  | 'errorMessage.502.message'
  | 'errorMessage.502.description'
  | 'errorMessage.503.message'
  | 'errorMessage.503.description'
  | 'errorMessage.504.message'
  | 'errorMessage.504.description'
  | 'errorMessage.noInternet.message'
  | 'errorMessage.noInternet.description';
