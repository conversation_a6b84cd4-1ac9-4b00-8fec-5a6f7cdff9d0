/* eslint-disable max-params */
/* eslint-disable react-hooks/exhaustive-deps */
import {
  Animated,
  PanResponder,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import MapView, {Mark<PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/icomoon';
import mapStyle from './mapStyle.json';
import {WButton, WImage, WText, WTouchable, WView} from '@components/index';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors, Icons} from '@themes/index';
import {ILocationItem} from '@global';
import {
  INITIAL_REGION,
  IS_ANDROID,
  IS_IOS,
  MAX_HEIGHT_MODAL_PICKUP,
  MIN_HEIGHT_MODAL_PICKUP,
  SCREEN_WIDTH,
} from '@themes/Constants';
import {useLocationsServiceLocationControllerGetLocationsByPlaceIds} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import Spin from '@components/Spin';
import {CreateBookingWithCodeDto} from '@requests';

function PickupSubLocation() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {bottom: paddingBottom, top: paddingTop} = useSafeAreaInsets();

  const [selectedPickUp, setSelectedPickUp] = useState<ILocationItem | null>(
    null,
  );
  const [containerHeight, setContainerHeight] = useState(
    MIN_HEIGHT_MODAL_PICKUP,
  );
  const [isLoadingDrop, setIsLoadingDrop] = useState(false);

  const mapRef = useRef<MapView>(null);
  const containerHeightRef = useRef(containerHeight);

  const [pickUpConfirm] = useGlobalState('pickUpConfirm');
  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );

  const {data: dataSubLocation, isLoading: isLoadingSubLocation} =
    useLocationsServiceLocationControllerGetLocationsByPlaceIds(
      {
        placeIds: JSON.stringify([pickUpConfirm?.placeId]),
      },

      undefined,
      {
        enabled: !!pickUpConfirm?.placeId,
      },
    );

  const onMapLoaded = () => {
    if (
      mapRef?.current &&
      containerHeightRef.current &&
      selectedPickUp &&
      !isLoadingDrop
    ) {
      setTimeout(() => {
        if (containerHeight !== MIN_HEIGHT_MODAL_PICKUP) {
          return mapRef?.current?.fitToSuppliedMarkers(['marker1'], {
            animated: true,
            edgePadding: {
              bottom: containerHeight,
              left: 120,
              right: 120,
              top: 120,
            },
          });
        }
        mapRef?.current?.animateCamera({
          center: {
            latitude: selectedPickUp?.coordinates?.[1] ?? 0,
            longitude: selectedPickUp?.coordinates?.[0] ?? 0,
          },
          zoom: 18,
        });
      }, 1000);
    }
  };

  const onDefaultPickUp = () => {
    const {address, geo} = bookingWithCodeParams?.originLocation || {};
    if (bookingWithCodeParams?.originLocation) {
      setSelectedPickUp({
        address: address?.address ?? '',
        coordinates: [geo?.longitude ?? 0, geo?.latitude ?? 0],
        name: address?.name ?? '',
      });
    }
  };

  const onConfirmPickUpLocation = () => {
    setBookingWithCodeParams({
      ...bookingWithCodeParams,
      originLocation: {
        address: {
          address: selectedPickUp?.address ?? '',
          name: selectedPickUp?.name ?? '',
        },
        geo: {
          latitude: selectedPickUp?.coordinates?.[1] as number,
          longitude: selectedPickUp?.coordinates?.[0] as number,
        },
      },
    } as unknown as CreateBookingWithCodeDto);
    setTimeout(() => {
      navigation.navigate('BookingMaps', {isTopBarEnable: false});
    }, 800);
  };

  const goBack = () => navigation.goBack();

  useEffect(() => {
    onMapLoaded();
  }, [
    selectedPickUp,
    containerHeightRef?.current,
    isLoadingSubLocation,
    isLoadingDrop,
  ]);

  useEffect(() => {
    onDefaultPickUp();
  }, [bookingWithCodeParams]);

  const panResponder = useRef(
    PanResponder.create({
      onPanResponderMove: (_, gestureState) => {
        const newHeight = Math.max(
          MIN_HEIGHT_MODAL_PICKUP,
          Math.min(
            MAX_HEIGHT_MODAL_PICKUP,
            containerHeightRef?.current - gestureState?.dy,
          ),
        );
        setContainerHeight(newHeight);
        setIsLoadingDrop(true);
      },
      onPanResponderRelease: (_, gestureState) => {
        const newHeight = Math.max(
          MIN_HEIGHT_MODAL_PICKUP,
          Math.min(
            MAX_HEIGHT_MODAL_PICKUP,
            containerHeightRef?.current - gestureState?.dy,
          ),
        );
        setContainerHeight(newHeight);
        containerHeightRef.current = newHeight;
        setIsLoadingDrop(false);
      },
      onPanResponderTerminationRequest: () => true,
      onStartShouldSetPanResponder: () => true,
    }),
  )?.current;

  const renderItem = (item: ILocationItem, index: number) => {
    const isSelected =
      JSON.stringify(item?.coordinates) ===
      JSON.stringify(selectedPickUp?.coordinates);
    return (
      <WTouchable
        borderColor={isSelected ? Colors.neutral700 : Colors.neutral100}
        borderRadius={8}
        borderWidth={1}
        color={Colors.neutral100}
        gap={3}
        key={index}
        mBottom={8}
        onPress={() => setSelectedPickUp(item)}
        padding={16}>
        <WText color={Colors.neutral875} type="medium14">
          {item?.name ?? ''}
        </WText>
        <WText color={Colors.neutral600} type="regular12">
          {item?.address ?? ''}
        </WText>
      </WTouchable>
    );
  };

  const data = dataSubLocation?.data?.[0] || {};
  const isMapReady = isLoadingSubLocation || !selectedPickUp;
  const heightModal = containerHeight + 100; // add 100 for the bottom sheet

  return (
    <Spin loading={isMapReady}>
      <WView color={Colors.transparent} fill>
        <MapView
          customMapStyle={mapStyle}
          initialRegion={INITIAL_REGION}
          onMapReady={() => null}
          pitchEnabled
          provider={PROVIDER_GOOGLE}
          ref={mapRef}
          showsBuildings={false}
          showsUserLocation={true}
          style={styles.map}>
          <Marker
            coordinate={{
              latitude: selectedPickUp?.coordinates?.[1] || 0,
              longitude: selectedPickUp?.coordinates?.[0] || 0,
            }}
            identifier="marker1"
            tracksViewChanges={IS_IOS}>
            <WImage
              h={43}
              resizeMode="cover"
              source={Icons.icSelectorLocation}
              w={24}
            />
            {IS_ANDROID && (
              <WText style={styles.cheatText} type="regular14">
                {Math.random()}
              </WText>
            )}
          </Marker>
        </MapView>

        <Animated.View
          style={[styles.viewDrop, {height: heightModal}]}
          {...panResponder?.panHandlers}>
          <WView
            borderTopLeftRadius={16}
            borderTopRightRadius={16}
            color={Colors.white}
            fill
            mTop={-10}
            style={GlobalStyle.shadow}
            w={SCREEN_WIDTH}>
            <View>
              <WView
                borderRadius={5}
                color={Colors.neutral400}
                h={5}
                mVer={12}
                selfCenter
                w={36}
              />
            </View>
            <WView alignCenter justifyBetween mHoz={16} mVer={8} row>
              <WText type="semiBold18">
                {t('locationPicker.pickUpConfirmation')}
              </WText>
              <TouchableWithoutFeedback onPress={goBack}>
                <WView
                  borderRadius={20}
                  center
                  color={Colors.neutral500}
                  h={20}
                  w={20}>
                  <IonIcon
                    color={Colors.white}
                    name="close-outline"
                    size={16}
                  />
                </WView>
              </TouchableWithoutFeedback>
            </WView>
            <ScrollView>
              <WView mHoz={16} mTop={12}>
                <WText color={Colors.neutral875} type="medium14">
                  {data?.title || ''}
                </WText>
                <WView mTop={8}>
                  {(data?.subLocations || [])?.map(renderItem)}
                </WView>
                <WView h={150} />
              </WView>
            </ScrollView>
          </WView>
        </Animated.View>

        <TouchableWithoutFeedback onPress={goBack}>
          <WView
            aTop={paddingTop + 12}
            absolute
            alignCenter
            borderRadius={20}
            color={Colors.white}
            h={40}
            justifyCenter
            mLeft={16}
            mTop={12}
            style={GlobalStyle.shadow}
            w={40}>
            <Icon name="Outline-CaretLeft" size={20} />
          </WView>
        </TouchableWithoutFeedback>
        <WView
          color={Colors.white}
          pBottom={paddingBottom + 12}
          pHoz={16}
          pTop={8}>
          <WButton
            label={t('button.confirmPickUp')}
            onPress={onConfirmPickUpLocation}
          />
        </WView>
      </WView>
    </Spin>
  );
}

export default PickupSubLocation;

const styles = StyleSheet.create({
  cheatText: {height: 0, width: 0},
  map: {
    flex: 1,
  },
  viewDrop: {
    bottom: 0,
    position: 'absolute',
  },
});
