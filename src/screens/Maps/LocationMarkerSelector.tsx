/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
import {RouteProp, useNavigation} from '@react-navigation/native';
import {getDistance} from 'geolib';
import React, {useEffect, useRef, useState} from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import MapView, {PROVIDER_GOOGLE} from 'react-native-maps';
import Icon from 'react-native-vector-icons/icomoon';
import IonIcon from 'react-native-vector-icons/Ionicons';
import mapStyle from './mapStyle.json';
import {WButton, WText, WView} from '@components';
import {LocationMarkerSelectorRouteProps} from '@global';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  BookingEntity,
  CreateBookingWithCodeDto,
  GeoLocationType,
  LocationDto,
} from '@requests';
import {Colors, Images} from '@themes';
import {
  GOOGLE_MAPS_API_KEY,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
  SEARCH_TYPE,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import Icons from '@themes/Icons';
import {useLocationPermission} from '@utils/hooks/useGeoLocation';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {showErrorAlert, sortByLocationType} from '@utils/Tools';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

interface LocationMarkerSelectorProps {
  route: RouteProp<LocationMarkerSelectorRouteProps, 'LocationMarkerSelector'>;
}

let isLoadedMap = false;
let isFirstRender = true;

function LocationMarkerSelector({route}: LocationMarkerSelectorProps) {
  const {t} = useTypeSafeTranslation();
  const buttonTitle = {
    [SEARCH_TYPE.PICKUP]: t('button.confirmPickUp'),
    [SEARCH_TYPE.DESTINATION]: t('button.confirmDestination'),
    [SEARCH_TYPE.SAVED_LOCATION]: t('button.saveLocation'),
  };

  const {currentSearchType, location, onConfirm, isConfirmPickupLocation} =
    route?.params || {};

  const navigation = useNavigation();
  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );

  const mapRef = useRef<MapView>(null);

  const [userLocation, setUserLocation] = useState<LocationDto | undefined>(
    location,
  );
  const [startMoveMarker, setStartMoveMarker] = useState(false);
  const [isShowToolTip, setIsShowToolTip] = useState(false);

  const {setCurrentStatus} = useBookingGlobalStatus();

  useEffect(
    () => () => {
      isLoadedMap = false;
      isFirstRender = true;
    },
    [],
  );

  const {requestLocationPermission, data: dataCurrentLocation} =
    useLocationPermission();

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const onRegionChangeComplete = (region: GeoLocationType) => {
    setStartMoveMarker(false);
    if (isFirstRender) {
      isFirstRender = false;
      return;
    }

    fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${region.latitude},${region.longitude}&key=${GOOGLE_MAPS_API_KEY}`,
    )
      .then(response => response.json())
      .then(responseJson => {
        const sortedData = sortByLocationType(responseJson.results);
        const addressName = sortedData?.[0]?.formatted_address;
        const addressArray = addressName?.split(', ');
        setUserLocation({
          address: {
            address: addressName as string,
            name: `${addressArray?.[0]} ${addressArray?.[1]}`,
          },
          geo: region,
        });
      })
      .catch(() => {
        showErrorAlert({message: t('error.500')});
      })
      .finally(() => {
        if (
          dataCurrentLocation?.location?.geo?.latitude &&
          dataCurrentLocation?.location?.geo?.longitude
        ) {
          onCheckShowTooTip({
            latitude: region?.latitude,
            longitude: region?.longitude,
          });
        }

        // dismissLoading();
      });
  };

  const onRegionChange = () => {
    if (!startMoveMarker) {
      setStartMoveMarker(true);
    }
  };

  const onConfirmPickUpLocation = () => {
    setBookingWithCodeParams({
      ...bookingWithCodeParams,
      originLocation: userLocation,
    } as CreateBookingWithCodeDto);
    setTimeout(() => {
      setCurrentStatus(BookingEntity.status.CONFIRMED_ADDRESS);
      navigation.navigate('BookingMaps', {isTopBarEnable: false});
    }, 800);
  };

  const onMapLoaded = () => {
    if (!isLoadedMap) {
      isLoadedMap = true;
      mapRef?.current?.animateCamera({
        center: {
          latitude: location?.geo?.latitude as number,
          longitude: location?.geo?.longitude as number,
        },
        zoom: 18.5,
      });
    }
  };

  const onCheckShowTooTip = ({
    latitude = 0,
    longitude = 0,
  }: {
    latitude?: number;
    longitude?: number;
  }) => {
    const pointA = {
      latitude,
      longitude,
    };
    const pointB = {
      latitude: dataCurrentLocation?.location?.geo?.latitude || 0,
      longitude: dataCurrentLocation?.location?.geo?.longitude || 0,
    };
    const distance = getDistance(pointA, pointB); // meters
    if (Number(distance) >= 500) {
      setIsShowToolTip(true);
    } else {
      setIsShowToolTip(false);
    }
  };

  const initializeTooltip = () => {
    const {latitude, longitude} =
      bookingWithCodeParams?.originLocation?.geo ?? {};
    if (latitude && longitude && currentSearchType === SEARCH_TYPE.PICKUP) {
      onCheckShowTooTip({
        latitude,
        longitude,
      });
    }
  };

  useEffect(() => {
    initializeTooltip();
  }, [bookingWithCodeParams?.originLocation?.geo]);

  return (
    <WView fill>
      <MapView
        camera={{
          center: {
            latitude: location?.geo?.latitude as number,
            longitude: location?.geo?.longitude as number,
          },
          heading: 0,
          pitch: 0,
          zoom: 18.5,
        }}
        customMapStyle={mapStyle}
        onMapLoaded={onMapLoaded}
        onMapReady={() => null}
        onRegionChange={onRegionChange}
        onRegionChangeComplete={onRegionChangeComplete}
        pitchEnabled
        provider={PROVIDER_GOOGLE}
        ref={mapRef}
        showsBuildings={false}
        showsUserLocation={true}
        style={styles.map}
      />
      <SafeAreaView style={styles.container}>
        <TouchableWithoutFeedback
          onPress={() => {
            navigation.goBack();
          }}>
          <WView
            alignCenter
            borderRadius={20}
            color={Colors.white}
            h={40}
            justifyCenter
            mTop={12}
            style={GlobalStyle.shadow}
            w={40}>
            <Icon name="Outline-CaretLeft" size={20} />
          </WView>
        </TouchableWithoutFeedback>
      </SafeAreaView>
      <WView style={styles.markerContainer}>
        {currentSearchType === SEARCH_TYPE.PICKUP ? (
          <>
            {isShowToolTip && (
              <WView
                borderRadius={8}
                center
                color={Colors.neutral850}
                h={28}
                style={styles.viewToolTip}
                w={(t('locationPicker.soFarAway')?.length || 1) * 6.8}>
                <WText color={Colors.white} type="regular14">
                  {t('locationPicker.soFarAway')}
                </WText>
              </WView>
            )}
            <Image
              resizeMode="contain"
              source={
                startMoveMarker
                  ? Icons.icSelectorLocationPickup
                  : Icons.icSelectorLocation
              }
              style={styles.markerImg}
            />
          </>
        ) : (
          <Image
            resizeMode="contain"
            source={Images.destinationMarker}
            style={styles.destinationMarkerImg}
          />
        )}
      </WView>

      <SafeAreaView style={styles.bottomViewContainer}>
        <SafeAreaView style={styles.bottomView}>
          <WView alignCenter justifyBetween margin={16} row>
            <WText type="semiBold18">
              {isConfirmPickupLocation
                ? t('locationPicker.pickUpConfirmation')
                : t('locationPicker.title')}
            </WText>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral500}
                h={20}
                w={20}>
                <IonIcon color={Colors.white} name="close-outline" size={16} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>

          <WView
            alignCenter
            borderRadius={8}
            color={Colors.neutral100}
            mHoz={16}
            mVer={8}
            padding={16}
            row>
            {currentSearchType === SEARCH_TYPE.DESTINATION && (
              <Icon color={Colors.primary} name="Fill-MapPin" size={22} />
            )}

            {currentSearchType === SEARCH_TYPE.PICKUP && (
              <WView
                alignCenter
                borderRadius={10}
                color={Colors.transparent}
                justifyCenter
                mRight={12}>
                <Image
                  resizeMode="contain"
                  source={Images.userMarker}
                  style={styles.fakeMarkerImg}
                />
              </WView>
            )}

            <WView fill mLeft={8}>
              <WText lineHeight={24} type="medium14">
                {userLocation?.address?.name}
              </WText>
              <WText color={Colors.neutral600} type="regular12">
                {userLocation?.address?.address}
              </WText>
            </WView>
          </WView>

          <WButton
            label={
              buttonTitle?.[currentSearchType || SEARCH_TYPE.SAVED_LOCATION]
            }
            onPress={() => {
              if (isConfirmPickupLocation) {
                onConfirmPickUpLocation();
              } else {
                onConfirm?.(userLocation);
              }
            }}
            style={styles.confirmBtn}
          />
        </SafeAreaView>
      </SafeAreaView>
    </WView>
  );
}

export default LocationMarkerSelector;

const styles = StyleSheet.create({
  bottomView: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    width: SCREEN_WIDTH - 32,
  },
  bottomViewContainer: {
    bottom: 12,
    left: 16,
    position: 'absolute',
    right: 16,
  },
  confirmBtn: {margin: 16},
  container: {
    left: 16,
    position: 'absolute',
    top: 0,
  },
  destinationMarkerImg: {marginBottom: 12, tintColor: Colors.primary},
  fakeMarkerImg: {height: 20, width: 20},
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerContainer: {
    alignItems: 'center',
    bottom: (SCREEN_HEIGHT - 50) / 2,
    justifyContent: 'center',
    left: (SCREEN_WIDTH - 50) / 2,
    position: 'absolute',
    right: (SCREEN_WIDTH - 50) / 2,
    top: (SCREEN_HEIGHT - 50) / 2,
  },
  markerImg: {
    height: 43,
    width: 24,
  },
  viewToolTip: {
    marginBottom: 5,
  },
});
