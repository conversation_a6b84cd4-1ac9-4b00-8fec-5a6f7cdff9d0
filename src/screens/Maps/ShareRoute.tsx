import React from 'react';
import MapView, {<PERSON>er, <PERSON>yline, PROVIDER_GOOGLE} from 'react-native-maps';
import {Image, StyleSheet, Text, TouchableWithoutFeedback} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/icomoon';
import LottieView from 'lottie-react-native';
import {isEmpty} from 'lodash-es';
import mapStyle from './mapStyle.json';
import {WImage, WText, WView} from '@components/index';
import {Colors, Images} from '@themes/index';
import {
  INITIAL_REGION,
  IS_ANDROID,
  IS_IOS,
  SCREEN_WIDTH,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import ModalBottomDrop from '@components/Modal/ModalBottomDrop';
import {useShareRoute} from '@utils/hooks/useShareRoute';
import {getPoints} from '@utils/Tools';
import Spin from '@components/Spin';

const MAX_HEIGHT = 300;
const MIN_HEIGHT = 230;

function ShareRoute() {
  const {top: paddingTop} = useSafeAreaInsets();

  const {
    mapRef,
    distance,
    carDegree,
    driverLocation,
    goBack,
    onCallUser,
    getLineDashPattern,
    titleModal,
    progressPercent,
    isLookingForDriver,
    isWaitingForPickup,
    isShowPolyline,
    isCompleteDestination,
    isCompleteOrigin,
    isShowDestination,
    isShowMinAway,
    isArrivalAtClient,
    isCompletedOrReceived,
    isAmountMoneyReceived,
    isVehicleConfirmed,
    isClientOnBoard,
    isCompleted,
    isCancelled,
    waitTimeTitle,
    booking,
  } = useShareRoute();

  const renderTitleHeader = () => {
    const getProgressColor = (condition: boolean) =>
      condition ? Colors.primary : Colors.neutral400;

    const getImageSource = () => {
      if (isClientOnBoard) return Images.userMarker;
      if (isCompletedOrReceived || isArrivalAtClient)
        return Images.destinationMarker;
      return Images.carHorizontalMarker;
    };

    const getImagesEnd = () => {
      if (isArrivalAtClient) return Images.userMarker;
      return Images.destinationMarker;
    };

    const renderProgressBar = () => (
      <WView alignCenter mBottom={10} mTop={16} row>
        {isVehicleConfirmed ? (
          <>
            <WView
              color={Colors.primary}
              h={2}
              maxWidth={SCREEN_WIDTH - 86}
              w={progressPercent}
            />
            <WImage
              h={16}
              resizeMode="contain"
              source={Images.carHorizontalMarker}
              w={32}
            />
            <WImage
              resizeMode="contain"
              size={20}
              source={Images.userMarker}
              style={styles.userMarker}
            />
            <WView
              color={Colors.neutral400}
              h={2}
              maxWidth={SCREEN_WIDTH - 32}
              style={styles.lineCar}
              w="100%"
            />
          </>
        ) : (
          <>
            {!isArrivalAtClient && !isCompleted && !isAmountMoneyReceived && (
              <Image
                resizeMode={isClientOnBoard ? 'contain' : undefined}
                source={getImageSource()}
                style={[styles.carImg, isClientOnBoard && styles.pickUpMarker]}
              />
            )}
            <WView
              color={getProgressColor(
                isClientOnBoard || isCompletedOrReceived || isArrivalAtClient,
              )}
              fill
              h={2}
            />
            {isClientOnBoard && (
              <Image
                source={Images.carHorizontalMarker}
                style={styles.carHorizontalImg}
              />
            )}
            <WView
              color={getProgressColor(
                isCompletedOrReceived || isArrivalAtClient,
              )}
              fill
              h={2}
            />
            {(isArrivalAtClient || isCompletedOrReceived) && (
              <Image
                source={Images.carHorizontalMarker}
                style={[styles.carImg, !isArrivalAtClient && styles.mRight14]}
              />
            )}
            <Image
              resizeMode="contain"
              source={getImagesEnd()}
              style={
                isCompletedOrReceived || isClientOnBoard
                  ? styles.destinationMarkerImg
                  : styles.userMarkerImg
              }
            />
          </>
        )}
      </WView>
    );

    return (
      <WView color={Colors.neutral100} pHoz={16}>
        <WView alignCenter justifyBetween row>
          <WText color={Colors.neutral875} type="semiBold12">
            {titleModal}
          </WText>
          <WView alignCenter gap={24} row>
            {isShowMinAway && (
              <WText color={Colors.neutral700} type="regular12">
                {waitTimeTitle}
              </WText>
            )}
            <TouchableWithoutFeedback onPress={onCallUser}>
              <WView
                alignCenter
                borderRadius={16}
                color={Colors.neutral200}
                h={32}
                justifyCenter
                w={32}>
                <Icon name="Outline-PhoneCall" size={20} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>
        </WView>
        {!isLookingForDriver && !isCancelled && renderProgressBar()}
        <WView color={Colors.neutral200} h={1} mTop={12} w="100%" />
      </WView>
    );
  };

  const renderLocation = () => (
    <WView mHoz={16} mTop={16}>
      <WView alignCenter gap={16} justifyBetween row>
        <WView alignCenter gap={16} row>
          <WImage resizeMode="contain" size={20} source={Images.userMarker} />
          <WText color={Colors.neutral900} type="regular14">
            {booking?.originAddress?.address ?? ''}
          </WText>
        </WView>
        <Icon
          color={isCompleteOrigin ? Colors.success : Colors.neutral300}
          name={isCompleteOrigin ? 'Fill-CheckCircle' : 'Outline-CheckCircle'}
          size={20}
        />
      </WView>
      <WImage
        resizeMode="contain"
        source={Images.dotLine}
        style={styles.dotLine}
      />
      <WView alignCenter gap={16} justifyBetween row>
        <WView alignCenter gap={16} row>
          <Icon color={Colors.primary} name="Fill-MapPin" size={22} />
          <WText color={Colors.neutral900} type="regular14">
            {booking?.destinationAddress?.address ?? ''}
          </WText>
        </WView>
        <Icon
          color={isCompleteDestination ? Colors.success : Colors.neutral300}
          name={
            isCompleteDestination ? 'Fill-CheckCircle' : 'Outline-CheckCircle'
          }
          size={20}
        />
      </WView>
      <WView color={Colors.neutral200} h={1} mVer={12} w={'100%'} />
    </WView>
  );

  const renderDriver = () => {
    const driverName = `${booking?.driver?.firstName ?? ''} ${
      booking?.driver?.lastName ?? ''
    }`;

    if (isEmpty(booking?.driver)) return null;
    return (
      <WView alignCenter justifyBetween mHoz={16} row>
        <WView alignCenter gap={16} row>
          <WImage source={Images.defaultAvatar} style={styles.driverAvatar} />
          <WView
            borderColor={Colors.neutral200}
            borderRightWidth={1}
            pRight={6}>
            <WText color={Colors.neutral900} type="medium14">
              {booking?.vehicle?.makeModel || 'N/A'}
            </WText>
            <WText color={Colors.neutral600} type="regular12">
              {booking?.vehicle?.licensePlateNumber || 'N/A'}
            </WText>
          </WView>
        </WView>
        <WView alignItems="flex-end">
          <WText color={Colors.neutral900} type="medium14">
            {driverName}
          </WText>
          <WView alignCenter gap={2} row>
            <WText color={Colors.neutral600} type="regular12">
              {booking?.driver?.avgRating?.toFixed(1) || 'N/A'}
            </WText>
            <Icon color={Colors.starYellow} name="Fill-Star" size={12} />
          </WView>
        </WView>
      </WView>
    );
  };

  return (
    <Spin loading={!booking}>
      <WView color={Colors.transparent} fill>
        <MapView
          customMapStyle={mapStyle}
          initialRegion={INITIAL_REGION}
          onMapReady={() => null}
          pitchEnabled
          provider={PROVIDER_GOOGLE}
          ref={mapRef}
          showsBuildings={false}
          showsUserLocation={true}
          style={GlobalStyle.flex1}>
          {isShowPolyline && (
            <Polyline
              coordinates={getPoints([
                {
                  latitude: booking?.originLocation?.[1],
                  longitude: booking?.originLocation?.[0],
                },
                {
                  latitude: booking?.destinationLocation?.[1],
                  longitude: booking?.destinationLocation?.[0],
                },
              ])}
              lineCap="round"
              lineDashPattern={getLineDashPattern(distance)}
              strokeColor={Colors.success}
              strokeWidth={4}
              style={styles.polyline}
              zIndex={99}
            />
          )}

          {!isEmpty(booking?.originLocation) && (
            <Marker
              anchor={{x: 0.45, y: 0.7}}
              coordinate={{
                latitude: booking?.originLocation?.[1],
                longitude: booking?.originLocation?.[0],
              }}
              identifier={'marker1'}
              tracksViewChanges={IS_IOS}
              zIndex={9}>
              <WView>
                <WImage resizeMode="contain" size={55} source={Images.marker} />
                <WView
                  borderRadius={10}
                  color={Colors.neutral500}
                  h={15}
                  style={styles.markerImg}
                  w={15}
                />
              </WView>
              {IS_ANDROID && (
                <Text style={styles.cheatText}>{Math.random()}</Text>
              )}
            </Marker>
          )}

          {isLookingForDriver && (
            <Marker
              anchor={{x: 0.49, y: 0.49}}
              coordinate={{
                latitude: booking?.originLocation?.[1],
                longitude: booking?.originLocation?.[0],
              }}
              identifier={'marker1'}
              tracksViewChanges={true}>
              <LottieView
                autoPlay
                loop
                source={require('../../assets/GIF/radar.json')}
                style={{height: SCREEN_WIDTH - 100, width: SCREEN_WIDTH - 100}}
              />
              {IS_ANDROID && (
                <Text style={styles.cheatText}>{Math.random()}</Text>
              )}
            </Marker>
          )}

          {isWaitingForPickup && (
            <Marker
              anchor={{x: 0.45, y: 0.7}}
              coordinate={driverLocation?.data?.currentLocation}
              identifier="marker2"
              tracksViewChanges={IS_IOS}>
              <WImage
                resizeMode="center"
                size={55}
                source={Images.carMarker}
                style={{transform: [{rotate: `${carDegree || 0}deg`}]}}
              />
              {IS_ANDROID && (
                <Text style={styles.cheatText}>{Math.random()}</Text>
              )}
            </Marker>
          )}

          {isShowDestination && (
            <Marker
              anchor={{x: 0.8, y: 0.9}}
              coordinate={{
                latitude: booking?.destinationLocation?.[1],
                longitude: booking?.destinationLocation?.[0],
              }}
              identifier={'marker2'}
              tracksViewChanges={IS_IOS}>
              <Image
                resizeMode="contain"
                source={Images.destinationMarker}
                style={styles.iconDestination}
              />

              {IS_ANDROID && (
                <Text style={styles.cheatText}>{Math.random()}</Text>
              )}
            </Marker>
          )}
        </MapView>

        <TouchableWithoutFeedback onPress={goBack}>
          <WView
            aTop={paddingTop + 12}
            absolute
            alignCenter
            borderRadius={20}
            color={Colors.white}
            h={40}
            justifyCenter
            mLeft={16}
            mTop={12}
            style={GlobalStyle.shadow}
            w={40}>
            <Icon name="Outline-CaretLeft" size={20} />
          </WView>
        </TouchableWithoutFeedback>
        <ModalBottomDrop
          bgTop={Colors.neutral100}
          maxHeight={MAX_HEIGHT}
          minHeight={MIN_HEIGHT}>
          {renderTitleHeader()}
          {renderLocation()}
          {renderDriver()}
        </ModalBottomDrop>
      </WView>
    </Spin>
  );
}

export default ShareRoute;
const styles = StyleSheet.create({
  carHorizontalImg: {height: 16, width: 32},
  carImg: {height: 16, width: 32},
  cheatText: {height: 0, width: 0},
  destinationMarkerImg: {
    bottom: -8,
    height: 38,
    position: 'absolute',
    right: -8,
    tintColor: Colors.primary,
    width: 22,
  },
  dotLine: {
    marginLeft: 8,
    marginTop: 6,
  },
  driverAvatar: {
    borderColor: Colors.neutral150,
    borderRadius: 24,
    borderWidth: 1,
    height: 43,
    width: 43,
  },
  iconDestination: {
    height: 28,
    marginLeft: 45,
    tintColor: Colors.primary,
    width: 28,
  },
  lineCar: {position: 'absolute', zIndex: -10},
  mRight14: {marginRight: 14},
  markerImg: {
    alignSelf: 'center',
    borderColor: Colors.neutral50,
    borderWidth: 3,
    bottom: 10,
    position: 'absolute',
    zIndex: -1,
  },

  pickUpMarker: {
    bottom: 0,
    height: 20,
    left: 0,
    position: 'absolute',
    width: 20,
    zIndex: 10,
  },
  polyline: {zIndex: 99},
  userMarker: {
    marginLeft: 'auto',
  },
  userMarkerImg: {
    height: 20,
    width: 20,
  },
});
