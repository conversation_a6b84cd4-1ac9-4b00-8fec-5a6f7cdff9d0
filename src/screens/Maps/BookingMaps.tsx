/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
import {useNavigation} from '@react-navigation/native';
import {isEmpty} from 'lodash-es';
import LottieView from 'lottie-react-native';
import React, {useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Image,
  Platform,
  SafeAreaView,
  Share,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
} from 'react-native';
import Config from 'react-native-config';
import MapView, {
  MapMarker,
  Marker,
  PROVIDER_GOOGLE,
  Polyline,
} from 'react-native-maps';
import Icon from 'react-native-vector-icons/icomoon';
import Ionicons from 'react-native-vector-icons/Ionicons';
import BookingProgress from '../Booking/BookingProgress';
import FindDriver from '../Booking/FindDriver';
import mapStyle from './mapStyle.json';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  calculateDirection,
  calculateDistance,
  calculateTravelTime,
  formatTime,
  generateEncryptedId,
  getPoints,
} from '@utils/Tools';
import {GlobalStyle} from '@themes/GlobalStyle';
import {
  BOOKING_TYPE,
  IS_ANDROID,
  SCREEN_WIDTH,
  SEARCH_TYPE,
} from '@themes/Constants';
import {Colors, Icons, Images} from '@themes';
import {BookingEntity, CoordinatesDto, NearbyVehiclesEntity} from '@requests';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  useBookingsServiceBookingControllerGetCurrentLocation,
  useBookingsServiceBookingControllerGetNearbyVehicles,
} from '@queries';
import {PopTo} from '@navigation/utils';
import {WImage, WText, WView} from '@components';
import BookingConfirmation from '@screens/Booking/BookingConfirmation';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

function BookingMaps() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const [bookingWithCodeParams] = useGlobalState('bookingWithCodeParams');
  const [_searchType, setSearchType] = useGlobalState('searchType');

  const {
    currentUserBooking,
    currentStatus,
    setCurrentStatus,
    setBookingId,
    setDataBooking,
  } = useBookingGlobalStatus();

  const mapRef = useRef<MapView>(null);
  const callOutMarkerRef = useRef<MapMarker>(null);

  const [listCarLocation, setListCarLocation] = useState([]);
  const [currentDriverLocation, setCurrentDriverLocation] = useState();
  const [carDegree, setCarDegree] = useState(0);

  const {data: driverLocation, isFetching: isFetchingCurrentLocation} =
    useBookingsServiceBookingControllerGetCurrentLocation(
      {
        id: currentUserBooking?.id,
      },
      undefined,
      {
        enabled: !!currentUserBooking?.id,
        refetchInterval:
          currentStatus === BookingEntity.status.COMPLETED ? false : 5000,
        refetchIntervalInBackground: true,
        refetchOnWindowFocus: false,
      },
    );

  useEffect(() => {
    if (!isFetchingCurrentLocation) {
      if (driverLocation?.data?.currentLocation?.latitude) {
        if (!currentDriverLocation) {
          setCurrentDriverLocation(driverLocation?.data?.currentLocation);
        }
        const newListCar = [
          ...listCarLocation,
          driverLocation?.data?.currentLocation,
        ];
        if (newListCar?.length < 2) {
          setCarDegree(calculateDirection(newListCar?.[0], newListCar?.[1]));
          setListCarLocation(newListCar as any);
        } else {
          setCarDegree(
            calculateDirection(
              listCarLocation?.[1],
              driverLocation?.data?.currentLocation,
            ),
          );
          setListCarLocation([
            listCarLocation?.[1],
            driverLocation?.data?.currentLocation,
          ] as any);
        }
      }
    }
  }, [isFetchingCurrentLocation]);

  const onRegionChangeComplete = () => {};

  const onRegionChange = () => {};

  const onNavigateMaps = () => {
    if (mapRef.current) {
      if (currentStatus === BookingEntity.status.AWAITING_CONFIRMED_VEHICLE) {
        mapRef.current?.animateCamera({
          center: bookingWithCodeParams?.originLocation?.geo,
          zoom: 16,
        });
      } else {
        mapRef.current.fitToSuppliedMarkers(['marker1', 'marker2', 'marker3'], {
          animated: true,
          edgePadding: {bottom: 80, left: 80, right: 80, top: 80},
        });
      }
    }
  };

  useEffect(() => {
    setTimeout(() => {
      onNavigateMaps();
    }, 1500);
  }, [
    currentUserBooking?.distance,
    currentStatus,
    driverLocation?.data?.currentLocation,
  ]);

  const isClientOnBoard =
    currentStatus === BookingEntity.status.CLIENT_ON_BOARD;

  const isShowBtnShare =
    currentStatus !== BookingEntity.status.CONFIRMED_ADDRESS;

  const isShowBtnHelp = ![
    BookingEntity.status.AWAITING_CONFIRMED_VEHICLE,
    BookingEntity.status.CONFIRMED_ADDRESS,
    BookingEntity.status.DEMAND_CREATION,
  ].includes(currentStatus);

  const isFindDriver =
    currentStatus === BookingEntity.status.AWAITING_CONFIRMED_VEHICLE ||
    currentStatus === BookingEntity.status.DEMAND_CREATION;

  const isBookingConfirmation = [
    BookingEntity.status.CONFIRMED_ADDRESS,
    BookingEntity.status.DRAFT,
  ].includes(currentStatus);

  const isBookingProgress = ![
    BookingEntity.status.CONFIRMED_ADDRESS,
    BookingEntity.status.AWAITING_CONFIRMED_VEHICLE,
    BookingEntity.status.DEMAND_CREATION,
    BookingEntity.status.DRAFT,
  ].includes(currentStatus);

  const waitTime = parseFloat(
    (
      calculateTravelTime(
        isClientOnBoard
          ? (bookingWithCodeParams?.destinationLocation?.geo as CoordinatesDto)
          : (bookingWithCodeParams?.originLocation?.geo as CoordinatesDto),
        driverLocation?.data?.currentLocation,
      ) || 0
    ).toFixed(0),
  );

  const waitTimeTitle = `${formatTime(waitTime)} ${t('activities.away')}`;

  const {data: nearByCars} =
    useBookingsServiceBookingControllerGetNearbyVehicles(
      {
        latitude: bookingWithCodeParams?.originLocation?.geo
          ?.latitude as number,
        longitude: bookingWithCodeParams?.originLocation?.geo
          ?.longitude as number,
      },
      undefined,
      {
        enabled: !isEmpty(bookingWithCodeParams?.originLocation?.geo),
      },
    );

  const distance = calculateDistance(
    bookingWithCodeParams?.originLocation?.geo as CoordinatesDto,
    bookingWithCodeParams?.destinationLocation?.geo as CoordinatesDto,
  );

  const getLineDashPattern = (distance = 0) => {
    const defaultLineDashPattern = [10, 10];

    if (IS_ANDROID) {
      return [18, 18];
    }
    if (distance > 1) {
      return [distance * 18, distance * 18];
    }
    return defaultLineDashPattern;
  };

  const [loadingFake, setLoadingFake] = useState(true);

  useEffect(() => {
    if (currentStatus === BookingEntity.status.VEHICLE_CONFIRMED) {
      setTimeout(() => {
        setLoadingFake(false);
      }, 500);
    }
  }, [currentStatus]);

  const handleBack = () => {
    if (currentStatus === BookingEntity.status.CONFIRMED_ADDRESS) {
      PopTo(2);
    } else {
      navigation.navigate('Main');
    }
  };

  const onTouchBadge = (type: string) => {
    setSearchType(type);
    navigation.navigate('SelectDestination', {
      isTopBarEnable: false,
    });
  };

  const handleHelp = () => {
    const isRemovePayment =
      currentUserBooking?.type === BOOKING_TYPE.BUSINESS ||
      currentUserBooking?.paymentMethodType ===
        BookingEntity.paymentMethodType.CASH;

    navigation.navigate('ModalNeedHelp', {
      bookingId: currentUserBooking?.id,
      isRemovePayment,
    });
  };

  const handleShare = async () => {
    const encryptedId = encodeURIComponent(
      currentUserBooking?.encryptedId ||
        generateEncryptedId(currentUserBooking?.bookingCode?.toString() || ''),
    );

    const message = `${t('shareRoute.linkShare')} ${
      Config?.SHARE_ROUTE ?? ''
    }${encryptedId}`;

    await Share.share({
      message,
    });
  };

  function RenderBadges({address}: {address: string}) {
    return (
      <WView
        alignCenter
        borderRadius={8}
        center
        color={Colors.white}
        mRight={5}
        maxWidth={144}
        pHoz={10}
        pVer={6}
        row
        style={GlobalStyle.shadow}>
        <WText color={Colors.neutral600} numberOfLine={1} type="regular14">
          {address || ''}
        </WText>
        <Icon color={Colors.neutral500} name={'Outline-CaretRight'} size={16} />
      </WView>
    );
  }

  return (
    <WView color={Colors.transparent} fill>
      {currentStatus === BookingEntity.status.VEHICLE_CONFIRMED &&
      loadingFake ? (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.neutral500} size={'large'} />
        </WView>
      ) : (
        <MapView
          customMapStyle={mapStyle}
          initialRegion={{
            latitude: 45.267870606955704,
            latitudeDelta: 3.578196971751211,
            longitude: -75.21365392953157,
            longitudeDelta: 2.35107421875,
          }}
          onMapReady={() => null}
          onRegionChange={onRegionChange}
          onRegionChangeComplete={onRegionChangeComplete}
          provider={PROVIDER_GOOGLE}
          ref={mapRef}
          showsBuildings={false}
          style={styles.map}>
          {bookingWithCodeParams?.originLocation?.geo &&
            (currentStatus === BookingEntity.status.CONFIRMED_ADDRESS ||
              currentStatus === BookingEntity.status.CLIENT_ON_BOARD ||
              currentStatus === BookingEntity.status.COMPLETED ||
              currentStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED) && (
              <Polyline
                coordinates={getPoints([
                  {...bookingWithCodeParams?.originLocation?.geo},
                  {...bookingWithCodeParams?.destinationLocation?.geo},
                ])}
                lineCap="round"
                lineDashPattern={getLineDashPattern(distance)}
                strokeColor={Colors.success}
                strokeWidth={4}
                style={styles.polyline}
                zIndex={99}
              />
            )}

          {bookingWithCodeParams?.originLocation?.geo && (
            <Marker
              anchor={{x: 0.45, y: 0.7}}
              coordinate={{
                latitude: bookingWithCodeParams?.originLocation?.geo?.latitude,
                longitude:
                  bookingWithCodeParams?.originLocation?.geo?.longitude,
              }}
              identifier={'marker1'}
              ref={callOutMarkerRef}
              tracksViewChanges={Platform.OS === 'ios'}
              zIndex={9}>
              <WView>
                <Image
                  resizeMode="contain"
                  source={Images.marker}
                  style={styles.carMarkerImg}
                />
                <WView
                  borderRadius={10}
                  color={Colors.neutral500}
                  h={15}
                  style={styles.markerImg}
                  w={15}
                />
              </WView>
              {/* this fix image not render in android */}
              {Platform.OS === 'android' && (
                <Text style={styles.cheatText}>{Math.random()}</Text>
              )}
            </Marker>
          )}
          {bookingWithCodeParams?.originLocation?.geo &&
            !currentUserBooking?.id && (
              <Marker
                anchor={{x: 0.5, y: 2.0}}
                coordinate={{
                  latitude:
                    bookingWithCodeParams?.originLocation?.geo?.latitude,
                  longitude:
                    bookingWithCodeParams?.originLocation?.geo?.longitude,
                }}
                identifier={'marker1'}
                onPress={() => onTouchBadge(SEARCH_TYPE.PICKUP)}
                ref={callOutMarkerRef}
                tracksViewChanges={Platform.OS === 'ios'}
                zIndex={9}>
                {RenderBadges({
                  address: bookingWithCodeParams?.originLocation?.address?.name,
                })}
              </Marker>
            )}

          {(currentStatus === BookingEntity.status.AWAITING_CONFIRMED_VEHICLE ||
            currentStatus === BookingEntity.status.DEMAND_CREATION) && (
            <Marker
              anchor={{x: 0.5, y: 0.49}}
              coordinate={{
                latitude:
                  (bookingWithCodeParams?.originLocation?.geo
                    ?.latitude as number) + 0.00001,
                longitude:
                  (bookingWithCodeParams?.originLocation?.geo
                    ?.longitude as number) + 0.00001,
              }}
              identifier={'marker1'}
              ref={callOutMarkerRef}
              tracksViewChanges={true}>
              <LottieView
                autoPlay
                loop
                source={require('../../assets/GIF/radar.json')}
                style={{height: SCREEN_WIDTH - 100, width: SCREEN_WIDTH - 100}}
              />
              {/* this fix image not render in android */}
              {Platform.OS === 'android' && (
                <Text style={styles.cheatText}>{Math.random()}</Text>
              )}
            </Marker>
          )}

          {bookingWithCodeParams?.destinationLocation?.geo &&
            currentStatus !== BookingEntity.status.VEHICLE_CONFIRMED &&
            currentStatus !== BookingEntity.status.AWAITING_CONFIRMED_VEHICLE &&
            currentStatus !== BookingEntity.status.ARRIVAL_AT_CLIENT && (
              <WView>
                <Marker
                  anchor={{x: 0.8, y: 0.9}}
                  coordinate={{
                    latitude:
                      bookingWithCodeParams?.destinationLocation?.geo?.latitude,
                    longitude:
                      bookingWithCodeParams?.destinationLocation?.geo
                        ?.longitude,
                  }}
                  identifier={'marker2'}
                  tracksViewChanges={Platform.OS === 'ios'}>
                  <Image
                    resizeMode="contain"
                    source={Images.destinationMarker}
                    style={styles.iconDestination}
                  />

                  {Platform.OS === 'android' && (
                    <Text style={styles.cheatText}>{Math.random()}</Text>
                  )}
                </Marker>
              </WView>
            )}

          {bookingWithCodeParams?.destinationLocation?.geo &&
            !currentUserBooking?.id && (
              <Marker
                anchor={{x: 0.5, y: 2.0}}
                coordinate={{
                  latitude:
                    bookingWithCodeParams?.destinationLocation?.geo?.latitude,
                  longitude:
                    bookingWithCodeParams?.destinationLocation?.geo?.longitude,
                }}
                identifier={'marker1'}
                onPress={() => onTouchBadge(SEARCH_TYPE.DESTINATION)}
                ref={callOutMarkerRef}
                tracksViewChanges={Platform.OS === 'ios'}
                zIndex={9}>
                {RenderBadges({
                  address:
                    bookingWithCodeParams?.destinationLocation?.address?.name,
                })}
              </Marker>
            )}

          {!isEmpty(driverLocation?.data?.currentLocation) &&
            (currentStatus === BookingEntity.status.VEHICLE_CONFIRMED ||
              currentStatus === BookingEntity.status.ARRIVAL_AT_CLIENT) && (
              <Marker
                anchor={{x: 0.45, y: 0.7}}
                coordinate={driverLocation?.data?.currentLocation}
                identifier="marker2"
                tracksViewChanges={Platform.OS === 'ios'}>
                <Image
                  resizeMode="center"
                  source={Images.carMarker}
                  style={[
                    styles.carMarkerImg,
                    {transform: [{rotate: `${carDegree || 0}deg`}]},
                  ]}
                />
                {/* this fix image not render in android */}
                {Platform.OS === 'android' && (
                  <Text style={styles.cheatText}>{Math.random()}</Text>
                )}
              </Marker>
            )}

          {nearByCars?.data?.length !== 0 &&
            currentStatus === BookingEntity.status.CONFIRMED_ADDRESS && (
              <>
                {nearByCars?.data?.map((ele: NearbyVehiclesEntity) => (
                  <Marker
                    anchor={{x: 0.45, y: 0.7}}
                    coordinate={{
                      latitude: ele?.latitude,
                      longitude: ele?.longitude,
                    }}
                    key={`${ele?.id}`}
                    tracksViewChanges={Platform.OS === 'ios'}
                    zIndex={1}>
                    <Image
                      resizeMode="center"
                      source={Images.carMarker}
                      style={styles.carMarkerImg}
                    />
                    {/* this fix image not render in android */}
                    {Platform.OS === 'android' && (
                      <Text style={styles.cheatText}>{Math.random()}</Text>
                    )}
                  </Marker>
                ))}
              </>
            )}
        </MapView>
      )}

      <SafeAreaView style={styles.backBtn}>
        <TouchableWithoutFeedback onPress={handleBack}>
          <WView
            alignCenter
            borderRadius={20}
            color={Colors.white}
            h={40}
            justifyCenter
            mTop={12}
            style={GlobalStyle.shadow}
            w={40}>
            <Icon name="Outline-CaretLeft" size={20} />
          </WView>
        </TouchableWithoutFeedback>
      </SafeAreaView>

      <SafeAreaView style={styles.btnRight}>
        {isShowBtnShare && (
          <TouchableWithoutFeedback onPress={handleShare}>
            <WView
              alignCenter
              borderRadius={20}
              color={Colors.white}
              h={40}
              justifyCenter
              mTop={12}
              style={GlobalStyle.shadow}
              w={40}>
              <Ionicons name="share-social-outline" size={20} />
            </WView>
          </TouchableWithoutFeedback>
        )}
        {isShowBtnHelp && (
          <TouchableWithoutFeedback onPress={handleHelp}>
            <WView
              alignCenter
              borderRadius={20}
              color={Colors.white}
              h={40}
              justifyCenter
              mTop={12}
              style={GlobalStyle.shadow}
              w={40}>
              <WImage size={24} source={Icons.icQuestion} />
            </WView>
          </TouchableWithoutFeedback>
        )}
      </SafeAreaView>

      <WView
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        color={Colors.white}
        mTop={-10}
        style={GlobalStyle.shadow}
        w={SCREEN_WIDTH}>
        {isFindDriver && <FindDriver booking={currentUserBooking} />}
        {isBookingConfirmation && (
          <BookingConfirmation
            onBookCar={(res: BookingEntity) => {
              setBookingId(res?.id);
              setDataBooking(res);
              setCurrentStatus(BookingEntity.status.AWAITING_CONFIRMED_VEHICLE);
            }}
          />
        )}
        {isBookingProgress && (
          <BookingProgress
            booking={currentUserBooking}
            bookingStatus={currentStatus}
            waitTimeTitle={waitTimeTitle}
          />
        )}
      </WView>
    </WView>
  );
}

export default BookingMaps;

const styles = StyleSheet.create({
  backBtn: {
    left: 16,
    position: 'absolute',
    top: 0,
  },

  btnRight: {
    flexDirection: 'row',
    gap: 8,
    position: 'absolute',
    right: 16,
    top: 0,
  },
  carMarkerImg: {height: 55, width: 55},
  cheatText: {height: 0, width: 0},
  iconDestination: {
    height: 28,
    marginLeft: 45,
    tintColor: Colors.primary,
    width: 28,
  },
  map: {
    flex: 1,
  },
  markerImg: {
    alignSelf: 'center',
    borderColor: Colors.neutral50,
    borderWidth: 3,
    bottom: 10,
    position: 'absolute',
    zIndex: -1,
  },
  polyline: {zIndex: 99},
});
