import React, {useState, useRef} from 'react';
import {StyleSheet, ActivityIndicator} from 'react-native';
import {WebView} from 'react-native-webview';
import {useRoute} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {NavHeader, WView} from '@components';
import {Colors} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';

interface WebViewScreenProps {
  url: string;
  title?: string;
}

function WebViewScreen() {
  const {top} = useSafeAreaInsets();
  const route = useRoute();
  const {url, title} = route.params as WebViewScreenProps;
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const webViewRef = useRef(null);

  const handleLoadStart = () => {
    setLoading(true);
    setProgress(0);
  };

  const handleLoadEnd = () => {
    setLoading(false);
    setProgress(1);
  };

  const handleLoadProgress = ({
    nativeEvent,
  }: {
    nativeEvent: {progress: number};
  }) => {
    setProgress(nativeEvent?.progress);
  };

  return (
    <WView pBottom={12} pTop={top} style={GlobalStyle.flex1}>
      <NavHeader title={title || ''} />
      <WView color={Colors.neutral200} h={3} mTop={12} w={'100%'}>
        <WView
          color={progress * 100 === 100 ? Colors.neutral200 : Colors.blue500}
          h={3}
          w={`${progress * 100}%`}
        />
      </WView>

      <WView fill>
        <WebView
          onLoadEnd={handleLoadEnd}
          onLoadProgress={handleLoadProgress}
          onLoadStart={handleLoadStart}
          ref={webViewRef}
          source={{uri: url}}
          style={styles.webview}
        />
        {loading && (
          <WView
            aBottom={0}
            aLeft={0}
            aRight={0}
            aTop={0}
            absolute
            alignCenter
            justifyCenter>
            <ActivityIndicator color={Colors.primary} size="large" />
          </WView>
        )}
      </WView>
    </WView>
  );
}

export default WebViewScreen;

const styles = StyleSheet.create({
  webview: {
    flex: 1,
  },
});
