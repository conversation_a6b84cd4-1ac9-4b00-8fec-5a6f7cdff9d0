/* eslint-disable max-lines */
/* eslint-disable react-hooks/exhaustive-deps */
import {RouteProp, useNavigation} from '@react-navigation/native';
import {useQueries} from '@tanstack/react-query';
import {isEmpty} from 'lodash-es';
import React, {useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  ListRenderItem,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/icomoon';
import dayjs from 'dayjs';
import CouponItem from './CouponItem';
import {CustomToastProps} from '@components/Modal/CustomToast';
import {NavHeader, WText, WView} from '@components';
import {CouponRouteProps, SearchCouponParamsProps} from '@global';
import {PopTo} from '@navigation/utils';
import {
  useCouponsServiceCouponControllerGetCouponStoreList,
  useUsersServiceUserCouponControllerGetCouponLists,
  useUsersServiceUserCouponControllerGetMyCoupons,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {CouponEntity, MyCouponEntity, UsersService} from '@requests';
import {Colors, Images} from '@themes';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {formatNumberLocalized} from '@utils/Tools';

interface CouponProps {
  route: RouteProp<CouponRouteProps, 'Coupon'>;
}

interface CouponEntityCustom extends CouponEntity {
  isValid?: boolean;
}
interface OnPressProps {
  item: CouponEntityCustom;
  isApply: boolean;
}

const MY_COUPON = 'MY_COUPON';
const COUPON_STORE = 'COUPON_STORE';

function Coupon({route}: CouponProps) {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const TAB_DATA = [
    {icon: 'Outline-Ticket', id: MY_COUPON, title: t('coupon.myCoupon')},
    {
      icon: 'Outline-ShoppingCartSimple',
      id: COUPON_STORE,
      title: t('coupon.couponStore'),
    },
  ];
  const {coupon, isFromBooking, onApply, bookingParams, tabIndex, isDisabled} =
    route?.params || {};
  const [manualCoupons] = useGlobalState('manualCoupons');
  const [userData] = useGlobalState('userData');
  const [navigateTab, setNavigateTab] = useGlobalState('navigateTab');

  const [selectedTab, setSelectedTab] = useState(TAB_DATA?.[tabIndex || 0]);
  const [selectedCoupon, setSelectedCoupon] = useState(coupon);
  const [headerHeight, setHeaderHeight] = useState(0);
  const [toDate, setToDate] = useState(dayjs());

  const isCouponStore = selectedTab?.id === COUPON_STORE;

  const validateManualCoupons = useQueries({
    combine: validateCoupons =>
      validateCoupons
        ?.filter(item => item?.data)
        ?.map(item => item?.data?.data),
    queries: (manualCoupons || [])?.map(coupon => ({
      enabled: !!coupon?.code,
      queryFn: () =>
        UsersService.userCouponControllerSearchManualCoupon(
          bookingParams?.bookingType,
          bookingParams?.vehicleType,
          bookingParams?.amount,
          bookingParams?.date,
          bookingParams?.originLatitude,
          bookingParams?.originLongitude,
          bookingParams?.destinationLatitude,
          bookingParams?.destinationLongitude,
          coupon?.code,
          bookingParams?.paymentMethod,
        ),
      queryKey: ['coupon', coupon?.code],
    })),
  });

  const flatListRef = useRef<FlatList>(null);

  const insets = useSafeAreaInsets();

  const keyExtractor = React.useCallback(
    (item: MyCouponEntity) => item?.id,
    [],
  );

  const {
    data: coupons,
    refetch,
    isFetching,
    isLoading,
  } = useUsersServiceUserCouponControllerGetCouponLists();
  const {...fullData} = bookingParams || {};
  const {
    data: userCoupon,
    isFetching: isFetchingUserCoupon,
    isLoading: isLoadingUserCoupon,
  } = useUsersServiceUserCouponControllerGetMyCoupons(
    {
      ...fullData,
      date: toDate.toISOString(),
    } as SearchCouponParamsProps,
    undefined,
    {
      enabled: !isEmpty(fullData),
      staleTime: 0,
    },
  );

  const onRefreshListCoupon = () => {
    if (isFromBooking) {
      setTimeout(() => {
        setToDate(dayjs());
      }, 500);
    } else {
      refetch();
    }
  };

  const resetTabIfNeeded = () => {
    if (navigateTab) {
      setSelectedTab(TAB_DATA?.[Number(navigateTab) || 0]);
      setNavigateTab('');
    }
  };

  useEffect(() => {
    resetTabIfNeeded();
  }, [navigateTab]);

  const onPressItem = ({item, isApply}: OnPressProps) => {
    navigation.navigate('CouponDetail', {
      coupon: item,
      isApply: !isCouponStore && isApply,
      isFromBooking,
      isFromCouponStore: isCouponStore,
      isTopBarEnable: false,
      isUnavailable: !item?.isValid || isDisabled,
      onApply: () => {
        if (isFromBooking) {
          if (isApply) {
            onApply?.(undefined);
            setSelectedCoupon(undefined);
          } else {
            onApply?.(item);
            setSelectedCoupon(item);
          }
        }
        if (isCouponStore) {
          onRefreshListCoupon();
          setSelectedTab(TAB_DATA?.[0]);
        }
        navigation.goBack();
      },
    });
  };

  const onApplyCoupon = ({isApply, item}: OnPressProps) => {
    if (isCouponStore) {
      return navigation.navigate('RedeemCouponModal', {
        applyBtnLabel: isFromBooking
          ? t('button.redeemAndUse')
          : t('button.redeem'),
        coupon: item,
        isTopBarEnable: false,
        onApply: async () => {
          Toast.show({
            bottomOffset: -10,
            position: 'bottom',
            swipeable: false,
            topOffset: 10,
            type: 'loading',
            visibilityTime: 9000,
          });
          onRefreshListCoupon();
          const newCoupon = await userCoupon?.data?.find(
            (ele: {id: string}) => ele?.id === item?.id,
          );
          onApply?.(newCoupon ? newCoupon : item);
          setSelectedTab(TAB_DATA?.[0]);
          Toast.hide();
          Toast.show({
            bottomOffset: 24,
            position: 'top',
            props: {
              showCloseIcon: false,
              title: t('coupon.redeemCouponSuccess'),
            } as CustomToastProps['props'],
            swipeable: false,
            topOffset: 10,
            type: 'success',
            visibilityTime: 2000,
          });
        },
        titleModal: isFromBooking
          ? t('coupon.redeemCoupon')
          : t('coupon.redeemThisCoupon'),
      });
    }
    if (isFromBooking) {
      if (isApply) {
        return onApply?.(undefined);
      }
      return onApply?.(item);
    }
    navigation.navigate('CouponDetail', {
      coupon: item,
      isApply,
      isTopBarEnable: false,
      onApply: () => {
        if (isApply) {
          setSelectedCoupon(undefined);
        } else {
          setSelectedCoupon(item);
        }
        PopTo(2);
      },
    });
  };

  const renderItem: ListRenderItem<CouponEntityCustom> = ({item}) => {
    const isApply = selectedCoupon?.id === item?.id;
    let applyLabel = t('button.view');
    if (isFromBooking) {
      applyLabel = '';
    }
    if (isCouponStore) {
      applyLabel = t('button.redeem');
    }

    return (
      <CouponItem
        allowForceViewDetail={isFromBooking || isCouponStore}
        applyLabel={applyLabel}
        coupon={item}
        couponPointCost={item?.redeemPoint}
        disable={
          (!item?.isValid && isFromBooking) ||
          (!item?.isValid && isCouponStore) ||
          isDisabled
        }
        isApply={isApply}
        isSubscriptionCoupon={
          (item as MyCouponEntity)?.userCouponType ===
          MyCouponEntity.userCouponType.COUPON_SUBSCRIPTION
        }
        key={item?.id}
        mode={isCouponStore ? 'redeem' : 'button'}
        onApply={() => onApplyCoupon({isApply, item})}
        onPress={() => onPressItem({isApply, item})}
        quantity={(item as MyCouponEntity)?.quantity}
      />
    );
  };

  const renderEmpty = () => {
    if (
      isLoading ||
      isFetching ||
      isFetchingUserCoupon ||
      isLoadingUserCoupon
    ) {
      return (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </WView>
      );
    }
    return (
      <WView alignCenter fill h={SCREEN_HEIGHT * 0.7} justifyCenter>
        <Image source={Images.emptyPromoCode} />
        <WText color={Colors.neutral700} marginTop={16} type="regular14">
          {t('empty.coupon')}
        </WText>
      </WView>
    );
  };

  const {data: getCouponStore} =
    useCouponsServiceCouponControllerGetCouponStoreList();

  const getCouponData = () => {
    if (isCouponStore) {
      return getCouponStore?.data;
    }
    if (isFromBooking) {
      return userCoupon?.data as MyCouponEntity[];
    }

    return coupons?.data as CouponEntity[];
  };

  /*
   * useEffect(() => {
   *   const loading = isFromBooking ? isMyCouponFetching : isFetching;
   *   if (selectedCoupon && !loading) {
   *     const existsInManualCoupons = manualCoupons?.some(
   *       ele => ele?.id === selectedCoupon?.id,
   *     );
   */

  /*
   *     if (existsInManualCoupons) {
   *       const currentIndex = manualCoupons?.findIndex(
   *         ele => ele?.id === selectedCoupon?.id,
   *       );
   *       if (currentIndex) {
   *         setTimeout(() => {
   *           flatListRef?.current?.scrollToOffset({
   *             animated: true,
   *             offset: couponItemHeight * (currentIndex || 0),
   *           });
   *         }, 500);
   *       }
   *     } else {
   *       const findIndex = (userCoupon?.data as MyCouponEntity[])?.findIndex(
   *         ele => ele?.id === selectedCoupon?.id,
   *       );
   *       if (findIndex >= 0) {
   *         setTimeout(() => {
   *           flatListRef?.current?.scrollToIndex({
   *             animated: true,
   *             index: findIndex,
   *           });
   *           flatListRef?.current?.flashScrollIndicators();
   *         }, 500);
   *       }
   *     }
   *   }
   * }, [isFetching, isMyCouponFetching]);
   */

  const handleHeaderLayout = (event: any) => {
    const {height} = event.nativeEvent.layout;
    if (!headerHeight) {
      setHeaderHeight(height);
    }
  };

  const handleNavigateAddCoupon = () => {
    navigation.navigate('AddCouponModal', {
      isFromBooking,
      onApply: (coupon: CouponEntity) => {
        navigation.goBack();
        onApplyCoupon({
          isApply:
            selectedCoupon?.id === coupon?.id && coupon?.id !== undefined,
          item: coupon,
        });
      },
      onPress: (coupon: CouponEntity) =>
        onPressItem({
          isApply: coupon?.id === selectedCoupon?.id,
          item: coupon,
        }),
      searchParams: fullData as SearchCouponParamsProps,
      selectedCoupon,
    });
  };

  const safeViewHeight = SCREEN_HEIGHT - insets?.bottom - insets?.top;
  const couponItemHeight = 94;

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <WView onLayout={handleHeaderLayout}>
        <NavHeader
          renderRightButton={() => (
            <WView alignCenter row>
              <WText type="medium16">
                {formatNumberLocalized(userData?.user?.currentPoint || 0)}
              </WText>
              <Image source={Images.roundLogo} style={styles.roundLogoImg} />
            </WView>
          )}
          title={t('coupon.title')}
        />
        <WView
          alignCenter
          color={Colors.white}
          mTop={12}
          row
          style={styles.zIndex99}>
          {TAB_DATA?.map(item => {
            const isSelected = item?.id === selectedTab?.id;
            return (
              <TouchableWithoutFeedback
                key={item?.id}
                onPress={() => {
                  setSelectedTab(item);
                }}>
                <WView
                  alignCenter
                  borderBottomColor={isSelected ? Colors.primary : Colors.white}
                  borderBottomWidth={1}
                  fill
                  h={40}
                  justifyCenter
                  row>
                  <Icon
                    color={isSelected ? Colors.primary : Colors.neutral600}
                    name={item?.icon}
                    size={20}
                  />
                  <WText
                    color={isSelected ? Colors.primary : Colors.neutral600}
                    marginLeft={4}
                    type="medium14">
                    {item?.title}
                  </WText>
                </WView>
              </TouchableWithoutFeedback>
            );
          })}
        </WView>
        <WView style={[styles.shadowView, GlobalStyle.shadowSearchInput]} />
        {!isCouponStore && (
          <TouchableWithoutFeedback onPress={handleNavigateAddCoupon}>
            <WView
              borderColor={Colors.neutral300}
              borderRadius={8}
              borderWidth={1}
              h={48}
              justifyCenter
              mHoz={16}
              mTop={16}>
              <WText color={Colors.neutral600} marginLeft={16} type="regular14">
                {t('coupon.searchCoupon')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>
        )}
      </WView>
      <FlatList
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={() => {
          if (coupons?.data?.length === 0) {
            return null;
          }
          return (
            <WView
              color={Colors.transparent}
              h={safeViewHeight - headerHeight - couponItemHeight}
            />
          );
        }}
        ListHeaderComponent={() => {
          const listManualCoupon: CouponEntityCustom[] =
            (validateManualCoupons as CouponEntityCustom[]) || [];
          return (
            <WView>
              {listManualCoupon?.map(item => {
                const isValid =
                  selectedCoupon?.id === item?.id
                    ? selectedCoupon?.isValid
                    : item?.isValid;
                return (
                  <CouponItem
                    allowForceViewDetail={isFromBooking}
                    applyLabel={isFromBooking ? undefined : t('button.view')}
                    coupon={item}
                    disable={!isValid && isFromBooking}
                    isApply={item?.id === selectedCoupon?.id}
                    key={item?.id}
                    onApply={() =>
                      onApplyCoupon({
                        isApply: item?.id === selectedCoupon?.id,
                        item,
                      })
                    }
                    onPress={() =>
                      onPressItem({
                        isApply: item?.id === selectedCoupon?.id,
                        item,
                      })
                    }
                  />
                );
              })}
            </WView>
          );
        }}
        contentContainerStyle={styles.flexGrow}
        data={getCouponData()}
        keyExtractor={keyExtractor}
        onEndReachedThreshold={0.2}
        onScrollToIndexFailed={error => {
          console.log(error);
        }}
        ref={flatListRef}
        refreshControl={
          <RefreshControl
            colors={[Colors.primary]}
            onRefresh={onRefreshListCoupon}
            refreshing={false}
            tintColor={Colors.primary}
          />
        }
        renderItem={renderItem}
      />
    </SafeAreaView>
  );
}

export default Coupon;

const styles = StyleSheet.create({
  flexGrow: {flexGrow: 1},
  roundLogoImg: {height: 24, width: 24},
  shadowView: {
    backgroundColor: Colors.white,
    height: 6,
    marginTop: -5,
    width: SCREEN_WIDTH,
    zIndex: 88,
  },
  zIndex99: {zIndex: 99},
});
