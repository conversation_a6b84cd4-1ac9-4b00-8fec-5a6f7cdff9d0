import React from 'react';
import {
  Image,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {WText, WView} from '@components';
import {CouponEntity} from '@requests';
import {Colors, Images} from '@themes';
import {FORMAT_COUPON} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {formatNumberLocalized, formatTimeLocale} from '@utils/Tools';
import {useBirthdayEvent} from '@utils/hooks/useBirthdayEvent';

interface CouponItemProps {
  coupon: CouponEntity;
  isApply?: boolean;
  onPress?: () => void;
  onApply?: () => void;
  isViewOnly?: boolean;
  style?: StyleProp<ViewStyle>;
  mode?: 'redeem' | 'button';
  disable?: boolean;
  applyLabel?: string;
  removeLabel?: string;
  allowForceViewDetail?: boolean;
  couponPointCost?: number;
  quantity?: number;
  isSubscriptionCoupon?: boolean;
}

function CouponItem({
  coupon,
  isApply,
  onPress,
  onApply,
  isViewOnly,
  mode = 'button',
  style,
  disable,
  applyLabel,
  removeLabel,
  allowForceViewDetail = false,
  couponPointCost,
  quantity,
  isSubscriptionCoupon,
}: CouponItemProps) {
  const {isHappening} = useBirthdayEvent();
  const {t} = useTypeSafeTranslation();
  let buttonColor = Colors.neutral700;

  if (isApply) {
    buttonColor = Colors.primary;
  }

  const expiredTitle = `${t('business.expiredDate')}: ${formatTimeLocale(
    coupon?.validTo ?? '',
    FORMAT_COUPON,
  )} `;

  const _onPress = () => {
    onPress?.();
  };

  const _onApply = () => {
    onApply?.();
  };

  const renderSelectOptionView = () => {
    if (isViewOnly) {
      if (couponPointCost) {
        return (
          <>
            <Image
              resizeMode="contain"
              source={Images.dashLine}
              style={styles.dashImg}
            />
            <WView alignCenter w={80}>
              <WView
                alignCenter
                borderRadius={4}
                color={Colors.primary200}
                mTop={4}
                pHoz={8}
                pVer={4}
                row>
                <WText color={Colors.primary} type="semiBold12">
                  {formatNumberLocalized(couponPointCost || 0)}
                </WText>
                <Image source={Images.roundLogo} style={styles.roundLogoImg} />
              </WView>
            </WView>
          </>
        );
      }
      return null;
    }
    if (disable && isApply) {
      return (
        <>
          <Image
            resizeMode="contain"
            source={Images.dashLine}
            style={styles.dashImg}
          />
          <WView alignCenter color={Colors.white} h={84} justifyCenter w={80}>
            <Icon
              color={Colors.warningBorderHover}
              name="Outline-WarningCircle"
              size={24}
            />
          </WView>
        </>
      );
    }
    if (mode === 'redeem') {
      return (
        <>
          <Image
            resizeMode="contain"
            source={Images.dashLine}
            style={styles.dashImg}
          />
          <TouchableWithoutFeedback disabled={disable} onPress={_onApply}>
            <WView alignCenter w={80}>
              {!!applyLabel && <WText type="medium14">{applyLabel}</WText>}
              <WView
                alignCenter
                borderRadius={4}
                color={Colors.primary200}
                mTop={4}
                pHoz={8}
                pVer={4}
                row>
                <WText color={Colors.primary} type="semiBold12">
                  {formatNumberLocalized(couponPointCost || 0)}
                </WText>
                <Image source={Images.roundLogo} style={styles.roundLogoImg} />
              </WView>
            </WView>
          </TouchableWithoutFeedback>
        </>
      );
    }

    return (
      <>
        <Image
          resizeMode="contain"
          source={Images.dashLine}
          style={styles.dashImg}
        />
        <TouchableWithoutFeedback disabled={disable} onPress={_onApply}>
          <WView alignCenter color={Colors.white} h={84} justifyCenter w={80}>
            <WText center color={buttonColor} type="medium14">
              {isApply
                ? removeLabel || t('button.useLater2Line')
                : applyLabel || t('button.use')}
            </WText>
          </WView>
        </TouchableWithoutFeedback>
      </>
    );
  };

  const quantityLabel = `x${quantity}`;

  return (
    <TouchableOpacity
      activeOpacity={allowForceViewDetail ? 0.35 : 1}
      disabled={!allowForceViewDetail && (isViewOnly || disable)}
      onPress={_onPress}
      style={[styles.container, disable && styles.disableContainer, style]}>
      <WView
        alignCenter
        borderColor={Colors.neutral150}
        borderRadius={4}
        borderWidth={1}
        color={Colors.white}
        row
        style={GlobalStyle.shadowSoft}>
        <WView
          alignCenter
          borderBottomLeftRadius={4}
          borderTopLeftRadius={4}
          color={Colors.white}
          h={84}
          justifyCenter
          overflowHidden
          padding={1}
          w={84}>
          <Image
            resizeMode="contain"
            source={isHappening ? Images.couponBirthday : Images.defaultCoupon}
            style={styles.couponImg}
          />
        </WView>
        <WView fill mLeft={12}>
          <WText marginRight={12} numberOfLine={2} type="medium14">
            {coupon?.title}
          </WText>
          {!!coupon?.validTo && (
            <WText
              capitalize
              color={Colors.neutral600}
              marginTop={6}
              type="regular12">
              {expiredTitle}
            </WText>
          )}
        </WView>
        {renderSelectOptionView()}
      </WView>
      {!!quantity && (
        <WView
          color={Colors.successSoft}
          pHoz={8}
          pVer={4}
          style={styles.quantityContainer}>
          <WText color={Colors.successDefault} type="medium12">
            {quantityLabel}
          </WText>
        </WView>
      )}
      {isSubscriptionCoupon && (
        <WView style={styles.subsContainer}>
          <Image source={Images.goldSubscription} style={styles.subsImg} />
        </WView>
      )}
    </TouchableOpacity>
  );
}

export default CouponItem;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginTop: 8,
  },
  couponImg: {
    borderBottomLeftRadius: 4,
    borderTopLeftRadius: 4,
    height: '100%',
    marginLeft: 2,
  },
  dashImg: {
    height: '80%',
    marginLeft: 'auto',
  },
  disableContainer: {
    opacity: 0.35,
  },
  quantityContainer: {
    borderRadius: 6,
    position: 'absolute',
    right: -3,
    top: -2,
  },
  roundLogoImg: {height: 14, marginLeft: 2, width: 14},
  subsContainer: {left: -3, position: 'absolute', top: -2},
  subsImg: {height: 24, width: 24},
});
