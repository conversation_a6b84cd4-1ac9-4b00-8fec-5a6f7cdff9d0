import React from 'react';
import {
  Image,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
  SafeAreaView,
} from 'react-native';
import {RouteProp, useNavigation} from '@react-navigation/native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import CouponItem from './CouponItem';
import {CouponEntity} from '@requests';
import {GlobalStyle} from '@themes/GlobalStyle';
import {SCREEN_WIDTH} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {CouponDetailRouteProps} from '@global';
import {WButton, WText, WView} from '@components';
import {Colors, Images} from '@themes';
import {formatNumberLocalized} from '@utils/Tools';

interface CouponDetailProps {
  route: RouteProp<CouponDetailRouteProps, 'CouponDetail'>;
}

function CouponDetail({route}: CouponDetailProps) {
  const {
    isApply,
    onApply,
    coupon,
    isFromBooking,
    isUnavailable,
    isFromCouponStore,
  } = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const _onPress = () => {
    if (isFromCouponStore) {
      return navigation.navigate('RedeemCouponModal', {
        applyBtnLabel: isFromBooking
          ? t('button.redeemAndUse')
          : t('button.redeem'),
        coupon,
        isTopBarEnable: false,
        onApply,
        titleModal: isFromBooking
          ? t('coupon.redeemCoupon')
          : t('coupon.redeemThisCoupon'),
      });
    }
    onApply?.();
  };

  const getButtonLabel = () => {
    let label = t('button.useNow');
    if (isApply) {
      label = t('button.useLater');
    }

    if (isUnavailable) {
      label = t('business.unavailable');
    }
    if (isFromCouponStore) {
      label = t('button.redeemCouponPoint', {
        count: formatNumberLocalized(coupon?.redeemPoint),
      });
    }
    return label;
  };

  return (
    <WView color={Colors.white} fill>
      <WView color={Colors.primary300}>
        <WView color={Colors.primary300} row>
          <TouchableWithoutFeedback
            onPress={() => {
              navigation.goBack();
            }}>
            <SafeAreaView>
              <WView
                borderRadius={20}
                center
                color={Colors.white}
                h={36}
                mLeft={16}
                mTop={12}
                w={36}>
                <IonIcon
                  color={Colors.neutral875}
                  name="close-outline"
                  size={24}
                />
              </WView>
            </SafeAreaView>
          </TouchableWithoutFeedback>
          <Image source={Images.largeSimpleLogo} style={styles.logoImg} />
        </WView>
      </WView>
      <SafeAreaView style={GlobalStyle.flex1}>
        <CouponItem
          coupon={coupon as CouponEntity}
          couponPointCost={coupon?.redeemPoint}
          isViewOnly={true}
          style={styles.coupon}
        />
        <ScrollView>
          <WText
            lineHeight={20}
            marginBottom={16}
            marginHorizontal={16}
            marginTop={21}
            type="regular14">
            {coupon?.description}
          </WText>
        </ScrollView>
        {(isFromBooking || isFromCouponStore) && (
          <WView>
            <WView
              color={Colors.white}
              h={4}
              pBottom={12}
              style={GlobalStyle.shadowCard}
            />
            <WView color={Colors.white} pBottom={12}>
              <WButton
                disabled={isUnavailable}
                label={getButtonLabel()}
                labelColor={
                  isApply && !isUnavailable ? Colors.primary : undefined
                }
                onPress={_onPress}
                style={[
                  styles.bottomBtn,
                  isApply && !isUnavailable && styles.applyBtn,
                ]}
              />
            </WView>
          </WView>
        )}
      </SafeAreaView>
    </WView>
  );
}

export default CouponDetail;

const styles = StyleSheet.create({
  applyBtn: {
    backgroundColor: Colors.primary100,
    borderColor: Colors.primary300,
    borderWidth: 1,
  },
  bottomBtn: {marginHorizontal: 16, marginTop: 12},
  coupon: {marginTop: -52},
  logoImg: {
    height: SCREEN_WIDTH * 0.45,
    marginLeft: 'auto',
    tintColor: Colors.primary400,
    width: SCREEN_WIDTH * 0.45,
  },
});
