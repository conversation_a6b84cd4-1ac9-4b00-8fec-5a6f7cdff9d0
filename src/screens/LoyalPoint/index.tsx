/* eslint-disable no-nested-ternary */
import {Image, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {ContentType, LoyalPointRewardEntity} from '@requests';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {NavHeader, WImage, WText, WTouchable, WView} from '@components';
import {Colors, Icons, Images} from '@themes';
import {ACTIVE_OPACITY} from '@themes/Constants';
import {useGlobalState} from '@react-query/clientStateManage';
import {useLoyalPointServiceLoyalPointControllerGetLoyalPointRewards} from '@queries';
import {formatNumberLocalized} from '@utils/Tools';
import {EKeyLoyalPoint} from '@themes/Enums';

function LoyalPoint() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [userData] = useGlobalState('userData');
  const [language] = useGlobalState('language');

  const loyalPoint = userData?.user?.currentPoint || 0;
  const {data} = useLoyalPointServiceLoyalPointControllerGetLoyalPointRewards();

  return (
    <SafeAreaView>
      <NavHeader
        renderRightButton={() => (
          <TouchableOpacity
            activeOpacity={ACTIVE_OPACITY}
            onPress={() => {
              navigation.navigate('HistoryLoyalPoint', {isTopBarEnable: false});
            }}>
            <WImage size={20} source={Icons.icHistory} />
          </TouchableOpacity>
        )}
        title={t('loyalPoints.title')}
      />

      <WView color={Colors.primary} mTop={18} padding={20} row>
        <WView fill>
          <WText color={Colors.neutral50} type="semiBold16">
            {t('loyalPoints.redeemYourPoints')}
          </WText>
          <WView mTop={16} row>
            <WTouchable
              alignCenter
              borderRadius={38}
              color={Colors.white}
              gap={4}
              onPress={() => {
                navigation.navigate('Coupon', {
                  isTopBarEnable: false,
                  tabIndex: 1,
                });
              }}
              pHoz={20}
              pVer={8}
              row>
              <WText color={Colors.neutral875} type="semiBold12">
                {t('button.redeem')}
              </WText>
              <Ionicons name="arrow-forward-outline" size={16} />
            </WTouchable>
          </WView>
        </WView>
        <WView alignItems="flex-end">
          <WText color={Colors.neutral50} type="regular12">
            {t('loyalPoints.yourPoints')}
          </WText>
          <WText color={Colors.neutral50} type="semiBold30">
            {formatNumberLocalized(loyalPoint)}
          </WText>
        </WView>
      </WView>
      <WView borderRadius={8} mHoz={16} mTop={24}>
        <WText lineHeight={22} type="medium16">
          {t('loyalPoints.howGetPoints')}
        </WText>
        {data?.data?.map((item: LoyalPointRewardEntity) => {
          const point = item?.value || 0;
          const pointTitle = `+${point}`;

          const images =
            {
              [EKeyLoyalPoint.ReferNewFriendPoint]: Icons.icRefer,
              [EKeyLoyalPoint.FinishFirstRidePoint]: Icons.icFirstRide,
              [EKeyLoyalPoint.DropRate]: Icons.icRideAndEarn,
            }[item?.key] || Images.defaultReward;

          return (
            <WView
              alignCenter
              borderRadius={8}
              color={Colors.neutral100}
              key={item?.key}
              mTop={8}
              pVer={4}
              row>
              <Image
                resizeMode="contain"
                source={images}
                style={styles.rewardImg}
              />
              <WView fill mLeft={12}>
                <WView alignCenter row>
                  <WText type="medium14">
                    {item?.title?.[language as keyof ContentType]}
                  </WText>
                  <WView
                    alignCenter
                    borderRadius={6}
                    color={Colors.neutral200}
                    mLeft={8}
                    pLeft={6}
                    pRight={4}
                    pVer={2}
                    row>
                    <WText type="semiBold12">{pointTitle}</WText>
                    <Image
                      source={Images.roundLogo}
                      style={styles.smallPointImg}
                    />
                  </WView>
                </WView>
                <WText
                  color={Colors.neutral700}
                  lineHeight={20}
                  mWidth={'100%'}
                  marginTop={2}
                  type="regular14">
                  {item?.description?.[language as keyof ContentType]}
                </WText>
              </WView>
            </WView>
          );
        })}
      </WView>
    </SafeAreaView>
  );
}

export default LoyalPoint;

const styles = StyleSheet.create({
  rewardImg: {height: 52, width: 52},
  smallPointImg: {height: 14, marginLeft: 2, width: 14},
});
