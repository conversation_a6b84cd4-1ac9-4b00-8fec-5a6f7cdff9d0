import {
  ActivityIndicator,
  FlatList,
  Image,
  ListRenderItem,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import React from 'react';
import {NavHeader, WText, WView} from '@components';
import {Colors, Images} from '@themes';
import {useLoyalPointServiceLoyalPointUserControllerGetPointsKey} from '@queries';
import {useLoadMoreQuery} from '@utils/hooks/useLoadMoreQuery';
import {LoyalPointService, UserPointEntity} from '@requests';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {HISTORY_POINT_FORMAT} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {formatNumberLocalized, formatTimeLocale} from '@utils/Tools';

interface TitleProps {
  [key: string]: string;
}

function HistoryLoyalPoint() {
  const {t} = useTypeSafeTranslation();
  const {
    data: historyPoints,
    refetch,
    loadMore,
    loading,
    isFetching,
    isFetchingNextPage,
    isRefetching,
  } = useLoadMoreQuery({
    limit: 20,
    query: (offset: number) =>
      LoyalPointService.loyalPointUserControllerGetPoints(20, offset),
    queryKey: [useLoyalPointServiceLoyalPointUserControllerGetPointsKey],
  });

  const TITLE_EVENT: TitleProps = {
    ['COMPLETED_RIDE']: t('historyPoint.completedRide'),
    ['FIRST_RIDE']: t('historyPoint.completedFirstRide'),
    ['REDEEM_POINT']: t('button.redeemCoupon'),
    ['REFERRAL_BONUS_UNLOCKED']: t('referral.referralBonus'),
    ['SUCCESSFUL_REFERRAL']: t('referral.successfulReferral'),
  };

  const renderEmpty = () => {
    if (loading || isFetching || isFetchingNextPage) {
      return (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </WView>
      );
    }
    return (
      <WView alignCenter fill justifyCenter>
        <Image source={Images.emptyLoyalPoint} />
        <WText color={Colors.neutral700} marginTop={20} type="regular14">
          {t('empty.pointHistory')}
        </WText>
      </WView>
    );
  };

  const renderFooter = () => {
    if (
      (loading || isFetching || isFetchingNextPage) &&
      historyPoints?.length > 0 &&
      !isRefetching
    ) {
      return (
        <WView h={40}>
          <ActivityIndicator color={Colors.primary} size={'small'} />
        </WView>
      );
    }
    return null;
  };

  const renderItem: ListRenderItem<UserPointEntity> = ({item}) => {
    const pointLabel = `${item?.amount < 0 ? '' : '+'}${formatNumberLocalized(
      item?.amount,
    )}`;

    return (
      <WView mBottom={12} mHoz={16} row>
        <WView mRight="auto">
          <WText type="regular14">
            {TITLE_EVENT?.[item?.type] || 'Taxi Loyal'}
          </WText>
          <WText color={Colors.neutral600} marginTop={2} type="regular12">
            {formatTimeLocale(item?.createdAt, HISTORY_POINT_FORMAT)}
          </WText>
        </WView>
        <WText
          color={item?.amount < 0 ? Colors.primary : Colors.neutral875}
          type="medium14">
          {pointLabel}
        </WText>
        <Image source={Images.roundLogo} style={styles.smallPointImg} />
      </WView>
    );
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader title={t('historyPoint.title')} />
      <WView fill mTop={24}>
        <FlatList
          ListEmptyComponent={renderEmpty}
          ListFooterComponent={renderFooter}
          contentContainerStyle={styles.flatListContent}
          data={historyPoints}
          onEndReached={() => {
            loadMore();
          }}
          onEndReachedThreshold={0.4}
          refreshControl={
            <RefreshControl
              colors={[Colors.primary]}
              onRefresh={refetch}
              refreshing={false}
              tintColor={Colors.primary}
            />
          }
          renderItem={renderItem}
        />
      </WView>
    </SafeAreaView>
  );
}

export default HistoryLoyalPoint;

const styles = StyleSheet.create({
  flatListContent: {flexGrow: 1},
  smallPointImg: {height: 20, marginLeft: 2, width: 20},
});
