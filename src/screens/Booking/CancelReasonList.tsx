import {RouteProp, useNavigation} from '@react-navigation/native';
import React from 'react';
import {SafeAreaView, ScrollView, StyleSheet} from 'react-native';
import CancelReasonItem from './components/CancelReasonItem';
import {TranslationKeys} from '@generated/translationKeys';
import {CancelReasonListRouteProps, ReasonListProps} from '@global';
import {
  useBookingsServiceBookingControllerCancelBooking,
  useBookingsServiceBookingControllerGetBookingOfMeKey,
  useBookingsServiceBookingControllerGetCurrentBookingKey,
} from '@queries';
import {CancelBookingDto, CreateBookingWithCodeDto} from '@requests';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useGlobalState} from '@react-query/clientStateManage';
import {REASON_LIST} from '@themes/Constants';
import {queryClient} from '@react-query/queryClient';
import {Colors} from '@themes';
import {NavHeader, WButton, WText, WView} from '@components';
import MixPanelSdk from '@utils/mixPanelSdk';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

interface CancelReasonListProps {
  route: RouteProp<CancelReasonListRouteProps, 'CancelReasonList'>;
}

function CancelReasonList({route}: CancelReasonListProps) {
  const {t} = useTypeSafeTranslation();
  const [_, setCurrentBooking] = useGlobalState('currentBooking');
  const [_currentWaitingTime, setCurrentWaitingTime] =
    useGlobalState('currentWaitingTime');
  const [_maxWaitingTime, setMaxWaitingTime] = useGlobalState('maxWaitingTime');
  const [_bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const {bookingId, booking} = route?.params || {};
  const navigation = useNavigation();
  const {handleClearBooking} = useBookingGlobalStatus();

  const {mutate: cancelBooking} =
    useBookingsServiceBookingControllerCancelBooking({
      onSuccess: () => {
        setCurrentBooking({id: undefined});
      },
    });

  const onCancelBooking = (reason?: CancelBookingDto.reason) => {
    cancelBooking(
      {
        id: bookingId as string,
        requestBody: {
          note: '---',
          reason: reason || CancelBookingDto.reason.OTHER,
        },
      },
      {
        onSuccess: () => {
          const eventValues = {
            email: booking?.user?.email,
            phoneNumber: booking?.user?.phoneNumber,
            reasonOfCancel: reason || CancelBookingDto.reason.OTHER,
            userName: booking?.user?.fullName,
          };
          MixPanelSdk.logEvent({
            eventName: 'booking_cancel',
            eventValues,
          });
          setMaxWaitingTime(0);
          setCurrentWaitingTime(0);
          setBookingWithCodeParams({} as CreateBookingWithCodeDto);
          navigation.navigate('Main');
          handleClearBooking();
          queryClient.refetchQueries({
            queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
            type: 'all',
          });
          queryClient.refetchQueries({
            queryKey: [useBookingsServiceBookingControllerGetCurrentBookingKey],
            type: 'all',
          });
        },
      },
    );
  };

  const handleOnPressReason = (item: ReasonListProps) => {
    if (item?.id === CancelBookingDto.reason.OTHER) {
      navigation.navigate('InputCancelReasonModal', {
        bookingId,
        isTopBarEnable: false,
      });
    } else {
      onCancelBooking(item?.id);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <NavHeader title={t('cancelBooking.title')} />
      <ScrollView style={styles.content}>
        <WView mTop={26} pHoz={16}>
          <WText type="medium14">{t('cancelBooking.whyCancel')}</WText>
          {REASON_LIST.map(item => (
            <CancelReasonItem
              icon={item?.icon}
              key={item?.id}
              label={t(item?.label as TranslationKeys)}
              onPress={() => handleOnPressReason(item)}
            />
          ))}
          <WText color={Colors.neutral600} marginTop={16} type="regular12">
            {t('cancelBooking.optionalChoice')}
          </WText>
        </WView>
      </ScrollView>
      <WView alignCenter justifyBetween mBottom={12} mTop={'auto'} pHoz={6} row>
        <WButton
          label={t('button.skipAndCancel')}
          onPress={onCancelBooking}
          outline
          style={styles.cancelBtn}
        />
        <WButton
          label={t('button.keepMyRide')}
          onPress={() => {
            navigation.goBack();
          }}
          style={styles.cancelBtn}
        />
      </WView>
    </SafeAreaView>
  );
}

export default CancelReasonList;

const styles = StyleSheet.create({
  cancelBtn: {flex: 1, marginHorizontal: 6},
  container: {flex: 1},
  content: {
    flexGrow: 1,
  },
});
