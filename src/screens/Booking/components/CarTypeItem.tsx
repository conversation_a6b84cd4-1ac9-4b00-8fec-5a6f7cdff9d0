import React from 'react';
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {WText, WView} from '@components';
import {EstimateAmountEntity} from '@requests';
import {Colors} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface CarTypeItemProps {
  item?: any;
  isLastItem?: boolean;
  onPress?: () => void;
  isSelected?: boolean;
  price?: string;
  discountPrice?: string;
}

const PACKAGE_ITEM_HEIGHT = 96;

function CarTypeItem({
  item,
  isLastItem,
  onPress,
  isSelected,
  discountPrice,
  price,
}: CarTypeItemProps) {
  const _onPress = () => {
    onPress?.();
  };
  const {t} = useTypeSafeTranslation();

  return (
    <TouchableWithoutFeedback key={`${item?.id}`} onPress={_onPress}>
      <WView
        borderColor={isSelected ? Colors.neutral900 : Colors.neutral150}
        borderRadius={8}
        borderWidth={1}
        color={isSelected ? Colors.neutral100 : Colors.white}
        h={PACKAGE_ITEM_HEIGHT}
        mRight={isLastItem ? 36 : 12}
        padding={12}>
        {
          <WView alignCenter justifyBetween row>
            <WText type="medium14">{t(item?.name)}</WText>
            {item?.id === EstimateAmountEntity.vehicleType.ACCESSIBLE_VAN && (
              <TouchableOpacity style={styles.iconInfo}>
                <Icon name={'Outline-Info'} size={18} />
              </TouchableOpacity>
            )}
          </WView>
        }
        <WText color={Colors.neutral600} marginTop={2} type="regular12">
          {t(item?.seat)}
        </WText>

        <WView style={[styles.carShadowView, GlobalStyle.shadowCar]} />
        {!!discountPrice && (
          <WText
            color={Colors.neutral700}
            marginRight={60}
            marginTop="auto"
            style={styles.discountPrice}
            type="regular12">
            {price}
          </WText>
        )}
        <WText
          color={!discountPrice ? Colors.neutral875 : Colors.success}
          marginRight={60}
          marginTop="auto"
          style={styles.minWidthCar}
          type="semiBold14">
          {discountPrice || price}
        </WText>
        <Image resizeMode="cover" source={item?.image} style={styles.carImg} />
      </WView>
    </TouchableWithoutFeedback>
  );
}

export default CarTypeItem;

const styles = StyleSheet.create({
  carImg: {
    bottom: 0,
    position: 'absolute',
    right: -6,
  },
  carShadowView: {
    backgroundColor: Colors.white,
    bottom: 4,
    height: 16,
    position: 'absolute',
    right: 24,
    width: 20,
  },
  discountPrice: {
    minWidth: 56,
    textDecorationLine: 'line-through',
  },
  iconInfo: {
    marginLeft: 10,
    marginRight: -5,
  },
  minWidthCar: {
    minWidth: 56,
  },
});
