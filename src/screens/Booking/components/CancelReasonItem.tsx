import React from 'react';
import {TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {WText, WView} from '@components';
import {Colors} from '@themes';
import {ACTIVE_OPACITY} from '@themes/Constants';

interface CancelReasonItem {
  icon?: string;
  label?: string;
  onPress?: () => void;
}

function CancelReasonItem({icon, label, onPress}: CancelReasonItem) {
  const _onPress = () => {
    onPress?.();
  };
  return (
    <TouchableOpacity activeOpacity={ACTIVE_OPACITY} onPress={_onPress}>
      <WView alignCenter mTop={12} pVer={8} row>
        <Icon
          color={Colors.neutral600}
          name={icon || 'Outline-Moped'}
          size={24}
        />
        <WText fill marginLeft={8} type="regular14">
          {label}
        </WText>
      </WView>
    </TouchableOpacity>
  );
}

export default CancelReasonItem;
