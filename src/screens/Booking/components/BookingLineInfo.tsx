/* eslint-disable react-hooks/exhaustive-deps */
import React, {useMemo} from 'react';
import {Platform, StyleProp, ViewStyle, Image, StyleSheet} from 'react-native';
import {PaymentIcon} from 'react-native-payment-icons';
import Icon from 'react-native-vector-icons/icomoon';
import {TranslationKeys} from '@generated/translationKeys';
import {useGlobalState} from '@react-query/clientStateManage';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {BookingEntity, CouponEntity} from '@requests';
import {WText, WTooltip, WView} from '@components';
import {Colors, Images} from '@themes';
import {BOOKING_TYPE, CASH, CardViewType, WALLET} from '@themes/Constants';
import {
  formatDistance,
  formatDuration,
  formatNumberPrice,
  formatTime,
  getCarData,
  getCouponValueTitle,
} from '@utils/Tools';

interface BookingLineInfo {
  booking?: BookingEntity;
  style?: StyleProp<ViewStyle>;
  enableTooltip?: boolean;
  isShowSort?: boolean;
}

function BookingLineInfo({
  booking,
  style,
  enableTooltip,
  isShowSort = false,
}: BookingLineInfo) {
  const {t} = useTypeSafeTranslation();
  const isAnyCar = booking?.vehicleType === BookingEntity.vehicleType.ALL;
  const isCancelBooking = booking?.status === BookingEntity.status.CANCELLED;

  // const [anyCarPrice] = useGlobalState('anyCarPrice');
  const [currentPayment] = useGlobalState('currentPayment');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const getBookingTotal = () => {
    if (isAnyCar && !isCancelBooking) {
      return `${formatNumberPrice(
        booking?.minAmount || 0,
      )} - ${formatNumberPrice(booking?.amount || 0)}`;
    }
    return formatNumberPrice(booking?.amount);
  };

  const bookingInfo = useMemo(
    () => [
      {
        content:
          booking?.type == BOOKING_TYPE.PERSONAL ? 'Personal' : 'Business',
        isEnable: true,
        isPayment: true,
        title: t('booking.paymentMethod'),
      },

      {
        content: booking?.duration
          ? `${formatTime(booking?.duration)} • ${formatDuration(
              booking?.duration,
              booking?.createdAt,
            )}`
          : undefined,
        enableTooltip,
        isEnable: !isShowSort,
        title: t('booking.estArrivalTime'),
      },
      {
        content: formatDistance(booking?.distance),
        isEnable: !isShowSort,
        title: t('booking.distance'),
      },
      {
        content: t(
          getCarData(booking?.vehicleType as BookingEntity.vehicleType)
            ?.name as TranslationKeys,
        ),
        isEnable: !isShowSort,
        title: t('booking.typeOfCar'),
      },
      {
        content: getCouponValueTitle(booking?.coupon as CouponEntity) || 'No',
        isEnable: true,
        title: t('booking.coupon'),
      },
      {
        content: formatNumberPrice(booking?.tipAmount || 0),
        isEnable: Number(booking?.tipAmount) > 0,
        title: t('rating.tipAmount'),
      },
      {
        content: getBookingTotal(),
        isEnable: true,
        title: t('booking.totalIncludingTax'),
      },
      /*
       * {
       *   content: 'No',
       *   title: 'Others',
       * },
       */
    ],
    [
      booking?.type,
      booking?.duration,
      booking?.createdAt,
      booking?.distance,
      booking?.vehicleType,
      booking?.coupon,
      t,
      enableTooltip,
      getBookingTotal,
      isShowSort,
    ],
  );
  const dotText = '•';

  const filteredBookingInfo = useMemo(
    () => bookingInfo.filter(item => item.isEnable),
    [bookingInfo],
  );

  const renderPaymentMethod = () => {
    if (booking?.type == BOOKING_TYPE.BUSINESS) {
      return (
        <WView alignCenter row>
          <WText marginLeft={4} numberOfLine={1} type="regular12">
            {booking?.business?.name || 'N/A'}
          </WText>
        </WView>
      );
    }
    if (!currentPayment) {
      return null;
    }
    if (currentPayment?.id === CASH.id) {
      return (
        <WView alignCenter row>
          <Icon name="Outline-Money" size={24} />
          <WText marginLeft={4} type="medium14">
            {t('payment.cash')}
          </WText>
        </WView>
      );
    }

    if (currentPayment?.id === WALLET.id) {
      return (
        <WView alignCenter row>
          <Image
            resizeMode="contain"
            source={Platform.OS === 'ios' ? Images.applePay : Images.googlePay}
            style={
              Platform.OS === 'ios' ? styles.walletImg : styles.walletImgAndroid
            }
          />
          <WText marginLeft={4} type="medium14">
            {Platform.OS === 'ios'
              ? t('payment.applePay')
              : t('payment.googlePay')}
          </WText>
        </WView>
      );
    }

    return (
      <WView alignCenter row>
        <PaymentIcon type={currentPayment?.brand as CardViewType} />
        <WText marginLeft={4} type="medium14">
          {currentPayment?.last4}
        </WText>
      </WView>
    );
  };
  return (
    <WView
      borderColor={Colors.neutral150}
      borderRadius={8}
      borderTopWidth={1}
      color={Colors.white}
      justifyCenter
      pHoz={16}
      pTop={12}
      style={style}>
      {filteredBookingInfo?.map(item => {
        if (!item?.content) {
          return null;
        }
        return (
          <WView justifyBetween key={item.title} mBottom={10} row>
            <WView alignCenter row>
              <WText marginRight={4} type="medium14">
                {item?.title}
              </WText>
              {item?.enableTooltip && (
                <WTooltip
                  direction="right"
                  label={t('tooltip.estimatedDestination')}
                  width={160}
                />
              )}
            </WView>
            <WView alignCenter row>
              <WText type="regular14">{item?.content}</WText>
              {item?.isPayment && !!currentPayment && (
                <WText marginHorizontal={4} type="regular14">
                  {dotText}
                </WText>
              )}
              {item?.isPayment && renderPaymentMethod()}
            </WView>
          </WView>
        );
      })}
    </WView>
  );
}

export default BookingLineInfo;

const styles = StyleSheet.create({
  walletImg: {height: 16, width: 36},
  walletImgAndroid: {height: 28, width: 48},
});
