import {StyleSheet, Image, TouchableOpacity} from 'react-native';
import React, {useRef} from 'react';
import Icon from 'react-native-vector-icons/icomoon';
import {SCREEN_WIDTH} from '../../../themes/Constants';
import {WText, WTooltip, WView} from '@components';
import {Colors, Images} from '@themes';

interface BookingAddressProps {
  originAddress: string;
  destinationAddress: string;
}

function BookingAddress({
  originAddress,
  destinationAddress,
}: BookingAddressProps) {
  const originTooltipRef = useRef(null);
  const destinationTooltipRef = useRef(null);

  return (
    <WView alignCenter justifyBetween mTop={16} row>
      <WView alignCenter>
        <Image
          resizeMode="contain"
          source={Images.userMarker}
          style={styles.userMarkerImg}
        />
        <Image resizeMode="contain" source={Images.dotLine} />
        <Icon color={Colors.primary} name="Fill-MapPin" size={22} />
      </WView>
      <WView fill mLeft={8}>
        {(originAddress?.length || 0) > 50 && (
          <WTooltip
            direction="right"
            label={originAddress}
            ref={originTooltipRef}
            showIcon={false}
            style={styles.absolute}
            width={SCREEN_WIDTH - 80}
          />
        )}
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            originTooltipRef?.current?.onToggle();
          }}
          style={styles.flex1}>
          <WView alignItems="flex-start" fill justifyBetween row>
            <WText color={Colors.neutral700} numberOfLine={1} type="regular14">
              {originAddress}
            </WText>
          </WView>
        </TouchableOpacity>
        <WView alignItems="flex-end" fill justifyBetween row>
          {(destinationAddress?.length || 0) > 50 && (
            <WTooltip
              direction="right"
              label={destinationAddress}
              ref={destinationTooltipRef}
              showIcon={false}
              style={styles.absolute}
              width={SCREEN_WIDTH - 80}
            />
          )}
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              destinationTooltipRef?.current?.onToggle();
            }}
            style={styles.flex1}>
            <WText numberOfLine={1} type="regular14">
              {destinationAddress}
            </WText>
          </TouchableOpacity>
        </WView>
      </WView>
    </WView>
  );
}

export default BookingAddress;

const styles = StyleSheet.create({
  absolute: {position: 'absolute'},
  flex1: {flex: 1},
  userMarkerImg: {height: 20, width: 20},
});
