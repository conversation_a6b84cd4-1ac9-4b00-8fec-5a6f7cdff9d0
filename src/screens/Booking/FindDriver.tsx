import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useRef, useState} from 'react';
import {Image, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/icomoon';
import BookingLineInfo from './components/BookingLineInfo';
import {WText, WTooltip, WView} from '@components';
import {useGlobalState} from '@react-query/clientStateManage';
import {BookingEntity} from '@requests';
import {Colors, Images} from '@themes';
import {SCREEN_WIDTH} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {getCarData} from '@utils/Tools';
import {useAppState} from '@utils/hooks/useAppState';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface FindDriverProps {
  booking?: BookingEntity;
}

interface TooltipRef {
  onToggle: () => void;
}

function FindDriver({booking}: FindDriverProps) {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  // const intervalRef = useRef<ReturnType<typeof setTimeout>>();

  const destinationTooltipRef = useRef<TooltipRef | null>(null);
  const originTooltipRef = useRef<TooltipRef | null>(null);

  const [bookingWithCodeParams] = useGlobalState('bookingWithCodeParams');
  const [configData] = useGlobalState('configData');

  const [heightContainer, setHeightContainer] = useState<number | undefined>(
    400,
  );

  const warningCancelTime = configData?.warningCancelTime || 60;
  const autoCancelTime = configData?.autoCancelTime || 60;

  const onCancelBooking = () => {
    navigation.navigate('CancelReasonList', {
      booking,
      bookingId: booking?.id,
      isTopBarEnable: false,
    });
  };

  const currentWaitingTime = moment().diff(
    moment(booking?.createdAt).toDate(),
    'seconds',
  );

  useAppState({
    appActiveHandler: () => {
      if (
        currentWaitingTime >= warningCancelTime &&
        currentWaitingTime < autoCancelTime
      ) {
        // showNoCarStateModal();
      }
    },
  });

  const car = getCarData(booking?.vehicleType as string);

  const bookingCode = `${t('booking.bookingID')} ${booking?.bookingCode}`;

  useEffect(() => {
    setTimeout(() => {
      setHeightContainer(undefined);
    }, 500);
    return () => {
      setHeightContainer(300);
    };
  }, []);

  return (
    <WView
      borderTopLeftRadius={16}
      borderTopRightRadius={16}
      color={Colors.white}
      h={heightContainer}
      pHoz={16}>
      <WView color={Colors.white} style={styles.containerWrapper}>
        <WView alignCenter mBottom={10} mTop={18} row>
          <WView>
            <WView alignCenter row>
              <WText type="semiBold12">{t('booking.connectDrivers')}</WText>
              <Image source={Images.dotLoading} style={styles.dotGif} />
            </WView>
            <WText color={Colors.neutral700} type="regular12">
              {bookingCode}
            </WText>
          </WView>
          <WView fill>
            <Image
              resizeMode="contain"
              source={car?.image || Images.appLogo}
              style={styles.carImg}
            />
            <WView style={[styles.carShadowView, GlobalStyle.shadowCar]} />
          </WView>
        </WView>

        <WView borderTopColor={Colors.neutral200} borderTopWidth={1} mTop={4}>
          <WView alignCenter justifyBetween mVer={12} row>
            <WView alignCenter>
              <Image
                resizeMode="contain"
                source={Images.userMarker}
                style={styles.destinationImg}
              />
              <Image resizeMode="contain" source={Images.dotLine} />
              <Icon color={Colors.primary} name="Fill-MapPin" size={22} />
            </WView>

            <WView fill mLeft={16} pVer={4}>
              <WView alignItems="flex-start" fill justifyBetween row>
                {(bookingWithCodeParams?.originLocation?.address?.name
                  ?.length || 0) > 50 && (
                  <WTooltip
                    direction="right"
                    label={
                      bookingWithCodeParams?.originLocation?.address
                        ?.name as string
                    }
                    ref={originTooltipRef}
                    showIcon={false}
                    style={styles.absolute}
                    width={SCREEN_WIDTH - 80}
                  />
                )}
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => {
                    originTooltipRef?.current?.onToggle();
                  }}
                  style={styles.flex1}>
                  <WText
                    color={Colors.neutral700}
                    fill
                    numberOfLine={1}
                    type="regular14">
                    {bookingWithCodeParams?.originLocation?.address?.name}
                    {/* {t('booking.yourLocation')} */}
                  </WText>
                </TouchableOpacity>
                <IonIcon
                  color={Colors.neutral300}
                  name="checkmark-circle-outline"
                  size={20}
                />
              </WView>
              <WView alignItems="flex-end" fill justifyBetween row>
                {(bookingWithCodeParams?.destinationLocation?.address?.name
                  ?.length || 0) > 50 && (
                  <WTooltip
                    direction="right"
                    label={
                      bookingWithCodeParams?.destinationLocation?.address
                        ?.name as string
                    }
                    ref={destinationTooltipRef}
                    showIcon={false}
                    style={styles.absolute}
                    width={SCREEN_WIDTH - 80}
                  />
                )}
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => {
                    destinationTooltipRef?.current?.onToggle();
                  }}
                  style={styles.mRight4}>
                  <WText numberOfLine={1} type="regular14">
                    {bookingWithCodeParams?.destinationLocation?.address?.name}
                  </WText>
                </TouchableOpacity>
                <IonIcon
                  color={Colors.neutral300}
                  name="checkmark-circle-outline"
                  size={20}
                />
              </WView>
            </WView>
          </WView>
        </WView>
      </WView>
      <BookingLineInfo booking={booking} enableTooltip style={styles.mHoz0} />
      {booking?.note && (
        <WView
          borderColor={Colors.neutral600}
          borderRadius={6}
          borderWidth={1}
          h={56}
          mBottom={8}
          pHoz={4}
          pVer={8}>
          <WText type="regular14">{booking?.note}</WText>
        </WView>
      )}
      <SafeAreaView style={styles.bottomView}>
        <TouchableOpacity onPress={onCancelBooking} style={styles.cancelBtn}>
          <WText color={Colors.primary} type="semiBold14">
            {t('button.cancelBooking')}
          </WText>
        </TouchableOpacity>
      </SafeAreaView>
    </WView>
  );
}

export default FindDriver;

const styles = StyleSheet.create({
  absolute: {position: 'absolute'},
  bottomView: {
    backgroundColor: Colors.white,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  cancelBtn: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderTopColor: Colors.neutral200,
    borderTopWidth: 1,
    height: 46,
    justifyContent: 'center',
    marginTop: 8,
    minWidth: 80,
  },

  carImg: {
    height: 28,
    marginLeft: 'auto',
    transform: [{scaleX: -1}],
    width: 60,
  },

  /*
   * noCarStateContainer: {
   *   alignItems: 'center',
   *   backgroundColor: Colors.warning100,
   *   borderColor: Colors.warning600,
   *   borderRadius: 16,
   *   borderWidth: 1,
   *   flexDirection: 'row',
   *   height: 64,
   *   left: 16,
   *   paddingHorizontal: 16,
   *   position: 'absolute',
   *   right: 16,
   *   top: -(64 + 8),
   * },
   */
  carShadowView: {
    backgroundColor: Colors.white,
    bottom: 8,
    height: 12,
    position: 'absolute',
    right: 28,
    width: 16,
    zIndex: -9,
  },

  containerWrapper: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },

  destinationImg: {height: 20, width: 20},

  dotGif: {height: 10, marginTop: 6, width: 32},

  flex1: {flex: 1},
  /*
   * line: {
   *   backgroundColor: Colors.warning600,
   *   height: 1.3,
   *   left: -2,
   *   position: 'absolute',
   *   right: -2,
   *   top: 12,
   *   transform: [{rotate: '140deg'}],
   * },
   */
  mHoz0: {marginHorizontal: -16},
  mRight4: {
    flex: 1,
    marginRight: 4,
  },
});
