/* eslint-disable react-native/no-inline-styles */
/* eslint-disable max-lines */
import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Image,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {PaymentIcon} from 'react-native-payment-icons';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {RatingDriverRouteProps} from '@global';
import {
  useBookingsServiceBookingControllerReviewBooking,
  useUsersServiceUserControllerListCards,
  useUsersServiceUserControllerListCardsKey,
} from '@queries';
import {WButton, WText, WView} from '@components';
import ModalWrapper from '@components/Modal/ModalWrapper';
import {Colors, Fonts, Images} from '@themes';
import {
  BOOKING_TYPE,
  CardViewType,
  CASH,
  IS_ANDROID,
  IS_IOS,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
  WALLET,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {BookingEntity, CardEntity, CreateReviewDto} from '@requests';
import usePayment from '@utils/hooks/usePayment';
import {formatNumberPrice} from '@utils/Tools';
import MixPanelSdk from '@utils/mixPanelSdk';
import AppsFlyer from '@utils/appsFlyerSdk';

interface RatingDriverProps {
  route: RouteProp<RatingDriverRouteProps, 'RatingDriver'>;
}

function RatingDriver({route}: RatingDriverProps) {
  const {
    bookingDetail,
    onClose,
    isActivitiesFlow = false,
  } = route?.params || {};

  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [rating, setRating] = useState(0);
  const [note, setNote] = useState<string | undefined>('');
  const [tipAmount, setTipAmount] = useState<number | string>('');
  const [isInputTipVisible, setIsInputTipVisible] = useState(false);
  const [isPaymentOptionsVisible, setIsPaymentOptionsVisible] = useState(false);
  const [isInsufficientFundsError, setIsInsufficientFundsError] =
    useState(false);

  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<CardEntity | null>(null);

  const tipInputRef = useRef<TextInput | null>(null);

  const {mutate: reviewDriver, isPending: isPendingReview} =
    useBookingsServiceBookingControllerReviewBooking();

  const {data: listCard} = useUsersServiceUserControllerListCards([
    useUsersServiceUserControllerListCardsKey,
  ]);

  const {applePay, googlePay, payByCard, isWalletSupported, loading} =
    usePayment({
      onError: error => {
        setIsPaymentOptionsVisible(false);
        if (error?.declineCode === 'insufficient_funds') {
          setIsInsufficientFundsError(true);
        }
      },
      onSuccess: bookingData => {
        if (bookingData) {
          openModalFinishRide();
        }
      },
    });

  useEffect(() => {
    if (!listCard || !bookingDetail) return;
    const paymentMethodId = bookingDetail?.detailPayment?.payment_method;
    const selectedCard = listCard?.data?.find(
      (card: CardEntity) => card?.id === paymentMethodId,
    );
    const cardDefault = listCard?.data?.find(
      (card: CardEntity) => card?.isDefault,
    );

    const defaultPaymentMethod = isWalletSupported ? WALLET : undefined;
    setSelectedPaymentMethod(
      selectedCard || cardDefault || defaultPaymentMethod,
    );
  }, [listCard, bookingDetail, isWalletSupported]);

  const onDismissModal = () => {
    navigation.goBack();
    onClose?.();
  };

  const onChangeNote = (text?: string) => {
    setNote(text);
  };

  const handlePaymentMethod = (
    paymentIntent: string,
    bookingTip: any,
    paymentMethodId?: string,
  ) => {
    if (paymentMethodId === WALLET.id) {
      if (IS_IOS) {
        return applePay(paymentIntent, t('rating.tipYourDriver'), bookingTip);
      }
      return googlePay(paymentIntent, bookingTip);
    }

    if (paymentMethodId) {
      return payByCard(paymentIntent, String(paymentMethodId), bookingTip);
    }
  };

  const onReview = () => {
    const roundedTipAmount = Math.round(Number(tipAmount) * 100);
    const isTipValid =
      Number(tipAmount) > 0 && selectedPaymentMethod?.id !== CASH.id;

    const requestBody: CreateReviewDto = {
      ...(note && {note}),
      ...(rating && {rating}),
      ...(roundedTipAmount > 0 && {tipAmount: roundedTipAmount}),
      ...(Number(rating) === 0 &&
        Number(tipAmount) >= 0 && {
          reviewStatus: BookingEntity.reviewStatus.NOT_REVIEWED,
        }),
    };

    reviewDriver(
      {
        id: bookingDetail?.id,
        requestBody,
      },
      {
        onSuccess: data => {
          if (isTipValid) {
            const bookingTip = {amount: roundedTipAmount};
            handlePaymentMethod(
              data?.data?.paymentIntent,
              bookingTip,
              selectedPaymentMethod?.id,
            );
          } else {
            openModalFinishRide();
          }
        },
      },
    );
  };

  const dataTip = useMemo(
    () => [
      {
        label: t('rating.noTip'),
        value: 0,
        isHighlight: true,
      },
      {
        label: '10%',
        value: ((Number(bookingDetail?.price) / 100) * 0.1).toFixed(2),
        isHighlight: Number(bookingDetail?.price) !== 0,
      },
      {
        label: '15%',
        value: ((Number(bookingDetail?.price) / 100) * 0.15).toFixed(2),
        isHighlight: Number(bookingDetail?.price) !== 0,
      },
      {
        label: '20%',
        value: ((Number(bookingDetail?.price) / 100) * 0.2).toFixed(2),
        isHighlight: Number(bookingDetail?.price) !== 0,
      },
    ],
    [bookingDetail?.price, t],
  );

  const handleLater = () => {
    if (isActivitiesFlow) {
      return navigation.goBack();
    }
    return openModalFinishRide();
  };

  const handleOpenKeyBoard = () => {
    if (tipInputRef?.current) {
      tipInputRef?.current?.focus();
    }
  };

  const handleCustomAmount = () => {
    setIsInputTipVisible(true);
    setTimeout(() => {
      handleOpenKeyBoard();
    }, 300);
  };

  const handleChangeAmountTip = (value: string) => {
    let formattedValue = value?.replace(/[^0-9.,]/g, '');

    formattedValue = formattedValue?.replace(',', '.');
    const parts = formattedValue?.split('.');

    if (Number(parts?.length) > 2) {
      formattedValue = `${parts?.[0]}.${parts
        ?.slice(1)
        ?.join('')
        ?.slice(0, 2)}`;
    } else if (Number(parts?.length) === 2) {
      formattedValue = `${parts?.[0]}.${parts[1]?.slice(0, 2)}`;
    }
    setTipAmount(formattedValue);
  };

  const renderPaymentMethod = () => {
    if (!selectedPaymentMethod) {
      return null;
    }
    if (selectedPaymentMethod?.id === WALLET?.id) {
      return (
        <WView alignCenter row>
          <Image
            resizeMode="contain"
            source={IS_IOS ? Images.applePay : Images.googlePay}
            style={styles.applePayImg}
          />
          <WText marginLeft={4} type="medium14">
            {IS_IOS ? t('payment.applePay') : t('payment.googlePay')}
          </WText>
        </WView>
      );
    }

    return (
      <WView alignCenter row>
        <PaymentIcon
          height={16}
          type={selectedPaymentMethod?.brand as CardViewType}
          width={24}
        />
        <WText marginLeft={4} type="medium14">
          {selectedPaymentMethod?.last4 || ''}
        </WText>
      </WView>
    );
  };

  const driverName = `${bookingDetail?.driver?.firstName || ''} ${
    bookingDetail?.driver?.lastName || ''
  }`;

  const isShowSectionTip =
    bookingDetail?.type !== BOOKING_TYPE.BUSINESS &&
    !bookingDetail?.tipAmount &&
    bookingDetail?.tipAmount !== 0 &&
    (isWalletSupported || listCard?.data?.length > 0);

  const openModalFinishRide = () => {
    const eventValues = {
      email: bookingDetail?.user?.email,
      phoneNumber: bookingDetail?.user?.phoneNumber,
      userName: bookingDetail?.user?.fullName,
      carSelect: bookingDetail?.vehicle?.category,
      rideValue: Number(bookingDetail?.amount) / 100 || 0,
      ...(bookingDetail?.coupon?.value && {
        couponValue: Number(bookingDetail?.coupon?.value) / 100,
      }),
      ...(bookingDetail?.coupon?.title && {
        couponName: bookingDetail?.coupon?.title,
      }),
    };
    MixPanelSdk.logEvent({
      eventName: 'booking_complete',
      eventValues,
    });
    AppsFlyer.logEvent({
      eventName: 'booking_complete',
      eventValues,
    });

    navigation.goBack();
    if (!isActivitiesFlow) {
      setTimeout(() => {
        navigation.navigate('ActionModal', {
          confirmLabel: t('button.confirm'),
          content: t('rating.contentFinish'),
          enableCloseOnMask: false,
          hasCancelBtn: false,
          heightIcon: 97,
          image: Images.imgTipBanner,
          onApply: onDismissModal,
          title: t('rating.titleFinish'),
          widthIcon: 310,
        });
      }, 200);
    } else {
      onClose?.();
    }
  };

  const isDisabled =
    (!rating && !note && !tipAmount && !isInputTipVisible) ||
    (!selectedPaymentMethod?.id && isShowSectionTip);

  return (
    <ModalWrapper enableCloseOnMask={false} style={styles.modelWrapper}>
      <WView style={styles.container}>
        <WView
          borderRadius={12}
          color={Colors.white}
          maxHeight={SCREEN_HEIGHT / 1.2}>
          <KeyboardAwareScrollView contentContainerStyle={styles.contentScroll}>
            <WView alignCenter>
              <WText marginTop={4} type="semiBold22">
                {t('rating.content')}
              </WText>
              <Image source={Images.defaultAvatar} style={styles.driverImg} />
              <WText marginTop={6} type="medium14">
                {driverName}
              </WText>
              <WView alignCenter mBottom={24} mTop={16} row>
                {[1, 2, 3, 4, 5].map((id: number, index: number) => (
                  <TouchableOpacity
                    hitSlop={GlobalStyle.hitSlop}
                    key={`star-${index}`}
                    onPress={() => setRating(id)}>
                    <Icon
                      color={id <= rating ? Colors.primary : Colors.neutral200}
                      name="Fill-Star"
                      size={40}
                    />
                  </TouchableOpacity>
                ))}
              </WView>
            </WView>
            <TextInput
              allowFontScaling={false}
              editable={rating !== 0}
              multiline
              onChangeText={text => onChangeNote(text)}
              placeholder={t('rating.enterNote')}
              style={[
                styles.input,
                !rating && {backgroundColor: Colors.neutral100},
              ]}
              textAlignVertical={'top'}
            />

            {isShowSectionTip && (
              <>
                <WView alignCenter gap={4} mTop={6}>
                  <WText type="semiBold18">{t('rating.addTip')}</WText>
                  <WText type="regular14">
                    {t('rating.noteTip', {
                      value: formatNumberPrice(bookingDetail?.price),
                    })}
                  </WText>
                </WView>
                <WView alignCenter justifyBetween mTop={12} row w={'100%'}>
                  {dataTip?.map((item, index) => {
                    const isSelected =
                      tipAmount == item?.value &&
                      isInputTipVisible &&
                      item?.isHighlight;

                    return (
                      <TouchableOpacity
                        disabled={!item?.isHighlight}
                        key={index}
                        onPress={() => {
                          setTipAmount(item.value);
                          setIsInputTipVisible(true);
                        }}
                        style={[
                          styles.btnItemTip,
                          isSelected && styles.btnItemTipSelected,
                          !item?.isHighlight && styles.btnDisabled,
                        ]}>
                        <WText
                          color={isSelected ? Colors.white : Colors.neutral900}
                          type="regular14">
                          {item.label}
                        </WText>
                      </TouchableOpacity>
                    );
                  })}
                </WView>
                <WView gap={12} mTop={12} w={'100%'}>
                  {isInputTipVisible ? (
                    <TouchableWithoutFeedback onPress={handleOpenKeyBoard}>
                      <WView alignCenter justifyCenter row>
                        <WText style={styles.txtDollar} type="regular24">
                          {`$`}
                        </WText>
                        <TextInput
                          keyboardType="numeric"
                          onChangeText={handleChangeAmountTip}
                          ref={tipInputRef}
                          style={styles.inputAmount}
                          value={String(tipAmount)}
                          allowFontScaling={false}
                        />
                      </WView>
                    </TouchableWithoutFeedback>
                  ) : (
                    <TouchableOpacity onPress={handleCustomAmount}>
                      <WText
                        color={Colors.blue700}
                        textAlign="center"
                        type="regular14">
                        {t('rating.enterCustomAmount')}
                      </WText>
                    </TouchableOpacity>
                  )}
                  <WView color={Colors.neutral150} h={2} />
                  <WView alignItems="center" justifyBetween row>
                    <WText type="medium16">{t('rating.payment')}</WText>
                    <TouchableOpacity
                      onPress={() => setIsPaymentOptionsVisible(prev => !prev)}>
                      <WView alignItems="center" gap={4} row>
                        {renderPaymentMethod()}
                        <Icon
                          name={
                            isPaymentOptionsVisible
                              ? 'Outline-CaretDown'
                              : 'Outline-CaretRight'
                          }
                          size={20}
                        />
                      </WView>
                    </TouchableOpacity>
                  </WView>

                  {isPaymentOptionsVisible && (
                    <WView>
                      {isWalletSupported && (
                        <TouchableWithoutFeedback
                          onPress={() => {
                            setSelectedPaymentMethod(WALLET as CardEntity);
                          }}>
                          <WView
                            alignCenter
                            borderColor={
                              selectedPaymentMethod?.id === WALLET.id
                                ? Colors.neutral900
                                : Colors.neutral300
                            }
                            borderRadius={8}
                            borderWidth={1}
                            mTop={8}
                            padding={12}
                            row>
                            <Image
                              resizeMode="contain"
                              source={
                                IS_ANDROID ? Images.googlePay : Images.applePay
                              }
                              style={{height: 16, width: 24}}
                            />
                            <WText marginLeft={8} type="medium14">
                              {IS_ANDROID
                                ? t('payment.googlePay')
                                : t('payment.applePay')}
                            </WText>
                          </WView>
                        </TouchableWithoutFeedback>
                      )}
                      {listCard?.data?.length > 0 &&
                        listCard?.data?.map(
                          (item: CardEntity, index: number) => {
                            const isSelected =
                              selectedPaymentMethod?.id === item?.id;
                            return (
                              <TouchableOpacity
                                key={index}
                                onPress={() => setSelectedPaymentMethod(item)}>
                                <WView
                                  alignCenter
                                  borderColor={
                                    isSelected
                                      ? Colors.neutral900
                                      : Colors.neutral300
                                  }
                                  borderRadius={8}
                                  borderWidth={1}
                                  mTop={8}
                                  padding={12}
                                  row>
                                  <PaymentIcon
                                    height={16}
                                    type={item?.brand as CardViewType}
                                    width={24}
                                  />
                                  <WText marginLeft={8} type="medium14">
                                    {item?.last4}
                                  </WText>
                                </WView>
                              </TouchableOpacity>
                            );
                          },
                        )}
                    </WView>
                  )}

                  {isInsufficientFundsError && (
                    <WView
                      alignCenter
                      borderColor={Colors.warningBorderHover}
                      borderRadius={8}
                      borderWidth={1}
                      color={Colors.warning100}
                      gap={8}
                      pHoz={16}
                      pVer={8}
                      row>
                      <Icon
                        color={Colors.warningBorderHover}
                        name="Outline-WarningCircle"
                        size={24}
                      />
                      <WText color={Colors.warningBorderHover} type="regular14">
                        {t('rating.insufficientBalance')}
                      </WText>
                    </WView>
                  )}
                </WView>
              </>
            )}

            {!isWalletSupported && listCard?.data?.length === 0 && (
              <WText type="regular14">{t('rating.noCard')}</WText>
            )}
            <WView alignCenter mTop={24} row>
              <WButton
                label={t('button.later')}
                onPress={handleLater}
                outline
                style={styles.laterBtn}
              />
              <WButton
                label={t('button.done')}
                loading={isPendingReview || loading}
                onPress={onReview}
                disabled={isDisabled}
                style={GlobalStyle.flex1}
              />
            </WView>
          </KeyboardAwareScrollView>
        </WView>
      </WView>
    </ModalWrapper>
  );
}

export default RatingDriver;

const styles = StyleSheet.create({
  applePayImg: {height: 16, marginRight: 4, width: 24},
  btnItemTip: {
    alignItems: 'center',
    borderColor: Colors.grey6,
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: 'center',
    paddingHorizontal: 13,
    paddingVertical: 8,
    width: '22%',
  },
  btnItemTipSelected: {
    backgroundColor: Colors.primary,
    borderWidth: 0,
  },
  btnDisabled: {
    backgroundColor: Colors.grey6,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  contentScroll: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 25,
    width: SCREEN_WIDTH - 32,
  },
  driverImg: {
    borderColor: Colors.neutral300,
    borderRadius: 999,
    borderWidth: 0.5,
    height: 64,
    marginTop: 24,
    width: 64,
  },
  input: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral400,
    borderRadius: 8,
    borderWidth: 1,
    height: SCREEN_HEIGHT / 7,
    marginTop: 'auto',
    marginVertical: 12,
    padding: 15,
    paddingTop: 15,
    width: SCREEN_WIDTH - 64,
  },
  inputAmount: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S34,
    textAlign: 'center',
  },
  laterBtn: {flex: 1, marginRight: 12},
  modelWrapper: {alignItems: 'center', justifyContent: 'center'},
  txtDollar: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S34,
  },
});
