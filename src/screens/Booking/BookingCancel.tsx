import React from 'react';
import {StyleSheet, TouchableWithoutFeedback, View} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/icomoon';
import {hasNotch} from 'react-native-device-info';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {WButton, WText, WView} from '@components';
import ModalWrapper from '@components/Modal/ModalWrapper';
import {Colors} from '@themes';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {BookingCancelRouteProps} from '@global';
import moment from 'moment';

interface BookingCancelProps {
  route: RouteProp<BookingCancelRouteProps, 'BookingCancel'>;
}

function BookingCancel({route}: BookingCancelProps) {
  const {bookingId, date} = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const formatText = () => {
    const isOver5Minutes = moment().diff(moment(date), 'minutes') > 5;
    const count = 5;
    if (isOver5Minutes) {
      return t('booking.confirmCancelChargeLate', {count});
    }
    return t('booking.confirmCancelNoCharge');
  };

  return (
    <ModalWrapper style={styles.modalContainer}>
      <TouchableWithoutFeedback>
        <View style={styles.container}>
          <WView alignCenter justifyBetween margin={16} row>
            <WText type="semiBold18">{t('booking.cancelRide')}</WText>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral500}
                h={20}
                w={20}>
                <IonIcon color={Colors.white} name="close-outline" size={16} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>

          <WView mBottom={16} mHoz={16}>
            <WText type="regular12">{t('booking.driverComing')}</WText>
            <WView style={styles.content}>
              <Icon color={Colors.warning600} name="Outline-Money" size={24} />
              <WView fill mLeft={8} mRight={4}>
                <WText color={Colors.neutral850} type="regular12">
                  {formatText()}
                </WText>
              </WView>
            </WView>
          </WView>
          <WButton
            label={t('button.confirmCancelBooking')}
            onPress={() => {
              navigation.goBack();
              navigation.navigate('CancelReasonList', {
                bookingId,
                isTopBarEnable: false,
              });
            }}
            style={styles.mBottom16}
          />
        </View>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default BookingCancel;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    marginBottom: hasNotch() ? 0 : 12,
    marginHorizontal: 16,
  },
  content: {
    alignItems: 'center',
    backgroundColor: Colors.warning100,
    borderColor: Colors.warning600,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginTop: 12,
    padding: 16,
  },
  mBottom16: {marginBottom: 16, marginHorizontal: 16},
  modalContainer: {justifyContent: 'center'},
});
