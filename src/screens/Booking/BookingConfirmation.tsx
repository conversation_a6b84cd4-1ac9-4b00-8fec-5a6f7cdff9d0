/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable max-lines */
import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useRef, useState} from 'react';
import {
  FlatList,
  Image,
  Keyboard,
  LayoutAnimation,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {PaymentIcon} from 'react-native-payment-icons';
import Icon from 'react-native-vector-icons/icomoon';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {useTranslation} from 'react-i18next';
import CarTypeItem from './components/CarTypeItem';
import {
  formatDistance,
  formatNumberPrice,
  formatTime,
  getDiscountPrice,
  getTimeNotify,
} from '@utils/Tools';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import useBooking from '@utils/hooks/useBooking';
import {
  CARS,
  CASH,
  CardViewType,
  FORMAT_DATE_MONTH,
  FORMAT_DATE_TIME_PICKER,
  SCREEN_WIDTH,
  TIME_NOT_24H_FORMAT,
  WALLET,
} from '@themes/Constants';
import {Colors, Icons, Images} from '@themes';
import {
  BookingConfigEntity,
  BookingEntity,
  CouponEntity,
  CreateBookingWithCodeDto,
} from '@requests';
import {useGlobalState} from '@react-query/clientStateManage';
// import {TranslationKeys} from '@generated/translationKeys';
import ModalNoteToDriver from '@components/Modal/ModalNoteToDriver';
import {WButton, WImage, WText, WTooltip, WTouchable, WView} from '@components';
import {useBookingConfigsServiceBookingConfigControllerGetBookingConfig} from '@queries';
import {ISelectedCar} from '@interface/booking';

interface IDataNote {
  id: number;
  label: string;
  icon: string;
}

const BOOKING_TYPE = {
  BUSINESS: 'BUSINESS',
  PERSONAL: 'PERSONAL',
};

/*
 * const BOOKING_TYPE_TABS = [
 *   {icon: 'Outline-UserCircle', id: 'PERSONAL', title: 'booking.personal'},
 *   {icon: 'Outline-Briefcase', id: 'BUSINESS', title: 'booking.business'},
 * ];
 */

interface BookingConfirmationProps {
  onBookCar?: (data?: any) => void;
  onSelectCarType?: (data?: any) => void;
}

function BookingConfirmation({
  onBookCar,
  onSelectCarType,
}: BookingConfirmationProps) {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const {i18n} = useTranslation();
  const listCarRef = useRef<FlatList>(null);
  const tooltipRef = useRef(null);
  const tooltipVanRef = useRef(null);

  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  // const [_anyCarPrice, setAnyCarPrice] = useGlobalState('anyCarPrice');
  const [_currentPayment, setCurrentPayment] = useGlobalState('currentPayment');

  const [bookingType, setBookingType] = useState(BOOKING_TYPE.PERSONAL);
  const [visibleNote, setVisibleNote] = useState(false);
  const [prevDate, setPrevDate] = useState<string | null>(null);
  const [hasHandledFirstRender, setHasHandledFirstRender] = useState(false);
  const [heightContainer, setHeightContainer] = useState<number | undefined>(
    400,
  );

  const DATA_NOTE = [
    {icon: 'Outline-Cat', id: 1, label: t('note.travelWithCat')},
    {icon: 'Outline-Dog', id: 2, label: t('note.travelWithDog')},
    {icon: 'Outline-Baby', id: 3, label: t('note.babySeat')},
    {icon: 'Outline-Package', id: 4, label: t('note.heavyPackage')},
  ];
  const {
    isAvailable,
    bookingEstimation,
    couponValueTitle,
    onBook,
    showWarningBanner,
    goToBusinessList,
    goToPaymentList,
    business,
    card,
    onGoToCouponList,
    coupon,
    selectedCar,
    setSelectedCar,
    onSetNote,
    note,
    showWarningSchedule,
    onGotoBeforeTip,
    tipAmount,
    showWarningPrice,
    getLastAmount,
    displayCars,
    showWarningFixedFare,
    showWarningRideUpdated,
  } = useBooking({bookingType, onBookCar});

  const {data: dataConfig} =
    useBookingConfigsServiceBookingConfigControllerGetBookingConfig();

  const isSchedule = !!bookingWithCodeParams?.date;

  const titleButton = isSchedule
    ? t('button.reserveBooking')
    : t('button.bookNow');

  useEffect(() => {
    if (bookingEstimation?.data?.estimateAmounts && !hasHandledFirstRender) {
      handleSelectedCar();
      setHasHandledFirstRender(true);
    }
  }, [bookingEstimation?.data?.estimateAmounts, hasHandledFirstRender]);

  useEffect(() => {
    setPrevDate(bookingWithCodeParams?.date || null);
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setHeightContainer(undefined);
    }, 500);
    return () => {
      setHeightContainer(300);
    };
  }, []);

  const handleSelectedCar = () => {
    const car = CARS?.find(
      e => e?.vehicleType === bookingWithCodeParams?.vehicleType,
    );
    if (car) {
      const price = bookingEstimation?.data?.estimateAmounts?.find(
        ele => ele?.vehicleType === car?.id,
      )?.amount;
      setSelectedCar({
        ...car,
        amount: price,
      });
    }
  };

  const [noteTemp, setNoteTemp] = useState<string>(note || '');

  const renderCarItem = ({
    item,
    index,
  }: {
    item: ISelectedCar;
    index: number;
  }) => {
    const isLastItem = index === CARS?.length - 1;
    const isSelected = selectedCar?.id === item?.id;
    const price = bookingEstimation?.data?.estimateAmounts?.find(
      ele => ele?.vehicleType === item?.id,
    )?.amount;
    const discountPrice = formatNumberPrice(
      getDiscountPrice(Number(price), coupon as CouponEntity),
    );

    const isHideDiscount =
      (!coupon?.vehicleTypes?.includes(item?.id as any) &&
        !coupon?.vehicleTypes?.includes(BookingEntity.vehicleType.ALL)) ||
      card?.id === CASH.id;

    const onPressItem = () => {
      if (item?.isScheduled) {
        const bookingTime = moment(
          bookingWithCodeParams?.date,
          FORMAT_DATE_TIME_PICKER,
        );
        const currentTimePlus24h = moment().add(24, 'hours');
        const isBookingAfter24h = bookingTime.isSameOrAfter(currentTimePlus24h);

        if (!isBookingAfter24h) {
          setBookingWithCodeParams({
            ...bookingWithCodeParams,
            date: moment().add(24, 'hours').format(FORMAT_DATE_TIME_PICKER),
          } as CreateBookingWithCodeDto);
        }
      } else if (prevDate) {
        setBookingWithCodeParams({
          ...bookingWithCodeParams,
          date: prevDate,
        } as CreateBookingWithCodeDto);
      } else {
        setBookingWithCodeParams({
          ...bookingWithCodeParams,
          date: null,
        } as CreateBookingWithCodeDto);
      }

      listCarRef.current?.scrollToItem?.({
        animated: true,
        item,
      });
      setSelectedCar({
        ...item,
        amount: price,
      });
      onSelectCarType?.({
        ...item,
        amount: price,
      });
    };
    return (
      <CarTypeItem
        discountPrice={coupon && !isHideDiscount ? discountPrice : undefined}
        isLastItem={isLastItem}
        isSelected={isSelected}
        item={item}
        onPress={onPressItem}
        price={formatNumberPrice(Number(price))}
      />
    );
  };

  const renderPaymentMethod = () => {
    if (!card) {
      return (
        <WText color={Colors.neutral500} type="medium14">
          {t('booking.placeholderSelect')}
        </WText>
      );
    }
    if (card?.id === CASH.id) {
      return (
        <WView alignCenter row>
          <Icon name="Outline-Money" size={24} />

          <WText marginLeft={4} type="medium14">
            {t('payment.cash')}
          </WText>
        </WView>
      );
    }

    if (card?.id === WALLET.id) {
      return (
        <WView alignCenter row>
          <Image
            resizeMode="contain"
            source={Platform.OS === 'ios' ? Images.applePay : Images.googlePay}
            style={
              Platform.OS === 'ios' ? styles.applePayImg : styles.googlePayImg
            }
          />
          <WText marginLeft={4} type="medium14">
            {Platform.OS === 'ios'
              ? t('payment.applePay')
              : t('payment.googlePay')}
          </WText>
        </WView>
      );
    }

    return (
      <WView alignCenter row>
        <PaymentIcon type={card?.brand as CardViewType} />
        <WText marginLeft={4} type="medium14">
          {card?.last4}
        </WText>
      </WView>
    );
  };

  const onNextScheduleStep = () => {
    if (!card) {
      return navigation.navigate('ActionModal', {
        cancelLabel: t('button.discard'),
        confirmBtnStyle: {flex: 2},
        confirmLabel: t('button.addPaymentMethod'),
        content: t('booking.cardPaymentShouldUsedContent'),
        enableCloseOnMask: false,
        onApply: () => {
          navigation.goBack();
          goToPaymentList();
        },
        title: t('booking.cardPaymentShouldUsed'),
      });
    }
    setCurrentPayment(card);
    LayoutAnimation.easeInEaseOut();
    onBook();
  };

  const onBookingAction = () => {
    if (bookingWithCodeParams?.date) {
      if (coupon && !isAvailable) {
        return navigation.navigate('ActionModal', {
          buttonAlign: 'vertical',
          cancelLabel: t('button.continueWithoutCoupon'),
          confirmLabel: t('button.seeOtherCoupon'),
          content: t('booking.couponCannotUsedContent'),
          enableCloseOnMask: false,
          onApply: () => {
            navigation.goBack();
            onGoToCouponList();
          },
          onCancel: () => {
            navigation.goBack();
            onNextScheduleStep();
          },
          title: t('booking.couponCannotUsed'),
        });
      }

      const isBookingExpired = moment(
        bookingWithCodeParams?.date,
        FORMAT_DATE_TIME_PICKER,
      )?.isBefore(moment());

      if (isBookingExpired) {
        return navigation.navigate('ActionModal', {
          cancelLabel: t('button.reschedule'),
          confirmLabel: t('button.bookNow'),
          content: t('booking.timeExceededMessage'),
          enableCloseOnMask: false,
          onApply: () => {
            navigation.goBack();
            setBookingWithCodeParams({
              ...bookingWithCodeParams,
              date: null,
            } as CreateBookingWithCodeDto);
          },
          onCancel: () => {
            navigation.goBack();
            setTimeout(() => {
              handleNavigateSchedule(true);
            }, 200);
          },
          title: t('booking.timeExceeded'),
        });
      }

      onNextScheduleStep();
    } else {
      if (coupon && !isAvailable) {
        return navigation.navigate('ActionModal', {
          buttonAlign: 'vertical',
          cancelLabel: t('button.continueWithoutCoupon'),
          confirmLabel: t('button.seeOtherCoupon'),
          content: t('booking.couponCannotUsedContent'),
          enableCloseOnMask: false,
          onApply: () => {
            navigation.goBack();
            onGoToCouponList();
          },
          onCancel: () => {
            navigation.goBack();
            onBook();
          },
          title: t('booking.couponCannotUsed'),
        });
      }
      onBook();
    }
  };

  const handleNavigateSchedule = (isExpired = false) => {
    const isScheduled = selectedCar?.isScheduled;

    const currentTimePlusTwoAndHalfHours = moment()
      .add(2, 'hours')
      .add(30, 'minutes');

    const currentTimePlus1Day = moment().add(1, 'day');

    const bookingDate = moment(bookingWithCodeParams?.date);

    const isCheckTimeValid = bookingDate?.isBefore(
      currentTimePlusTwoAndHalfHours,
    );

    const isCheckTimeValidScheduled =
      bookingDate?.isBefore(currentTimePlus1Day);

    let defaultDate = '';

    if (isScheduled) {
      if (isCheckTimeValidScheduled) {
        defaultDate = moment().add(1, 'day').format(FORMAT_DATE_TIME_PICKER);
      } else {
        defaultDate = moment(bookingWithCodeParams?.date).format(
          FORMAT_DATE_TIME_PICKER,
        );
      }
    } else if (isCheckTimeValid || isExpired || !bookingWithCodeParams?.date) {
      defaultDate = moment()
        .add(2, 'hours')
        .add(30, 'minutes')
        .format(FORMAT_DATE_TIME_PICKER);
    } else {
      defaultDate = moment(bookingWithCodeParams?.date).format(
        FORMAT_DATE_TIME_PICKER,
      );
    }

    navigation.navigate('ScheduleTimeModal', {
      defaultDate,
      isMinDate: !isExpired && isScheduled,
      isRide: !isExpired && !isScheduled,
      isTopBarEnable: false,
      onApply: date => {
        if (prevDate) {
          setPrevDate(date as string);
        }
        navigation.goBack();
      },
      onPickupNow: () => {
        setPrevDate(null);
      },
    });
  };

  const openModalNote = () => {
    setVisibleNote(true);
    setTimeout(() => {
      setNoteTemp(note);
    }, 200);
  };
  const closeModalNote = () => {
    setVisibleNote(false);
    if (!note) {
      setNoteTemp('');
    } else {
      setNoteTemp(note);
    }
  };

  const onSaveNote = async () => {
    Keyboard.dismiss();
    setVisibleNote(false);
    await onSetNote(noteTemp);
  };

  useEffect(() => {
    if (noteTemp?.startsWith(',')) {
      setNoteTemp(noteTemp?.substring(1));
    }
  }, [noteTemp]);

  const onSelectItem = (item: IDataNote) => {
    const inputString = item?.label || '';
    let temp = noteTemp;
    if (temp.includes(inputString)) {
      const regex = new RegExp(`(, )?${inputString}(, )?`);
      temp = temp.replace(regex, '').trim();
      temp = temp.replace(/^, |, $/, '');
      setNoteTemp(temp);
    } else {
      if (temp.length > 0) {
        temp += ', ';
      }
      temp += inputString;
      setNoteTemp(temp);
    }
  };

  const handleNavigateDestination = () => {
    navigation.navigate('SelectDestination', {
      isSchedule: isSchedule ?? false,
      isTopBarEnable: false,
    });
  };

  const renderButton = () => (
    <WView alignCenter row>
      <WTouchable
        borderRadius={8}
        center
        color={Colors.neutral150}
        gap={4}
        h={46}
        mLeft={16}
        mTop={8}
        onPress={() => handleNavigateSchedule(false)}
        pHoz={16}
        pVer={2}
        w={100}>
        {bookingWithCodeParams?.date ? (
          <>
            <WText capitalize color={Colors.neutral700} type="regular12">
              {getTimeNotify({
                createdAt: bookingWithCodeParams?.date || '',
                format: FORMAT_DATE_MONTH,
                locale: i18n.language,
              })}
            </WText>
            <WText color={Colors.neutral700} type="regular12">
              {getTimeNotify({
                createdAt: bookingWithCodeParams?.date || '',
                format: TIME_NOT_24H_FORMAT,
                locale: i18n.language,
              })}
            </WText>
          </>
        ) : (
          <Icon color={Colors.neutral900} name="Fill-CalendarPlus" size={16} />
        )}
      </WTouchable>
      <WButton
        disabled={getLastAmount === 0}
        fill
        label={`${titleButton} (${formatNumberPrice(getLastAmount)})`}
        onPress={onBookingAction}
        style={styles.bookBtn}
      />
    </WView>
  );

  return (
    <WView
      borderTopLeftRadius={16}
      borderTopRightRadius={16}
      color={Colors.white}
      h={heightContainer}>
      <WView pHoz={16}>
        {showWarningRideUpdated && (
          <WView
            mHoz={16}
            style={[
              styles.warningContainer,
              {top: showWarningSchedule ? -110 : -56},
            ]}>
            <WView
              alignCenter
              borderRadius={8}
              color={'#D9FCDE'}
              pHoz={16}
              pVer={8}
              row>
              <WImage size={18} source={Icons.icSuccess} />
              <WText fill marginLeft={8} type="regular14">
                {t('booking.rideUpdated')}
              </WText>
            </WView>
          </WView>
        )}
        {showWarningPrice && (
          <WView mHoz={16} style={styles.warningContainer}>
            <WView
              alignCenter
              borderRadius={8}
              color={'#D9FCDE'}
              pHoz={16}
              pVer={8}
              row>
              <WImage size={18} source={Icons.icSuccess} />
              <WText fill marginLeft={8} type="regular14">
                {t('booking.priceUpdated')}
              </WText>
            </WView>
          </WView>
        )}

        {showWarningFixedFare && (
          <WView
            mHoz={16}
            style={[
              styles.warningContainer,
              {top: showWarningSchedule ? -110 : -56},
            ]}>
            <WView
              alignCenter
              borderColor={Colors.warningBorderHover}
              borderRadius={8}
              borderWidth={1}
              color={Colors.warning100}
              pHoz={16}
              pVer={8}
              row>
              <Icon
                color={Colors.warningBorderHover}
                name="Outline-WarningCircle"
                size={24}
              />
              <WText
                color={Colors.warningBorderHover}
                fill
                marginLeft={8}
                type="regular12">
                {t('booking.couponFixedFare')}
              </WText>
            </WView>
          </WView>
        )}
        {showWarningBanner && (
          <WView mHoz={16} style={styles.warningContainer}>
            <WView
              alignCenter
              borderColor={Colors.warningBorderHover}
              borderRadius={8}
              borderWidth={1}
              color={Colors.warning100}
              pHoz={16}
              pVer={8}
              row>
              <Icon
                color={Colors.warningBorderHover}
                name="Outline-WarningCircle"
                size={24}
              />
              <WText
                color={Colors.warningBorderHover}
                fill
                marginLeft={8}
                type="regular12">
                {t('coupon.unavailable')}
              </WText>
            </WView>
          </WView>
        )}
        {showWarningSchedule && (
          <WView
            mHoz={16}
            style={[
              styles.warningContainer,
              {top: showWarningBanner ? -110 : -56},
            ]}>
            <WView
              alignCenter
              borderColor={Colors.warningBorderHover}
              borderRadius={8}
              borderWidth={1}
              color={Colors.warning100}
              pHoz={16}
              pVer={8}
              row>
              <Icon
                color={Colors.warningBorderHover}
                name="Outline-WarningCircle"
                size={24}
              />
              <WText
                color={Colors.warningBorderHover}
                fill
                marginLeft={8}
                type="regular12">
                {t('accessibleVan.content')}
              </WText>
            </WView>
          </WView>
        )}
        <WView alignCenter justifyBetween mVer={16} row>
          <WText type="semiBold18">
            {isSchedule
              ? t('booking.scheduleConfirmation')
              : t('booking.bookingConfirmation')}
          </WText>
          <TouchableWithoutFeedback onPress={handleNavigateDestination}>
            <WView
              borderRadius={20}
              center
              color={Colors.neutral500}
              h={20}
              w={20}>
              <IonIcon color={Colors.white} name="close-outline" size={16} />
            </WView>
          </TouchableWithoutFeedback>
        </WView>

        <WView alignCenter mRight={-30} mVer={8} row>
          <FlatList
            ListFooterComponent={() => <WView w={20} />}
            data={displayCars}
            horizontal
            ref={listCarRef}
            renderItem={renderCarItem}
            showsHorizontalScrollIndicator={false}
          />
          {selectedCar?.id === BookingEntity.vehicleType.ACCESSIBLE_VAN && (
            <WView style={styles.tooltipsVan}>
              <WTooltip
                direction="left"
                label={t('accessibleVan.contentModal')}
                ref={tooltipVanRef}
                showIcon={false}
              />
            </WView>
          )}
        </WView>

        <WView>
          <WView borderTopColor={Colors.neutral200} borderTopWidth={1} mTop={4}>
            <WView
              borderRadius={8}
              color={Colors.neutral100}
              mTop={12}
              pHoz={16}
              pVer={8}
              row>
              <WView alignCenter fill justifyCenter style={styles.distanceView}>
                <WText lineHeight={22} type="medium14">
                  {formatDistance(bookingEstimation?.data?.distance)}
                </WText>
                <WText color={Colors.neutral600} type="semiBold12">
                  {t('booking.distance')}
                </WText>
              </WView>
              <WView alignCenter fill justifyCenter>
                <WText lineHeight={22} type="medium14">
                  {formatTime(bookingEstimation?.data?.duration)}
                </WText>
                <WText color={Colors.neutral600} type="semiBold12">
                  {t('booking.distanceTime')}
                </WText>
              </WView>
              <WView style={styles.tooltips}>
                <WTooltip
                  direction="left"
                  label={t('tooltip.distanceAndTime')}
                  ref={tooltipRef}
                />
              </WView>
            </WView>
          </WView>

          {/* <WView
            alignCenter
            borderColor={Colors.neutral150}
            borderRadius={8}
            borderTopColor={Colors.neutral200}
            borderWidth={1}
            justifyCenter
            mTop={12}
            padding={4}>
            <WView borderRadius={6} color={Colors.white} row>
              {BOOKING_TYPE_TABS.map(item => {
                const isSelected = item?.id === bookingType;
                return (
                  <TouchableWithoutFeedback
                    key={item?.id}
                    onPress={() => {
                      setCoupon(undefined);
                      setBookingType(item?.id);
                    }}>
                    <WView
                      alignCenter
                      borderColor={
                        isSelected ? Colors.neutral875 : Colors.white
                      }
                      borderRadius={6}
                      borderWidth={1}
                      color={isSelected ? Colors.neutral100 : Colors.white}
                      fill
                      justifyCenter
                      padding={12}
                      row>
                      <Icon
                        color={Colors.neutral500}
                        name={item?.icon}
                        size={20}
                      />
                      <WText marginLeft={6} type="medium14">
                        {t(item?.title as TranslationKeys)}
                      </WText>
                    </WView>
                  </TouchableWithoutFeedback>
                );
              })}
            </WView>
          </WView> */}

          {bookingType === BOOKING_TYPE.PERSONAL ? (
            <TouchableWithoutFeedback onPress={goToPaymentList}>
              <WView h={38} justifyBetween mTop={12} row>
                <WView alignCenter row>
                  <Icon
                    color={Colors.neutral500}
                    name="Outline-CreditCard"
                    size={22}
                  />
                  <WText marginLeft={4} type="medium14">
                    {t('booking.paymentMethod')}
                  </WText>
                </WView>
                <WView alignCenter gap={4} row>
                  {renderPaymentMethod()}
                  <Icon
                    color={Colors.neutral500}
                    name="Outline-CaretRight"
                    size={22}
                  />
                </WView>
              </WView>
            </TouchableWithoutFeedback>
          ) : (
            <TouchableWithoutFeedback onPress={goToBusinessList}>
              <WView alignCenter h={38} justifyBetween mTop={12} pVer={8} row>
                <WView alignCenter row>
                  <Icon
                    color={Colors.neutral500}
                    name="Outline-Briefcase"
                    size={22}
                  />
                  <WText marginLeft={4} type="medium14">
                    {t('business.yourBusiness')}
                  </WText>
                </WView>
                {business?.business?.name ? (
                  <WText
                    color={Colors.neutral600}
                    marginLeft={4}
                    type="regular12">
                    {business?.business?.name}
                  </WText>
                ) : (
                  <WView alignCenter gap={4} row>
                    <WText color={Colors.neutral500} type="medium14">
                      {t('booking.placeholderSelect')}
                    </WText>
                    <Icon
                      color={Colors.neutral500}
                      name="Outline-CaretRight"
                      size={22}
                    />
                  </WView>
                )}
              </WView>
            </TouchableWithoutFeedback>
          )}
          {bookingType === BOOKING_TYPE.PERSONAL && (
            <TouchableWithoutFeedback onPress={onGoToCouponList}>
              <WView justifyBetween mTop={12} pVer={8} row>
                <WView alignCenter row>
                  <Icon
                    color={Colors.neutral500}
                    name="Outline-Ticket"
                    size={22}
                  />
                  <WText marginLeft={4} type="medium14">
                    {t('booking.coupon')}
                  </WText>
                </WView>
                <WView alignCenter gap={4} row>
                  {!isAvailable && (
                    <Icon
                      color={Colors.warningBorderHover}
                      name="Outline-WarningCircle"
                      size={16}
                    />
                  )}
                  {couponValueTitle ? (
                    <WText type="medium14">{couponValueTitle}</WText>
                  ) : (
                    <WText color={Colors.neutral500} type="medium14">
                      {t('booking.placeholderSelect')}
                    </WText>
                  )}
                  <Icon
                    color={Colors.neutral500}
                    name="Outline-CaretRight"
                    size={22}
                  />
                </WView>
              </WView>
            </TouchableWithoutFeedback>
          )}

          {dataConfig?.data?.tipPosition ===
            BookingConfigEntity.tipPosition.BEFORE && (
            <TouchableWithoutFeedback onPress={onGotoBeforeTip}>
              <WView gap={8} justifyBetween mTop={12} pVer={8} row>
                <WView alignCenter fill row>
                  <WImage
                    resizeMode="cover"
                    size={22}
                    source={Icons.icAddMoney}
                  />
                  <WText fill marginLeft={4} type="medium14">
                    {t('rating.tipTheDriver')}
                  </WText>
                </WView>
                <WView alignCenter gap={4} row>
                  {tipAmount ? (
                    <WText color={Colors.success} type="medium14">
                      {t('rating.extraTip', {
                        value: formatNumberPrice(tipAmount),
                      })}
                    </WText>
                  ) : tipAmount === 0 ? (
                    <WText color={Colors.neutral700} type="medium14">
                      {t('rating.noTip')}
                    </WText>
                  ) : (
                    <WText color={Colors.neutral500} type="medium14">
                      {t('booking.placeholderSelect')}
                    </WText>
                  )}
                  <Icon
                    color={Colors.neutral500}
                    name="Outline-CaretRight"
                    size={22}
                  />
                </WView>
              </WView>
            </TouchableWithoutFeedback>
          )}
        </WView>
      </WView>

      <SafeAreaView style={styles.bottomBtnContainer}>
        <TouchableOpacity onPress={openModalNote} style={styles.note}>
          <WText
            color={Colors.colorLink}
            mWidth={'80%'}
            marginRight={'auto'}
            type="medium14">
            {note ? t('note.addedPickupDetails') : t('note.title')}
          </WText>
          <Icon
            color={Colors.colorLink}
            name={note ? 'Outline-PencilSimpleLine' : 'Outline-PlusCircle'}
            size={22}
          />
        </TouchableOpacity>
        {note ? (
          <WView
            borderColor={Colors.neutral600}
            borderRadius={6}
            borderWidth={1}
            h={56}
            mBottom={8}
            mHoz={16}
            pHoz={4}
            pVer={8}>
            <WText type="regular14">{note}</WText>
          </WView>
        ) : null}
        <WView
          color={Colors.neutral150}
          h={1}
          mLeft={-16}
          mTop={8}
          w={SCREEN_WIDTH}
        />
        {renderButton()}
        <ModalNoteToDriver
          data={DATA_NOTE as IDataNote[]}
          note={noteTemp}
          onChangText={setNoteTemp}
          onClosed={closeModalNote}
          onSave={onSaveNote}
          onSelectItem={onSelectItem}
          visible={visibleNote}
        />
      </SafeAreaView>
    </WView>
  );
}
export default BookingConfirmation;

const styles = StyleSheet.create({
  applePayImg: {height: 40, marginRight: 4, width: 40},
  bookBtn: {
    marginBottom: 8,
    marginHorizontal: 16,
    marginTop: 16,
  },
  bottomBtnContainer: {
    backgroundColor: Colors.white,
    marginTop: 'auto',
  },

  distanceView: {
    borderRightColor: Colors.neutral200,
    borderRightWidth: 1,
  },
  googlePayImg: {height: 28, marginRight: 4, width: 48},
  note: {
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 16,
  },
  tooltips: {
    position: 'absolute',
    right: 12,
    top: 12,
    zIndex: 999,
  },
  tooltipsVan: {
    height: 20,
    justifyContent: 'center',
    position: 'absolute',
    right: 42,
    top: 12,
    width: 20,
    zIndex: 999,
  },
  warningContainer: {
    height: 48,
    justifyContent: 'flex-end',
    left: 0,
    position: 'absolute',
    right: 0,
    top: -56,
  },
});
