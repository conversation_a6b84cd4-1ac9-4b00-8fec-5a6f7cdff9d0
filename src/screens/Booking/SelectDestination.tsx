/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable max-lines */
import {useNavigation, useRoute} from '@react-navigation/native';
import {debounce, isEmpty, isEqual} from 'lodash-es';
import moment from 'moment';
import React, {useEffect, useRef, useState} from 'react';
import {
  Image,
  InputAccessoryView,
  Keyboard,
  Platform,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {useTranslation} from 'react-i18next';
import {WText, WView} from '@components';
import SearchLocationResult from '@components/SearchLocationResult';
import {TranslationKeys} from '@generated/translationKeys';
import {
  useBookingsServiceBookingControllerCheckWorkingArea,
  useLocationsServiceLocationControllerGetLocationsByPlaceIds,
  useUsersServiceUserControllerAddRecentLocation,
  useUsersServiceUserControllerGetRecentLocation,
  useUsersServiceUserControllerGetSavedLocation,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  CoordinatesDto,
  CreateBookingWithCodeDto,
  GeoLocationType,
  LocationDto,
  LocationEntity,
  SavedLocationDto,
  SavedLocationEntity,
} from '@requests';
import {Colors, Fonts, Images} from '@themes';
import {
  ACTIVE_OPACITY,
  CURRENT_USER_LOCATION,
  DATE_MONTH_HOUR_FORMAT,
  DISTANCE,
  FORMAT_DATE_TIME_PICKER,
  GOOGLE_MAPS_API_KEY,
  INPUT_ACCESSORY_VIEW_ID,
  MIN_SEARCH_CHARACTER,
  SEARCH_TYPE,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {useLocationPermission} from '@utils/hooks/useGeoLocation';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  capitalizeFirstLetter,
  getTimeNotify,
  showErrorAlert,
  sortByLocationType,
} from '@utils/Tools';
import {ILocationSelect, SelectDestinationProps} from '@global';

const SEARCH_INPUT_HEIGHT = 96;

let isFirstTimeOpen = true;

function SelectDestination() {
  const {t} = useTypeSafeTranslation();
  const {i18n} = useTranslation();
  const navigation = useNavigation();
  const {isSchedule} = useRoute()?.params as SelectDestinationProps;

  useEffect(() => {
    setTimeout(() => {
      !isSchedule && destinationInputRef?.current?.focus();
    }, 200);
    const unsubscribe = navigation.addListener('focus', () => {
      if (!isFirstTimeOpen) {
        destinationInputRef?.current?.focus();
      } else {
        isFirstTimeOpen = false;
      }
      // Your custom logic when screen is focused
    });

    // Clean up the listener on unmount
    return () => {
      isFirstTimeOpen = true;
      unsubscribe();
    };
  }, []);

  const destinationInputRef = useRef<TextInput>(null);
  const pickupInputRef = useRef<TextInput>(null);

  const [_, setSavedLocationType] = useGlobalState('savedLocationType');
  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [_manualCoupons, setManualCoupons] = useGlobalState('manualCoupons');
  const [useData] = useGlobalState('userData');
  const [searchType, setSearchType] = useGlobalState('searchType');

  const [_pickUpConfirm, setPickUpConfirm] = useGlobalState('pickUpConfirm');

  const defaultGeoLocation = useData?.location || CURRENT_USER_LOCATION;

  const [currentSearchType, setCurrentSearchType] = useState(
    SEARCH_TYPE.PICKUP,
  );
  const [googleAPILocationRes, setGoogleAPILocationRes] =
    useState<Response | null>();

  const [destinationLocation, setDestinationLocation] =
    useState<ILocationSelect | null>(null);
  const [pickupLocation, setPickupLocation] = useState<ILocationSelect | null>(
    null,
  );
  const [value, setValue] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [listDefaultLocation, setListDefaultLocation] = useState([
    {
      address: '',
      coordinates: undefined,
      icon: 'Outline-HouseLine',
      id: SavedLocationDto.type.HOME,
      label: 'savedLocation.home',
      name: 'savedLocation.home',
      type: SavedLocationDto.type.HOME,
    },
    {
      address: '',
      coordinates: undefined,
      icon: 'Outline-Briefcase',
      id: SavedLocationDto.type.WORK,
      label: 'savedLocation.work',
      name: 'savedLocation.work',
      type: SavedLocationDto.type.WORK,
    },
  ]);
  const [isFocus, setIsFocus] = useState(false);
  const [placeIds, setPlaceIds] = useState<string[]>([]);

  const {requestLocationPermission, data: dataCurrentLocation} =
    useLocationPermission();

  const handleSetBookingCodeParams = () => {
    if (isSchedule) {
      setBookingWithCodeParams({
        ...bookingWithCodeParams,
        date: moment()
          .add(2, 'hours')
          .add(30, 'minutes')
          .format(FORMAT_DATE_TIME_PICKER),
      } as CreateBookingWithCodeDto);
      handleNavigateSchedule();
    } else {
      setBookingWithCodeParams({
        ...bookingWithCodeParams,
        date: null,
      } as CreateBookingWithCodeDto);
    }
  };

  const setLocationsFromParams = () => {
    if (searchType) {
      if (bookingWithCodeParams?.destinationLocation) {
        setDestinationLocation(bookingWithCodeParams?.destinationLocation);
      }
      if (bookingWithCodeParams?.originLocation) {
        setPickupLocation({
          ...bookingWithCodeParams?.originLocation,
          placeId: pickupLocation?.placeId ?? '',
        });
      }
      setTimeout(() => {
        setCurrentSearchType(searchType);
      }, 300);
    }
  };

  const setPickupLocationToCurrent = () => {
    if (dataCurrentLocation?.location && !searchType) {
      const {address, geo} = dataCurrentLocation?.location || {};
      setPickupLocation({
        address: {
          address: address?.address || '',
          name: address?.name || '',
        },
        geo: {
          latitude: geo?.latitude || 0,
          longitude: geo?.longitude || 0,
        },
      });
      setCurrentSearchType(SEARCH_TYPE.DESTINATION);
    }
  };

  useEffect(() => {
    handleSetBookingCodeParams();
  }, [isSchedule]);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  useEffect(() => {
    setLocationsFromParams();
  }, [searchType]);

  useEffect(() => {
    setPickupLocationToCurrent();
  }, [dataCurrentLocation?.location]);

  const {data: savedLocation, isFetching} =
    useUsersServiceUserControllerGetSavedLocation();

  const {data: recentLocation, refetch: refetchRecentLocation} =
    useUsersServiceUserControllerGetRecentLocation();

  const {mutate: addRecentLocation} =
    useUsersServiceUserControllerAddRecentLocation();

  const {mutate: checkWorkingArea} =
    useBookingsServiceBookingControllerCheckWorkingArea();

  const {data: dataSubLocation, isLoading: isLoadingSubLocation} =
    useLocationsServiceLocationControllerGetLocationsByPlaceIds(
      {
        placeIds: JSON.stringify(placeIds),
      },

      undefined,
      {
        enabled: placeIds?.length > 0,
      },
    );

  useEffect(() => {
    if (savedLocation?.data) {
      setListDefaultLocation(
        listDefaultLocation?.map(ele => {
          const newItem = savedLocation?.data?.items?.find(
            (item: {type: SavedLocationDto.type}) => item?.type === ele?.id,
          );
          return {
            ...ele,
            address: newItem?.address,
            coordinates: newItem?.coordinates,
            name: newItem?.name || ele?.name,
          };
        }),
      );
    }
  }, [isFetching]);

  useEffect(
    () => () => {
      if (searchType) {
        setSearchType('');
      } else {
        setBookingWithCodeParams({} as CreateBookingWithCodeDto);
        setManualCoupons([]);
        setPickUpConfirm({});
      }
    },
    [],
  );

  const searchGoogleAPILocation = async (textQuery: string) => {
    setLoading(true);
    const urlAutocomplete = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
      textQuery,
    )}&components=country:ca&radius=${DISTANCE}&key=${GOOGLE_MAPS_API_KEY}`;

    try {
      const response = await fetch(urlAutocomplete);
      return await response.json();
    } catch (err) {
      if (err instanceof Error) {
        // showErrorAlert({messageCode: 'NETWORK_ERROR'});
      }
    } finally {
      setLoading(false);
    }
  };

  const onSearch = debounce(async (input: string) => {
    if (input?.length < MIN_SEARCH_CHARACTER) {
      return;
    }
    setSearchValue(input);
    setValue(input);

    if (value !== input) {
      setValue(input);
    }

    try {
      const response = await searchGoogleAPILocation(input);

      const filteredAddress = (response?.predictions || [])?.filter(
        (item: {terms: string[]}) => item?.terms?.length > 3,
      );

      const arrPlaceIds = filteredAddress?.map(
        (item: {place_id: string}) => item?.place_id,
      );
      setPlaceIds(arrPlaceIds);
      setGoogleAPILocationRes(filteredAddress);
    } catch (err) {
      if (err instanceof Error) {
        // showErrorAlert({message: err?.message});
      }
    }
  }, 500);

  const checkWorkingAreaAsync = ({
    originAddress,
    destinationAddress,
  }: {
    originAddress: string;
    destinationAddress: string;
  }): Promise<void> =>
    new Promise((resolve, reject) => {
      if (!originAddress || !destinationAddress) {
        return resolve();
      }
      checkWorkingArea(
        {
          requestBody: {
            destinationAddress,
            originAddress,
          },
        },
        {
          onError: () => {
            reject(new Error('Error'));
          },
          onSuccess: res => {
            if (res?.data) {
              resolve();
            } else {
              reject(new Error('Error'));
            }
          },
        },
      );
    });

  const getAddressCity = async region => {
    if (region) {
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${region?.latitude},${region?.longitude}&key=${GOOGLE_MAPS_API_KEY}`,
        );
        const responseJson = await response.json();
        const sortedData = sortByLocationType(responseJson.results);
        let newLocalityComponent = '';
        if (sortedData?.[0]?.address_components) {
          sortedData?.[0]?.address_components?.map(e => {
            newLocalityComponent += `${e?.long_name} `;
          });
          const city = newLocalityComponent;
          // Return addressArray in the resolved promise
          return city?.trim() || '';
        }
        return '';
      } catch (error) {
        showErrorAlert({message: t('error.500')});
        return null; // Return null or handle error as necessary
      } finally {
        /*
         * Optionally handle cleanup or other logic here
         * dismissLoading();
         */
      }
    }
  };

  const onSelectLocationItem = async (item?: ILocationSelect) => {
    try {
      //origin
      const originData =
        currentSearchType === SEARCH_TYPE.PICKUP ? item : pickupLocation;
      const originCity = await getAddressCity(originData?.geo);
      const originAddress = originCity || '';
      //destination
      const destinationData =
        currentSearchType === SEARCH_TYPE.DESTINATION
          ? item
          : destinationLocation;
      const destinationCity = await getAddressCity(destinationData?.geo);
      const destinationAddress = destinationCity || '';

      await checkWorkingAreaAsync({
        destinationAddress,
        originAddress,
      });
      if (item) {
        if (currentSearchType === SEARCH_TYPE.DESTINATION) {
          setDestinationLocation(item);
          if (isEqual(item?.geo, pickupLocation?.geo)) {
            return showErrorAlert({message: t('error.closeDestination')});
          }
          setBookingWithCodeParams({
            ...bookingWithCodeParams,
            amount: 0,
            codeId: '',
            destinationLocation: item,
            originLocation: pickupLocation as LocationDto,
            vehicleType: CreateBookingWithCodeDto.vehicleType.ALL,
          });
          setTimeout(() => {
            setSearchValue('');
          }, 400);
          if (pickupLocation) {
            setTimeout(() => {
              if (pickupLocation?.placeId) {
                return navigation.navigate('PickupSubLocation', {
                  isTopBarEnable: false,
                });
              }
              navigation.navigate('LocationMarkerSelector', {
                currentSearchType: SEARCH_TYPE.PICKUP,
                isConfirmPickupLocation: true,
                isTopBarEnable: false,
                location: pickupLocation,
              });
            }, 500);
          }
          if (!pickupLocation) {
            setCurrentSearchType(SEARCH_TYPE.PICKUP);
          }
        } else {
          if (isEqual(item?.geo, destinationLocation?.geo)) {
            return showErrorAlert({message: t('error.closeDestination')});
          }
          setPickupLocation(item);
          destinationInputRef?.current?.blur();
          setTimeout(() => {
            setSearchValue('');
          }, 400);
          setBookingWithCodeParams({
            ...bookingWithCodeParams,
            amount: 0,
            codeId: '',
            destinationLocation: destinationLocation as LocationDto,
            originLocation: item,
            vehicleType: CreateBookingWithCodeDto.vehicleType.ALL,
          });

          if (destinationLocation) {
            if (item?.placeId) {
              return navigation.navigate('PickupSubLocation', {
                isTopBarEnable: false,
              });
            }
            setTimeout(() => {
              navigation.navigate('LocationMarkerSelector', {
                currentSearchType: SEARCH_TYPE.PICKUP,
                isConfirmPickupLocation: true,
                isTopBarEnable: false,
                location: item,
              });
            }, 500);
          }
          if (!destinationLocation) {
            setCurrentSearchType(SEARCH_TYPE.DESTINATION);
          }
        }

        addRecentLocation(
          {
            requestBody: {
              address: item?.address?.address,
              coordinates: item?.geo,
              name: item?.address?.name,
              ...(item?.placeId && {placeId: item.placeId}),
            },
          },
          {
            onSuccess: () => {
              refetchRecentLocation();
            },
          },
        );
      }
    } catch (error) {
      showErrorAlert({
        message: t('error.notSupportedLocation'),
      });
    }
  };

  const onBack = () => {
    navigation.goBack();
  };

  const goToLocationMarkerSelector = () => {
    let tempLocation = null;
    if (currentSearchType === SEARCH_TYPE.DESTINATION && destinationLocation) {
      tempLocation = destinationLocation;
    }
    if (currentSearchType === SEARCH_TYPE.PICKUP && pickupLocation) {
      tempLocation = pickupLocation;
    } else {
      tempLocation = defaultGeoLocation;
    }
    destinationInputRef?.current?.blur();
    navigation.navigate('LocationMarkerSelector', {
      currentSearchType,
      isTopBarEnable: false,
      location: tempLocation,
      onConfirm: item => {
        navigation.goBack();
        onSelectLocationItem(item);
      },
    });
  };

  const getLocationData = () => {
    if (searchValue) {
      return googleAPILocationRes;
    }
    return recentLocation?.data?.items;
  };

  const searchLocationResultData = getLocationData();

  const handleNavigateSchedule = () => {
    navigation.navigate('ScheduleTimeModal', {
      defaultDate: !bookingWithCodeParams?.date
        ? moment()
            .add(2, 'hours')
            .add(30, 'minutes')
            .format(FORMAT_DATE_TIME_PICKER)
        : moment(bookingWithCodeParams?.date).format(FORMAT_DATE_TIME_PICKER),
      isTopBarEnable: false,
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <WView alignCenter justifyBetween mTop={12} pHoz={16} row>
        <WView alignCenter fill row>
          <TouchableOpacity onPress={onBack} style={styles.buttonBack}>
            <Icon
              color={Colors.neutral900}
              name="Outline-CaretLeft"
              size={20}
            />
            <WText fill numberOfLine={1} type="medium16">
              {t('booking.selectLocation')}
            </WText>
          </TouchableOpacity>
        </WView>
        <TouchableOpacity
          activeOpacity={ACTIVE_OPACITY}
          onPress={handleNavigateSchedule}>
          {bookingWithCodeParams?.date && isSchedule && (
            <WView
              alignCenter
              borderRadius={4}
              color={Colors.neutral150}
              gap={8}
              padding={10}
              row
              style={GlobalStyle.shadowSoft}>
              <Icon
                color={Colors.neutral900}
                name="Fill-CalendarPlus"
                size={16}
              />
              <WText type="semiBold14">
                {capitalizeFirstLetter(
                  String(
                    getTimeNotify({
                      createdAt: bookingWithCodeParams?.date,
                      format: DATE_MONTH_HOUR_FORMAT,
                      locale: i18n.language,
                    }),
                  ),
                )}
              </WText>
              <Icon
                color={Colors.neutral600}
                name="Outline-CaretDown"
                size={16}
              />
            </WView>
          )}
        </TouchableOpacity>
      </WView>

      <WView
        alignCenter
        borderRadius={8}
        color={Colors.neutral50}
        h={SEARCH_INPUT_HEIGHT}
        justifyBetween
        mHoz={16}
        mTop={10}
        pLeft={16}
        row
        style={GlobalStyle.shadowSearchInput}>
        <WView alignCenter>
          <Image
            resizeMode="contain"
            source={Images.userMarker}
            style={{height: 20, width: 20}}
          />
          <Image resizeMode="contain" source={Images.dotLine} />
          <Icon color={Colors.primary} name="Fill-MapPin" size={22} />
        </WView>

        <WView fill mLeft={10} pVer={8}>
          <TouchableWithoutFeedback
            onPress={() => {
              setCurrentSearchType(SEARCH_TYPE.PICKUP);
              setTimeout(() => {
                pickupInputRef?.current?.focus();
              }, 100);
              setSearchValue('');
              // pickupInputRef?.current?.blur();
            }}>
            <WView
              borderBottomColor={Colors.neutral200}
              borderBottomWidth={1}
              borderRadius={4}
              color={
                currentSearchType === SEARCH_TYPE.PICKUP
                  ? Colors.neutral150
                  : Colors.neutral50
              }
              fill
              justifyCenter
              mRight={8}
              pLeft={6}>
              {currentSearchType === SEARCH_TYPE.PICKUP ? (
                <TextInput
                  allowFontScaling={false}
                  defaultValue={pickupLocation?.address?.address}
                  inputAccessoryViewID={INPUT_ACCESSORY_VIEW_ID}
                  onBlur={() => {
                    setIsFocus(false);
                  }}
                  onChangeText={onSearch}
                  onFocus={() => {
                    setIsFocus(true);
                  }}
                  placeholder={t('booking.yourLocation')}
                  ref={pickupInputRef}
                  selectionColor={Colors.neutral700}
                  style={styles.pickupInput}
                  /*
                   * onFocus={() => {
                   *   pickupInputRef?.current?.clear();
                   * }}
                   */
                />
              ) : (
                <WText marginRight={8} numberOfLine={1} type="regular14">
                  {pickupLocation?.address?.address ||
                    t('booking.yourLocation')}
                </WText>
              )}
              {currentSearchType === SEARCH_TYPE.PICKUP && isFocus && (
                <TouchableWithoutFeedback
                  hitSlop={GlobalStyle.hitSlop}
                  onPress={() => {
                    pickupInputRef?.current?.clear();
                    setPickupLocation(null);
                    setSearchValue('');
                  }}>
                  <WView style={styles.closeBtn}>
                    <Icon
                      color={Colors.neutral500}
                      name="Fill-XCircle"
                      size={20}
                    />
                  </WView>
                </TouchableWithoutFeedback>
              )}
            </WView>
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback
            onPress={() => {
              setCurrentSearchType(SEARCH_TYPE.DESTINATION);
              setTimeout(() => {
                destinationInputRef?.current?.focus();
              }, 100);
              setSearchValue('');
              // destinationInputRef?.current?.blur();
            }}>
            <WView
              color={
                currentSearchType === SEARCH_TYPE.DESTINATION
                  ? Colors.neutral150
                  : Colors.neutral50
              }
              fill
              justifyCenter
              mRight={8}
              pLeft={6}>
              {currentSearchType === SEARCH_TYPE.DESTINATION ? (
                <TextInput
                  allowFontScaling={false}
                  defaultValue={destinationLocation?.address?.address}
                  inputAccessoryViewID={INPUT_ACCESSORY_VIEW_ID}
                  onBlur={() => {
                    setIsFocus(false);
                  }}
                  onChangeText={onSearch}
                  onFocus={() => {
                    setIsFocus(true);
                  }}
                  onSubmitEditing={() => {
                    onSelectLocationItem(destinationLocation);
                  }}
                  placeholder={t('selectDestination.whereTo')}
                  placeholderTextColor={Colors.neutral500}
                  ref={destinationInputRef}
                  returnKeyType="done"
                  selectionColor={Colors.primary}
                  style={styles.pickupInput}
                />
              ) : (
                <WText marginRight={8} numberOfLine={1} type="regular14">
                  {destinationLocation?.address?.address ||
                    t('selectDestination.whereTo')}
                </WText>
              )}
              {currentSearchType === SEARCH_TYPE.DESTINATION && isFocus && (
                <TouchableWithoutFeedback
                  hitSlop={GlobalStyle.hitSlop}
                  onPress={() => {
                    destinationInputRef?.current?.clear();
                    setSearchValue('');
                    setDestinationLocation(null);
                  }}>
                  <WView style={styles.closeBtn}>
                    <Icon
                      color={Colors.neutral500}
                      name="Fill-XCircle"
                      size={20}
                    />
                  </WView>
                </TouchableWithoutFeedback>
              )}
            </WView>
          </TouchableWithoutFeedback>
        </WView>
      </WView>
      <WView gap={4} justifyBetween mTop={8} pHoz={16} row>
        <WView alignCenter row>
          {listDefaultLocation?.map(ele => (
            <TouchableWithoutFeedback
              key={ele?.id}
              onPress={() => {
                const hasSavedLocation = !isEmpty(ele?.coordinates);
                if (hasSavedLocation) {
                  // setCurrentSearchType(SEARCH_TYPE.DESTINATION);
                  onSelectLocationItem({
                    address: {
                      address: ele?.address || t(ele?.name as TranslationKeys),
                      name: t(ele?.name as TranslationKeys),
                    },
                    geo: ele?.coordinates as unknown as CoordinatesDto,
                  });
                } else {
                  navigation.navigate('SearchSavedLocation', {
                    isTopBarEnable: false,
                    location: {
                      ...ele,
                      address: defaultGeoLocation?.address?.address,
                      coordinates:
                        defaultGeoLocation?.geo as unknown as GeoLocationType,
                      name: defaultGeoLocation?.address?.name,
                    },
                  });
                  setSavedLocationType(ele?.id);
                }
              }}>
              <WView
                borderColor={Colors.neutral200}
                borderRadius={5}
                borderWidth={1}
                mRight={4}
                pHoz={10}
                pVer={8}
                row>
                <Icon color={Colors.primary} name={ele?.icon} size={16} />
                <WText marginLeft={8} type="regular12">
                  {t(ele?.label as TranslationKeys)}
                </WText>
              </WView>
            </TouchableWithoutFeedback>
          ))}
        </WView>
        <TouchableWithoutFeedback
          onPress={() => {
            navigation.navigate('ListSavedLocation', {
              isFromBooking: true,
              isTopBarEnable: false,
              onApply: (item: SavedLocationEntity) => {
                onSelectLocationItem({
                  address: {
                    address: item?.address,
                    name: item?.name,
                  },
                  geo: item?.coordinates,
                });
                navigation.goBack();
              },
            });
          }}>
          <WView alignCenter justifyContent="flex-end" row>
            <WView
              borderColor={Colors.neutral200}
              borderRadius={5}
              borderWidth={1}
              mRight={4}
              pHoz={10}
              pVer={8}
              row>
              <Icon
                color={Colors.primary}
                name="Fill-FolderSimpleStar"
                size={16}
              />
              <WText
                mWidth={120}
                marginLeft={8}
                numberOfLine={1}
                type="regular12">
                {t('savedLocation.title.default')}
              </WText>
            </WView>
          </WView>
        </TouchableWithoutFeedback>
      </WView>

      <SearchLocationResult
        currentSearchType={currentSearchType}
        data={searchLocationResultData}
        dataSubLocation={(dataSubLocation?.data || []) as LocationEntity[]}
        hasSearchText={!!searchValue}
        loading={loading || isLoadingSubLocation}
        onPressItem={item => {
          onSelectLocationItem(item as LocationDto);
        }}
        onTouch={() => {
          Keyboard.dismiss();
        }}
        style={styles.searchContainer}
      />

      <SafeAreaView style={styles.chooseMapContainer}>
        <WView
          color={Colors.white}
          h={3}
          style={[styles.shadowContainer, GlobalStyle.shadowButton]}
          w={'100%'}
        />
        <TouchableWithoutFeedback onPress={goToLocationMarkerSelector}>
          <WView color={Colors.white} row style={styles.chooseMapBtn}>
            <Icon
              color={Colors.neutral500}
              name="Outline-MapTrifold"
              size={20}
            />
            <WText color={Colors.neutral700} marginLeft={8} type="regular14">
              {t('selectDestination.chooseOnMap')}
            </WText>
          </WView>
        </TouchableWithoutFeedback>
      </SafeAreaView>
      {Platform.OS === 'ios' && (
        <InputAccessoryView nativeID={INPUT_ACCESSORY_VIEW_ID}>
          <TouchableWithoutFeedback onPress={goToLocationMarkerSelector}>
            <SafeAreaView style={[styles.bottomView, GlobalStyle.shadow]}>
              <Icon
                color={Colors.neutral500}
                name="Outline-MapTrifold"
                size={20}
              />
              <WText color={Colors.neutral700} marginLeft={8} type="regular14">
                {t('selectDestination.chooseOnMap')}
              </WText>
            </SafeAreaView>
          </TouchableWithoutFeedback>
        </InputAccessoryView>
      )}
    </SafeAreaView>
  );
}

export default SelectDestination;

const styles = StyleSheet.create({
  bottomView: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    flexDirection: 'row',
    height: 50,
    justifyContent: 'center',
    paddingVertical: 8,
  },
  buttonBack: {
    alignItems: 'center',
    flexDirection: 'row',
    marginRight: 10,
  },
  chooseMapBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 1,
  },
  chooseMapContainer: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    height: 50,
    justifyContent: 'center',
    paddingVertical: 8,
    zIndex: 999,
  },
  closeBtn: {position: 'absolute', right: 8},
  container: {
    backgroundColor: Colors.white,
    flex: 1,
  },
  pickupInput: {
    fontFamily: Fonts.type.regular,
    fontSize: 14,
    paddingRight: 32,
    paddingVertical: 4,
  },
  searchContainer: {
    backgroundColor: Colors.white,
    borderTopColor: Colors.neutral200,
    borderTopWidth: 1,
    marginTop: 24,
  },
  shadowContainer: {position: 'absolute', top: 0, zIndex: 1},
});
