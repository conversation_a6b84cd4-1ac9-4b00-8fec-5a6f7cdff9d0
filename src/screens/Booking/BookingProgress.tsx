/* eslint-disable react/hook-use-state */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable max-lines */
/* eslint-disable no-nested-ternary */
import {useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Image,
  LayoutAnimation,
  Linking,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import BookingLineInfo from './components/BookingLineInfo';
import {WText, WTooltip, WView} from '@components';
import {
  useBookingsServiceBookingControllerGetBooking,
  useBookingsServiceBookingControllerGetBookingOfMeKey,
  useBookingsServiceBookingControllerGetCurrentBookingKey,
  useDriverServiceDriverControllerGetDriver,
  useUsersServiceUserControllerGet<PERSON>ser<PERSON><PERSON>,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import {BookingEntity} from '@requests';
import {Colors, Images} from '@themes';
import {HAS_NOTCH, PHONE_SUPPORT, SCREEN_WIDTH} from '@themes/Constants';
import useBooking from '@utils/hooks/useBooking';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useAppState} from '@utils/hooks/useAppState';

const SEARCH_INPUT_HEIGHT = 68;
interface TitleDataProps {
  [key: string]: string;
}

interface BookingProgressProps {
  onBookCar?: () => void;
  bookingStatus: string;
  booking?: BookingEntity;
  waitTimeTitle?: string;
}

function BookingProgress({
  onBookCar,
  bookingStatus,
  booking,
  waitTimeTitle,
}: BookingProgressProps) {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const destinationTooltipRef = useRef(null);
  const originTooltipRef = useRef(null);

  const TITLE_DATA: TitleDataProps = {
    [BookingEntity.status.COMPLETED]: t('booking.status.completed'),
    [BookingEntity.status.ARRIVAL_AT_CLIENT]: t(
      'booking.status.arrivalAtClient',
    ),
    [BookingEntity.status.CLIENT_ON_BOARD]: t('booking.status.clientOnBoard'),
    [BookingEntity.status.VEHICLE_CONFIRMED]: t(
      'booking.status.vehicleConfirm',
    ),
    [BookingEntity.status.AMOUNT_MONEY_RECEIVED]: t('booking.status.completed'),
  };

  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [currentWaitingTime, setCurrentWaitingTime] =
    useGlobalState('currentWaitingTime');
  const [maxWaitingTime, setMaxWaitingTime] = useGlobalState('maxWaitingTime');

  const waitTime = parseFloat((maxWaitingTime || 0).toFixed(0));

  const defaultPercent = currentWaitingTime
    ? `${((waitTime - currentWaitingTime) / waitTime) * 100}%`
    : '0%';
  const [progressPercent, setProgressPercent] = useState(defaultPercent);
  const [currentBookingId, _setCurrentBookingId] = useState(booking?.id || '');

  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const {setBookingId} = useBooking({});
  const isComplete = bookingStatus === BookingEntity.status.COMPLETED;

  const {data: bookingDetail, refetch: refetchDetail} =
    useBookingsServiceBookingControllerGetBooking(
      {
        id: currentBookingId,
      },
      undefined,
      {
        enabled: !!currentBookingId,
      },
    );

  const {data: dataDriver} = useDriverServiceDriverControllerGetDriver(
    {
      id: bookingDetail?.data?.driverId,
    },
    undefined,
    {
      enabled: !!bookingDetail?.data?.driverId,
    },
  );

  useAppState({
    appActiveHandler: () => {
      refetchDetail();

      queryClient.refetchQueries({
        queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
        type: 'all',
      });
      queryClient.refetchQueries({
        queryKey: [useBookingsServiceBookingControllerGetCurrentBookingKey],
        type: 'all',
      });
      queryClient.refetchQueries({
        queryKey: [useUsersServiceUserControllerGetUserKey],
        type: 'all',
      });
    },
  });

  const onDone = useCallback(() => {
    const id = bookingDetail?.data?.id || '';
    setBookingId(id);
    queryClient.refetchQueries({
      queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
      type: 'all',
    });
    queryClient.refetchQueries({
      queryKey: [useBookingsServiceBookingControllerGetCurrentBookingKey],
      type: 'all',
    });
    queryClient.refetchQueries({
      queryKey: [useUsersServiceUserControllerGetUserKey],
      type: 'all',
    });
    setBookingWithCodeParams({});
    setMaxWaitingTime(0);
    setCurrentWaitingTime(0);
    navigation.navigate('Main');
    onBookCar?.();
  }, [bookingDetail]);

  useEffect(() => {
    if (waitTime) {
      let seconds = currentWaitingTime || waitTime;
      intervalRef.current = setInterval(() => {
        const percent = ((waitTime - seconds) / waitTime) * 100;
        seconds = seconds - 5;
        if (seconds > 0) {
          setCurrentWaitingTime(seconds);
        }
        if (seconds < 0) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          LayoutAnimation.linear();
          setProgressPercent(`100%`);
        }
        setProgressPercent(`${percent.toFixed(2)}%`);
        LayoutAnimation.linear();
      }, 5000);
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [waitTime]);

  useEffect(() => {
    if (isComplete) {
      if (booking?.id) {
        refetchDetail();
      }
      timerRef.current = setTimeout(async () => {
        onDone();
      }, 2000);
    }
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [bookingStatus, onDone]);

  const renderBottomButton = () => {
    if (
      [
        BookingEntity.status.COMPLETED,
        BookingEntity.status.AMOUNT_MONEY_RECEIVED,
        BookingEntity.status.CLIENT_ON_BOARD,
        BookingEntity.status.ARRIVAL_AT_CLIENT,
      ].includes(bookingStatus as BookingEntity.status)
    ) {
      return null;
    }

    const navigateCancel = () => {
      navigation.navigate('BookingCancel', {
        bookingId: booking?.id,
        date: booking?.date,
      });
    };

    return (
      <WView style={styles.bottomBtnWrapper}>
        <TouchableOpacity onPress={navigateCancel} style={styles.cancelBtn}>
          <WText color={Colors.primary} type="semiBold14">
            {t('button.cancelBooking')}
          </WText>
        </TouchableOpacity>
      </WView>
    );
  };

  const driverName = `${bookingDetail?.data?.driver?.firstName ?? ''} ${
    bookingDetail?.data?.driver?.lastName ?? ''
  }`;

  const onCallDriver = () => {
    const phone = bookingDetail?.data?.driver?.phoneNumber || PHONE_SUPPORT;
    Linking.openURL(`tel:${phone}`);
  };

  const heightSuccessForm =
    Platform.OS === 'android'
      ? 390 + (bookingDetail?.data?.note ? 90 : 16)
      : 374 + (bookingDetail?.data?.note ? 90 : 56);
  const heightProgressForm =
    Platform.OS === 'android'
      ? 500 + (bookingDetail?.data?.note ? 90 : 16)
      : 484 + (bookingDetail?.data?.note ? 90 : 16);

  const MODAL_HEIGHT =
    bookingStatus === BookingEntity.status.COMPLETED ||
    bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
      ? heightSuccessForm
      : heightProgressForm;

  const MODAL_HEIGHT_WITH_NOTCH =
    bookingStatus === BookingEntity.status.COMPLETED ||
    bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
      ? 396 + (bookingDetail?.data?.note ? 60 : 0)
      : 506 + (bookingDetail?.data?.note ? 60 : 0);

  return (
    <WView
      borderTopLeftRadius={16}
      borderTopRightRadius={16}
      h={HAS_NOTCH ? MODAL_HEIGHT_WITH_NOTCH : MODAL_HEIGHT}>
      <WView
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        color={Colors.white}
        pHoz={16}>
        <WView
          borderBottomColor={Colors.neutral200}
          borderBottomWidth={1}
          borderTopLeftRadius={16}
          borderTopRightRadius={16}
          color={Colors.neutral100}
          pTop={4}
          style={styles.alignSelf}
          w={SCREEN_WIDTH}>
          <WView
            justifyBetween
            mBottom={
              bookingStatus === BookingEntity.status.CLIENT_ON_BOARD ||
              bookingStatus === BookingEntity.status.COMPLETED ||
              bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
                ? 24
                : 12
            }
            mHoz={16}
            mTop={12}
            row>
            <WView>
              <WText type="semiBold12">{TITLE_DATA?.[bookingStatus]}</WText>
              <WText color={Colors.neutral700} type="regular12">
                {`${t('booking.bookingID')} ${booking?.bookingCode ?? ''}`}
              </WText>
            </WView>
            {(bookingStatus === BookingEntity.status.VEHICLE_CONFIRMED ||
              bookingStatus === BookingEntity.status.CLIENT_ON_BOARD) && (
              <WText color={Colors.neutral700} type="regular12">
                {waitTimeTitle}
              </WText>
            )}
          </WView>

          {bookingStatus === BookingEntity.status.VEHICLE_CONFIRMED ? (
            <WView alignCenter mBottom={10} mHoz={16} row>
              <WView
                color={Colors.primary}
                h={2}
                maxWidth={SCREEN_WIDTH - 86}
                w={progressPercent}
              />
              <Image
                resizeMode="contain"
                source={Images.carHorizontalMarker}
                style={styles.carImg}
              />
              <Image
                resizeMode="contain"
                source={Images.userMarker}
                style={{height: 20, marginLeft: 'auto', width: 20}}
              />
              <WView
                color={Colors.neutral400}
                h={2}
                maxWidth={SCREEN_WIDTH - 32}
                style={{position: 'absolute', zIndex: -10}}
                w={'100%'}
              />
            </WView>
          ) : (
            <WView alignCenter mBottom={10} mHoz={16} row>
              {bookingStatus !== BookingEntity.status.ARRIVAL_AT_CLIENT &&
                bookingStatus !== BookingEntity.status.COMPLETED &&
                bookingStatus !==
                  BookingEntity.status.AMOUNT_MONEY_RECEIVED && (
                  <Image
                    resizeMode={
                      bookingStatus === BookingEntity.status.CLIENT_ON_BOARD
                        ? 'contain'
                        : undefined
                    }
                    source={
                      bookingStatus === BookingEntity.status.CLIENT_ON_BOARD
                        ? Images.userMarker
                        : Images.carHorizontalMarker
                    }
                    style={[
                      styles.carImg,
                      bookingStatus === BookingEntity.status.CLIENT_ON_BOARD &&
                        styles.pickUpMarker,
                    ]}
                  />
                )}
              <WView
                color={
                  bookingStatus === BookingEntity.status.CLIENT_ON_BOARD ||
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus ===
                    BookingEntity.status.AMOUNT_MONEY_RECEIVED ||
                  bookingStatus === BookingEntity.status.ARRIVAL_AT_CLIENT
                    ? Colors.primary
                    : Colors.neutral400
                }
                fill
                h={2}
              />

              {bookingStatus === BookingEntity.status.CLIENT_ON_BOARD && (
                <Image
                  source={Images.carHorizontalMarker}
                  style={styles.carHorizontalImg}
                />
              )}

              <WView
                color={
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus ===
                    BookingEntity.status.AMOUNT_MONEY_RECEIVED ||
                  bookingStatus === BookingEntity.status.ARRIVAL_AT_CLIENT
                    ? Colors.primary
                    : Colors.neutral400
                }
                fill
                h={2}
              />
              {(bookingStatus === BookingEntity.status.ARRIVAL_AT_CLIENT ||
                bookingStatus === BookingEntity.status.COMPLETED ||
                bookingStatus ===
                  BookingEntity.status.AMOUNT_MONEY_RECEIVED) && (
                <Image
                  source={Images.carHorizontalMarker}
                  style={[
                    styles.carImg,
                    bookingStatus !== BookingEntity.status.ARRIVAL_AT_CLIENT &&
                      styles.mRight14,
                  ]}
                />
              )}

              <Image
                resizeMode="contain"
                source={
                  bookingStatus === BookingEntity.status.CLIENT_ON_BOARD ||
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
                    ? Images.destinationMarker
                    : Images.userMarker
                }
                style={
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus ===
                    BookingEntity.status.AMOUNT_MONEY_RECEIVED ||
                  bookingStatus === BookingEntity.status.CLIENT_ON_BOARD
                    ? styles.destinationMarkerImg
                    : styles.userMarkerImg
                }
              />
            </WView>
          )}
        </WView>

        {bookingStatus !== BookingEntity.status.COMPLETED &&
          bookingStatus !== BookingEntity.status.AMOUNT_MONEY_RECEIVED && (
            <WView alignCenter justifyBetween mBottom={6} mTop={12} row>
              <WView alignCenter fill row>
                <Image
                  source={Images.defaultAvatar}
                  style={styles.driverAvatar}
                />
                <WView mLeft={12}>
                  <WText type="medium14">{driverName}</WText>
                  <WView alignCenter gap={10} mTop={2} row>
                    <WView alignCenter gap={2} row>
                      <WText color={Colors.neutral600} type="regular12">
                        {dataDriver?.data?.avgRating?.toFixed(1) || 'N/A'}
                      </WText>
                      <Icon
                        color={Colors.starYellow}
                        name="Fill-Star"
                        size={12}
                      />
                    </WView>

                    {bookingDetail?.data?.vehicle?.licensePlateNumber && (
                      <WView alignCenter gap={10} row>
                        <WView color={Colors.neutral200} h={16} w={1} />
                        <WText color={Colors.neutral600} type="regular12">
                          {bookingDetail?.data?.vehicle?.licensePlateNumber}
                        </WText>
                      </WView>
                    )}

                    {bookingDetail?.data?.vehicle?.vehicleNumber && (
                      <WView alignCenter gap={10} row>
                        <WView color={Colors.neutral200} h={16} w={1} />
                        <WText color={Colors.neutral600} type="regular12">
                          {`#${bookingDetail?.data?.vehicle?.vehicleNumber}`}
                        </WText>
                      </WView>
                    )}
                  </WView>
                </WView>
              </WView>
              <WView color={Colors.neutral200} h={'90%'} w={1} />

              <WView alignCenter mLeft={16} row>
                {!bookingDetail?.data?.driver?.phoneNumber && (
                  <TouchableWithoutFeedback onPress={onCallDriver}>
                    <WView
                      alignCenter
                      borderRadius={16}
                      color={Colors.neutral200}
                      h={32}
                      justifyCenter
                      w={32}>
                      <Icon name="Outline-PhoneCall" size={20} />
                    </WView>
                  </TouchableWithoutFeedback>
                )}
              </WView>
            </WView>
          )}
      </WView>
      <WView color={Colors.white}>
        <WView alignCenter h={SEARCH_INPUT_HEIGHT} justifyBetween mVer={12} row>
          <WView alignCenter pHoz={16}>
            <Image
              resizeMode="contain"
              source={Images.userMarker}
              style={{height: 20, width: 20}}
            />
            <Image resizeMode="contain" source={Images.dotLine} />
            <Icon color={Colors.primary} name="Fill-MapPin" size={22} />
          </WView>

          <WView fill mLeft={16} pRight={16} pVer={4}>
            <WView alignItems="flex-start" fill justifyBetween row>
              {(bookingWithCodeParams?.originLocation?.address?.name?.length ||
                0) > 50 && (
                <WTooltip
                  direction="right"
                  label={
                    bookingWithCodeParams?.originLocation?.address
                      ?.name as string
                  }
                  ref={originTooltipRef}
                  showIcon={false}
                  style={styles.absolute}
                  width={SCREEN_WIDTH - 80}
                />
              )}
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  originTooltipRef?.current?.onToggle();
                }}
                style={styles.flex1}>
                <WText color={Colors.neutral700} fill type="regular14">
                  {bookingWithCodeParams?.originLocation?.address?.name}
                  {/* {t('booking.yourLocation')} */}
                </WText>
              </TouchableOpacity>
              <Icon
                color={
                  bookingStatus === BookingEntity.status.CLIENT_ON_BOARD ||
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
                    ? Colors.success
                    : Colors.neutral300
                }
                name={
                  bookingStatus === BookingEntity.status.CLIENT_ON_BOARD ||
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
                    ? 'Fill-CheckCircle'
                    : 'Outline-CheckCircle'
                }
                size={20}
              />
            </WView>
            <WView alignItems="flex-end" fill justifyBetween row>
              {(bookingWithCodeParams?.destinationLocation?.address?.name
                ?.length || 0) > 50 && (
                <WTooltip
                  direction="right"
                  label={
                    bookingWithCodeParams?.destinationLocation?.address
                      ?.name as string
                  }
                  ref={destinationTooltipRef}
                  showIcon={false}
                  style={styles.absolute}
                  width={SCREEN_WIDTH - 80}
                />
              )}
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  destinationTooltipRef?.current?.onToggle();
                }}
                style={styles.mRight4}>
                <WText numberOfLine={1} type="regular14">
                  {bookingWithCodeParams?.destinationLocation?.address?.name}
                </WText>
              </TouchableOpacity>
              <Icon
                color={
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
                    ? Colors.success
                    : Colors.neutral300
                }
                name={
                  bookingStatus === BookingEntity.status.COMPLETED ||
                  bookingStatus === BookingEntity.status.AMOUNT_MONEY_RECEIVED
                    ? 'Fill-CheckCircle'
                    : 'Outline-CheckCircle'
                }
                size={20}
              />
            </WView>
          </WView>
        </WView>
      </WView>
      <SafeAreaView
        style={{
          backgroundColor: Colors.white,
        }}>
        <BookingLineInfo
          booking={bookingDetail?.data as BookingEntity}
          enableTooltip={true}
          style={styles.margin0}
        />
        {bookingDetail?.data?.note && (
          <WView
            borderColor={Colors.neutral600}
            borderRadius={6}
            borderWidth={1}
            h={56}
            mBottom={8}
            mHoz={16}
            pHoz={4}
            pVer={8}>
            <WText type="regular14">{bookingDetail?.data?.note}</WText>
          </WView>
        )}
        {renderBottomButton()}
      </SafeAreaView>
    </WView>
  );
}

export default BookingProgress;

const styles = StyleSheet.create({
  absolute: {position: 'absolute'},
  alignSelf: {alignSelf: 'center'},
  bottomBtnWrapper: {
    alignSelf: 'center',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingHorizontal: 16,
    width: SCREEN_WIDTH,
  },
  cancelBtn: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderTopColor: Colors.neutral200,
    borderTopWidth: 1,
    height: 46,
    justifyContent: 'center',
    marginBottom: 16,
    marginTop: 8,
    minWidth: 80,
  },
  carHorizontalImg: {height: 16, width: 32},
  carImg: {height: 16, width: 32},
  destinationMarkerImg: {
    bottom: -8,
    height: 38,
    position: 'absolute',
    right: -8,
    tintColor: Colors.primary,
    width: 22,
  },
  driverAvatar: {
    borderColor: Colors.neutral150,
    borderRadius: 24,
    borderWidth: 1,
    height: 43,
    width: 43,
  },
  flex1: {
    flex: 1,
  },
  mRight14: {marginRight: 14},
  mRight4: {
    flex: 1,
    marginRight: 4,
  },
  margin0: {marginHorizontal: 0},
  pickUpMarker: {
    bottom: 0,
    height: 20,
    left: 0,
    position: 'absolute',
    width: 20,
    zIndex: 10,
  },
  userMarkerImg: {
    height: 20,
    width: 20,
  },
});
