import {useNavigation} from '@react-navigation/native';
import {
  BillingDetails,
  CardForm,
  confirmSetupIntent,
} from '@stripe/stripe-react-native';
import React, {useState} from 'react';
import {Platform, SafeAreaView, StyleSheet} from 'react-native';
import useLoading from '@utils/hooks/useLoading';
import {showErrorAlert} from '@utils/Tools';
import {queryClient} from '@react-query/queryClient';
import {Colors} from '@themes';
import {
  useUsersServiceUserControllerCreateUserCreditCard,
  useUsersServiceUserControllerListCardsKey,
} from '@queries';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {SCREEN_WIDTH} from '@themes/Constants';
import {NavHeader, WButton} from '@components';

function AddCreditCard() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {showLoading, dismissLoading} = useLoading();

  const [allowSubmit, setAllowSubmit] = useState(false);

  const {mutate: addCard} = useUsersServiceUserControllerCreateUserCreditCard();

  return (
    <SafeAreaView>
      <NavHeader title={t('button.addCard')} />
      <CardForm
        cardStyle={styles.card}
        onFormComplete={() => {
          setAllowSubmit(true);
        }}
        style={styles.cardForm}
      />
      <WButton
        disabled={!allowSubmit}
        label={t('button.confirm')}
        onPress={() => {
          addCard(undefined, {
            onSuccess: async res => {
              showLoading();
              const billingDetails: BillingDetails = {};
              const {error} = await confirmSetupIntent(
                res.data?.clientSecret || '',
                {
                  paymentMethodData: {
                    billingDetails,
                  },
                  paymentMethodType: 'Card',
                },
              );
              if (error) {
                //Handle the error
                showErrorAlert({message: error.message});
                dismissLoading();
              } else {
                dismissLoading();
                navigation.goBack();
                setTimeout(() => {
                  queryClient.refetchQueries({
                    queryKey: [useUsersServiceUserControllerListCardsKey],
                    type: 'active',
                  });
                }, 1000);
              }
            },
          });
        }}
        style={styles.submitBtn}
      />
    </SafeAreaView>
  );
}

export default AddCreditCard;

const styles = StyleSheet.create({
  card: {
    borderColor: Colors.neutral300,
    borderRadius: 12,
    borderWidth: 1,
    cursorColor: Colors.neutral900,
    placeholderColor: Colors.neutral900,
  },
  cardForm: {
    alignSelf: 'center',
    height: Platform.OS === 'android' ? 260 : 200,
    marginTop: 32,
    width: SCREEN_WIDTH - 32,
  },
  submitBtn: {marginHorizontal: 16, marginTop: 24},
});
