import {RouteProp, useNavigation} from '@react-navigation/native';
import {
  isPlatformPaySupported,
  usePlatformPay,
} from '@stripe/stripe-react-native';
import React, {useEffect, useState} from 'react';
import {
  Image,
  Linking,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import {PaymentIcon} from 'react-native-payment-icons';
import Icon from 'react-native-vector-icons/icomoon';
import {showErrorAlert} from '@utils/Tools';
import useLoading from '@utils/hooks/useLoading';
import {useAppState} from '@utils/hooks/useAppState';
import {NavHeader, WText, WView} from '@components';
import {ListPaymentRouteProps} from '@global';
import {
  useUsersServiceUserControllerListCards,
  useUsersServiceUserControllerListCardsKey,
} from '@queries';
import {CardEntity} from '@requests';
import {Colors, Images} from '@themes';
import {CASH, CardViewType, WALLET, isReleaseMode} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

const IS_TEST_ENV = !isReleaseMode;

interface ListPaymentProps {
  route: RouteProp<ListPaymentRouteProps, 'ListPaymentProps'>;
}

function ListPayment({route}: ListPaymentProps) {
  const {
    onApply,
    isFromBooking,
    defaultPayment,
    hideHeader,
    hideCash,
    hidePayment = false,
  } = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const {dismissLoading, showLoading} = useLoading();

  const [isWalletSupported, setIsWalletSupported] = useState(false);

  const {isPlatformPaySupported: isPlatformGooglePaySupported} =
    usePlatformPay();

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    CardEntity | undefined
  >();

  useEffect(() => {
    setSelectedPaymentMethod(defaultPayment);
  }, [defaultPayment]);

  const checkPlatformPaySupport = async () => {
    try {
      if (Platform.OS === 'android') {
        const googlePaySupportedResponse = await isPlatformGooglePaySupported({
          googlePay: {testEnv: IS_TEST_ENV},
        });
        setIsWalletSupported(googlePaySupportedResponse);
      } else {
        const applePaySupportedResponse = await isPlatformPaySupported();
        setIsWalletSupported(applePaySupportedResponse);
      }
    } catch (error) {
      showErrorAlert({message: error?.message});
    }
  };

  useAppState({
    appActiveHandler: () => {
      if (!isWalletSupported) {
        checkPlatformPaySupport();
      }
    },
  });

  useEffect(() => {
    checkPlatformPaySupport();
  }, [isPlatformPaySupported]);

  const {data: listCard, isFetching} = useUsersServiceUserControllerListCards([
    useUsersServiceUserControllerListCardsKey,
  ]);

  useEffect(() => {
    if (isFetching) {
      showLoading();
    } else {
      if (!defaultPayment && isFromBooking) {
        setSelectedPaymentMethod(
          listCard?.data?.find((ele: CardEntity) => ele?.isDefault),
        );
      }
      dismissLoading();
    }
  }, [isFetching]);

  const setUpWallet = async () => {
    // Define the URL for Google Pay
    const walletAppUrl =
      Platform?.OS === 'android'
        ? 'https://pay.google.com/gp/v/pubtransaction'
        : 'shoebox://';

    // Check if the Google Pay app is installed
    const isInstalled = await Linking.canOpenURL(walletAppUrl);

    // If Google Pay app is installed, open it; otherwise, open the Google Pay website
    if (isInstalled) {
      // Open Google Pay app
      await Linking.openURL(walletAppUrl);
    } else {
      /*
       * Google Pay app is not installed, open Google Pay website
       * You can also handle this case differently, such as displaying a message to the user
       * await Linking.openURL('https://pay.google.com/');
       */
      const IOS_LINK =
        'itms-apps://itunes.apple.com/us/app/apple-store/id1160481993?mt=8';
      const ANDROID_LINK =
        'market://details?id=com.google.android.apps.nbu.paisa.user';
      Linking.openURL(
        Platform.select({
          android: ANDROID_LINK,
          ios: IOS_LINK,
        }) as string,
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {!hideHeader && <NavHeader title={t('payment.title')} />}
      <ScrollView>
        <WView padding={16}>
          {!hidePayment && (
            <TouchableWithoutFeedback
              // onPress={Platform.OS === 'android' ? googlePay : applePay}
              onPress={() => {
                if (isWalletSupported) {
                  if (isFromBooking) {
                    onApply?.(WALLET);
                    setSelectedPaymentMethod(WALLET);
                  }
                } else {
                  setUpWallet();
                  // Linking.openURL('shoebox://');
                }
              }}>
              <WView
                alignCenter
                borderColor={
                  selectedPaymentMethod?.id === WALLET.id
                    ? Colors.neutral900
                    : Colors.neutral300
                }
                borderRadius={8}
                borderWidth={1}
                mTop={8}
                padding={16}
                row>
                <Image
                  resizeMode="contain"
                  source={
                    Platform.OS === 'android'
                      ? Images.googlePay
                      : Images.applePay
                  }
                  style={{height: 28, width: 48}}
                />
                <WText marginLeft={8} type="medium14">
                  {Platform.OS === 'android'
                    ? t('payment.googlePay')
                    : t('payment.applePay')}
                </WText>
                {!isWalletSupported && Platform.OS === 'ios' && (
                  <WText marginLeft={'auto'} type="regular12">
                    {t('payment.clickToSetUp')}
                  </WText>
                )}
              </WView>
            </TouchableWithoutFeedback>
          )}

          {listCard?.data?.map((item: CardEntity) => {
            const isDefault = item?.isDefault;
            const isSelected = selectedPaymentMethod?.id === item?.id;
            return (
              <TouchableWithoutFeedback
                key={item?.id}
                onPress={() => {
                  if (isFromBooking) {
                    onApply?.(item);
                    setSelectedPaymentMethod(item);
                  } else {
                    navigation.navigate('CardDetailModal', {card: item});
                  }
                }}>
                <WView
                  alignCenter
                  borderColor={
                    isSelected ? Colors.neutral900 : Colors.neutral300
                  }
                  borderRadius={8}
                  borderWidth={1}
                  mTop={8}
                  padding={16}
                  row>
                  <PaymentIcon type={item?.brand as CardViewType} />
                  <WText marginLeft={8} type="medium14">
                    {item?.last4}
                  </WText>
                  {isDefault && (
                    <WText
                      color={Colors.neutral500}
                      style={styles.defaultText}
                      type="regular12">
                      {t('payment.default')}
                    </WText>
                  )}
                </WView>
              </TouchableWithoutFeedback>
            );
          })}
          {isFromBooking && !hideCash && (
            <TouchableWithoutFeedback
              onPress={() => {
                if (isFromBooking) {
                  onApply?.(CASH);
                }
              }}>
              <WView
                alignCenter
                borderColor={
                  selectedPaymentMethod?.id === CASH.id
                    ? Colors.neutral900
                    : Colors.neutral300
                }
                borderRadius={8}
                borderWidth={1}
                mTop={8}
                padding={16}
                row>
                <Icon name="Outline-Money" size={24} />
                <WText marginLeft={8} type="medium14">
                  {t('payment.cash')}
                </WText>
              </WView>
            </TouchableWithoutFeedback>
          )}
          {selectedPaymentMethod?.id === CASH.id && (
            <WView alignCenter mTop={8} row>
              <Icon
                color={Colors.warning700}
                name="Outline-WarningCircle"
                size={18}
              />
              <WText color={Colors.warning700} marginLeft={4} type="regular14">
                {t('payment.cashWaring')}
              </WText>
            </WView>
          )}

          <WText marginTop={24} type="medium14">
            {t('button.addMethod')}
          </WText>
          {!hideCash && (
            <WText marginTop={4} type="regular14">
              {t('payment.promoCodeApplied')}
            </WText>
          )}

          <TouchableWithoutFeedback
            onPress={() => {
              navigation.navigate('AddCreditCard', {isTopBarEnable: false});
            }}>
            <WView
              alignCenter
              borderColor={Colors.neutral300}
              borderRadius={8}
              borderWidth={1}
              mTop={8}
              padding={16}
              row>
              <Icon name="Outline-CreditCard" size={20} />
              <WText marginLeft={8} type="medium14">
                {t('button.addCard')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>
        </WView>
      </ScrollView>
    </SafeAreaView>
  );
}

export default ListPayment;

const styles = StyleSheet.create({
  container: {flex: 1},
  defaultText: {fontStyle: 'italic', marginLeft: 'auto'},
});
