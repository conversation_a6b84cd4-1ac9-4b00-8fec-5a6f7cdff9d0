/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable max-lines */
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {isPlatformPaySupported} from '@stripe/stripe-react-native';
import i18next from 'i18next';
import React, {useCallback, useEffect, useState} from 'react';
import {
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Config from 'react-native-config';
import Icon from 'react-native-vector-icons/icomoon';
import LottieView from 'lottie-react-native';
import * as Sentry from '@sentry/react-native';
import Blogs from './components/Blogs';
import BannerSlider from './components/BannerSlider';
import ToolTipLoyalPoints from './components/ToolTipLoyalPoints';
import PopupBanner from './components/PopupBanner';
import MixPanelSdk from '@utils/mixPanelSdk';
import {WImage, WText, WTouchable, WView} from '@components';
import AvatarUser from '@components/AvatarUser';
import FocusAwareStatusBar from '@components/FocusAwareStatusBar';
import CurrentBookingModal from '@components/Modal/CurrentBookingModal';
import {
  useBookingsServiceBookingControllerGetBookingOfMe,
  useNotificationsServiceNotificationControllerGetNotificationsKey,
  useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
  useUsersServiceUserControllerGetUser,
  useUsersServiceUserControllerGetUserKey,
  useUsersServiceUserControllerIsVerifyPhoneNumber,
  useUsersServiceUserControllerListCards,
  useUsersServiceUserControllerListCardsKey,
  useVersionAppServiceVersionAppControllerCheckVersion,
  useVersionAppServiceVersionAppControllerCheckVersionKey,
} from '@queries';
import {UserDataProps, useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import {BookingEntity, CreateBookingWithCodeDto} from '@requests';
import {Colors, Icons, Images} from '@themes';
import {
  ACTIVE_OPACITY,
  APP_VERSION,
  IS_ANDROID,
  LANGUAGE,
  SCREEN_WIDTH,
  isReleaseMode,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useFirebase from '@utils/hooks/useFirebase';
import {useLocationPermission} from '@utils/hooks/useGeoLocation';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {getStoreVersion, onRedirectToStore, showErrorAlert} from '@utils/Tools';
import AppsFlyer from '@utils/appsFlyerSdk';
import {useSpecialDay} from '@utils/hooks/useSpecialDay';
import {EDaySpecial} from '@themes/Enums';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';
import {useTipOpenApp} from '@utils/hooks/useTipOpenApp';
import {useBirthdayEvent} from '@utils/hooks/useBirthdayEvent';
import {LOTTIE} from '@themes/Lottie';

const SEARCH_INPUT_HEIGHT = 52;
const IS_TEST_ENV = !isReleaseMode;
function Home() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  useFirebase();

  const {specialDay, dataSpecial} = useSpecialDay();
  const {isHappening} = useBirthdayEvent();
  const [isWalletSupported, setIsWalletSupported] = useState(false);
  const [versionStore, setVersionStore] = useState('');

  const {currentUserBooking, showCurrentBooking, setShowCurrentBooking} =
    useBookingGlobalStatus();

  const [_bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [_, setCurrentBooking] = useGlobalState('currentBooking');
  const [userData, setUserData] = useGlobalState('userData');

  const {data: listCard} = useUsersServiceUserControllerListCards([
    useUsersServiceUserControllerListCardsKey,
  ]);
  const {data: userRes, isFetching} = useUsersServiceUserControllerGetUser([
    useUsersServiceUserControllerGetUserKey,
  ]);

  const {data} = useBookingsServiceBookingControllerGetBookingOfMe({
    limit: 1,
    offset: 0,
    status: BookingEntity.status.COMPLETED,
  });

  const {refetch: refetchVerifyPhone} =
    useUsersServiceUserControllerIsVerifyPhoneNumber(undefined, {
      enabled: false,
    });

  const {onGotoTip} = useTipOpenApp({
    dataLastCompleted: data?.data?.items?.[0],
  });

  const eventValues = {
    email: userData?.user?.email,
    phoneNumber: userData?.user?.phoneNumber,
    userName: userData?.user?.fullName,
  };

  const {data: dataVersion, isLoading: isLoadingVersion} =
    useVersionAppServiceVersionAppControllerCheckVersion(
      {
        platform: IS_ANDROID ? 'ANDROID' : 'IOS',
        version: APP_VERSION,
      },
      [useVersionAppServiceVersionAppControllerCheckVersionKey],
      {enabled: !!versionStore && versionStore !== APP_VERSION},
    );

  useEffect(() => {
    const getVersionStore = async () => {
      const res = await getStoreVersion();
      setVersionStore(res);
    };
    getVersionStore();
  }, []);

  useEffect(() => {
    if (userRes?.data) {
      const newData = {
        ...userData,
        user: {
          ...userRes?.data,
          avatarUrl: userRes?.data?.avatarUrl,
        },
      };
      setUserData(newData as UserDataProps);
    }
  }, [isFetching, userRes?.data]);

  useLocationPermission();

  const onCheckStoreVersion = async () => {
    try {
      if (versionStore !== APP_VERSION && dataVersion?.data?.forceUpdate) {
        navigation.navigate('ActionModal', {
          confirmLabel: t('button.updateNow'),
          content: t('updateAppVersion.content'),
          enableCloseOnMask: false,
          hasCancelBtn: false,
          icon: 'Outline-RocketLaunch',
          iconSize: 55,
          image: Images.roundLogo,
          onApply: () => {
            onRedirectToStore();
          },
          title: t('updateAppVersion.title'),
          type: 'default',
        });
      }
    } catch (error) {
      showErrorAlert({message: error?.message});
    }
  };

  const checkWalletSupported = async () => {
    try {
      const params =
        Platform.OS === 'android'
          ? {googlePay: {testEnv: IS_TEST_ENV}}
          : undefined;
      const applePaySupportedResponse = await isPlatformPaySupported(params);
      setIsWalletSupported(applePaySupportedResponse);
    } catch (error) {
      showErrorAlert({message: error?.message});
    }
  };

  useEffect(() => {
    if (
      dataVersion?.data?.forceUpdate &&
      versionStore &&
      Config.APP_ENV_NAME === 'RELEASE'
    ) {
      onCheckStoreVersion();
    }
  }, [dataVersion, versionStore]);

  useEffect(() => {
    const shouldGotoTip =
      // !dataVersion?.data?.forceUpdate &&
      !isLoadingVersion && (data?.data?.items?.length ?? 0) > 0;

    if (shouldGotoTip) {
      onGotoTip();
    }
  }, [dataVersion?.data?.forceUpdate, data?.data?.items, isLoadingVersion]);

  const refetchAPINotification = useCallback(() => {
    queryClient.refetchQueries({
      queryKey: [
        useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
      ],
      type: 'all',
    });
    queryClient.refetchQueries({
      queryKey: [
        useNotificationsServiceNotificationControllerGetNotificationsKey,
      ],
      type: 'all',
    });
  }, []);

  useEffect(() => {
    if (!isFocused) return;
    refetchAPINotification();
    checkWalletSupported();
  }, [isFocused]);

  const welcomeTitle =
    specialDay !== EDaySpecial.Normal
      ? `${t('home.happyHoliday')} \n${
          userData?.user?.fullName || t('home.there')
        }`
      : `${t('home.hello')}, ${userData?.user?.fullName || t('home.there')}!`;
  const loyalPoint = userData?.user?.currentPoint || 0;

  const onGoProfile = () => {
    navigation.navigate('Profile', {isTopBarEnable: false});
  };

  const onUpdatePhoneNumber = (isNeedRide = false) => {
    navigation.navigate('VerifyPhoneModal', {
      defaultPhoneNumber: userData?.user?.phoneNumber,
      onApply: () => {
        if (isNeedRide) {
          return navigation.navigate('ModalNeedRide');
        }
        navigation.navigate('SelectDestination', {
          isTopBarEnable: false,
        });
      },
    });
  };

  const onGoToBooking = async (isSchedule = false) => {
    const dataVerify = await refetchVerifyPhone();
    const isVerifyPhone = dataVerify?.data?.data;
    if (!userData?.user?.phoneNumber || !isVerifyPhone) {
      Sentry.captureMessage('Unexpected phone verification failure', {
        extra: {
          isVerifyPhone,
          phoneNumber: userData?.user?.phoneNumber,
          rawVerifyResponse: dataVerify,
          userId: userData?.user?.id,
        },
        level: 'warning',
        tags: {context: 'onGoToBooking'},
      });
      return onUpdatePhoneNumber(false);
    }
    if (showCurrentBooking) {
      showErrorAlert({message: t('error.inAnotherBooking')});
    } else {
      MixPanelSdk.logEvent({
        eventName: 'booking_start',
        eventValues,
      });
      AppsFlyer.logEvent({
        eventName: 'booking_start',
        eventValues,
      });
      navigation.navigate('SelectDestination', {
        isSchedule,
        isTopBarEnable: false,
      });
    }
  };

  const isFirstRide = data?.data?.items?.find(
    (e: BookingEntity) => e?.status === BookingEntity.status.COMPLETED,
  );

  const isEnglish = i18next.language === LANGUAGE.EN;

  const onClickBanner = () => {
    onGoToBooking();
    MixPanelSdk.logEvent({
      eventName: 'booking_start_homescreen_banner',
      eventValues,
    });
  };

  const handleNavigateNeedRide = async () => {
    const dataVerify = await refetchVerifyPhone();
    const isVerifyPhone = dataVerify?.data?.data;
    if (!userData?.user?.phoneNumber || !isVerifyPhone) {
      return onUpdatePhoneNumber(true);
    }
    if (showCurrentBooking) {
      return showErrorAlert({message: t('error.inAnotherBooking')});
    }

    navigation.navigate('ModalNeedRide');
  };

  const renderAnimationBanner = () => {
    if (isHappening) {
      return (
        <LottieView
          autoPlay
          loop
          source={LOTTIE.newYear}
          speed={0.5}
          style={styles.bannerAnimation}
        />
      );
    }
    if (specialDay === EDaySpecial.Normal) return;
    const animationSource = dataSpecial?.banner;

    if (!animationSource) return null;
    return (
      <LottieView
        autoPlay
        loop
        source={animationSource}
        speed={0.5}
        style={[
          styles.bannerAnimation,
          specialDay === EDaySpecial.Noel && {width: '100%'},
        ]}
      />
    );
  };

  return (
    <WView color={Colors.white} fill>
      <FocusAwareStatusBar backgroundColor={Colors.primary} />
      <WView color={Colors.primary} fill justifyContent="flex-end">
        <SafeAreaView style={styles.container}>
          <WView alignCenter mTop={16} row>
            <AvatarUser
              avatarUrl={userData?.user?.avatarUrl}
              fullName={userData?.user?.fullName}
              onPress={onGoProfile}
              size={48}
            />

            <WText color={Colors.white} fill marginLeft={8} type="semiBold16">
              {welcomeTitle}
            </WText>
            <ToolTipLoyalPoints loyalPoint={loyalPoint} />
          </WView>
        </SafeAreaView>
        <Image
          resizeMode="cover"
          source={Images.homeBackground}
          style={{
            height: SCREEN_WIDTH / 1.5,
            width: SCREEN_WIDTH,
          }}
        />
        {renderAnimationBanner()}
      </WView>

      <WView color={Colors.white} fill>
        <WView
          alignCenter
          borderColor={Colors.neutral100}
          borderRadius={12}
          borderWidth={4}
          color={Colors.white}
          mTop={-SEARCH_INPUT_HEIGHT}
          minHeight={SEARCH_INPUT_HEIGHT}
          padding={2}
          row
          style={[styles.alignSelf, GlobalStyle.shadow]}
          w={SCREEN_WIDTH - 32}>
          <TouchableWithoutFeedback onPress={() => onGoToBooking()}>
            <WView alignCenter borderRadius={8} fill pHoz={12} pVer={16} row>
              <Icon
                color={Colors.neutral500}
                name="Outline-MagnifyingGlass"
                size={24}
              />
              <WText color={Colors.neutral700} marginLeft={8} type="regular14">
                {t('home.search')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>
          <WTouchable
            borderRadius={8}
            center
            color={Colors.neutral150}
            gap={12}
            mRight={12}
            onPress={handleNavigateNeedRide}
            pHoz={10}
            pVer={12}
            row>
            <WView alignCenter gap={5} row>
              <WImage size={14} source={Icons.icThunder} />
              <WText type="semiBold12">{t('modalRide.now')}</WText>
            </WView>
            <Icon
              color={Colors.neutral500}
              name="Outline-CaretDown"
              size={16}
            />
          </WTouchable>
        </WView>
        <ScrollView style={styles.scrollContainer}>
          {listCard?.data?.length === 0 && !isWalletSupported && (
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.navigate('ListPayment', {isTopBarEnable: false});
              }}>
              <WView style={styles.setupPaymentContainer}>
                <Icon
                  color={Colors.warning600}
                  name="Outline-CreditCard"
                  size={24}
                />
                <WView fill pHoz={8}>
                  <WText
                    color={Colors.neutral850}
                    lineHeight={22}
                    type="regular14">
                    {t('home.setupPayment')}
                  </WText>
                </WView>
                <Icon
                  color={Colors.neutral700}
                  name="Outline-CaretRight"
                  size={20}
                />
              </WView>
            </TouchableWithoutFeedback>
          )}
          <BannerSlider onGoToBooking={onGoToBooking} />
          {!isFirstRide && (
            <WView
              mBottom={12}
              style={[styles.couponContainer, GlobalStyle.shadowSearchInput]}>
              <TouchableOpacity
                activeOpacity={ACTIVE_OPACITY}
                onPress={onClickBanner}>
                <Image
                  resizeMode="cover"
                  source={
                    isEnglish
                      ? Images.firstRideBanner
                      : Images.firstRideBannerFr
                  }
                  style={styles.couponImg}
                />
              </TouchableOpacity>
            </WView>
          )}
          <Blogs />
        </ScrollView>
      </WView>
      {showCurrentBooking && (
        <SafeAreaView>
          <CurrentBookingModal
            booking={currentUserBooking}
            onClose={() => {
              setShowCurrentBooking(false);
            }}
            onGoToBooking={async () => {
              setCurrentBooking(currentUserBooking);
              setBookingWithCodeParams({
                destinationLocation: {
                  address: currentUserBooking?.destinationAddress,
                  geo: {
                    latitude: currentUserBooking?.destinationLocation?.[1],
                    longitude: currentUserBooking?.destinationLocation?.[0],
                  },
                },
                originLocation: {
                  address: currentUserBooking?.originAddress,
                  geo: {
                    latitude: currentUserBooking?.originLocation?.[1],
                    longitude: currentUserBooking?.originLocation?.[0],
                  },
                },
              } as CreateBookingWithCodeDto);
              navigation.navigate('BookingMaps', {
                defaultBookingDetail: currentUserBooking,
                isTopBarEnable: false,
              });
            }}
          />
        </SafeAreaView>
      )}
      <PopupBanner />
    </WView>
  );
}

export default Home;

const styles = StyleSheet.create({
  alignSelf: {alignSelf: 'center', zIndex: 99},
  bannerAnimation: {
    alignSelf: 'center',
    height: '100%',
    position: 'absolute',
    width: '70%',
    zIndex: -2,
  },

  container: {
    left: 16,
    position: 'absolute',
    right: 16,
    top: 0,
  },

  couponContainer: {
    alignSelf: 'center',
  },

  couponImg: {
    alignSelf: 'center',
    borderRadius: 12,
    height: 120,
    width: SCREEN_WIDTH - 32,
  },

  scrollContainer: {marginTop: -4, paddingTop: 4},
  setupPaymentContainer: {
    alignItems: 'center',
    backgroundColor: Colors.warning100,
    borderColor: Colors.warning600,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 24,
    padding: 12,
    width: SCREEN_WIDTH - 32,
  },
});
