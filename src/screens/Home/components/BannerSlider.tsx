import React, {useState} from 'react';
import {isEmpty} from 'lodash-es';
import Carousel, {CarouselRenderItem} from 'react-native-reanimated-carousel';
import ItemBanner from './ItemBanner';
import {WView} from '@components/index';
import {SCREEN_WIDTH} from '@themes/Constants';
import {Colors} from '@themes/index';
import {useBannersServiceBannerControllerGetBannerDisplayed} from '@queries';
import {BannerEntity} from '@requests';

interface Props {
  onGoToBooking: (isSchedule?: boolean) => void;
}
function BannerSlider({onGoToBooking}: Props) {
  const [activeIndex, setActiveIndex] = useState(0);
  const {data: dataBanner} =
    useBannersServiceBannerControllerGetBannerDisplayed();

  const handleSnapToItem = (index: number) => {
    setActiveIndex(index);
  };

  const renderItem: CarouselRenderItem<BannerEntity> = ({item}) => (
    <ItemBanner item={item} onGoToBooking={onGoToBooking} />
  );

  if (isEmpty(dataBanner?.data)) {
    return null;
  }

  return (
    <WView mVer={16}>
      <Carousel
        autoPlay={true}
        autoPlayInterval={3000}
        data={(dataBanner?.data ?? []) as BannerEntity[]}
        height={SCREEN_WIDTH / 3}
        loop
        mode="parallax"
        modeConfig={{
          parallaxScrollingOffset: 50,
          parallaxScrollingScale: 0.9,
        }}
        onProgressChange={(_, absoluteProgress) => {
          handleSnapToItem(Math.round(absoluteProgress));
        }}
        pagingEnabled
        renderItem={renderItem}
        scrollAnimationDuration={1000}
        width={SCREEN_WIDTH}
      />
      {dataBanner?.data?.length > 1 && (
        <WView center gap={8} mTop={12} row>
          {dataBanner?.data?.map((_: any, index: number) => {
            const isSelected = index === activeIndex;
            return (
              <WView
                borderRadius={6}
                color={isSelected ? Colors.primary : Colors.primary400}
                h={6}
                key={index}
                w={isSelected ? 16 : 6}
              />
            );
          })}
        </WView>
      )}
    </WView>
  );
}

export default BannerSlider;
