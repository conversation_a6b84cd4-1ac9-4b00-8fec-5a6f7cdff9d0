import React, {useMemo, useState} from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import {ERide, SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import ModalWrapper from '@components/Modal/ModalWrapper';
import WText from '@components/WText';
import {WButton, WTouchable, WView} from '@components/index';
import {Colors} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {IRide} from '@global';

function ModalNeedRide() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [selectedRide, setSelectedRide] = useState(ERide.NOW);

  const DATA_RIDE = useMemo(
    () => [
      {
        description: t('modalRide.nowDesc'),
        id: ERide.NOW,
        title: t('modalRide.now'),
      },
      {
        description: t('modalRide.scheduleDesc'),
        id: ERide.SCHEDULE,
        title: t('modalRide.schedule'),
      },
    ],
    [t],
  );

  const handleNavigate = () => {
    navigation.goBack();
    setTimeout(() => {
      navigation.navigate('SelectDestination', {
        isSchedule: selectedRide === ERide.SCHEDULE,
        isTopBarEnable: false,
      });
    }, 300);
  };

  const renderItem = (item: IRide, index: number) => {
    const isCheck = selectedRide === item?.id;
    return (
      <WTouchable
        activeOpacity={0.7}
        alignCenter
        borderRadius={8}
        color={Colors.neutral100}
        gap={12}
        key={index}
        onPress={() => setSelectedRide(item?.id)}
        padding={16}
        row>
        {isCheck ? (
          <WView
            borderColor={Colors.neutral900}
            borderRadius={999}
            borderWidth={6}
            h={18}
            w={18}
          />
        ) : (
          <WView
            borderColor={Colors.neutral600}
            borderRadius={999}
            borderWidth={1}
            h={18}
            w={18}
          />
        )}
        <WView fill gap={4}>
          <WText type="medium14">{item?.title}</WText>
          <WText color={Colors.neutral600} type="regular12">
            {item?.description}
          </WText>
        </WView>
      </WTouchable>
    );
  };

  return (
    <ModalWrapper enableCloseOnMask={false} style={styles.modelWrapper}>
      <SafeAreaView style={styles.alignCenter}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
          keyboardVerticalOffset={SCREEN_HEIGHT * 0.4}>
          <TouchableWithoutFeedback
            onPress={() => {
              Keyboard.dismiss();
            }}>
            <WView
              borderRadius={16}
              color={Colors.white}
              padding={16}
              w={SCREEN_WIDTH - 32}>
              <WView alignCenter justifyBetween row>
                <WText type="semiBold18">{t('modalRide.title')}</WText>
                <TouchableWithoutFeedback onPress={navigation.goBack}>
                  <WView
                    borderRadius={20}
                    center
                    color={Colors.neutral500}
                    h={20}
                    w={20}>
                    <IonIcon
                      color={Colors.white}
                      name="close-outline"
                      size={16}
                    />
                  </WView>
                </TouchableWithoutFeedback>
              </WView>
              <WView gap={16} mTop={24}>
                {DATA_RIDE?.map(renderItem)}
              </WView>
              <WView mTop={24}>
                <WButton label={t('button.next')} onPress={handleNavigate} />
              </WView>
            </WView>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ModalWrapper>
  );
}

export default ModalNeedRide;

const styles = StyleSheet.create({
  alignCenter: {alignItems: 'center'},
  modelWrapper: {alignItems: 'center', justifyContent: 'center'},
});
