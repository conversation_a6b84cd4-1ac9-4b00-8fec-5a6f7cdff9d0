/* eslint-disable react-native/no-color-literals */
import React, {useState, useEffect, memo} from 'react';
import {StyleSheet} from 'react-native';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import LottieView from 'lottie-react-native';
import WTouchable from '@components/WTouchable';
import WImage from '@components/WImage';
import WText from '@components/WText';
import Images from '@themes/Images';
import {Colors} from '@themes/index';
import {formatNumberLocalized} from '@utils/Tools';
import {WView} from '@components/index';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import useTypewriter from '@utils/hooks/useTypewriter';
import {LOTTIE} from '@themes/Lottie';
import {useCouponsServiceCouponControllerGetCouponStoreList} from '@queries';
import {CouponEntity} from '@requests';

interface Props {
  loyalPoint: number;
}

function ToolTipLoyalPoints({loyalPoint}: Props) {
  const {t} = useTypeSafeTranslation();
  const isFocused = useIsFocused();
  const navigation = useNavigation();
  const [showTooltip, setShowTooltip] = useState(false);

  const {data: dataListCoupon} =
    useCouponsServiceCouponControllerGetCouponStoreList();

  const hasCoupons = dataListCoupon?.data && dataListCoupon?.data?.length > 0;
  const isCheckCouponsValid = hasCoupons
    ? dataListCoupon?.data?.find(
        (item: CouponEntity & {isValid: boolean}) => item?.isValid,
      )
    : false;

  const typedText = useTypewriter(t('home.tapHere'), showTooltip, false);

  const onGotoLoyalPoint = () => {
    navigation.navigate('LoyalPoint', {isTopBarEnable: false});
  };

  useEffect(() => {
    if (isCheckCouponsValid && isFocused) {
      const timer = setTimeout(() => {
        setShowTooltip(true);
      }, 6000);

      return () => {
        clearTimeout(timer);
      };
    }
    setShowTooltip(false);

    return undefined;
  }, [loyalPoint, isFocused, dataListCoupon, isCheckCouponsValid]);

  return (
    <WTouchable
      activeOpacity={1}
      alignCenter
      borderRadius={20}
      color={'#0000001A'}
      gap={4}
      onPress={onGotoLoyalPoint}
      pLeft={10}
      pVer={5}
      row>
      <WImage size={24} source={Images.roundLogo} />
      <WText color={Colors.white} type="semiBold16">
        {formatNumberLocalized(loyalPoint)}
      </WText>
      <LottieView
        autoPlay
        loop
        source={LOTTIE.caretRight}
        style={styles.icCaretRight}
      />

      {showTooltip && (
        <WView style={styles.tooltips}>
          <WTouchable activeOpacity={0.8} onPress={() => setShowTooltip(false)}>
            <WView color={Colors.warning100} style={styles.tooltipContainer}>
              <WView
                color={Colors.white}
                style={[
                  styles.tooltipBubble,
                  GlobalStyle.shadow,
                  styles.tooltipPosition,
                ]}
                w={150}>
                <WText color={Colors.neutral900} fill type="regular12">
                  {typedText}
                </WText>
                <WView
                  style={[
                    styles.tooltipArrow,
                    GlobalStyle.shadow,
                    styles.tooltipArrowPosition,
                  ]}
                />
              </WView>
            </WView>
          </WTouchable>
        </WView>
      )}
    </WTouchable>
  );
}

export default memo(ToolTipLoyalPoints);

const styles = StyleSheet.create({
  icCaretRight: {
    height: 18,
    width: 18,
  },
  tooltipArrow: {
    backgroundColor: Colors.white,
    height: 10,
    position: 'absolute',
    top: -5,
    transform: [{rotate: '45deg'}],
    width: 10,
    zIndex: 999,
  },
  tooltipArrowPosition: {
    right: 14,
  },
  tooltipBubble: {
    borderRadius: 8,
    padding: 12,
    position: 'absolute',
    zIndex: 999,
  },
  tooltipContainer: {
    backgroundColor: 'transparent',
    bottom: -80,
    height: 40,
    position: 'absolute',
    right: -10,
    width: 40,
    zIndex: 999,
  },
  tooltipPosition: {
    right: 0,
  },
  tooltips: {
    position: 'absolute',
    right: 12,
    top: 12,
    zIndex: 999,
  },
});
