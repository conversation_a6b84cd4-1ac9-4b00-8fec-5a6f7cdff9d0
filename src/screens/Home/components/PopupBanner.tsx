/* eslint-disable react-native/no-raw-text */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import {ImageSourcePropType, StyleSheet} from 'react-native';
import React, {useEffect, useState} from 'react';
import Modal from 'react-native-modal';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import Images from '@themes/Images';
import {WImage, WText, WTouchable, WView} from '@components/index';
import {Colors} from '@themes/index';
import {useModal} from '@utils/hooks/useModal';
import {getLocalizedString} from '@utils/Tools';
import {useBirthdayEvent} from '@utils/hooks/useBirthdayEvent';
import {SCREEN_WIDTH} from '@themes/Constants';
import {
  savePopupBannerClosedDate,
  shouldShowPopupBanner,
} from '@utils/popupBannerHelper';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {getResponsiveTextStyle} from '@utils/fontScaling';
import {GlobalStyle} from '@themes/GlobalStyle';

function PopupBanner() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {
    isVisible: isVisiblePopup,
    closeModal: closeModalPopup,
    openModal: openModalPopup,
  } = useModal();

  const {
    isVisible: isVisibleMini,
    closeModal: closeModalMini,
    openModal: openModalMini,
  } = useModal();

  const {timeRemaining, isHappening} = useBirthdayEvent();
  const [canShowPopup, setCanShowPopup] = useState(false);

  useEffect(() => {
    const checkPopupAvailability = async () => {
      const shouldShow = await shouldShowPopupBanner();
      setCanShowPopup(shouldShow);
    };

    checkPopupAvailability();
  }, []);

  useEffect(() => {
    if (isHappening) {
      if (canShowPopup) {
        setTimeout(() => {
          openModalPopup();
        }, 400);
      } else {
        openModalMini();
      }
    }
  }, [isHappening, canShowPopup]);

  const handleClosePopup = async () => {
    closeModalPopup();
    openModalMini();
    await savePopupBannerClosedDate();
  };

  if (!isHappening) {
    return null;
  }

  const onGotoBirthDay = () => {
    closeModalPopup();
    setTimeout(() => {
      navigation.navigate('BirthDay40Year', {
        headerTitle: t('notification.promotion'),
      });
    }, 400);
  };

  return (
    <>
      <Modal isVisible={isVisiblePopup} style={styles.modalContainer}>
        <WView mHoz={16}>
          <WView justifyContent="flex-end" mBottom={12} row>
            <WTouchable
              borderRadius={999}
              color={Colors.white}
              onPress={handleClosePopup}
              padding={6}
              style={GlobalStyle.shadow}>
              <Ionicons color={Colors.neutral850} name="close" size={20} />
            </WTouchable>
          </WView>
          <WTouchable
            borderRadius={16}
            onPress={onGotoBirthDay}
            style={{
              aspectRatio: 0.88,
              overflow: 'hidden',
              width: '100%',
            }}>
            <WImage
              h={'100%'}
              source={
                getLocalizedString(
                  Images.popupBannerEn,
                  Images.popupBannerFr,
                ) as ImageSourcePropType
              }
              w={'100%'}
            />

            <WView
              absolute
              style={{
                position: 'absolute',
                top: '75%',
                width: '100%',
              }}>
              <WView center gap={12} row>
                <WView center>
                  <WText
                    color={Colors.primary}
                    style={getResponsiveTextStyle(24, 0.5, 1.2)}
                    type="montserratBold24">
                    {timeRemaining.days}
                  </WText>
                  <WText
                    color={Colors.neutral850}
                    style={getResponsiveTextStyle(14)}
                    type="montserratMedium14">
                    {t('home.days')}
                  </WText>
                </WView>
                <WText
                  style={getResponsiveTextStyle(20, 0.5, 1.2)}
                  type="bold20">
                  :
                </WText>
                <WView center>
                  <WText
                    color={Colors.primary}
                    style={getResponsiveTextStyle(24, 0.5, 1.2)}
                    type="montserratBold24">
                    {timeRemaining.hours}
                  </WText>
                  <WText
                    color={Colors.neutral850}
                    style={getResponsiveTextStyle(14)}
                    type="montserratMedium14">
                    {t('home.hours')}
                  </WText>
                </WView>
                <WText
                  style={getResponsiveTextStyle(20, 0.5, 1.2)}
                  type="bold20">
                  :
                </WText>
                <WView center>
                  <WText
                    color={Colors.primary}
                    style={getResponsiveTextStyle(24, 0.5, 1.2)}
                    type="montserratBold24">
                    {timeRemaining.minutes}
                  </WText>
                  <WText
                    color={Colors.neutral850}
                    style={getResponsiveTextStyle(14)}
                    type="montserratMedium14">
                    {t('home.mins')}
                  </WText>
                </WView>
                <WText
                  style={getResponsiveTextStyle(20, 0.5, 1.2)}
                  type="bold20">
                  :
                </WText>
                <WView center>
                  <WText
                    color={Colors.primary}
                    style={getResponsiveTextStyle(24, 0.5, 1.2)}
                    type="montserratBold24">
                    {timeRemaining.seconds}
                  </WText>
                  <WText
                    color={Colors.neutral850}
                    style={getResponsiveTextStyle(14)}
                    type="montserratMedium14">
                    {t('home.secs')}
                  </WText>
                </WView>
              </WView>
            </WView>
          </WTouchable>
        </WView>
      </Modal>

      {!isVisiblePopup && isVisibleMini && (
        <WView style={styles.miniBanner}>
          <WView justifyContent="flex-end" row>
            <WTouchable
              activeOpacity={1}
              borderRadius={999}
              color={Colors.white}
              onPress={async () => {
                closeModalMini();
                await savePopupBannerClosedDate();
              }}
              padding={6}
              style={GlobalStyle.shadow}>
              <Ionicons color={Colors.neutral850} name="close" size={20} />
            </WTouchable>
          </WView>
          <WTouchable gap={8} onPress={onGotoBirthDay}>
            <WImage
              borderRadius={6}
              h={SCREEN_WIDTH / 3}
              resizeMode="contain"
              source={
                getLocalizedString(
                  Images.miniBannerEn,
                  Images.miniBannerFr,
                ) as ImageSourcePropType
              }
              w={SCREEN_WIDTH / 3}
            />
            <WView gap={4} row>
              <WView
                borderColor={Colors.primary}
                borderRadius={4}
                borderWidth={0.5}
                center
                color={Colors.white}
                padding={4}
                w={'23%'}>
                <WText
                  color={Colors.primary}
                  style={getResponsiveTextStyle(12)}
                  type="montserratBold14">
                  {timeRemaining.days}
                </WText>
              </WView>
              <WView
                borderColor={Colors.primary}
                borderRadius={4}
                borderWidth={0.5}
                center
                color={Colors.white}
                padding={4}
                w={'23%'}>
                <WText
                  color={Colors.primary}
                  style={getResponsiveTextStyle(12)}
                  type="montserratBold14">
                  {timeRemaining.hours}
                </WText>
              </WView>
              <WView
                borderColor={Colors.primary}
                borderRadius={4}
                borderWidth={0.5}
                center
                color={Colors.white}
                padding={4}
                w={'23%'}>
                <WText
                  color={Colors.primary}
                  style={getResponsiveTextStyle(12)}
                  type="montserratBold14">
                  {timeRemaining.minutes}
                </WText>
              </WView>
              <WView
                borderColor={Colors.primary}
                borderRadius={4}
                borderWidth={0.5}
                center
                color={Colors.white}
                padding={4}
                w={'23%'}>
                <WText
                  color={Colors.primary}
                  style={getResponsiveTextStyle(12)}
                  type="montserratBold14">
                  {timeRemaining.seconds}
                </WText>
              </WView>
            </WView>
          </WTouchable>
        </WView>
      )}
    </>
  );
}

export default PopupBanner;

const styles = StyleSheet.create({
  miniBanner: {
    borderRadius: 12,
    bottom: 24,
    gap: 8,
    position: 'absolute',
    right: 16,
    shadowColor: Colors.neutral900,
    shadowOpacity: 0.2,
    shadowRadius: 8,
    width: SCREEN_WIDTH * 0.33,
    zIndex: 999,
  },
  modalContainer: {
    flex: 1,
    margin: 0,
  },
});
