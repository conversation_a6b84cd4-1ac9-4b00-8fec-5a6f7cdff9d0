import {Linking, StyleSheet} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {SvgUri} from 'react-native-svg';
import FastImage from 'react-native-fast-image';
import {useNavigation} from '@react-navigation/native';
import {
  EFilterStatus,
  HTTP,
  HTTPS,
  LANGUAGE,
  SCREEN_WIDTH,
} from '@themes/Constants';
import {checkSvgInUrlStrict, showErrorAlert} from '@utils/Tools';
import {BannerEntity, BannerParamDto} from '@requests';
import WTouchable from '@components/WTouchable';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useGlobalState} from '@react-query/clientStateManage';
import MixPanelSdk from '@utils/mixPanelSdk';
import {WView} from '@components/index';
import Skeleton from '@components/Skeleton';

interface Props {
  onGoToBooking: (isSchedule?: boolean) => void;
  item: BannerEntity;
}

function ItemBanner({item, onGoToBooking}: Props) {
  const {i18n} = useTranslation();
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const [_, setNavigateTab] = useGlobalState('navigateTab');
  const [userData] = useGlobalState('userData');

  const [loading, setLoading] = useState(true);

  const languageKey = i18n.language === LANGUAGE.EN ? LANGUAGE.EN : LANGUAGE.FR;
  const uri = item?.bannerImages?.[languageKey];
  const isSvg = checkSvgInUrlStrict(uri ?? '') ?? false;

  const eventValues = {
    email: userData?.user?.email,
    phoneNumber: userData?.user?.phoneNumber,
    userName: userData?.user?.fullName,
  };

  const handleNavigate = (item: BannerEntity) => {
    if (item?.navigationType === BannerEntity.navigationType.URL) {
      const targetUrl =
        item?.target?.startsWith(HTTP) || item?.target?.startsWith(HTTPS)
          ? item?.target
          : `${HTTPS}${item?.target}`;

      return navigation.navigate('WebViewScreen', {
        isTopBarEnable: false,
        title: item?.campaign,
        url: targetUrl,
      });
    }

    const navigationMap: Record<string, () => void> = {
      // Booking
      [BannerParamDto.target.BOOK_A_CAR]: () => onGoToBooking?.(),
      [BannerParamDto.target.SCHEDULE_A_CAR]: () => onGoToBooking?.(true),

      // Activities
      [BannerParamDto.target.ACTIVITIES_RECENT]: () =>
        navigation.navigate('ActivitiesTab'),
      [BannerParamDto.target.ACTIVITIES_HISTORY]: () => {
        setNavigateTab(EFilterStatus.CurrentBooking);
        navigation.navigate('ActivitiesHistory', {isTopBarEnable: false});
      },

      // Notification
      [BannerParamDto.target.NOTIFICATION_GENERAL]: () => {
        setNavigateTab('0');
        navigation.navigate('Notification');
      },
      [BannerParamDto.target.NOTIFICATION_PROMOTION]: () => {
        setNavigateTab('1');
        navigation.navigate('Notification');
      },

      // Setting
      [BannerParamDto.target.SETTINGS_PROFILE]: () =>
        navigation.navigate('Profile', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_PAYMENT]: () =>
        navigation.navigate('ListPayment', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_SAVED_LOCATIONS]: () =>
        navigation.navigate('ListSavedLocation', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_LOYAL_HUB]: () =>
        navigation.navigate('ListBusiness', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_LOYAL_ONE]: () => {
        MixPanelSdk.logEvent({
          eventName: 'loyal_one_overview',
          eventValues,
        });
        navigation.navigate('Subscription', {isTopBarEnable: false});
      },
      [BannerParamDto.target.SETTINGS_COUPON_LIST]: () => {
        setNavigateTab('0');
        navigation.navigate('Coupon', {isTopBarEnable: false});
      },
      [BannerParamDto.target.SETTINGS_COUPON_STORE]: () => {
        setNavigateTab('1');
        navigation.navigate('Coupon', {isTopBarEnable: false});
      },
      [BannerParamDto.target.SETTINGS_LOYAL_POINTS]: () =>
        navigation.navigate('LoyalPoint', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_LOYAL_POINTS_HISTORY]: () =>
        navigation.navigate('HistoryLoyalPoint', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_CHANGE_PASSWORD]: () =>
        navigation.navigate('ChangePassword', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_ACCOUNT_DELETION]: () =>
        navigation.navigate('DeleteAccount', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_LANGUAGE]: () =>
        navigation.navigate('ChangeLanguage', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_ABOUT]: () =>
        navigation.navigate('About', {isTopBarEnable: false}),
      [BannerParamDto.target.SETTINGS_REFER_FRIENDS]: () =>
        navigation.navigate('ReferFriends', {isTopBarEnable: false}),
    };

    navigationMap[item?.target ?? '']?.();
  };

  useEffect(() => {
    if (isSvg) {
      const timeout = setTimeout(() => setLoading(false), 1000);
      return () => clearTimeout(timeout);
    }
  }, [isSvg]);

  return (
    <WTouchable
      activeOpacity={1}
      borderRadius={12}
      fill
      onPress={() => handleNavigate(item)}>
      {loading && (
        <WView absolute zIndex={1}>
          <Skeleton
            borderRadius={12}
            height={SCREEN_WIDTH / 3}
            width={SCREEN_WIDTH}
          />
        </WView>
      )}
      {isSvg ? (
        <SvgUri height={'100%'} uri={uri} width={'100%'} />
      ) : (
        <FastImage
          onLoadEnd={() => setLoading(false)}
          onLoadStart={() => setLoading(true)}
          resizeMode="cover"
          source={{
            priority: FastImage.priority.low,
            uri,
          }}
          style={styles.imgBanner}
        />
      )}
    </WTouchable>
  );
}

export default ItemBanner;

const styles = StyleSheet.create({
  imgBanner: {
    borderRadius: 12,
    height: '100%',
    width: '100%',
  },
});
