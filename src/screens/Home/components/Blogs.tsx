import {Image, ScrollView, StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import {isEmpty} from 'lodash-es';
import {useNavigation} from '@react-navigation/native';
import {ACTIVE_OPACITY, SCREEN_WIDTH} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {useArticlesServiceArticleControllerGetArticleDisplayed} from '@queries';
import {getLocalizedString} from '@utils/Tools';
import {WText, WView} from '@components';
import {Colors} from '@themes';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import Skeleton from '@components/Skeleton';
import {ArticleEntity} from '@requests';

function Blogs() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const {data: dataArticle, isLoading} =
    useArticlesServiceArticleControllerGetArticleDisplayed();

  const handleOpenUrl = (url: string, title: string) => {
    navigation.navigate('WebViewScreen', {
      isTopBarEnable: false,
      title,
      url,
    });
  };

  if (isEmpty(dataArticle?.data)) {
    return null;
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.newsContainer}>
      {isLoading ? (
        <WView gap={12} row style={GlobalStyle.shadowSearchInput}>
          {new Array(3).fill(true).map((_, index) => (
            <WView gap={12} key={index}>
              <Skeleton height={140} width={SCREEN_WIDTH * 0.6} />
              <Skeleton height={20} width={SCREEN_WIDTH * 0.4} />
              <Skeleton height={20} width={SCREEN_WIDTH * 0.4} />
              <Skeleton height={20} width={SCREEN_WIDTH * 0.2} />
            </WView>
          ))}
        </WView>
      ) : (
        <>
          <WView pRight={16} row>
            {dataArticle?.data?.map((ele: ArticleEntity) => (
              <TouchableOpacity
                activeOpacity={ACTIVE_OPACITY}
                key={ele?.id}
                onPress={() =>
                  handleOpenUrl(
                    getLocalizedString(ele?.urlEn ?? '', ele?.urlFr ?? ''),
                    getLocalizedString(ele?.titleEn ?? '', ele?.titleFr ?? ''),
                  )
                }>
                <WView
                  color={Colors.white}
                  mBottom={12}
                  style={[styles.newsContent, GlobalStyle.shadowSearchInput]}>
                  <Image
                    source={{
                      uri: getLocalizedString(
                        ele?.thumbnailEn ?? '',
                        ele?.thumbnailFr ?? '',
                      ),
                    }}
                    style={styles.newsImg}
                  />
                  <WView padding={12}>
                    <WText minHeight={36} numberOfLine={2} type="medium14">
                      {getLocalizedString(
                        ele?.titleEn ?? '',
                        ele?.titleFr ?? '',
                      )}
                    </WText>
                    <WText
                      color={Colors.neutral600}
                      fill
                      marginBottom={12}
                      marginTop={4}
                      minHeight={32}
                      numberOfLine={2}
                      type="regular12">
                      {getLocalizedString(
                        ele?.descriptionEn ?? '',
                        ele?.descriptionFr ?? '',
                      )}
                    </WText>
                    <WText color={Colors.primary} type="medium14">
                      {t('home.readMore')}
                    </WText>
                  </WView>
                </WView>
              </TouchableOpacity>
            ))}
          </WView>
        </>
      )}
    </ScrollView>
  );
}

export default Blogs;

const styles = StyleSheet.create({
  newsContainer: {marginBottom: 20, padding: 16},
  newsContent: {
    borderRadius: 12,
    marginRight: 12,
    width: SCREEN_WIDTH * 0.6,
  },
  newsImg: {
    aspectRatio: 51 / 23,
    backgroundColor: Colors.white,
    borderColor: Colors.neutral150,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderWidth: 1,
    width: '100%',
  },
});
