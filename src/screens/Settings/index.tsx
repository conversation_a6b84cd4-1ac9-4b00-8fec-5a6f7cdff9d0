import AsyncStorage from '@react-native-async-storage/async-storage';
import {useNavigation} from '@react-navigation/native';
import {useQueryClient} from '@tanstack/react-query';
import i18next from 'i18next';
import React, {useEffect, useMemo} from 'react';
import {
  Image,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import Icon from 'react-native-vector-icons/icomoon';
import messaging from '@react-native-firebase/messaging';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import notifee from '@notifee/react-native';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {AppConfigEntity, LoyalPointConfigEntity} from '@requests';
import {
  useAuthServiceAuthControllerLogout,
  useConfigsServiceAppConfigControllerGetAppConfigs,
  useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig,
  useNotificationTokensServiceNotificationTokenControllerDeleteNotificationToken,
} from '@queries';
import AvatarUser from '@components/AvatarUser';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors, Images} from '@themes';
import {useGlobalState} from '@react-query/clientStateManage';
import {WButton, WText, WView} from '@components';
import MixPanelSdk from '@utils/mixPanelSdk';

let count = 1;
let cacheLanguage = '';

interface LanguageTitleProps {
  [index: string]: string;
}

const LANGUAGE_TITLE: LanguageTitleProps = {
  en: 'English',
  fr: 'Français',
};

function Setting() {
  const [userData] = useGlobalState('userData');
  const [fcmData] = useGlobalState('fcmData');
  const [_configData, setConfigData] = useGlobalState('configData');
  const [__, setIsSkipIntro] = useGlobalState('isSkipIntro');
  const [language, setLanguage] = useGlobalState('language');
  const [_loyalPointConfig, setLoyalPointConfig] =
    useGlobalState('loyalPointConfig');

  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const {data: appConfigData} =
    useConfigsServiceAppConfigControllerGetAppConfigs();

  const {data: pointConfigData} =
    useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig();

  const queryClient = useQueryClient();

  const {mutate: deleteNotificationToken} =
    useNotificationTokensServiceNotificationTokenControllerDeleteNotificationToken();

  const {mutate: userLogout} = useAuthServiceAuthControllerLogout();

  useEffect(() => {
    cacheLanguage = language as string;
  }, []);

  const eventValues = {
    email: userData?.user?.email,
    phoneNumber: userData?.user?.phoneNumber,
    userName: userData?.user?.fullName,
  };

  const DATA = useMemo(
    () => [
      {
        data: [
          {
            label: t('setting.profileDetails'),
            onAction: () => {
              navigation.navigate('Profile', {isTopBarEnable: false});
            },
          },
          {
            label: t('booking.paymentMethod'),
            onAction: () => {
              navigation.navigate('ListPayment', {isTopBarEnable: false});
            },
          },
          {
            label: t('subscription.title'),
            onAction: () => {
              navigation.navigate('Subscription', {
                hidePayment: true,
                isTopBarEnable: false,
              });
              MixPanelSdk.logEvent({
                eventName: 'loyal_one_overview',
                eventValues,
              });
            },
          },
          {
            label: t('savedLocation.title.default'),
            onAction: () => {
              navigation.navigate('ListSavedLocation', {isTopBarEnable: false});
            },
          },
          {
            label: t('business.taxiLoyalBusinessHub'),
            onAction: () => {
              navigation.navigate('ListBusiness', {isTopBarEnable: false});
            },
          },
          {
            label: t('coupon.title'),
            onAction: () => {
              navigation.navigate('Coupon', {isTopBarEnable: false});
            },
          },
          {
            label: t('setting.referFriends'),
            onAction: () => {
              navigation.navigate('ReferFriends', {isTopBarEnable: false});
            },
          },
          {
            label: t('loyalPoints.title'),
            onAction: () => {
              navigation.navigate('LoyalPoint', {isTopBarEnable: false});
            },
          },
        ],
        title: t('setting.general'),
      },
      {
        data: [
          {
            label: t('changePassword.title'),
            onAction: () => {
              navigation.navigate('ChangePassword', {isTopBarEnable: false});
            },
          },
          {
            label: t('deleteAccount.title'),
            onAction: () => {
              navigation.navigate('DeleteAccount', {isTopBarEnable: false});
            },
          },
        ],
        title: t('setting.privacyCentre'),
      },
      {
        data: [
          {
            extraLabel: LANGUAGE_TITLE?.[i18next.language],
            label: t('language.title'),
            onAction: () => {
              navigation.navigate('ChangeLanguage', {isTopBarEnable: false});
            },
          },
          {
            label: t('setting.about'),
            onAction: () => {
              navigation.navigate('About', {isTopBarEnable: false});
            },
          },
        ],
        title: t('setting.others'),
      },
    ],
    [navigation, t, i18next.language],
  );

  const revokeThirdPartyAccess = async () => {
    try {
      await messaging().deleteToken();
      await messaging().unregisterDeviceForRemoteMessages();
      const isLoginSocial = await GoogleSignin.isSignedIn();
      if (isLoginSocial) {
        await GoogleSignin.revokeAccess();
        await GoogleSignin.signOut();
      }
    } catch (error) {
      console.log('💩: revokeThirdPartyAccess -> error', error);
    }
  };

  const onLogout = () => {
    revokeThirdPartyAccess();
    navigation.reset({routes: [{name: 'Login'}]});
    if (fcmData?.id) {
      deleteNotificationToken({
        id: fcmData?.id,
      });
    }
    userLogout();
    AsyncStorage.clear();
    queryClient.clear();
    setConfigData(appConfigData?.data as AppConfigEntity);
    setIsSkipIntro(true);
    setLanguage(cacheLanguage);
    setLoyalPointConfig(pointConfigData?.data as LoyalPointConfigEntity);
    notifee.setBadgeCount(0);
  };

  const version = `${t(
    'setting.version',
  )} ${DeviceInfo.getVersion()}.${DeviceInfo.getBuildNumber()}`;

  return (
    <View style={styles.container}>
      {/* <FocusAwareStatusBar
        backgroundColor={Colors.white}
        barStyle="light-content"
      /> */}
      <WView alignItems="flex-end" color={Colors.neutral900}>
        <Image source={Images.largeSimpleLogo} style={styles.bgImg} />
      </WView>
      <WView alignCenter>
        <AvatarUser
          avatarUrl={userData?.user?.avatarUrl}
          fullName={userData?.user?.fullName}
          size={82}
          style={{
            marginTop: -(82 / 2),
          }}
        />

        <WText marginTop={8} type="semiBold18">
          {userData?.user?.fullName}
        </WText>
      </WView>

      <SectionList
        ListFooterComponent={() => (
          <WView>
            <WButton
              label={t('button.logout')}
              onPress={onLogout}
              outline
              style={[styles.logoutBtn, GlobalStyle.shadowSoft]}
            />
            <TouchableOpacity
              activeOpacity={1}
              hitSlop={GlobalStyle.hitSlop}
              onPress={() => {
                if (count < 10) {
                  count = count + 1;
                } else {
                  count = 1;
                  navigation.navigate('NetworkMonitor');
                }
              }}>
              <WText
                center
                color={Colors.neutral500}
                marginBottom={12}
                type="regular14">
                {version}
              </WText>
            </TouchableOpacity>
          </WView>
        )}
        keyExtractor={(item, index) => item?.label + index}
        renderItem={({item}) => (
          <TouchableWithoutFeedback onPress={item?.onAction}>
            <WView alignCenter justifyBetween padding={16} row>
              <WText type="medium14">{item?.label}</WText>
              <WView alignCenter row>
                {item?.extraLabel && (
                  <WText color={Colors.neutral600} type="regular14">
                    {item?.extraLabel}
                  </WText>
                )}
                <Icon name="Outline-CaretRight" size={20} />
              </WView>
            </WView>
          </TouchableWithoutFeedback>
        )}
        renderSectionHeader={({section: {title}}) => (
          <WView
            borderBottomColor={Colors.neutral200}
            borderBottomWidth={1}
            mHoz={16}
            mTop={8}
            pVer={8}>
            <WText color={Colors.neutral600} type="semiBold12">
              {title}
            </WText>
          </WView>
        )}
        sections={DATA}
        showsVerticalScrollIndicator={false}
        stickySectionHeadersEnabled={false}
      />
    </View>
  );
}

export default Setting;

const styles = StyleSheet.create({
  bgImg: {marginRight: 40, marginVertical: 24},
  container: {flex: 1},
  logoutBtn: {marginBottom: 20, marginHorizontal: 16, marginTop: 20},
});
