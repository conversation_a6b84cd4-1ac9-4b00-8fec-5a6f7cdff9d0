import React from 'react';
import {Linking, SafeAreaView, TouchableWithoutFeedback} from 'react-native';
import {NavHeader, WText, WView} from '@components';
import {Colors} from '@themes';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {MAIL_SUPPORT, PHONE_SUPPORT} from '@themes/Constants';

function About() {
  const {t} = useTypeSafeTranslation();
  const address = '331 Main Street Gatineau, QC J8P 5K5';
  const contactInfo = `${PHONE_SUPPORT} & ${MAIL_SUPPORT}`;
  const policyLink = 'https://taxiloyal.com/en/policies/privacy-policy';
  const termsOfService = 'https://taxiloyal.com/en/policies/terms-of-service';
  const contact = 'https://taxiloyal.com/en/pages/contact';
  return (
    <SafeAreaView>
      <NavHeader title={t('setting.aboutTaxiLoyal')} />
      <WView mTop={26} pHoz={16}>
        <WView mBottom={16}>
          <WText type="medium14">{t('about.rental')}</WText>
          <WText marginTop={4} type="regular14">
            {address}
          </WText>
        </WView>
        <WView mBottom={16}>
          <WText type="medium14">{t('about.contactUs')}</WText>
          <WText marginTop={4} type="regular14">
            {contactInfo}
          </WText>
        </WView>
        <WView mBottom={16}>
          <WText type="medium14">{t('about.businessHours')}</WText>
          <WText marginTop={4} type="regular14">
            {t('about.workingTime')}
          </WText>
        </WView>
        <WText type="medium14">{t('about.taxiLoyalPolicies')}</WText>
        <TouchableWithoutFeedback
          onPress={() => {
            Linking.openURL(policyLink);
          }}>
          <WText color={Colors.purple900} marginTop={8} type="medium14">
            {t('about.privacyPolicy')}
          </WText>
        </TouchableWithoutFeedback>
        <TouchableWithoutFeedback
          onPress={() => {
            Linking.openURL(termsOfService);
          }}>
          <WText color={Colors.purple900} marginTop={8} type="medium14">
            {t('about.protectionPolicy')}
          </WText>
        </TouchableWithoutFeedback>
        <TouchableWithoutFeedback
          onPress={() => {
            Linking.openURL(contact);
          }}>
          <WText color={Colors.purple900} marginTop={8} type="medium14">
            {t('about.contactUs')}
          </WText>
        </TouchableWithoutFeedback>
      </WView>
    </SafeAreaView>
  );
}

export default About;
