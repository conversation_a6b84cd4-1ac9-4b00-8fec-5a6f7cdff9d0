import {useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import {NavHeader, WText, WView} from '@components';
import {
  useNotificationsServiceNotificationControllerGetNotificationsKey,
  useUsersServiceUserControllerUpdateLocale,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import {OpenAPI, UpdateLocaleDto} from '@requests';
import {Colors} from '@themes';
import {ACTIVE_OPACITY} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {setCalendarLanguage} from '@utils/Tools';

function ChangeLanguage() {
  const LIST_LANGUAGE = [
    {
      content: 'English',
      id: 'en',
      title: 'Anglaise',
    },
    {
      content: 'French',
      id: 'fr',
      title: 'Français',
    },
  ];
  const {t} = useTypeSafeTranslation();
  const {i18n} = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
  const [_, setLanguage] = useGlobalState('language');
  const navigation = useNavigation();

  const {mutate: updateLocale} = useUsersServiceUserControllerUpdateLocale();

  const changeLanguage = async (language: string) => {
    if (selectedLanguage !== language) {
      await i18n.changeLanguage(language);
      setLanguage(language);
      setCalendarLanguage(language);
      updateLocale({
        requestBody: {
          locale: language as UpdateLocaleDto.locale,
        },
      });
      OpenAPI.HEADERS = {['Accept-Language']: language};
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [
            useNotificationsServiceNotificationControllerGetNotificationsKey,
          ],
          type: 'all',
        });
      }, 500);
      navigation.goBack();
    } else {
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView>
      <NavHeader title={t('language.title')} />
      <WView mTop={26}>
        {LIST_LANGUAGE.map(item => (
          <TouchableOpacity
            activeOpacity={ACTIVE_OPACITY}
            key={item?.id}
            onPress={() => {
              setSelectedLanguage(item?.id);
              changeLanguage(item?.id);
            }}>
            <WView
              style={[
                styles.languageContainer,
                {
                  borderColor:
                    selectedLanguage === item?.id
                      ? Colors.neutral900
                      : Colors.neutral300,
                },
              ]}>
              <WText type="medium14">
                {selectedLanguage === 'en' ? item?.content : item?.title}
              </WText>
              <WText color={Colors.neutral600} marginTop={4} type="regular12">
                {selectedLanguage === 'en' ? item?.title : item?.content}
              </WText>
            </WView>
          </TouchableOpacity>
        ))}
      </WView>
    </SafeAreaView>
  );
}

export default ChangeLanguage;

const styles = StyleSheet.create({
  languageContainer: {
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
    marginHorizontal: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
