import {RouteProp, useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React from 'react';
import {TouchableWithoutFeedback} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {CodeBaseEntity} from '@requests';
import {WText, WView} from '@components';
import ModalWrapper from '@components/Modal/ModalWrapper';
import {BusinessDetailRouteProps} from '@global';
import {Colors} from '@themes';
import {STR_DATE_FORMAT} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {formatDistance, formatNumberPrice} from '@utils/Tools';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface RowInfoProps {
  title?: string;
  content?: string;
  status?: string;
}

interface RepeatProps {
  [key: string]: string;
}

function RowInfo({title, content, status}: RowInfoProps) {
  if (!content) {
    return null;
  }
  return (
    <WView alignItems="flex-start" justifyBetween mBottom={12} row>
      <WText
        color={Colors.neutral600}
        marginRight={8}
        marginTop={!status ? 0 : 2}
        type="regular14">
        {title}
      </WText>
      {status ? (
        <WView
          alignCenter
          borderRadius={20}
          color={Colors.success100}
          pHoz={8}
          pVer={4}
          row>
          <WView borderRadius={3} color={Colors.success500} h={6} w={6} />
          <WText color={Colors.success} marginLeft={6} type="medium12">
            {status}
          </WText>
        </WView>
      ) : (
        <WText
          color={Colors.neutral875}
          fill
          textAlign="right"
          type="regular14">
          {content}
        </WText>
      )}
    </WView>
  );
}

interface BusinessDetailProps {
  route: RouteProp<BusinessDetailRouteProps, 'BusinessDetail'>;
}

function BusinessDetail({route}: BusinessDetailProps) {
  const {business} = route?.params || {};

  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const REPEAT_DATA: RepeatProps = {
    [CodeBaseEntity.repeatType.NO_REPEAT]: t('business.noRepeat'),
    [CodeBaseEntity.repeatType.MONTHLY]: t('business.monthly'),
    [CodeBaseEntity.repeatType.WEEKLY]: t('business.weekly'),
  };

  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={12}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <WView alignCenter justifyBetween mBottom={16} row>
            <WText type="semiBold18">{t('business.detailInformation')}</WText>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral500}
                h={20}
                w={20}>
                <IonIcon color={Colors.white} name="close-outline" size={16} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>
          <RowInfo
            content={business?.business?.name}
            title={t('business.yourCompanyName')}
          />
          <RowInfo
            content={business?.employee.departmentRole?.name}
            title={t('business.yourDepartment')}
          />
          <RowInfo
            content={business?.employee.departmentGroup?.name}
            title={t('business.yourGroup')}
          />
          <RowInfo
            content={business?.employee.departmentRole?.name}
            title={t('business.yourRole')}
          />
          <WView
            borderRadius={12}
            color={Colors.white}
            mTop={4}
            padding={12}
            style={GlobalStyle.shadow}>
            <RowInfo
              status={business?.isAvailable ? 'Available' : 'Unavailable'}
              title={t('business.status')}
            />
            <RowInfo
              content={formatNumberPrice(business?.code?.maxSpendingLimit || 0)}
              title={t('business.maxSpendingLimit')}
            />
            <RowInfo
              content={business?.code?.maxNumberOfUsage}
              title={t('business.maxNumberUsage')}
            />
            <RowInfo
              content={formatDistance(business?.code?.distanceLimit || 0)}
              title={t('business.distanceLimit')}
            />
            <RowInfo
              content={
                business?.code?.expiredDate
                  ? moment(business?.code?.expiredDate).format(STR_DATE_FORMAT)
                  : ''
              }
              title={t('business.expiredDate')}
            />
            <RowInfo
              content={REPEAT_DATA?.[business?.code?.repeatType]}
              title={t('business.repeat')}
            />
          </WView>
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default BusinessDetail;
