/* eslint-disable react-native/no-raw-text */
import React, {useEffect, useState} from 'react';
import {
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import {RouteProp, useNavigation} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/icomoon';
import moment from 'moment';
import {CreateBookingWithCodeDto, EmployeeCodeEntity} from '@requests';
import {ListBusinessRouteProps} from '@global';
import {useGlobalState} from '@react-query/clientStateManage';
import {NavHeader, WButton, WText, WView} from '@components';
import {Colors, Images} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';
import {useUsersServiceEmployeeCodeControllerGetCodesOfUser} from '@queries';
import {formatNumberPrice} from '@utils/Tools';
import {STR_DATE_FORMAT} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface ListBusinessProps {
  route: RouteProp<ListBusinessRouteProps, 'ListBusiness'>;
}

function ListBusiness({route}: ListBusinessProps) {
  const {isFromBooking, headerTitle, selectedBusiness, onApplyCode} =
    route?.params || {};

  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );

  const [selectedItem, setSelectedItem] = useState(selectedBusiness);

  const {data, isFetching, isLoading, refetch} =
    useUsersServiceEmployeeCodeControllerGetCodesOfUser({
      limit: 50,
      offset: 0,
      order: 'createdAt:desc',
    });

  useEffect(() => {
    if (data?.data?.items?.length === 0 && !isFetching && data?.data?.items) {
      navigation.navigate('BusinessInfoModal', {isTopBarEnable: false});
    }
  }, [isFetching]);

  const renderItem = ({item}: {item: EmployeeCodeEntity}) => {
    const isSelected = selectedItem?.code?.id === item?.code?.id;
    /*
     * const backgroundColor = isSelected ? Colors.neutral900 : Colors.white;
     * const contentTextColor = isSelected ? Colors.neutral500 : Colors.neutral600;
     * const titleTextColor = isSelected ? Colors.white : Colors.neutral875;
     * const value1TextColor = isSelected ? Colors.neutral50 : Colors.neutral875;
     * const value2TextColor = isSelected ? Colors.neutral700 : Colors.neutral500;
     */
    const borderColor = isSelected ? Colors.neutral900 : Colors.neutral300;

    return (
      <TouchableWithoutFeedback
        onPress={() => {
          setSelectedItem(item);
        }}>
        <WView
          borderColor={borderColor}
          borderRadius={8}
          borderWidth={1}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <WView fill row>
            <WText color={Colors.neutral875} fill type="medium14">
              {item?.business?.name}
            </WText>
            <TouchableOpacity
              hitSlop={GlobalStyle.hitSlop}
              onPress={() => {
                navigation.navigate('BusinessConfigModal', {business: item});
              }}>
              <Icon
                color={Colors.neutral500}
                name="Fill-DotsThreeOutline"
                size={24}
              />
            </TouchableOpacity>
          </WView>
          <WView
            alignCenter
            borderRadius={20}
            color={item.isAvailable ? Colors.success100 : Colors.danger100}
            pHoz={10}
            pVer={5}
            row
            style={styles.alignSelf}>
            <WView
              borderRadius={4}
              color={item.isAvailable ? Colors.success500 : Colors.danger500}
              h={8}
              w={8}
            />
            <WText
              color={item.isAvailable ? Colors.success : Colors.danger700}
              marginLeft={6}
              type="medium12">
              {item.isAvailable
                ? t('business.available')
                : t('business.unavailable')}
            </WText>
          </WView>

          <WView alignCenter justifyBetween mTop={8} row>
            <WText color={Colors.neutral600} type="regular12">
              {t('business.codeAndPin')}
            </WText>
            <WText color={Colors.neutral875} type="regular12">
              {item?.code?.code}
            </WText>
          </WView>

          <WView alignCenter justifyBetween mTop={8} row>
            <WText color={Colors.neutral600} type="regular12">
              {t('business.maxSpendingLimit')}
            </WText>
            <WText color={Colors.neutral875} type="regular12">
              {formatNumberPrice(item?.remainingSpending || 0)}
              <WText color={Colors.neutral500} type="regular12">
                {`/${formatNumberPrice(item?.code?.maxSpendingLimit || 0)}`}
              </WText>
            </WText>
          </WView>
          <WView alignCenter justifyBetween mTop={8} row>
            <WText color={Colors.neutral600} type="regular12">
              {t('business.maxNumberUsage')}
            </WText>
            <WText color={Colors.neutral875} type="regular12">
              {item?.remainingNumberOfUsage}
              <WText color={Colors.neutral500} type="regular12">
                {`/${item?.code?.maxNumberOfUsage}`}
              </WText>
            </WText>
          </WView>
          <WView alignCenter justifyBetween mTop={8} row>
            <WText color={Colors.neutral600} type="regular12">
              {t('business.distanceLimit')}
            </WText>
            <WText color={Colors.neutral875} type="regular12">
              {`${(item?.remainingDistance || 0) / 1000 || 0} km`}
              <WText color={Colors.neutral500} type="regular12">
                {`/${(item?.code?.distanceLimit || 0) / 1000} km`}
              </WText>
            </WText>
          </WView>
          <WView alignCenter justifyBetween mTop={8} row>
            <WText color={Colors.neutral600} type="regular12">
              {t('business.expiredDate')}
            </WText>
            <WText color={Colors.neutral875} type="regular12">
              {moment(item?.code?.expiredDate).format(STR_DATE_FORMAT)}
            </WText>
          </WView>
        </WView>
      </TouchableWithoutFeedback>
    );
  };

  const renderEmpty = () => {
    if (isLoading || isFetching) {
      return (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </WView>
      );
    }
    return (
      <WView alignCenter fill justifyCenter>
        <Image source={Images.emptyBusiness} />
        <WText marginTop={20} type="regular14">
          {t('empty.business')}
        </WText>
      </WView>
    );
  };

  const renderRightButton = () => (
    <TouchableWithoutFeedback
      onPress={() => {
        navigation.navigate('AddBusinessCodeModal');
      }}>
      <WView
        alignCenter
        borderColor={Colors.neutral200}
        borderRadius={4}
        borderWidth={1}
        color={Colors.neutral100}
        h={34}
        pHoz={6}
        row>
        <Icon name="Outline-Plus" size={14} />
        <WText marginLeft={6} type="semiBold14">
          {t('business.newBusiness')}
        </WText>
      </WView>
    </TouchableWithoutFeedback>
  );

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader
        renderRightButton={isFromBooking ? renderRightButton : undefined}
        title={headerTitle || t('business.taxiLoyalBusinessHub')}
      />
      <WView fill mBottom={12} mTop={24}>
        <FlatList
          ListEmptyComponent={renderEmpty}
          contentContainerStyle={styles.flatListContent}
          data={data?.data?.items}
          refreshControl={
            <RefreshControl
              colors={[Colors.primary]}
              onRefresh={refetch}
              refreshing={false}
              tintColor={Colors.primary}
            />
          }
          renderItem={renderItem}
        />
      </WView>
      <WButton
        label={isFromBooking ? t('button.apply') : t('button.addNewBusiness')}
        onPress={() => {
          if (isFromBooking) {
            setBookingWithCodeParams({
              ...bookingWithCodeParams,
              codeId: `${selectedItem?.code?.id}`,
            } as CreateBookingWithCodeDto);
            onApplyCode?.(selectedItem);
            // navigation.goBack();
          } else {
            // navigation.navigate('BusinessInfoModal', {isTopBarEnable: false});
            navigation.navigate('AddBusinessCodeModal');
          }
        }}
        style={styles.applyBtn}
      />
    </SafeAreaView>
  );
}

export default ListBusiness;

const styles = StyleSheet.create({
  alignSelf: {alignSelf: 'flex-start'},
  applyBtn: {marginBottom: 16, marginHorizontal: 16},
  flatListContent: {flexGrow: 1},
});
