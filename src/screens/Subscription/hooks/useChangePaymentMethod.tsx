import {useNavigation} from '@react-navigation/native';
import {
  useSubscriptionsServiceSubscriptionMeControllerUpdateSubscriptionCard,
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod,
} from '@queries';
import {CardEntity} from '@requests';

interface Props {
  hidePayment: boolean;
  enabled?: boolean;
  subscriptionId?: string;
}
export default function useChangePaymentMethod({
  hidePayment,
  enabled,
  subscriptionId,
}: Props) {
  const navigation = useNavigation();
  const {data: getCurrentCard, refetch} =
    useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod(
      {
        id: subscriptionId ?? '',
      },
      undefined,
      {
        enabled: !!subscriptionId && enabled,
      },
    );

  const currentCard = getCurrentCard?.data as CardEntity;
  const {mutate: updateSubscriptionCard} =
    useSubscriptionsServiceSubscriptionMeControllerUpdateSubscriptionCard();

  const handleNavigatePayment = () => {
    navigation.navigate('ListPayment', {
      defaultPayment: currentCard,
      hideCash: true,
      hidePayment,
      isFromBooking: true,
      isTopBarEnable: false,
      onApply: item => {
        updateSubscriptionCard(
          {
            requestBody: {
              cardId: item?.id,
            },
          },
          {
            onSuccess: () => {
              navigation.goBack();
              refetch();
            },
          },
        );
      },
    });
  };
  return {
    currentCard,
    handleNavigatePayment,
  };
}
