import {useNavigation} from '@react-navigation/native';
import {PopTo} from '@navigation/utils';
import {
  useSubscriptionsServiceSubscriptionMeV2ControllerCancelRenewSubscription,
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
} from '@queries';
import {queryClient} from '@react-query/queryClient';
import {FORMAT_DOB} from '@themes/Constants';
import useLoading from '@utils/hooks/useLoading';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {capitalizeFirstLetter, formatTimeLocale} from '@utils/Tools';
import Icons from '@themes/Icons';

interface Props {
  name: string;
  renewDate?: string;
}
export default function useUnsubscription({name, renewDate}: Props) {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const {showLoading, dismissLoading} = useLoading();

  const {mutateAsync: cancelRenewSubscription, isPending} =
    useSubscriptionsServiceSubscriptionMeV2ControllerCancelRenewSubscription({
      onSuccess: () => {
        showLoading();
        setTimeout(() => {
          dismissLoading();
          navigation.navigate('ActionModal', {
            confirmLabel: t('button.ok'),
            content: t('subscription.unsubscribeModal.success.description', {
              value: capitalizeFirstLetter(
                formatTimeLocale(renewDate ?? '', FORMAT_DOB),
              ),
            }).replace('PACKAGE', name || ''),
            enableCloseOnMask: false,
            hasCancelBtn: false,
            icon: 'Outline-Check',
            iconSize: 54,
            image: Icons.icSuccess,
            onApply: () => {
              PopTo(3);
            },
            title: t('subscription.unsubscribeModal.success.title'),
          });
          queryClient.refetchQueries({
            queryKey: [
              useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
            ],
            type: 'all',
          });
        }, 2000);
      },
    });

  return {
    cancelRenewSubscription,
    isPending,
  };
}
