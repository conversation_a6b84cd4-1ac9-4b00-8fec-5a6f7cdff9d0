import {useNavigation} from '@react-navigation/native';
import {
  useSubscriptionsServiceSubscriptionControllerChangePlanSubscription,
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
} from '@queries';
import {queryClient} from '@react-query/queryClient';
import Icons from '@themes/Icons';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface Props {
  subscriptionName?: string;
  subscriptionId: string;
  content: string;
}
export default function useChangePlan({
  subscriptionName,
  subscriptionId,
  content,
}: Props) {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const {mutate: onChangePlan} =
    useSubscriptionsServiceSubscriptionControllerChangePlanSubscription({
      onSuccess: () => {
        queryClient.refetchQueries({
          queryKey: [
            useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
          ],
        });
        navigation.navigate('ActionModal', {
          confirmLabel: t('button.ok'),
          content,
          enableCloseOnMask: false,
          hasCancelBtn: false,
          iconSize: 54,
          image: Icons.icSuccess,
          onApply: () => {
            navigation.goBack();
            navigation.navigate('SubscriptionDetails', {
              headerTitle: `${subscriptionName || ''} ${t(
                'subscription.detail',
              )}`,
              hidePayment: true,
              isTopBarEnable: false,
              subscriptionId,
            });
          },
          title: t('subscription.yourAllSet'),
        });
      },
    });

  return {
    onChangePlan,
  };
}
