import {useCallback} from 'react';
import {
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscription,
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
} from '@queries';
import {UserSubscriptionEntity} from '@requests';

/**
 * Custom hook to fetch and manage user subscription data
 * @returns Object containing subscription data and utility functions
 */
export const useMeSubscription = () => {
  const {
    data: dataMeSubscription,
    isLoading,
    error,
    refetch,
  } = useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscription(
    {
      limit: 10,
      offset: 0,
    },
    [
      useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
    ],
  );

  const getMySubscriptionById = useCallback(
    (
      id: string,
    ): {
      isFirstTimeBuySubscription: boolean;
      isSubscribe: boolean;
      mySubscription?: UserSubscriptionEntity;
    } => {
      if (!id || !dataMeSubscription?.data?.items?.length) {
        return {
          isFirstTimeBuySubscription: true,
          isSubscribe: false,
        };
      }

      const mySubscription = dataMeSubscription.data.items.find(
        (item: UserSubscriptionEntity) =>
          item?.subscriptionId === id &&
          item?.status === UserSubscriptionEntity.status.SUBSCRIBED,
      );

      return {
        isFirstTimeBuySubscription: !(
          !!mySubscription && mySubscription?.renewDate === null
        ),
        isSubscribe:
          !!mySubscription?.renewDate ||
          (mySubscription?.isChangePlan && mySubscription?.nextPlan),
        mySubscription,
      };
    },
    [dataMeSubscription?.data?.items],
  );

  const hasActiveSubscriptions = useCallback((): boolean => {
    if (!dataMeSubscription?.data?.items?.length) return false;

    return dataMeSubscription.data.items.some(
      (item: UserSubscriptionEntity) =>
        item?.status === UserSubscriptionEntity.status.SUBSCRIBED &&
        !!item?.renewDate,
    );
  }, [dataMeSubscription?.data?.items]);

  return {
    dataMeSubscription,
    error,
    getMySubscriptionById,
    hasActiveSubscriptions,
    isLoading,
    refetch,
  };
};
