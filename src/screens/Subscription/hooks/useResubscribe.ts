import {
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
  useSubscriptionsServiceSubscriptionMeV2ControllerReSubscribeSubscription,
} from '@queries';
import {queryClient} from '@react-query/queryClient';
import useLoading from '@utils/hooks/useLoading';

export default function useResubscribe() {
  const {showLoading, dismissLoading} = useLoading();

  const {mutate: reSubscribeSubscription} =
    useSubscriptionsServiceSubscriptionMeV2ControllerReSubscribeSubscription({
      onSuccess: () => {
        showLoading();
        setTimeout(() => {
          dismissLoading();
          queryClient.refetchQueries({
            queryKey: [
              useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
            ],
            type: 'all',
          });
        }, 2000);
      },
    });

  return {
    reSubscribeSubscription,
  };
}
