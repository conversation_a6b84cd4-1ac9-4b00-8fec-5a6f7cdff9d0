import {SafeAreaView} from 'react-native';
import React from 'react';
import moment from 'moment';
import {RouteProp} from '@react-navigation/native';
import {useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransaction} from '../../api/queries';
import PackageHeader from './components/PackageHeader';
import {PlanBadge} from './components/PlanBadge';
import {SubscriptionHistoryDetailRouteProps} from '@global';
import {SubscriptionTransactionEntity} from '@requests';
import {formatNumberPrice, formatTimeLocale} from '@utils/Tools';
import {FORMAT_COUPON, STR_DATE_FORMAT} from '@themes/Constants';
import {NavHeader, WText, WView} from '@components';
import {Colors} from '@themes';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import Spin from '@components/Spin';

interface SubscriptionHistoryDetailProps {
  route: RouteProp<
    SubscriptionHistoryDetailRouteProps,
    'SubscriptionHistoryDetail'
  >;
}

function SubscriptionHistoryDetail({route}: SubscriptionHistoryDetailProps) {
  const {subscriptionHistoryItem} = route?.params || {};
  const {t} = useTypeSafeTranslation();

  const {data, isLoading} =
    useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransaction(
      {
        id: subscriptionHistoryItem?.id as string,
      },
      undefined,
      {
        enabled: !!subscriptionHistoryItem?.id,
      },
    );

  const subscriptionHistoryDetail = data?.data;

  const periodTitle = `${formatTimeLocale(
    subscriptionHistoryDetail?.subscriptionData?.periodStart,
    FORMAT_COUPON,
  )} - ${formatTimeLocale(
    subscriptionHistoryDetail?.subscriptionData?.periodEnd,
    FORMAT_COUPON,
  )}`;

  const paymentMethodTitle = `${subscriptionHistoryDetail?.card?.brand} - ${subscriptionHistoryDetail?.card?.last4}`;
  const isSuccess =
    subscriptionHistoryDetail?.status ===
    SubscriptionTransactionEntity.status.PURCHASED;

  return (
    <SafeAreaView>
      <NavHeader title={t('subscription.transactionDetails')} />
      <Spin loading={isLoading}>
        <WView mVer={12}>
          <PackageHeader
            description={formatNumberPrice(subscriptionHistoryDetail?.amount)}
            title={subscriptionHistoryDetail?.subscriptionData?.name}
            typeDesc="semiBold18"
          />
        </WView>
        <WView alignCenter justifyBetween mBottom={8} mHoz={16} row>
          <WText type="regular14">{t('subscription.plan')}</WText>
          <PlanBadge name={subscriptionHistoryDetail?.recurringType} />
        </WView>
        <WView alignCenter mBottom={8} mHoz={16} row>
          <WText type="regular14">{t('subscription.servicePeriod')}</WText>
          <WText
            capitalize
            fill
            marginLeft={12}
            textAlign="right"
            type="regular14">
            {periodTitle}
          </WText>
        </WView>
        <WView alignCenter mBottom={8} mHoz={16} row>
          <WText type="regular14">{t('subscription.processedDate')}</WText>
          <WText marginLeft="auto" type="regular14">
            {moment(subscriptionHistoryDetail?.processedDate).format(
              STR_DATE_FORMAT,
            )}
          </WText>
        </WView>
        <WView alignCenter justifyBetween mBottom={8} mHoz={16} row>
          <WText type="regular14">{t('business.status')}</WText>
          <WView
            borderRadius={6}
            color={isSuccess ? Colors.successDefault : Colors.error}
            pHoz={8}
            pVer={2}>
            <WText color={Colors.white} lineHeight={18} type="medium12">
              {isSuccess ? t('subscription.success') : t('subscription.failed')}
            </WText>
          </WView>
        </WView>
        <WView alignCenter mBottom={8} mHoz={16} row>
          <WText type="regular14">{t('payment.title')}</WText>
          <WText capitalize marginLeft="auto" type="regular14">
            {paymentMethodTitle}
          </WText>
        </WView>
        <WView alignCenter mBottom={8} mHoz={16} row>
          <WText type="regular14">{t('subscription.paymentID')}</WText>
          <WText marginLeft="auto" type="regular14">
            {subscriptionHistoryDetail?.stripePaymentIntentId}
          </WText>
        </WView>
      </Spin>
    </SafeAreaView>
  );
}

export default SubscriptionHistoryDetail;
