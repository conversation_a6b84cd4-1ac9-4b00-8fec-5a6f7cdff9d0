import {
  ActivityIndicator,
  FlatList,
  Image,
  RefreshControl,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import Icon from 'react-native-vector-icons/icomoon';
import {useNavigation} from '@react-navigation/native';
import {useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeKey} from '@queries';
import {useLoadMoreQuery} from '@utils/hooks/useLoadMoreQuery';
import {FORMAT_DOB} from '@themes/Constants';
import {formatNumberPrice, formatTimeLocale} from '@utils/Tools';
import {SubscriptionsService, SubscriptionTransactionEntity} from '@requests';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {NavHeader, WText, WView} from '@components';
import {Colors, Images} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';

function SubscriptionHistory() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const {
    data: transactions,
    refetch,
    loadMore,
    loading,
    isFetching,
    isFetchingNextPage,
    isRefetching,
  } = useLoadMoreQuery({
    query: (offset: number) =>
      SubscriptionsService.subscriptionMeControllerGetSubscriptionTransactionOfMe(
        10,
        offset,
      ),
    queryKey: [
      useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeKey,
    ],
  });

  const renderEmpty = () => {
    if (loading || isFetching || isFetchingNextPage) {
      return (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </WView>
      );
    }
    return (
      <WView alignCenter fill justifyCenter>
        <Image source={Images.emptyBooking} />
        <WText marginBottom={20} type="regular14">
          {t('empty.title')}
        </WText>
      </WView>
    );
  };

  const renderFooter = () => {
    if (
      (loading || isFetching || isFetchingNextPage) &&
      transactions?.length > 0 &&
      !isRefetching
    ) {
      return (
        <WView h={40}>
          <ActivityIndicator color={Colors.primary} size={'small'} />
        </WView>
      );
    }
    return null;
  };

  const renderItem = ({item}: {item: SubscriptionTransactionEntity}) => {
    const isSuccess =
      item?.status === SubscriptionTransactionEntity.status.PURCHASED;
    return (
      <WView pHoz={16}>
        <WText
          capitalize
          color={Colors.neutral600}
          marginTop={8}
          type="medium14">
          {formatTimeLocale(item?.processedDate, FORMAT_DOB)}
        </WText>
        <TouchableOpacity
          onPress={() => {
            navigation.navigate('SubscriptionHistoryDetail', {
              isTopBarEnable: false,
              subscriptionHistoryItem: item,
            });
          }}>
          <WView
            alignCenter
            borderColor={Colors.neutral150}
            borderRadius={8}
            borderWidth={1}
            color={Colors.white}
            mBottom={8}
            mTop={8}
            pVer={20}
            row
            style={GlobalStyle.shadow}>
            <Image source={Images.goldSubscription} style={styles.subsImg} />
            <WView>
              <WText type="medium14">{item?.subscriptionData?.name}</WText>
              <WText
                color={isSuccess ? Colors.success600 : Colors.error}
                marginVertical={4}
                type="regular12">
                {isSuccess
                  ? t('subscription.success')
                  : t('subscription.failed')}
              </WText>
              <WText type="regular12">
                {isSuccess && t('subscription.renewOn')}
                {formatTimeLocale(item?.processedDate, FORMAT_DOB)}
              </WText>
            </WView>
            <WView alignCenter mLeft="auto" mRight={6} row>
              <WText marginRight={4} type="medium16">
                {formatNumberPrice(item?.amount)}
              </WText>
              <Icon
                color={Colors.neutral500}
                name="Outline-CaretRight"
                size={20}
              />
            </WView>
          </WView>
        </TouchableOpacity>
      </WView>
    );
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader title={t('subscription.planHistory')} />
      <WView fill pTop={12}>
        <FlatList
          ListEmptyComponent={renderEmpty}
          ListFooterComponent={renderFooter}
          contentContainerStyle={styles.flatList}
          data={transactions}
          keyExtractor={(_, index) => `${index}`}
          onEndReached={() => {
            loadMore();
          }}
          onEndReachedThreshold={0.2}
          refreshControl={
            <RefreshControl
              colors={[Colors.primary]}
              onRefresh={refetch}
              refreshing={false}
              tintColor={Colors.primary}
            />
          }
          renderItem={renderItem}
        />
      </WView>
    </SafeAreaView>
  );
}

export default SubscriptionHistory;

const styles = StyleSheet.create({
  flatList: {flexGrow: 1},
  subsImg: {height: 54, marginLeft: 8, marginRight: 12, width: 54},
});
