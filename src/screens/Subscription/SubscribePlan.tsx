import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {Platform, SafeAreaView, ScrollView, StyleSheet} from 'react-native';
import ListPayment from '../Payment/ListPayment';
import PackageHeader from './components/PackageHeader';
import useLoading from '@utils/hooks/useLoading';
import {SubscribePlanRouteProps} from '@global';
import {
  useSubscriptionsServiceSubscriptionControllerSubscribeSubscription,
  useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
} from '@queries';
import {
  BookingEntity,
  CardEntity,
  SubscriptionPlanEntity,
  UserSubscriptionEntity,
} from '@requests';
import {FORMAT_DOB, WALLET} from '@themes/Constants';
import usePayment from '@utils/hooks/usePayment';
import {
  capitalizeFirstLetter,
  formatNumberPrice,
  formatTimeLocale,
} from '@utils/Tools';
import {calculatePlanPrices} from '@utils/subscription';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors} from '@themes';
import {NavHeader, WButton, WText, WView} from '@components';
import MixPanelSdk from '@utils/mixPanelSdk';
import {useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';

interface SubscribePlanProps {
  route: RouteProp<SubscribePlanRouteProps, 'SubscribePlan'>;
}

function SubscribePlan({route}: SubscribePlanProps) {
  const {t} = useTypeSafeTranslation();
  const {subscription, selectedPlan} = route?.params || {};
  const navigation = useNavigation();
  const [userData] = useGlobalState('userData');

  const {showLoading, dismissLoading} = useLoading();

  const {
    applePay,
    googlePay,
    payByCard,
    defaultPaymentMethod,
    isWalletSupported,
  } = usePayment({
    onSuccess: () => {
      showLoading();
      MixPanelSdk.logEvent({
        eventName: 'loyal_red_subscribe',
        eventValues: {
          email: userData?.user?.email,
          phoneNumber: userData?.user?.phoneNumber,
          userName: userData?.user?.fullName,
        },
      });
      setTimeout(() => {
        dismissLoading();
        queryClient.refetchQueries({
          queryKey: [
            useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
          ],
        });
        navigation.navigate('SuccessSubscribeModal', {
          headerTitle: subscription?.name || selectedPlan?.name,
        });
      }, 2000);
    },
  });

  const [paymentMethod, setPaymentMethod] = useState<CardEntity | undefined>(
    defaultPaymentMethod,
  );

  const {finalAmount} = calculatePlanPrices(
    selectedPlan as SubscriptionPlanEntity,
    t,
  );

  useEffect(() => {
    setPaymentMethod(defaultPaymentMethod);
  }, [defaultPaymentMethod]);

  const handlePayment = (data: {data: UserSubscriptionEntity}) => {
    const subscriptionData = data?.data;
    const clientSecret =
      subscriptionData?.paymentData?.paymentIntentClientSecret;

    if (paymentMethod?.id === WALLET?.id) {
      if (Platform.OS === 'ios') {
        return applePay(clientSecret, subscription?.name || '', {
          amount: finalAmount || 0,
        } as BookingEntity);
      }
      return googlePay(clientSecret, {
        amount: finalAmount || 0,
      } as BookingEntity);
    }
    payByCard(clientSecret, paymentMethod?.id as string);
  };

  const {mutate: onSubscribe} =
    useSubscriptionsServiceSubscriptionControllerSubscribeSubscription({
      onSuccess: handlePayment,
    });

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader title={t('subscription.title')} />
      <ScrollView style={styles.content}>
        <PackageHeader
          description={t('subscription.billingStarts', {
            count: capitalizeFirstLetter(
              formatTimeLocale(new Date(), FORMAT_DOB),
            ),
          })}
          title={
            selectedPlan?.recurringType ===
            SubscriptionPlanEntity.recurringType.MONTHLY_1
              ? t('subscription.monthlyCharge')
              : t('subscription.yearlyCharge')
          }
        />
        <WView
          borderColor={Colors.neutral150}
          borderRadius={8}
          borderWidth={1}
          color={Colors.white}
          mHoz={16}
          mTop={20}
          padding={16}
          style={GlobalStyle.shadowSoft}>
          <WText type="semiBold18">{t('subscription.billing')}</WText>
          <WView alignCenter mTop={16} row>
            <WText type="regular14">{t('subscription.planPrice')}</WText>
            <WText marginLeft={'auto'} type="semiBold14">
              {formatNumberPrice(finalAmount)}
            </WText>
          </WView>
          <WView color={Colors.neutral150} h={1} mVer={12} />
          <WView alignCenter row>
            <WView>
              <WText type="medium16">{t('subscription.totalPrice')}</WText>
              <WText color={Colors.neutral600} type="regular12">
                {t('subscription.includingTax')}
              </WText>
            </WView>
            <WText marginLeft={'auto'} type="semiBold18">
              {formatNumberPrice(finalAmount)}
            </WText>
          </WView>
        </WView>
        <WView
          borderColor={Colors.neutral150}
          borderRadius={8}
          borderWidth={1}
          color={Colors.white}
          fill
          mHoz={16}
          mTop={12}
          style={GlobalStyle.shadowSoft}>
          <WText marginLeft={16} marginTop={16} type="semiBold18">
            {t('payment.title')}
          </WText>
          <ListPayment
            route={
              {
                params: {
                  defaultPayment: defaultPaymentMethod,
                  hideCash: true,
                  hideHeader: true,
                  isFromBooking: true,
                  onApply: (res: CardEntity) => {
                    setPaymentMethod(res);
                  },
                },
              } as any
            }
          />
        </WView>
        <WText
          marginBottom={24}
          marginHorizontal={16}
          marginTop={12}
          type="regular12">
          {t('subscription.billNote', {
            value:
              selectedPlan?.recurringType ===
              SubscriptionPlanEntity.recurringType.MONTHLY_1
                ? t('subscription.month')
                : t('subscription.year'),
          })}
        </WText>
      </ScrollView>
      <WView style={[styles.shadowView, GlobalStyle.shadowCard]} />
      <WView color={Colors.white} pBottom={12} pHoz={16} pTop={16}>
        <WButton
          disabled={
            !isWalletSupported &&
            (defaultPaymentMethod?.id === WALLET?.id || !defaultPaymentMethod)
          }
          label={t('button.subscribeNow')}
          onPress={async () =>
            onSubscribe({
              requestBody: {
                planId: selectedPlan?.id || '',
              },
            })
          }
        />
      </WView>
    </SafeAreaView>
  );
}

export default SubscribePlan;

const styles = StyleSheet.create({
  content: {
    marginTop: 12,
  },
  shadowView: {
    backgroundColor: Colors.white,
    height: 10,
    marginBottom: -9,
  },
});
