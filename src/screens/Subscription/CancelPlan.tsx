import {RouteProp} from '@react-navigation/native';
import React, {useState} from 'react';
import {
  InputAccessoryView,
  Keyboard,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TextInput,
} from 'react-native';
import useUnsubscription from './hooks/useUnsubscription';
import {Nav<PERSON><PERSON><PERSON>, WButton, WText, WView} from '@components';
import {CancelPlanRouteProps} from '@global';
import {Colors} from '@themes';
import {
  INPUT_ACCESSORY_VIEW_ID,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface CancelPlanProps {
  route: RouteProp<CancelPlanRouteProps, 'CancelPlan'>;
}

function CancelPlan({route}: CancelPlanProps) {
  const {subscription} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const [note, setNote] = useState<string | undefined>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const {cancelRenewSubscription, isPending} = useUnsubscription({
    name: subscription?.name || '',
    renewDate: subscription?.renewDate || subscription?.endDate,
  });

  const onChangeNote = (text?: string) => {
    setNote(text);
  };

  const onCancelBooking = async () => {
    if (isPending || isProcessing) return;

    setIsProcessing(true);

    Keyboard.dismiss();
    await cancelRenewSubscription({
      id: subscription?.subscriptionId ?? '',
      requestBody: {
        cancelReason: note,
      },
    });
    setTimeout(() => {
      setIsProcessing(false);
    }, 2000);
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader title={t('subscription.unsubscribePackage')} />
      <ScrollView bounces={false}>
        <WView mHoz={16} mTop={32}>
          <WText lineHeight={24} marginBottom={24} type="semiBold18">
            {t('subscription.cancelTitle')}
          </WText>
          <TextInput
            allowFontScaling={false}
            inputAccessoryViewID={INPUT_ACCESSORY_VIEW_ID}
            multiline
            onChangeText={text => onChangeNote(text)}
            placeholder={t('rating.enterNote')}
            style={styles.input}
            textAlignVertical={'top'}
          />
        </WView>
      </ScrollView>
      <WButton
        disabled={isPending || isProcessing}
        label={t('button.confirm')}
        loading={isPending}
        onPress={onCancelBooking}
        style={styles.cancelBtn}
      />
      {Platform.OS === 'ios' && (
        <InputAccessoryView nativeID={INPUT_ACCESSORY_VIEW_ID}>
          <WView>
            <WButton
              disabled={isPending || isProcessing}
              label={t('subscription.unsubscribePackage')}
              loading={isPending}
              onPress={onCancelBooking}
              style={styles.cancelBtn}
            />
          </WView>
        </InputAccessoryView>
      )}
    </SafeAreaView>
  );
}

export default CancelPlan;

const styles = StyleSheet.create({
  cancelBtn: {marginBottom: 12, marginHorizontal: 16, marginTop: 'auto'},
  input: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral400,
    borderRadius: 8,
    borderWidth: 1,
    height: SCREEN_HEIGHT / 8,
    marginTop: 'auto',
    marginVertical: 12,
    padding: 15,
    paddingTop: 15,
    width: SCREEN_WIDTH - 32,
  },
});
