import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useCallback, useMemo} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  ListRenderItem,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import PackageCard from './components/list/PackageCard';
import {useMeSubscription} from './hooks/useMeSubscription';
import {Colors, Icons, Images} from '@themes';
import {NavHeader, WImage, WText, WView} from '@components';
import {useSubscriptionsServiceSubscriptionControllerGetAppSubscriptions} from '@queries';
import {AppSubscriptionEntity, UserSubscriptionEntity} from '@requests';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {SubscriptionRouteProps} from '@global';
import {ISubscription} from '@interface/subscription';
import MixPanelSdk from '@utils/mixPanelSdk';
import {useGlobalState} from '@react-query/clientStateManage';

interface SubscriptionProps {
  route: RouteProp<SubscriptionRouteProps, 'Subscription'>;
}

function Subscription({route}: SubscriptionProps) {
  const {hidePayment = false} = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const areaInsets = useSafeAreaInsets();
  const [userData] = useGlobalState('userData');

  const {dataMeSubscription} = useMeSubscription();

  const {
    data,
    isLoading: loading,
    isFetching,
  } = useSubscriptionsServiceSubscriptionControllerGetAppSubscriptions({
    limit: 20,
    offset: 0,
  });

  const subscriptionPackages: ISubscription[] = useMemo(() => {
    if (!dataMeSubscription?.data?.items?.length) {
      return data?.data?.items || [];
    }

    return (data?.data?.items || []).map((item: AppSubscriptionEntity) => {
      const userSubscription = dataMeSubscription?.data?.items?.find(
        (ele: UserSubscriptionEntity) =>
          ele?.subscriptionId === item?.id &&
          ele?.status === UserSubscriptionEntity.status.SUBSCRIBED,
      );

      if (userSubscription) {
        return {
          ...userSubscription,
          ...item,
          endDate: userSubscription?.endDate,
          isSubscribe:
            !!userSubscription?.renewDate ||
            (userSubscription?.isChangePlan && userSubscription?.nextPlan),
          plan: userSubscription?.plan,
          plans: item?.plans,
          renewDate: userSubscription?.renewDate,
        };
      }

      return item;
    });
  }, [dataMeSubscription?.data?.items, data?.data?.items]);

  const handleNavigateToDetails = useCallback(
    (item: ISubscription) => {
      MixPanelSdk.logEvent({
        eventName: 'loyal_red_view_detail',
        eventValues: {
          email: userData?.user?.email,
          phoneNumber: userData?.user?.phoneNumber,
          userName: userData?.user?.fullName,
        },
      });

      navigation.navigate('SubscriptionDetails', {
        headerTitle: `${item?.name || ''} ${t('subscription.detail')}`,
        hidePayment,
        isTopBarEnable: false,
        subscriptionId: item?.id ?? '',
      });
    },
    [hidePayment, navigation, t, userData?.user],
  );

  const renderItem: ListRenderItem<ISubscription> = useCallback(
    ({item}) => (
      <PackageCard data={item} onPress={() => handleNavigateToDetails(item)} />
    ),
    [handleNavigateToDetails],
  );

  const renderEmpty = useCallback(() => {
    if (loading || isFetching) {
      return (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.primary} size="large" />
        </WView>
      );
    }
    return (
      <WView alignCenter fill justifyCenter>
        <Image source={Images.emptySubscription} />
        <WText marginBottom={20} marginTop={24} type="regular14">
          {t('empty.subscription')}
        </WText>
      </WView>
    );
  }, [loading, isFetching, t]);

  const navigateToHistory = useCallback(() => {
    navigation.navigate('SubscriptionHistory', {
      isTopBarEnable: false,
    });
  }, [navigation]);

  const renderHistoryButton = () => (
    <TouchableOpacity onPress={navigateToHistory}>
      <WImage size={20} source={Icons.icHistory} />
    </TouchableOpacity>
  );

  const renderSeparator = useCallback(() => <WView h={16} />, []);

  return (
    <WView pTop={areaInsets.top} style={GlobalStyle.flex1}>
      <NavHeader
        renderRightButton={renderHistoryButton}
        title={t('subscription.title')}
      />
      <WView fill pTop={12}>
        <FlatList
          ItemSeparatorComponent={renderSeparator}
          ListEmptyComponent={renderEmpty}
          ListFooterComponent={renderSeparator}
          ListHeaderComponent={renderSeparator}
          contentContainerStyle={styles.flatList}
          data={subscriptionPackages}
          keyExtractor={item => item.id}
          renderItem={renderItem}
        />
      </WView>
    </WView>
  );
}

export default Subscription;

const styles = StyleSheet.create({
  flatList: {flexGrow: 1},
});
