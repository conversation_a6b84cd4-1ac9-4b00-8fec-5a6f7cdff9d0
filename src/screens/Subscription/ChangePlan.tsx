/* eslint-disable react-hooks/exhaustive-deps */
import {SafeAreaView, StyleSheet} from 'react-native';
import React, {useState, useMemo, useCallback} from 'react';
import {RouteProp} from '@react-navigation/native';
import moment from 'moment';
import PlanOption from './components/detail/PlanOption';
import useChangePlan from './hooks/useChangePlan';
import NavHeader from '@components/NavHeader';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {WButton, WText, WView} from '@components/index';
import {Colors} from '@themes/index';
import {ChangePlanRouteProps} from '@global';
import {calculatePlanPrices, getPlanName} from '@utils/subscription';
import {formatNumberPrice, formatTimeLocale} from '@utils/Tools';
import {FORMAT_DOB} from '@themes/Constants';
import {SubscriptionPlanEntity} from '@requests';

interface ChangePlanProps {
  route?: RouteProp<ChangePlanRouteProps, 'ChangePlan'>;
}

/**
 * Screen for changing subscription plan
 */
function ChangePlan({route}: ChangePlanProps) {
  const {subscription} = route?.params || {};
  const {t} = useTypeSafeTranslation();

  const currentPlan = useMemo(() => {
    if (subscription?.nextPlan && subscription?.isChangePlan) {
      return subscription?.nextPlan;
    }
    return subscription?.plan;
  }, [subscription?.plan]);

  const [selectedPlan, setSelectedPlan] = useState(currentPlan);

  const startDate = useMemo(
    () =>
      moment(subscription?.renewDate || subscription?.endDate).toISOString(),
    [subscription?.renewDate],
  );

  const formattedStartDate = useMemo(
    () => formatTimeLocale(startDate ?? '', FORMAT_DOB),
    [startDate],
  );

  const {onChangePlan} = useChangePlan({
    content:
      subscription?.nextPlan && subscription?.isChangePlan
        ? t('subscription.switchPlan', {
            value: getPlanName(subscription?.plan?.recurringType, true),
          })
        : t('subscription.changePlanDescription', {
            plan: getPlanName(selectedPlan?.recurringType, true),
            value: formatTimeLocale(
              subscription?.renewDate ?? (subscription?.endDate || ''),
              FORMAT_DOB,
            ),
          }),
    subscriptionId: subscription?.subscriptionId ?? '',
    subscriptionName: subscription?.name,
  });

  const handleChangePlan = useCallback(() => {
    if (subscription?.subscriptionId && selectedPlan?.id) {
      onChangePlan({
        id: subscription.subscriptionId,
        requestBody: {
          planId: selectedPlan.id,
        },
      });
    }
  }, [subscription?.subscriptionId, onChangePlan, selectedPlan?.id]);

  const handleSelectPlan = useCallback((plan: SubscriptionPlanEntity) => {
    setSelectedPlan(plan);
  }, []);

  const renderPlanOption = useCallback(
    (plan: SubscriptionPlanEntity) => {
      const priceInfo = calculatePlanPrices(
        plan,
        t,
        ((subscription?.appliedDiscounts ?? [])?.length > 0 &&
          plan?.id === subscription?.plan?.id) ||
          subscription?.isChangePlan,
      );
      const isSelected = selectedPlan?.id === plan.id;
      const isCurrentPlan = currentPlan?.id === plan.id;

      const period =
        plan.recurringType === SubscriptionPlanEntity.recurringType.MONTHLY_1
          ? t('subscription.month')
          : t('subscription.year');

      const price = priceInfo.hasDiscount
        ? priceInfo.discountedPrice
        : formatNumberPrice(plan.amount);

      return (
        <PlanOption
          discount={priceInfo.hasDiscount ? priceInfo.discountLabel : undefined}
          equivalentPrice={
            priceInfo.hasDiscount ? priceInfo.equivalentPrice : undefined
          }
          isCurrentPlan={isCurrentPlan}
          isSelected={isSelected}
          key={plan.id}
          onSelect={() => handleSelectPlan(plan)}
          originalPrice={
            priceInfo.hasDiscount ? priceInfo.originalPrice : undefined
          }
          period={period}
          price={price}
        />
      );
    },
    [currentPlan?.id, handleSelectPlan, selectedPlan?.id, t],
  );

  const isButtonDisabled = useMemo(
    () => selectedPlan?.id === currentPlan?.id,
    [selectedPlan?.id, currentPlan?.id],
  );

  return (
    <SafeAreaView style={GlobalStyle.container}>
      <NavHeader title={t('subscription.changePlan')} />

      <WView fill mHoz={16} mTop={16}>
        <WText type="regular14">
          {t('subscription.noteChangePlan', {
            date: formattedStartDate,
          })}
        </WText>

        <WView gap={8} mTop={12}>
          {subscription?.plans?.map(renderPlanOption)}
        </WView>
      </WView>

      <WView>
        <WView style={[styles.shadowView, GlobalStyle.shadowCar]} />
        <WView color={Colors.white} gap={12} pBottom={12} pHoz={16} pTop={16}>
          <WText center type="medium12">
            {t('subscription.changePlanNote', {
              date: formatTimeLocale(
                subscription?.renewDate || subscription?.endDate || '',
                FORMAT_DOB,
              ),
            })}
          </WText>
          <WButton
            disabled={isButtonDisabled}
            label={t('button.applyChange')}
            onPress={handleChangePlan}
          />
        </WView>
      </WView>
    </SafeAreaView>
  );
}

export default ChangePlan;

const styles = StyleSheet.create({
  shadowView: {
    backgroundColor: Colors.white,
    height: 10,
    marginBottom: -9,
  },
});
