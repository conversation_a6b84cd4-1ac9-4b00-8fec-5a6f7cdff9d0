import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useCallback, useMemo, useRef, useState} from 'react';
import {SafeAreaView, ScrollView, StyleSheet} from 'react-native';
import CollapseHeader from './components/CollapseHeader';
import WebDisplay from './components/WebDisplay';
import ChoosePlan from './components/ChoosePlan';
import useChangePaymentMethod from './hooks/useChangePaymentMethod';
import {useMeSubscription} from './hooks/useMeSubscription';
import PackageCard from './components/list/PackageCard';
import PackageHeader from './components/PackageHeader';
import useResubscribe from './hooks/useResubscribe';
import BenefitsSection from './components/detail/BenefitsSection';
import ManagePlanSection from './components/detail/ManagePlanSection';
import SubscriptionFooter from './components/detail/SubscriptionFooter';
import {NavHeader, WView} from '@components';
import {SubscriptionDetailsRouteProps} from '@global';
import {
  useSubscriptionsServiceSubscriptionControllerGetAppSubscription,
  useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscription,
} from '@queries';
import {
  AppSubscriptionEntity,
  CouponEntity,
  SubscriptionPlanEntity,
  UserSubscriptionEntity,
} from '@requests';
import {FORMAT_DOB} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import usePayment from '@utils/hooks/usePayment';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {formatTimeLocale} from '@utils/Tools';
import Spin from '@components/Spin';
import {ISubscription} from '@interface/subscription';

interface SubscriptionDetailProps {
  route?: RouteProp<SubscriptionDetailsRouteProps, 'SubscriptionDetails'>;
}

/**
 * Subscription details screen component
 * Displays subscription information, benefits, and management options
 */
function SubscriptionDetails({route}: SubscriptionDetailProps) {
  const {
    headerTitle,
    hidePayment = false,
    subscriptionId,
  } = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation<any>();
  const scrollRef = useRef<ScrollView>(null);

  const [expandBenefits, setExpandBenefits] = useState(false);
  const [expandTermOfUse, setExpandTermOfUse] = useState(false);
  const [expandUsageInstruction, setExpandUsageInstruction] = useState(false);
  const [selectedPlan, setSelectedPlan] =
    useState<SubscriptionPlanEntity | null>(null);

  const {getMySubscriptionById} = useMeSubscription();
  const {defaultPaymentMethod} = usePayment();
  const {reSubscribeSubscription} = useResubscribe();
  const {handleNavigatePayment} = useChangePaymentMethod({
    enabled: expandBenefits,
    hidePayment,
    subscriptionId,
  });

  const {data, isLoading} =
    useSubscriptionsServiceSubscriptionControllerGetAppSubscription(
      {
        id: subscriptionId ?? '',
      },
      undefined,
      {
        enabled: !!subscriptionId,
      },
    );

  const {data: couponSubscription} =
    useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscription({
      id: subscriptionId ?? '',
      limit: 50,
      offset: 0,
    });

  const coupons = useMemo(
    () => couponSubscription?.data?.items || [],
    [couponSubscription?.data?.items],
  );

  const {isFirstTimeBuySubscription, isSubscribe, mySubscription} = useMemo(
    () => getMySubscriptionById(subscriptionId ?? ''),
    [getMySubscriptionById, subscriptionId],
  );

  const subscriptionDetail = useMemo(
    () => ({
      ...(data?.data as AppSubscriptionEntity),
      ...(mySubscription && {
        endDate: mySubscription?.endDate,
        isSubscribe:
          !!mySubscription?.renewDate ||
          (mySubscription?.isChangePlan && mySubscription?.nextPlan),
        renewDate: mySubscription?.renewDate,
        ...mySubscription,
      }),
    }),
    [data?.data, mySubscription],
  );

  const formattedEndDate = useMemo(
    () => formatTimeLocale(mySubscription?.endDate ?? '', FORMAT_DOB),
    [mySubscription?.endDate],
  );

  const formattedRenewDate = useMemo(
    () => formatTimeLocale(mySubscription?.renewDate ?? '', FORMAT_DOB),
    [mySubscription?.renewDate],
  );

  const toggleBenefits = useCallback(() => {
    setExpandBenefits(prev => !prev);
  }, []);

  const toggleUsageInstruction = useCallback(() => {
    setExpandUsageInstruction(prev => {
      if (!prev) {
        setTimeout(() => {
          scrollRef?.current?.scrollToEnd();
        }, 100);
      }
      return !prev;
    });
  }, []);

  const toggleTermOfUse = useCallback(() => {
    setExpandTermOfUse(prev => !prev);
  }, []);

  const handleCouponPress = useCallback(
    (coupon: CouponEntity) => {
      navigation.navigate('CouponDetail', {
        coupon,
        isTopBarEnable: false,
      });
    },
    [navigation],
  );

  const handleResubscribe = useCallback(() => {
    if (mySubscription?.subscriptionId) {
      reSubscribeSubscription({
        id: mySubscription.subscriptionId,
      });
    }
  }, [reSubscribeSubscription, mySubscription?.subscriptionId]);

  const handleContinue = useCallback(() => {
    navigation.navigate('SubscribePlan', {
      isTopBarEnable: false,
      selectedPlan,
      subscription: subscriptionDetail,
    } as any);
  }, [navigation, selectedPlan, subscriptionDetail]);

  const handleUnsubscribe = useCallback(() => {
    navigation.navigate('CancelSubscriptionModal', {
      isTopBarEnable: false,
      subscription: subscriptionDetail,
    } as any);
  }, [navigation, subscriptionDetail]);

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader title={headerTitle} />
      <Spin loading={isLoading}>
        <ScrollView
          ref={scrollRef}
          showsVerticalScrollIndicator={false}
          style={styles.container}>
          {isSubscribe || (!isSubscribe && !isFirstTimeBuySubscription) ? (
            <PackageCard data={subscriptionDetail as ISubscription} isDetail />
          ) : (
            <PackageHeader
              description={subscriptionDetail?.description || ''}
              title={subscriptionDetail?.name || ''}
            />
          )}

          <ChoosePlan
            isFirstTimeBuySubscription={isFirstTimeBuySubscription}
            isSubscribe={isSubscribe}
            mySubscription={mySubscription as UserSubscriptionEntity}
            selectedPlan={selectedPlan}
            setSelectedPlan={setSelectedPlan}
            subscription={subscriptionDetail as any}
          />

          <BenefitsSection
            coupons={coupons}
            onCouponPress={handleCouponPress}
          />

          {isSubscribe && (
            <ManagePlanSection
              isOpen={expandBenefits}
              onEditPayment={handleNavigatePayment}
              onToggle={toggleBenefits}
              paymentMethod={defaultPaymentMethod}
            />
          )}

          <CollapseHeader
            isOpen={expandUsageInstruction}
            onPress={toggleUsageInstruction}
            title={t('subscription.usageInstruction')}
          />
          {expandUsageInstruction && (
            <WebDisplay html={subscriptionDetail?.usageInstruction} />
          )}

          <CollapseHeader
            isOpen={expandTermOfUse}
            onPress={toggleTermOfUse}
            title={t('subscription.termOfUse')}
          />
          <WView
            mBottom={!isSubscribe && !isFirstTimeBuySubscription ? 180 : 12}>
            {expandTermOfUse && !!subscriptionDetail?.termsOfUse && (
              <WebDisplay html={subscriptionDetail?.termsOfUse} />
            )}
          </WView>
        </ScrollView>

        <SubscriptionFooter
          formattedEndDate={formattedEndDate}
          formattedRenewDate={formattedRenewDate}
          isFirstTimeBuySubscription={isFirstTimeBuySubscription}
          isSubscribe={isSubscribe}
          onContinue={handleContinue}
          onResubscribe={handleResubscribe}
          onUnsubscribe={handleUnsubscribe}
          recurringType={subscriptionDetail?.plan?.recurringType}
          subscriptionStatus={
            data?.data?.status as AppSubscriptionEntity.status
          }
        />
      </Spin>
    </SafeAreaView>
  );
}

export default SubscriptionDetails;

const styles = StyleSheet.create({
  container: {marginTop: 16},
});
