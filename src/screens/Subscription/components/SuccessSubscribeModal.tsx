import {Image, StyleSheet} from 'react-native';
import React from 'react';
import {RouteProp} from '@react-navigation/native';
import {SuccessSubscribeModalRouteProps} from '../../../../global';
import {WButton, WText, WView} from '@components';
import ModalWrapper from '@components/Modal/ModalWrapper';
import {Colors, Images} from '@themes';
import {SCREEN_WIDTH} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {PopTo} from '@navigation/utils';

interface SuccessSubscribeModalProps {
  route: RouteProp<SuccessSubscribeModalRouteProps, 'SuccessSubscribeModal'>;
}

function SuccessSubscribeModal({route}: SuccessSubscribeModalProps) {
  const {headerTitle} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  return (
    <ModalWrapper enableCloseOnMask={false} style={styles.container}>
      <WView borderRadius={8} color={Colors.white} mHoz={16}>
        <WView
          alignCenter
          borderTopLeftRadius={8}
          borderTopRightRadius={8}
          color={Colors.pink}
          h={100}>
          <Image
            resizeMode="contain"
            source={Images.firework}
            style={styles.bgImg}
          />
          <Image source={Images.goldSubscription} style={styles.subsImg} />
        </WView>
        <WText center marginTop={24} type="semiBold22">
          {t('subscription.welcome', {count: headerTitle})}
        </WText>
        <WText center color={Colors.neutral700} marginTop={4} type="regular14">
          {t('subscription.welcomeContent')}
        </WText>
        <WButton
          label={t('button.confirm')}
          onPress={() => {
            PopTo(2);
          }}
          style={styles.confirmBtn}
        />
      </WView>
    </ModalWrapper>
  );
}

export default SuccessSubscribeModal;

const styles = StyleSheet.create({
  bgImg: {
    height: 100,
    position: 'absolute',
    width: SCREEN_WIDTH - 32,
  },
  confirmBtn: {marginHorizontal: 16, marginVertical: 24},
  container: {justifyContent: 'center'},
  subsImg: {height: 92, marginTop: 28, width: 92},
});
