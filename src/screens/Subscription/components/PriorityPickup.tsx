/* eslint-disable react-native/no-inline-styles */
import {Image, StyleSheet} from 'react-native';
import {WText, WView} from '@components/index';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors, Images} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useBirthdayEvent} from '@utils/hooks/useBirthdayEvent';

export function PriorityPickup() {
  const {isHappening} = useBirthdayEvent();
  const {t} = useTypeSafeTranslation();
  return (
    <WView mHoz={16}>
      <WView
        alignCenter
        borderColor={Colors.neutral150}
        borderRadius={4}
        borderWidth={1}
        color={Colors.white}
        row
        style={GlobalStyle.shadowSoft}>
        <WView
          alignCenter
          borderBottomLeftRadius={4}
          borderTopLeftRadius={4}
          color={Colors.white}
          h={84}
          justifyCenter
          padding={1}
          style={{overflow: 'hidden'}}
          w={84}>
          <Image
            resizeMode="contain"
            source={isHappening ? Images.couponBirthday : Images.priorityPickup}
            style={styles.couponImg}
          />
        </WView>
        <WView fill mLeft={12}>
          <WText marginRight={12} numberOfLine={2} type="medium14">
            {t('subscription.priorityPickup')}
          </WText>
        </WView>
      </WView>
      <WView style={styles.subsContainer}>
        <Image source={Images.goldSubscription} style={styles.subsImg} />
      </WView>
    </WView>
  );
}

const styles = StyleSheet.create({
  couponImg: {
    borderBottomLeftRadius: 4,
    borderTopLeftRadius: 4,
    height: '100%',
    marginLeft: 2,
  },
  subsContainer: {left: -3, position: 'absolute', top: -2},
  subsImg: {height: 24, width: 24},
});
