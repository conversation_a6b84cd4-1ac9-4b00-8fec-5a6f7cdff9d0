import React from 'react';
import {WText, WView} from '@components/index';
import {Colors} from '@themes/index';
import {StatusBadgeProps} from '@interface/subscription';

/**
 * Status badge component for subscription status
 */
const StatusBadge = React.memo(({color, label}: StatusBadgeProps) => {
  if (!label) return null;
  
  return (
    <WView>
      <WView borderRadius={6} color={color} pHoz={8} pVer={2}>
        <WText color={Colors.white} type="medium12">
          {label}
        </WText>
      </WView>
    </WView>
  );
});

export default StatusBadge;
