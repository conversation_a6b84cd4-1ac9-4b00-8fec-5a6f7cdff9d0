/* eslint-disable react-hooks/exhaustive-deps */
import React, {useCallback, useState} from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import PlanOption from '../detail/PlanOption';
import {WView, WText, WButton} from '@components/index';
import {Colors} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {AppSubscriptionEntity, SubscriptionPlanEntity} from '@requests';
import {calculatePlanPrices} from '@utils/subscription';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  dataDetail: AppSubscriptionEntity;
}

function ModalSubscribeNow({isVisible, onClose, dataDetail}: Props) {
  const {t} = useTypeSafeTranslation();
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation();
  const [selectedPlan, setSelectedPlan] =
    useState<SubscriptionPlanEntity | null>(dataDetail?.plans?.[0]);

  const handleSelectPlan = useCallback(
    (plan: SubscriptionPlanEntity) => {
      setSelectedPlan(plan);
    },
    [setSelectedPlan],
  );

  const handleContinue = useCallback(() => {
    onClose();
    navigation.navigate('SubscribePlan', {
      isTopBarEnable: false,
      selectedPlan,
      subscription: dataDetail,
    } as any);
  }, [navigation, selectedPlan, dataDetail]);

  const renderPlanOption = useCallback(
    (plan: SubscriptionPlanEntity, index: number) => {
      const period =
        plan.recurringType === SubscriptionPlanEntity.recurringType.MONTHLY_1
          ? t('subscription.month')
          : t('subscription.year');

      const {
        originalPrice,
        discountedPrice,
        discountLabel,
        equivalentPrice,
        hasDiscount,
      } = calculatePlanPrices(plan, t);

      return (
        <PlanOption
          discount={hasDiscount ? discountLabel : undefined}
          equivalentPrice={
            hasDiscount && equivalentPrice ? equivalentPrice : undefined
          }
          isSelected={selectedPlan?.recurringType === plan.recurringType}
          key={plan.id || index}
          onSelect={() => handleSelectPlan(plan)}
          originalPrice={hasDiscount ? originalPrice : undefined}
          period={period}
          price={hasDiscount ? discountedPrice : originalPrice}
        />
      );
    },
    [handleSelectPlan, selectedPlan?.recurringType, t],
  );

  return (
    <Modal
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropOpacity={0.5}
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      propagateSwipe
      style={styles.modal}
      swipeDirection="down">
      <WView
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        color={Colors.white}
        pBottom={bottom}>
        <WView justifyBetween pHoz={16} pTop={24} pVer={12} row>
          <WView gap={8}>
            <WText type="semiBold18">{t('subscription.chooseYourPlan')}</WText>
            <WText type="regular12">
              {t('subscription.chooseYourPlanDesc')}
            </WText>
          </WView>
          <TouchableOpacity hitSlop={GlobalStyle.hitSlop} onPress={onClose}>
            <Ionicons color={Colors.neutral500} name="close-circle" size={20} />
          </TouchableOpacity>
        </WView>

        <WView color={Colors.neutral150} h={1} />

        <WView gap={16} pHoz={16} pVer={16}>
          {dataDetail?.plans?.map(renderPlanOption)}
        </WView>

        <WView mHoz={16}>
          <WButton label={t('button.continue')} onPress={handleContinue} />
        </WView>
      </WView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
});

export default ModalSubscribeNow;
