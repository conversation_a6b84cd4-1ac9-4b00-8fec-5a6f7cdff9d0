/* eslint-disable react-native/no-raw-text */
import React, {useMemo, useCallback} from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import {PlanBadge} from '../PlanBadge';
import PackageInfo from './PackageInfo';
import CountdownTimer from './CountdownTimer';
import ModalSubscribeNow from './ModalSubsribeNow';
import {WView} from '@components/index';
import WText from '@components/WText';
import WTouchable from '@components/WTouchable';
import WImage from '@components/WImage';

import {Colors, Images} from '@themes/index';
import Icons from '@themes/Icons';
import {FORMAT_DOB} from '@themes/Constants';

import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {formatNumberPrice, formatTimeLocale} from '@utils/Tools';
import {
  calculateMonthlyPrice,
  getMinAmount,
  getPlanName,
  processDiscount,
} from '@utils/subscription';

import {
  AppSubscriptionEntity,
  SubscriptionCouponEntity,
  SubscriptionPlanEntity,
} from '@requests';
import {PackageCardProps} from '@interface/subscription';
import {useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscription} from '@queries';
import {useModal} from '@utils/hooks/useModal';

/**
 * Renders a subscription package card with different states based on subscription status
 */
function PackageCard({onPress, data, isDetail}: PackageCardProps) {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const {isVisible, closeModal, openModal} = useModal();

  const isDefaultSubscription =
    !data?.isSubscribe && data?.isSubscribe !== false;

  const {data: couponSubscription} =
    useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscription(
      {
        id: data?.id ?? '',
        limit: 50,
        offset: 0,
      },
      [data?.id],
      {
        enabled: !!data?.id && isDefaultSubscription,
      },
    );

  const isOneMonth = useMemo(
    () =>
      data?.plans?.length === 1 &&
      data?.plans?.[0]?.recurringType ===
        SubscriptionPlanEntity.recurringType.MONTHLY_1,
    [data?.plans],
  );

  const priceAmount = useMemo(
    () =>
      isOneMonth
        ? formatNumberPrice(data?.plans?.[0]?.amount)
        : formatNumberPrice(getMinAmount(data?.plans || [])),
    [isOneMonth, data?.plans],
  );

  const priceLabel = useMemo(
    () => (isOneMonth ? t('subscription.paidMonthly') : t('subscription.from')),
    [isOneMonth, t],
  );

  const renderSubscribedPackage = useCallback(() => {
    const planName = getPlanName(data?.plan?.recurringType);
    const formattedDate = formatTimeLocale(
      data?.renewDate || data?.endDate || '',
      FORMAT_DOB,
    );
    return (
      <PackageInfo
        date={formattedDate}
        dateLabel={t('subscription.renewOn')}
        isChangePlan={data?.isChangePlan as boolean}
        isDetail={isDetail}
        isShowChangePlan={isDetail && (data?.plans || [])?.length > 1}
        isSubscribed={true}
        name={data?.name}
        nextPlan={data?.nextPlan}
        planName={planName}
        statusColor={Colors.successDefault}
        statusLabel={t('subscription.subscribed')}
        subscription={data}
      />
    );
  }, [data, isDetail, t]);

  /**
   * Renders the canceled package information
   */
  const renderCanceledPackage = useCallback(() => {
    const planName = getPlanName(data?.plan?.recurringType);
    const formattedDate = data?.endDate
      ? formatTimeLocale(data.endDate, FORMAT_DOB)
      : '';

    return (
      <PackageInfo
        date={formattedDate}
        dateLabel={t('subscription.endOn')}
        isDetail={isDetail}
        isShowChangePlan={isDetail && (data?.plans || [])?.length > 1}
        isSubscribed={false}
        name={data?.name}
        planName={planName}
        statusColor={Colors.neutral500}
        statusLabel={t('subscription.canceled')}
        subscription={data}
      />
    );
  }, [data, isDetail, t]);

  /**
   * Renders the default package information (when not subscribed or canceled)
   */
  const renderDefaultPackage = useCallback(
    () => (
      <>
        <WView alignCenter row>
          <WImage size={62} source={Images.goldSubscription} />
          <WView fill gap={6} mLeft={8}>
            <WText type="semiBold20">{data?.name}</WText>
            <WView gap={4} row wrap>
              {data?.plans?.map((item, index) => (
                <PlanBadge
                  key={index}
                  name={getPlanName(item?.recurringType)}
                />
              ))}
            </WView>
          </WView>
          <WView alignItems="flex-end" gap={6}>
            <WText type="semiBold20">{priceAmount}</WText>
            <WText type="regular12">{priceLabel}</WText>
          </WView>
        </WView>
        <WText color={Colors.grey1} marginTop={16} type="regular14">
          {data?.description}
        </WText>

        <WView gap={8} mVer={12}>
          <WView alignCenter gap={8} row>
            <WImage size={17} source={Icons.icTick} />
            <WText type="regular12">{t('subscription.priorityPickup')}</WText>
          </WView>

          {(couponSubscription?.data?.items || [])?.map(
            (item: SubscriptionCouponEntity, index: number) => (
              <WView alignCenter gap={8} key={index} row>
                <WImage size={17} source={Icons.icTick} />
                <WText type="regular12">{item?.title || ''}</WText>
              </WView>
            ),
          )}
        </WView>

        <WView alignCenter gap={2} row>
          <WText color={Colors.neutral700} type="regular12">
            {t('button.viewDetail')}
          </WText>
          <Ionicons name="chevron-forward-outline" size={12} />
        </WView>
      </>
    ),
    [data, priceAmount, priceLabel, t, couponSubscription],
  );

  /**
   * Renders the appropriate package information based on subscription status
   */
  const renderInfoPackage = useCallback(() => {
    if (data?.isSubscribe) {
      return renderSubscribedPackage();
    }
    if (data?.isSubscribe === false) {
      return renderCanceledPackage();
    }
    return renderDefaultPackage();
  }, [
    data?.isSubscribe,
    renderCanceledPackage,
    renderDefaultPackage,
    renderSubscribedPackage,
  ]);

  const handleSubscribeNow = () => {
    if (data?.plans?.length === 1) {
      navigation.navigate('SubscribePlan', {
        isTopBarEnable: false,
        selectedPlan: data?.plans?.[0],
        subscription: data,
      } as any);
    } else {
      openModal();
    }
  };

  const renderDiscountInfo = useCallback(
    (plan: SubscriptionPlanEntity) => {
      const discountInfo = processDiscount(plan);
      if (!discountInfo) return null;

      const currentPlanType = data?.plan?.recurringType;

      const nextPlanType = data?.nextPlan?.recurringType;
      const isChangingPlan = data?.isChangePlan === true;

      const isSamePlanType =
        data?.isSubscribe && currentPlanType === plan.recurringType;

      const isNextSamePlanType =
        isChangingPlan && nextPlanType === plan.recurringType;

      if (isSamePlanType || isNextSamePlanType) return null;

      const {validTo, type, value, planType, isYearly, labelDiscount} =
        discountInfo;

      return (
        <WView
          alignCenter
          borderColor={Colors.primary}
          borderRadius={8}
          borderWidth={1}
          color={Colors.white}
          gap={8}
          justifyBetween
          key={plan.id}
          mBottom={12}
          pHoz={12}
          pVer={8}
          row>
          <WView fill>
            <WText type="regular16">
              <WText color={Colors.primary} type="semiBold16">
                {labelDiscount}
              </WText>
              {t('subscription.onPlan', {value: planType})}
            </WText>
            <CountdownTimer endDate={validTo} />
          </WView>
          {isYearly && (
            <WView alignItems="flex-end" gap={2}>
              <WText type="semiBold16">
                ${calculateMonthlyPrice(plan, type, value)}
              </WText>
              <WText type="regular12">/{t('subscription.month')}</WText>
            </WView>
          )}
        </WView>
      );
    },
    [
      t,
      data?.isSubscribe,
      data?.plan?.recurringType,
      data?.nextPlan?.recurringType,
      data?.isChangePlan,
    ],
  );

  if (!data) return null;

  return (
    <WTouchable disabled={!onPress} mHoz={16} onPress={onPress}>
      <LinearGradient
        colors={['#FFFFFF', '#FFDFE0']}
        end={{x: 0.4, y: 1}}
        start={{x: 0, y: 0}}
        style={styles.container}>
        <WImage
          h={120}
          resizeMode="cover"
          source={Icons.icMembership}
          style={styles.icMembership}
          w={130}
        />

        <WView pHoz={16}>
          <WView mTop={12} pBottom={16}>
            {renderInfoPackage()}
          </WView>
          {data?.plans?.length > 0 && (
            <WView>{data.plans.map(plan => renderDiscountInfo(plan))}</WView>
          )}

          {isDefaultSubscription && (
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={handleSubscribeNow}
              style={styles.buttonContainer}>
              <LinearGradient
                colors={['#ED1C24', '#871015']}
                end={{x: 1, y: 0}}
                start={{x: 0, y: 0}}
                style={styles.gradientButton}>
                <WText center color={Colors.white} type="semiBold14">
                  {t('button.subscribeNow')}
                </WText>
              </LinearGradient>
            </TouchableOpacity>
          )}
        </WView>
      </LinearGradient>
      <ModalSubscribeNow
        dataDetail={data as AppSubscriptionEntity}
        isVisible={isVisible}
        onClose={closeModal}
      />
    </WTouchable>
  );
}

export default React.memo(PackageCard);

const styles = StyleSheet.create({
  buttonContainer: {
    marginBottom: 16,
  },
  container: {
    borderRadius: 12,
    flex: 1,
    overflow: 'hidden',
  },
  gradientButton: {
    alignItems: 'center',
    borderRadius: 8,
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  icMembership: {
    position: 'absolute',
    right: 0,
    top: 12,
  },
});
