import React from 'react';
import {useNavigation} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import StatusBadge from './StatusBadge';
import {WImage, WText, WView} from '@components/index';
import WTouchable from '@components/WTouchable';
import {Colors, Images} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {PackageInfoProps} from '@interface/subscription';
import {getPlanName} from '@utils/subscription';
/**
 * Package information component for both subscribed and canceled packages
 */
const PackageInfo = React.memo(
  ({
    name,
    planName,
    date,
    dateLabel,
    statusColor,
    statusLabel,
    isSubscribed,
    isShowChangePlan,
    subscription,
    nextPlan,
    isChangePlan,
    isDetail,
  }: PackageInfoProps) => {
    const {t} = useTypeSafeTranslation();
    const navigation = useNavigation();

    const firstPlanName = getPlanName(subscription?.plan?.recurringType);
    const secondPlanName = getPlanName(nextPlan?.recurringType);

    const onNavigateChange = () => {
      navigation.navigate('ChangePlan', {
        isTopBarEnable: false,
        subscription,
      } as any);
    };

    return (
      <WView row>
        <WImage size={62} source={Images.goldSubscription} />
        <WView gap={8} mLeft={12}>
          <WView alignCenter fill gap={6} row>
            <WText mWidth={'70%'} type="semiBold20">
              {name || ''}
            </WText>
            <StatusBadge color={statusColor} label={statusLabel} />
          </WView>
          <WView gap={4}>
            {isChangePlan && nextPlan?.id ? (
              <>
                <WText type="regular12">
                  {t('subscription.planEnds', {package: firstPlanName})}
                  <WText type="bold12">{date}</WText>
                </WText>
                <WText type="regular12">
                  {t('subscription.planStarts', {package: secondPlanName})}
                  <WText type="bold12">{date}</WText>
                </WText>
              </>
            ) : (
              <>
                {planName && (
                  <WText type="regular12">
                    {isSubscribed
                      ? t('subscription.subscribedTo', {value: planName})
                      : t('subscription.cancelPackage', {value: planName})}
                  </WText>
                )}
                {date && (
                  <WText type="regular12">
                    {dateLabel}
                    <WText type="bold12">{date}</WText>
                  </WText>
                )}
              </>
            )}

            {isShowChangePlan && (
              <WView row>
                <WTouchable
                  borderRadius={38}
                  borderWidth={1}
                  mTop={16}
                  onPress={onNavigateChange}
                  pHoz={15}
                  pVer={6}
                  row>
                  <WText color={Colors.neutral875} type="semiBold12">
                    {t('subscription.changePlan')}
                  </WText>
                  <Ionicons
                    color={Colors.neutral875}
                    name="arrow-forward-outline"
                    size={16}
                  />
                </WTouchable>
              </WView>
            )}
          </WView>

          {!isDetail && (
            <WView alignCenter gap={2} row>
              <WText color={Colors.neutral700} type="regular12">
                {t('button.viewDetail')}
              </WText>
              <Ionicons name="chevron-forward-outline" size={12} />
            </WView>
          )}
        </WView>
      </WView>
    );
  },
);

export default PackageInfo;
