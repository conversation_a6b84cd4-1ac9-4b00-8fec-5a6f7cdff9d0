/* eslint-disable react-native/no-raw-text */
import React, {useEffect, useState, useCallback} from 'react';
import moment from 'moment-timezone';
import WText from '@components/WText';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {TZ_OTTAWA} from '@themes/Constants';

interface CountdownTimerProps {
  endDate?: string | Date;
}

function CountdownTimer({endDate}: CountdownTimerProps) {
  const {t} = useTypeSafeTranslation();
  const [timeRemaining, setTimeRemaining] = useState<string>('');

  const calculateTimeRemaining = useCallback((): string => {
    if (!endDate) return '';
    const targetTime = moment.utc(endDate).tz(TZ_OTTAWA, true);
    const nowTime = moment().tz(TZ_OTTAWA);

    const diff = targetTime.diff(nowTime);

    if (diff <= 0) {
      return '';
    }
    const duration = moment.duration(diff);
    const days = Math.floor(duration.asDays());
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();

    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
  }, [endDate]);

  useEffect(() => {
    if (!endDate) return;

    setTimeRemaining(calculateTimeRemaining());

    const interval = setInterval(() => {
      setTimeRemaining(calculateTimeRemaining());
    }, 1000);

    return () => clearInterval(interval);
  }, [calculateTimeRemaining, endDate]);

  if (!endDate) return null;

  return (
    <WText type="regular12">
      {t('subscription.endsIn')} {timeRemaining}
    </WText>
  );
}

export default React.memo(CountdownTimer);
