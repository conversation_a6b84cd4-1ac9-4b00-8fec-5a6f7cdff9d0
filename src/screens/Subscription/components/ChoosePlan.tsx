import React, {useEffect, useCallback, useMemo} from 'react';
import PlanOption from './detail/PlanOption';
import {WText, WView} from '@components/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  AppSubscriptionEntity,
  SubscriptionPlanEntity,
  UserSubscriptionEntity,
} from '@requests';
import {calculatePlanPrices} from '@utils/subscription';

interface ChoosePlanProps {
  selectedPlan: SubscriptionPlanEntity | null;
  setSelectedPlan: (value: SubscriptionPlanEntity) => void;
  isSubscribe: boolean;
  isFirstTimeBuySubscription: boolean;
  subscription?: AppSubscriptionEntity;
  mySubscription: UserSubscriptionEntity;
}

/**
 * Component for selecting a subscription plan
 */
const ChoosePlan = React.memo(
  ({
    isFirstTimeBuySubscription,
    isSubscribe,
    subscription,
    selectedPlan,
    setSelectedPlan,
    mySubscription,
  }: ChoosePlanProps) => {
    const {t} = useTypeSafeTranslation();

    useEffect(() => {
      if (isFirstTimeBuySubscription && !isSubscribe) {
        setSelectedPlan(
          (subscription?.plans?.[0] || null) as SubscriptionPlanEntity,
        );
      }

      if (isSubscribe && mySubscription) {
        setSelectedPlan(mySubscription?.plan);
      }
    }, [
      isFirstTimeBuySubscription,
      isSubscribe,
      mySubscription,
      subscription?.plans,
      setSelectedPlan,
    ]);

    const handleSelectPlan = useCallback(
      (plan: SubscriptionPlanEntity) => {
        setSelectedPlan(plan);
      },
      [setSelectedPlan],
    );

    const renderPlanOption = useCallback(
      (plan: SubscriptionPlanEntity, index: number) => {
        const period =
          plan.recurringType === SubscriptionPlanEntity.recurringType.MONTHLY_1
            ? t('subscription.month')
            : t('subscription.year');

        const {
          originalPrice,
          discountedPrice,
          discountLabel,
          equivalentPrice,
          hasDiscount,
        } = calculatePlanPrices(plan, t);

        return (
          <PlanOption
            discount={hasDiscount ? discountLabel : undefined}
            equivalentPrice={
              hasDiscount && equivalentPrice ? equivalentPrice : undefined
            }
            isSelected={selectedPlan?.recurringType === plan.recurringType}
            key={plan.id || index}
            onSelect={() => handleSelectPlan(plan)}
            originalPrice={hasDiscount ? originalPrice : undefined}
            period={period}
            price={hasDiscount ? discountedPrice : originalPrice}
          />
        );
      },
      [handleSelectPlan, selectedPlan?.recurringType, t],
    );

    const shouldRender = useMemo(
      () => !isSubscribe && isFirstTimeBuySubscription,
      [isSubscribe, isFirstTimeBuySubscription],
    );

    if (!shouldRender) {
      return null;
    }

    return (
      <WView mHoz={16} mTop={24}>
        <WText type="semiBold18">{t('subscription.chooseAPlan')}</WText>
        <WView gap={8} mTop={12}>
          {subscription?.plans?.map(renderPlanOption)}
        </WView>
      </WView>
    );
  },
);

export default ChoosePlan;
