/* eslint-disable react-native/no-raw-text */
import React from 'react';
import Ionicons from 'react-native-vector-icons/Ionicons';
import WTouchable from '@components/WTouchable';
import {WText, WView} from '@components/index';
import {Colors} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface PlanOptionProps {
  isSelected: boolean;
  onSelect: () => void;
  price?: string;
  period: string;
  discount?: string;
  originalPrice?: string;
  equivalentPrice?: string;
  isCurrentPlan?: boolean;
}

/**
 * Component to display a subscription plan option with price and discount information
 */
const PlanOption = React.memo(
  ({
    isSelected,
    onSelect,
    period,
    price,
    discount,
    equivalentPrice,
    originalPrice,
    isCurrentPlan,
  }: PlanOptionProps) => {
    const {t} = useTypeSafeTranslation();

    return (
      <WTouchable
        alignCenter
        borderColor={isSelected ? Colors.neutral875 : Colors.neutral300}
        borderRadius={12}
        borderWidth={1.5}
        justifyBetween
        onPress={onSelect}
        pHoz={20}
        pVer={12}
        row>
        <WView gap={8}>
          <WText type="regular12">
            <WText type="semiBold18">{price}</WText>
            <WText type="regular12">{` / ${period}`}</WText>
          </WText>

          {discount && originalPrice && (
            <WView gap={8} row>
              <WView borderRadius={8} color={Colors.blue700} pHoz={8} pVer={2}>
                <WText color={Colors.white} type="semiBold12">
                  {discount}
                </WText>
              </WView>
              <WText
                color={Colors.neutral700}
                textDecorationLine="line-through"
                type="regular14">
                {originalPrice}
              </WText>
            </WView>
          )}

          {equivalentPrice && (
            <WText color={Colors.neutral850} type="regular14">
              {equivalentPrice}
            </WText>
          )}

          {isCurrentPlan && (
            <WText color={Colors.neutral850} type="regular14">
              {t('subscription.currentPlan')}
            </WText>
          )}
        </WView>

        <Ionicons
          name={
            isSelected ? 'radio-button-on-outline' : 'radio-button-off-outline'
          }
          size={20}
        />
      </WTouchable>
    );
  },
);

export default PlanOption;
