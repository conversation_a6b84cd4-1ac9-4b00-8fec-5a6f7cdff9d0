import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import CollapseHeader from '../CollapseHeader';
import PaymentMethodDisplay from './PaymentMethodDisplay';
import {WText, WView} from '@components';
import {Colors} from '@themes';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {CardEntity} from '@requests';

interface ManagePlanSectionProps {
  isOpen: boolean;
  onToggle: () => void;
  paymentMethod?: CardEntity;
  onEditPayment: () => void;
}

/**
 * Section for managing subscription plan and payment method
 */
const ManagePlanSection = React.memo(
  ({
    isOpen,
    onToggle,
    paymentMethod,
    onEditPayment,
  }: ManagePlanSectionProps) => {
    const {t} = useTypeSafeTranslation();

    return (
      <>
        <CollapseHeader
          isOpen={isOpen}
          onPress={onToggle}
          title={t('booking.paymentMethod')}
        />

        {isOpen && (
          <WView alignCenter mHoz={16} row>
            <WText type="regular14">{t('subscription.paymentMethod')}</WText>
            <PaymentMethodDisplay paymentMethod={paymentMethod} />
            <TouchableOpacity onPress={onEditPayment} style={styles.manageBtn}>
              <WView
                alignCenter
                borderRadius={6}
                color={Colors.blue100}
                pHoz={10}
                pVer={4}
                row>
                <WText color={Colors.blue700} type="medium14">
                  {t('button.edit')}
                </WText>
                <Icon color={Colors.blue700} name="Outline-CaretRight" />
              </WView>
            </TouchableOpacity>
          </WView>
        )}
      </>
    );
  },
);

export default ManagePlanSection;

const styles = StyleSheet.create({
  manageBtn: {marginLeft: 'auto'},
});
