import React from 'react';
import CouponItem from '../../../Coupon/CouponItem';
import {PriorityPickup} from '../PriorityPickup';
import {WText, WView} from '@components';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {CouponEntity, SubscriptionCouponBaseEntity} from '@requests';

interface BenefitsSectionProps {
  coupons: SubscriptionCouponBaseEntity[];
  onCouponPress: (coupon: CouponEntity) => void;
}

/**
 * Section displaying subscription benefits including priority pickup and coupons
 */
const BenefitsSection = React.memo(
  ({coupons, onCouponPress}: BenefitsSectionProps) => {
    const {t} = useTypeSafeTranslation();

    return (
      <>
        <WView alignCenter mBottom={4} mHoz={16} mTop={24} row>
          <WText marginRight={'auto'} type="semiBold18">
            {t('subscription.benefits')}
          </WText>
        </WView>

        <PriorityPickup />

        {coupons.map((coupon: SubscriptionCouponBaseEntity) => (
          <CouponItem
            allowForceViewDetail
            coupon={coupon as unknown as CouponEntity}
            isSubscriptionCoupon
            isViewOnly
            key={coupon?.id}
            onPress={() => onCouponPress(coupon as unknown as CouponEntity)}
            quantity={coupon?.quantity}
          />
        ))}
      </>
    );
  },
);

export default BenefitsSection;
