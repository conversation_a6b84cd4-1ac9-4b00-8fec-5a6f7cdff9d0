import React from 'react';
import {StyleSheet} from 'react-native';
import {WButton, WText, WTouchable, WView} from '@components';
import {Colors} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {AppSubscriptionEntity, SubscriptionPlanEntity} from '@requests';
import {getPlanName} from '@utils/subscription';

interface SubscriptionFooterProps {
  isSubscribe: boolean;
  isFirstTimeBuySubscription: boolean;
  subscriptionStatus?: AppSubscriptionEntity.status;
  onResubscribe: () => void;
  onContinue: () => void;
  onUnsubscribe: () => void;
  formattedEndDate?: string;
  formattedRenewDate?: string;
  recurringType?: SubscriptionPlanEntity.recurringType;
}

/**
 * Footer component for subscription details screen with action buttons
 */
const SubscriptionFooter = React.memo(
  ({
    isSubscribe,
    isFirstTimeBuySubscription,
    subscriptionStatus,
    onResubscribe,
    onContinue,
    onUnsubscribe,
    formattedEndDate,
    formattedRenewDate,
    recurringType,
  }: SubscriptionFooterProps) => {
    const {t} = useTypeSafeTranslation();

    const planName = getPlanName(recurringType);

    if (
      !isSubscribe &&
      !isFirstTimeBuySubscription &&
      subscriptionStatus === AppSubscriptionEntity.status.IN_PROGRESS
    ) {
      return (
        <>
          <WView style={[styles.shadowView, GlobalStyle.shadowCar]} />
          <WView color={Colors.white} pBottom={12} pHoz={16} pTop={16}>
            <WButton label={t('button.resubscribe')} onPress={onResubscribe} />
          </WView>
          {formattedEndDate && (
            <WText
              center
              color={Colors.neutral600}
              marginHorizontal={16}
              type="medium12">
              {t('subscription.keepActive', {
                plan: planName,
                value: formattedEndDate,
              })}
            </WText>
          )}
        </>
      );
    }

    if (isFirstTimeBuySubscription && !isSubscribe) {
      return (
        <>
          <WView style={[styles.shadowView, GlobalStyle.shadowCar]} />
          <WView color={Colors.white} pBottom={12} pHoz={16} pTop={16}>
            <WButton label={t('button.continue')} onPress={onContinue} />
          </WView>
        </>
      );
    }

    if (isSubscribe) {
      return (
        <>
          <WView style={[styles.shadowView, GlobalStyle.shadowCar]} />
          <WView color={Colors.white} gap={12} pBottom={12} pHoz={16} pTop={16}>
            <WTouchable
              borderColor={Colors.neutral300}
              borderRadius={8}
              borderWidth={1}
              center
              h={48}
              onPress={onUnsubscribe}>
              <WText color={Colors.neutral875} type="medium16">
                {t('subscription.unsubscribe')}
              </WText>
            </WTouchable>
            {formattedRenewDate && (
              <WText center color={Colors.neutral600} type="medium12">
                {t('subscription.noteUnsubscribe', {date: formattedRenewDate})}
              </WText>
            )}
          </WView>
        </>
      );
    }

    return null;
  },
);

export default SubscriptionFooter;

const styles = StyleSheet.create({
  shadowView: {
    backgroundColor: Colors.white,
    height: 10,
    marginBottom: -9,
  },
});
