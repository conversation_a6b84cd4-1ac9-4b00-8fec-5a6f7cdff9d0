import React from 'react';
import {Image, Platform, StyleSheet} from 'react-native';
import {PaymentIcon} from 'react-native-payment-icons';
import Icon from 'react-native-vector-icons/icomoon';
import {WText, WView} from '@components';
import {Images} from '@themes';
import {CardViewType, CASH, WALLET} from '@themes/Constants';
import {CardEntity} from '@requests';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface PaymentMethodDisplayProps {
  paymentMethod?: CardEntity;
}

const PaymentMethodDisplay = React.memo(
  ({paymentMethod}: PaymentMethodDisplayProps) => {
    const {t} = useTypeSafeTranslation();

    if (!paymentMethod) {
      return null;
    }

    if (paymentMethod.id === CASH.id) {
      return (
        <WView alignCenter row>
          <Icon name="Outline-Money" size={24} />
          <WText marginLeft={4} type="medium14">
            {t('payment.cash')}
          </WText>
        </WView>
      );
    }

    if (paymentMethod.id === WALLET.id) {
      const isIOS = Platform.OS === 'ios';
      return (
        <WView alignCenter row>
          <Image
            resizeMode="contain"
            source={isIOS ? Images.applePay : Images.googlePay}
            style={isIOS ? styles.applePayImg : styles.googlePayImg}
          />
          <WText marginLeft={4} type="medium14">
            {isIOS ? t('payment.applePay') : t('payment.googlePay')}
          </WText>
        </WView>
      );
    }

    return (
      <WView alignCenter row>
        <PaymentIcon type={paymentMethod.brand as CardViewType} />
        <WText marginLeft={4} type="medium14">
          {paymentMethod.last4}
        </WText>
      </WView>
    );
  },
);

export default PaymentMethodDisplay;

const styles = StyleSheet.create({
  applePayImg: {height: 40, marginRight: 4, width: 40},
  googlePayImg: {height: 28, marginRight: 4, width: 48},
});
