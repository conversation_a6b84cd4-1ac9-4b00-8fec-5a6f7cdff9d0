import React from 'react';
import {LayoutAnimation, TouchableWithoutFeedback} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '@themes';
import {WText, WView} from '@components';

interface CollapseHeaderProps {
  title?: string;
  isOpen?: boolean;
  onPress?: () => void;
}

function CollapseHeader({title, onPress, isOpen}: CollapseHeaderProps) {
  const _onPress = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    onPress?.();
  };
  return (
    <TouchableWithoutFeedback onPress={_onPress}>
      <WView
        alignCenter
        borderBottomColor={Colors.neutral300}
        borderBottomWidth={1}
        mBottom={16}
        mHoz={16}
        mTop={24}
        pBottom={4}
        row>
        <WText marginRight={'auto'} type="semiBold18">
          {title}
        </WText>
        <Icon
          name={isOpen ? 'Outline-CaretUp' : 'Outline-CaretDown'}
          size={24}
        />
      </WView>
    </TouchableWithoutFeedback>
  );
}

export default CollapseHeader;
