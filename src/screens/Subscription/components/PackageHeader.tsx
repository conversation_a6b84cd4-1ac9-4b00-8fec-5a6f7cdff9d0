import {StyleSheet} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import WImage from '@components/WImage';
import Icons from '@themes/Icons';
import {WText, WView} from '@components/index';
import Images from '@themes/Images';

interface Props {
  title: string;
  description?: string;
  typeDesc?: string;
}

function PackageHeader({description, title, typeDesc = 'regular14'}: Props) {
  return (
    <LinearGradient
      colors={['#FFFFFF', '#FFDFE0']}
      end={{x: 0.4, y: 1}}
      start={{x: 0, y: 0}}>
      <WImage
        h={120}
        resizeMode="cover"
        source={Icons.icMembership}
        style={styles.icMembership}
        w={130}
      />

      <WView alignCenter gap={18} pHoz={16} pVer={12} row>
        <WImage size={68} source={Images.goldSubscription} />
        <WView fill gap={4}>
          <WView alignCenter gap={10} row>
            <WText type="semiBold18">{title}</WText>
          </WView>
          {description && <WText type={typeDesc as any}>{description}</WText>}
        </WView>
      </WView>
    </LinearGradient>
  );
}

export default PackageHeader;

const styles = StyleSheet.create({
  icMembership: {
    position: 'absolute',
    right: 0,
    top: 12,
  },
});
