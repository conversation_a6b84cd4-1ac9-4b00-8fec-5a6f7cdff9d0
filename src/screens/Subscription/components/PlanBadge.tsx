import React from 'react';
import {WImage, WText, WView} from '@components/index';
import {Colors, Icons} from '@themes/index';

interface PlanBadgeProps {
  name?: string;
}

export const PlanBadge = React.memo(({name}: PlanBadgeProps) => {
  if (!name) return null;

  return (
    <WView
      alignCenter
      borderRadius={6}
      color={Colors.blue700}
      gap={2}
      pHoz={8}
      pVer={2}
      row>
      <WImage size={12} source={Icons.icDiscount} />
      <WText color={Colors.white} type="medium12">
        {name}
      </WText>
    </WView>
  );
});
