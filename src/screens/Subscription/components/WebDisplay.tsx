import React from 'react';
import RenderHTML from 'react-native-render-html';
import {StyleSheet} from 'react-native';
import {Colors} from '@themes';
import {SCREEN_WIDTH} from '@themes/Constants';

interface WebDisplayProp {
  html: string;
}

const WebDisplay = React.memo(function WebDisplay({html}: WebDisplayProp) {
  const tagsStyles = {
    li: {
      color: Colors.neutral850,
    },
    ol: {
      color: Colors.neutral850,
      marginBottom: 0,
      marginTop: 0,
    },
    ul: {
      color: Colors.neutral850,
      marginBottom: 0,
      marginTop: 4,
    },
  };

  return (
    <RenderHTML
      baseStyle={styles.container}
      contentWidth={SCREEN_WIDTH - 32}
      source={{html}}
      tagsStyles={tagsStyles}
    />
  );
});

export default WebDisplay;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    width: SCREEN_WIDTH - 32,
  },
});
