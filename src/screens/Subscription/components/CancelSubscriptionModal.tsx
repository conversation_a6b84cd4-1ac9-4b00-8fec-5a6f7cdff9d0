import {RouteProp, useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleSheet} from 'react-native';
import {CancelSubscriptionModalRouteProps} from '@global';
import {GlobalStyle} from '@themes/GlobalStyle';
import {WButton, WImage, WText, WView} from '@components';
import ModalWrapper from '@components/Modal/ModalWrapper';
import {Colors, Icons} from '@themes';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface CancelSubscriptionModal {
  route: RouteProp<
    CancelSubscriptionModalRouteProps,
    'CancelSubscriptionModal'
  >;
}

function CancelSubscriptionModal({route}: CancelSubscriptionModal) {
  const {subscription} = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  return (
    <ModalWrapper style={styles.container}>
      <WView
        borderRadius={12}
        center
        color={Colors.white}
        mHoz={16}
        padding={16}>
        <WImage size={44} source={Icons.icPackage} />
        <WText center marginTop={20} type="semiBold18">
          {t('subscription.unsubscribeModal.confirm.title')}
        </WText>
        <WText
          center
          color={Colors.neutral700}
          lineHeight={20}
          marginTop={8}
          type="regular14">
          {t('subscription.unsubscribeModal.confirm.content')}
        </WText>
        <WView alignCenter mTop={24} row>
          <WButton
            label={t('button.proceedCancel')}
            onPress={() => {
              navigation.goBack();
              navigation.navigate('CancelPlan', {
                isTopBarEnable: false,
                subscription,
              });
            }}
            outline
            style={styles.cancelBtn}
          />
          <WButton
            label={t('button.keepPlan')}
            onPress={() => {
              navigation.goBack();
            }}
            style={GlobalStyle.flex1}
          />
        </WView>
      </WView>
    </ModalWrapper>
  );
}

export default CancelSubscriptionModal;

const styles = StyleSheet.create({
  cancelBtn: {flex: 1, marginRight: 12},
  container: {justifyContent: 'center'},
});
