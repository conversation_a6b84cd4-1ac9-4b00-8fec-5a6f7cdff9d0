import {StyleSheet} from 'react-native';
import React from 'react';
import {WView} from '@components';
import Skeleton from '@components/Skeleton';
import {SCREEN_WIDTH} from '@themes/Constants';

const DEFAULT_WIDTH = SCREEN_WIDTH - 64;

interface ActivitiesLoadingProps {
  width?: number;
}

function ActivitiesLoading({width = DEFAULT_WIDTH}: ActivitiesLoadingProps) {
  return (
    <WView>
      <Skeleton height={22} width={90} />
      <WView alignCenter row>
        <Skeleton height={52} style={styles.mTop16} width={52} />
        <WView mLeft={8}>
          <Skeleton
            height={14}
            style={styles.mTop12}
            width={SCREEN_WIDTH * 0.3}
          />
          <Skeleton
            height={14}
            style={styles.mTop12}
            width={SCREEN_WIDTH * 0.2}
          />
        </WView>
        <Skeleton
          height={24}
          style={styles.mLeftAuto}
          width={SCREEN_WIDTH * 0.25}
        />
      </WView>

      <Skeleton height={20} style={styles.mTop20} width={width} />
      <Skeleton height={20} style={styles.mTop12} width={width} />
      <WView mTop={20} row>
        <Skeleton height={44} width={44} />
        <WView fill justifyCenter mLeft={8}>
          <Skeleton height={12} width={118} />
          <Skeleton height={12} style={styles.mTop12} width={118} />
        </WView>
      </WView>
      <WView justifyBetween mTop={24} row>
        <Skeleton height={14} width={width * 0.2} />
        <Skeleton height={14} width={width * 0.6} />
      </WView>
      <WView justifyBetween mTop={12} row>
        <Skeleton height={14} width={width * 0.2} />
        <Skeleton height={14} width={width * 0.6} />
      </WView>
      <WView justifyBetween mTop={12} row>
        <Skeleton height={14} width={width * 0.2} />
        <Skeleton height={14} width={width * 0.6} />
      </WView>
      <WView justifyBetween mTop={12} row>
        <Skeleton height={14} width={width * 0.2} />
        <Skeleton height={14} width={width * 0.6} />
      </WView>
      <WView justifyBetween mTop={12} row>
        <Skeleton height={14} width={width * 0.2} />
        <Skeleton height={14} width={width * 0.6} />
      </WView>
    </WView>
  );
}

export default ActivitiesLoading;

const styles = StyleSheet.create({
  mLeftAuto: {marginLeft: 'auto', marginTop: 12},
  mTop12: {marginTop: 12},
  mTop16: {marginTop: 16},
  mTop20: {marginTop: 20},
});
