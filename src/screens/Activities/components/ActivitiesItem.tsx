/* eslint-disable react-native/no-raw-text */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-expressions */
import {useNavigation} from '@react-navigation/native';
import React, {useMemo} from 'react';
import {Image, StyleSheet} from 'react-native';
import moment from 'moment';
import {WImage, WText, WTouchable, WView} from '@components';
import {TranslationKeys} from '@generated/translationKeys';
import {useBookingsServiceBookingControllerGetBookingOfMeKey} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import {
  BookingAddressType,
  BookingEntity,
  CreateBookingWithCodeDto,
} from '@requests';
import {Colors, Images} from '@themes';
import {
  ACTIVE_OPACITY,
  BOOKING_STATUS,
  FORMAT_DATE_TIME_PICKER,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import Icons from '@themes/Icons';
import {
  formatBookingTime,
  formatNumberPrice,
  getCarData,
  showErrorAlert,
} from '@utils/Tools';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

interface ActivitiesItemProps {
  item?: BookingEntity;
  onPress?: () => void;
  isBooked: boolean;
}

function ActivitiesItem({item, onPress, isBooked}: ActivitiesItemProps) {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const [_bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [_currentBooking, setCurrentBooking] = useGlobalState('currentBooking');
  const {setCurrentStatus} = useBookingGlobalStatus();

  const {
    vehicleType,
    status,
    originAddress,
    originLocation,
    destinationAddress,
    destinationLocation,
    reviewStatus,
    refundTag,
  } = item || {};

  const car = getCarData(vehicleType || BookingEntity.vehicleType.ALL);

  const infoStatus = useMemo(() => {
    if (status !== BookingEntity.status.COMPLETED) {
      return BOOKING_STATUS[status as BookingEntity.status] || '';
    }
    if (refundTag === BookingEntity.refundTag.REFUNDED) {
      return {
        textColor: Colors.alertDefault,
        title: 'activities.fullyRefund',
      };
    }
    if (refundTag === BookingEntity.refundTag.PARTIAL_REFUND) {
      return {
        textColor: Colors.alertDefault,
        title: 'activities.partialRefund',
      };
    }
    return;
  }, [status, refundTag]);

  const handleClose = () => {
    queryClient.refetchQueries({
      queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
      type: 'all',
    });
  };

  const bookingWithCodeParams: CreateBookingWithCodeDto = {
    amount: 0,
    codeId: '',
    date:
      vehicleType === BookingEntity.vehicleType.ACCESSIBLE_VAN
        ? moment().add(24, 'hours').format(FORMAT_DATE_TIME_PICKER)
        : null,
    destinationLocation: {
      address: destinationAddress as BookingAddressType,
      geo: {
        latitude: destinationLocation?.[1] || 0,
        longitude: destinationLocation?.[0] || 0,
      },
    },
    originLocation: {
      address: originAddress as BookingAddressType,
      geo: {
        latitude: originLocation?.[1] || 0,
        longitude: originLocation?.[0] || 0,
      },
    },
    vehicleType: vehicleType as BookingEntity.vehicleType,
  };

  const handleRebook = () => {
    if (isBooked) {
      return showErrorAlert({message: t('error.inAnotherBooking')});
    }
    setCurrentBooking({id: undefined} as any);
    setBookingWithCodeParams(bookingWithCodeParams);
    setCurrentStatus(BookingEntity.status.CONFIRMED_ADDRESS);
    setTimeout(() => {
      navigation.navigate('BookingMaps', {isTopBarEnable: false});
    }, 800);
  };

  const handleRateAndTip = () => {
    navigation.navigate('RatingDriver', {
      bookingDetail: item as BookingEntity,
      isActivitiesFlow: true,
      onClose: handleClose,
    });
  };

  const isRateAndTip =
    reviewStatus === BookingEntity.reviewStatus.PENDING &&
    status === BookingEntity.status.COMPLETED;

  const isRebook =
    status === BookingEntity.status.CANCELLED ||
    status === BookingEntity.status.COMPLETED;

  const isCancel = status === BookingEntity.status.CANCELLED;

  return (
    <WTouchable
      activeOpacity={ACTIVE_OPACITY}
      alignItems="flex-start"
      borderRadius={12}
      color={Colors.white}
      gap={12}
      mBottom={16}
      mHoz={16}
      onPress={onPress}
      padding={12}
      row
      style={GlobalStyle.shadowSearchInput}>
      <WView alignCenter fill gap={12} row>
        <WImage
          resizeMode="contain"
          source={car?.image || Images.appLogo}
          style={styles.carImg}
        />
        <WView fill gap={10}>
          {infoStatus?.title && (
            <WText color={infoStatus?.textColor} type="semiBold12">
              {t(infoStatus?.title as TranslationKeys)}
            </WText>
          )}
          <WView gap={12} justifyBetween row>
            <WText color={Colors.neutral900} fill type="regular14">
              {t('activities.route', {
                destination: item?.destinationAddress?.name || '',
                origin: item?.originAddress?.name || '',
              })}
            </WText>
          </WView>
          <WText color={Colors.neutral600} type="regular12">
            {formatBookingTime(item?.date || new Date())}
          </WText>

          {(isRebook || isRateAndTip) && (
            <WView alignCenter row>
              {isRebook && (
                <WTouchable alignCenter mRight={16} onPress={handleRebook} row>
                  <WText
                    color={Colors.colorLink}
                    marginRight={4}
                    type="medium12">
                    {t('activities.rebook')}
                  </WText>
                  <Image
                    source={Icons.icArrowCircleRight}
                    style={styles.icArrowCircleRight}
                  />
                </WTouchable>
              )}
              {isRateAndTip && (
                <WTouchable onPress={handleRateAndTip}>
                  <WText color={Colors.colorLink} type="medium12">
                    {t('activities.rateOrTip')}
                  </WText>
                </WTouchable>
              )}
            </WView>
          )}
        </WView>
      </WView>
      {!isCancel && (
        <WView alignItems="flex-end" justifyContent="flex-end">
          <WText color={Colors.neutral900} type="regular14">
            {formatNumberPrice((item?.amount || 0) + (item?.tipAmount || 0))}
          </WText>
          {!!item?.tipAmount && (
            <WText color={Colors.neutral600} type="regular10">
              {t('rating.inclTip', {
                value: formatNumberPrice(item?.tipAmount),
              })}
            </WText>
          )}
        </WView>
      )}
    </WTouchable>
  );
}

export default ActivitiesItem;

const styles = StyleSheet.create({
  carImg: {height: 20, width: 40},
  icArrowCircleRight: {
    height: 18,
    width: 18,
  },
});
