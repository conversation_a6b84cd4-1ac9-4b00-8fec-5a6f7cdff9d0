/* eslint-disable react-hooks/exhaustive-deps */
import {
  ActivityIndicator,
  FlatList,
  Image,
  ListRenderItem,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import React, {useEffect} from 'react';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ActivitiesItem from './ActivitiesItem';
import {EKeyTabActivity} from '@themes/Enums';
import {WButton, WText, WTouchable, WView} from '@components/index';
import {
  BookingEntity,
  BookingsService,
  CreateBookingWithCodeDto,
} from '@requests';
import Images from '@themes/Images';
import {Colors} from '@themes/index';
import {useLoadMoreQuery} from '@utils/hooks/useLoadMoreQuery';
import {
  useBookingsServiceBookingControllerGetBookingOfMeKey,
  useUsersServiceUserControllerIsVerifyPhoneNumber,
} from '@queries';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {SCREEN_WIDTH} from '@themes/Constants';
import {useGlobalState} from '@react-query/clientStateManage';
import Spin from '@components/Spin';
import AppsFlyer from '@utils/appsFlyerSdk';
import MixPanelSdk from '@utils/mixPanelSdk';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

interface Props {
  type: EKeyTabActivity;
}

function ListActivities({type}: Props) {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  const [_bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [_, setCurrentBooking] = useGlobalState('currentBooking');
  const [userData] = useGlobalState('userData');

  const {currentUserBooking, refetchCurrentBooking, handleClearBooking} =
    useBookingGlobalStatus();

  const toDate =
    type === EKeyTabActivity.Recent
      ? dayjs().endOf('day').toISOString()
      : undefined;

  const fromDate =
    type === EKeyTabActivity.Recent
      ? dayjs().subtract(14, 'day').startOf('day').toISOString()
      : undefined;

  const {
    data: bookings,
    refetch,
    loadMore,
    loading,
    isFetching,
    isFetchingNextPage,
    isRefetching,
  } = useLoadMoreQuery({
    query: (offset: number) =>
      BookingsService.bookingControllerGetBookingOfMe(
        10,
        offset,
        fromDate,
        toDate,
        undefined,
        undefined,
      ),
    queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey, type],
  });

  const {refetch: refetchVerifyPhone} =
    useUsersServiceUserControllerIsVerifyPhoneNumber(undefined, {
      enabled: false,
    });

  const eventValues = {
    email: userData?.user?.email,
    phoneNumber: userData?.user?.phoneNumber,
    userName: userData?.user?.fullName,
  };

  const onGoActivitiesDetail = async (item: BookingEntity) => {
    if (currentUserBooking?.id === item.id) {
      const response = await refetchCurrentBooking();
      if (response?.data?.data?.id) {
        setCurrentBooking(currentUserBooking);
        setBookingWithCodeParams({
          destinationLocation: {
            address: currentUserBooking?.destinationAddress,
            geo: {
              latitude: currentUserBooking?.destinationLocation?.[1],
              longitude: currentUserBooking?.destinationLocation?.[0],
            },
          },
          originLocation: {
            address: currentUserBooking?.originAddress,
            geo: {
              latitude: currentUserBooking?.originLocation?.[1],
              longitude: currentUserBooking?.originLocation?.[0],
            },
          },
        } as CreateBookingWithCodeDto);
        navigation.navigate('BookingMaps', {
          defaultBookingDetail: currentUserBooking,
          isTopBarEnable: false,
        });
      } else {
        handleClearBooking();
        navigation.navigate('ActivitiesDetail', {
          isBooked: !!currentUserBooking?.id,
          isTopBarEnable: false,
          item,
        });
      }
    } else {
      navigation.navigate('ActivitiesDetail', {
        isBooked: !!currentUserBooking?.id,
        isTopBarEnable: false,
        item,
      });
    }
  };

  const renderItem: ListRenderItem<BookingEntity> = ({item}) => (
    <ActivitiesItem
      isBooked={!!currentUserBooking?.id}
      item={item}
      onPress={() => onGoActivitiesDetail(item)}
    />
  );

  const renderEmpty = () => {
    const onUpdatePhoneNumber = () => {
      navigation.navigate('VerifyPhoneModal', {
        defaultPhoneNumber: userData?.user?.phoneNumber,
        onApply: () => {
          navigation.navigate('SelectDestination', {
            isTopBarEnable: false,
          });
        },
      });
    };

    const onGoToBooking = async () => {
      const dataVerify = await refetchVerifyPhone();
      const isVerifyPhone = dataVerify?.data?.data;
      if (!userData?.user?.phoneNumber || !isVerifyPhone) {
        return onUpdatePhoneNumber();
      }

      MixPanelSdk.logEvent({
        eventName: 'booking_start',
        eventValues,
      });
      AppsFlyer.logEvent({
        eventName: 'booking_start',
        eventValues,
      });
      navigation.navigate('SelectDestination', {
        isSchedule: false,
        isTopBarEnable: false,
      });
    };

    if (loading || isFetching || isFetchingNextPage) {
      return (
        <WView alignCenter fill justifyCenter mBottom={24}>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </WView>
      );
    }
    return (
      <WView alignCenter fill justifyCenter>
        <Image source={Images.emptyBooking} />
        <WText marginBottom={20} type="regular14">
          {t('empty.booking')}
        </WText>
        <WButton
          label={t('button.bookACar')}
          onPress={onGoToBooking}
          style={styles.bookBtn}
        />
      </WView>
    );
  };

  const renderFooter = () => {
    const navigateHistory = () =>
      navigation.navigate('ActivitiesHistory', {isTopBarEnable: false});

    if (
      (loading || isFetching || isFetchingNextPage) &&
      bookings?.length > 0 &&
      !isRefetching
    ) {
      return (
        <WView h={40}>
          <ActivityIndicator color={Colors.primary} size={'small'} />
        </WView>
      );
    }
    if (type === EKeyTabActivity.Recent) {
      return (
        <WTouchable
          alignCenter
          gap={9}
          mBottom={32}
          mHoz={16}
          mTop={32}
          onPress={navigateHistory}
          row>
          <WText color={Colors.neutral850} type="semiBold14">
            {t('activities.viewHistory')}
          </WText>
          <Ionicons
            color={Colors.neutral850}
            name="arrow-forward-circle"
            size={13}
          />
        </WTouchable>
      );
    }
    return null;
  };

  useEffect(() => {
    if (isFocused) {
      refetch();
    }
  }, [isFocused]);

  return (
    <Spin loading={loading}>
      <FlatList
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        data={bookings || []}
        keyExtractor={item => item.id}
        onEndReached={loadMore}
        onEndReachedThreshold={0.2}
        refreshControl={
          <RefreshControl
            colors={[Colors.primary]}
            onRefresh={refetch}
            refreshing={false}
            tintColor={Colors.primary}
          />
        }
        renderItem={renderItem}
      />
    </Spin>
  );
}

export default ListActivities;

const styles = StyleSheet.create({
  bookBtn: {width: SCREEN_WIDTH - 32},
});
