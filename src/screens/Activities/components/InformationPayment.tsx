import React, {useMemo} from 'react';
import {StyleProp, ViewStyle, Image, StyleSheet} from 'react-native';
import {PaymentIcon} from 'react-native-payment-icons';
import Icon from 'react-native-vector-icons/icomoon';
import {TranslationKeys} from '@generated/translationKeys';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {BookingEntity, CouponEntity} from '@requests';
import {WText, WView} from '@components';
import {Colors, Images} from '@themes';
import {CardViewType} from '@themes/Constants';
import {
  formatDistance,
  formatDuration,
  formatNumberPrice,
  formatTime,
  getCarData,
  getCouponValueTitle,
} from '@utils/Tools';

interface BookingInfoItem {
  title: string;
  content?: string | any;
  isEnable: boolean;
  enableTooltip?: boolean;
  renderContent?: () => React.ReactNode;
}

interface InformationPaymentProps {
  booking?: BookingEntity;
  style?: StyleProp<ViewStyle>;
  enableTooltip?: boolean;
  isShowSort?: boolean;
}

enum WalletMethod {
  apple = 'apple_pay',
  google = 'google_pay',
}

function PaymentMethodDisplay({booking}: {booking?: BookingEntity}) {
  const {t} = useTypeSafeTranslation();

  if (booking?.paymentMethodType === BookingEntity.paymentMethodType.CASH) {
    return (
      <WView alignCenter row>
        <Icon name="Outline-Money" size={24} />
        <WText marginLeft={4} type="medium14">
          {t('payment.cash')}
        </WText>
      </WView>
    );
  }

  if (
    booking?.paymentMethodType === BookingEntity.paymentMethodType.CARD &&
    !booking?.card
  ) {
    return (
      <WText marginLeft={4} type="medium14">
        {t('payment.payInCard')}
      </WText>
    );
  }

  const isDigitalWallet = [WalletMethod.apple, WalletMethod.google].includes(
    booking?.card?.wallet?.type,
  );

  if (isDigitalWallet) {
    const isApplePay = booking?.card?.wallet?.type === WalletMethod.apple;
    return (
      <WView alignCenter row>
        <Image
          resizeMode="contain"
          source={isApplePay ? Images.applePay : Images.googlePay}
          style={isApplePay ? styles.walletImg : styles.walletImgAndroid}
        />
        <WText marginLeft={4} type="medium14">
          {isApplePay ? t('payment.applePay') : t('payment.googlePay')}
        </WText>
      </WView>
    );
  }

  return (
    <WView alignCenter row>
      <PaymentIcon type={booking?.card?.brand as CardViewType} />
      <WText marginLeft={4} type="medium14">
        {booking?.card?.last4}
      </WText>
    </WView>
  );
}

function PaymentMethodTip({booking}: {booking?: BookingEntity}) {
  const {t} = useTypeSafeTranslation();

  if (booking?.tipAmount) {
    const isDigitalWallet = [WalletMethod.apple, WalletMethod.google].includes(
      booking?.tipCard?.wallet?.type,
    );

    if (isDigitalWallet) {
      const isApplePay = booking?.tipCard?.wallet?.type === WalletMethod.apple;
      return (
        <WView alignCenter row>
          <Image
            resizeMode="contain"
            source={isApplePay ? Images.applePay : Images.googlePay}
            style={isApplePay ? styles.walletImg : styles.walletImgAndroid}
          />
          <WText marginLeft={4} type="medium14">
            {isApplePay ? t('payment.applePay') : t('payment.googlePay')}
          </WText>
        </WView>
      );
    }
    return (
      <WView alignCenter row>
        <PaymentIcon type={booking?.tipCard?.brand as CardViewType} />
        <WText marginLeft={4} type="medium14">
          {booking?.tipCard?.last4}
        </WText>
      </WView>
    );
  }

  return null;
}

function BookingInfoItem({item}: {item: BookingInfoItem}) {
  return (
    <WView justifyBetween mBottom={10} row>
      <WView alignCenter row>
        <WText marginRight={4} type="medium14">
          {item?.title}
        </WText>
      </WView>
      <WView alignCenter row>
        {item?.renderContent ? (
          item.renderContent()
        ) : (
          <WText type="regular14">{item.content}</WText>
        )}
      </WView>
    </WView>
  );
}

function InformationPayment({
  booking,
  style,
  enableTooltip,
  isShowSort = false,
}: InformationPaymentProps) {
  const {t} = useTypeSafeTranslation();

  const isAnyCar = booking?.vehicleType === BookingEntity.vehicleType.ALL;
  const isCancelBooking = booking?.status === BookingEntity.status.CANCELLED;

  const getBookingTotal = useMemo(() => {
    if (isAnyCar && !isCancelBooking) {
      return `${formatNumberPrice(
        booking?.minAmount || 0,
      )} - ${formatNumberPrice(booking?.amount || 0)}`;
    }
    return formatNumberPrice(booking?.amount);
  }, [booking?.amount, booking?.minAmount, isAnyCar, isCancelBooking]);

  const bookingInfo = useMemo(
    (): BookingInfoItem[] => [
      {
        isEnable: true,
        renderContent: () => <PaymentMethodDisplay booking={booking} />,
        title: t('booking.paymentMethod'),
      },
      {
        content: booking?.duration
          ? `${formatTime(booking?.duration)} • ${formatDuration(
              booking?.duration,
              booking?.createdAt,
            )}`
          : undefined,
        enableTooltip,
        isEnable: !isShowSort,
        title: t('booking.estArrivalTime'),
      },
      {
        content: formatDistance(booking?.distance),
        isEnable: !isShowSort,
        title: t('booking.distance'),
      },
      {
        content: t(
          getCarData(booking?.vehicleType as BookingEntity.vehicleType)
            ?.name as TranslationKeys,
        ),
        isEnable: !isShowSort,
        title: t('booking.typeOfCar'),
      },
      {
        content: getCouponValueTitle(booking?.coupon as CouponEntity) || 'No',
        isEnable: true,
        title: t('booking.coupon'),
      },
      {
        content: formatNumberPrice(booking?.tipAmount || 0),
        isEnable: Number(booking?.tipAmount || 0) > 0,
        title: t('rating.tipAmount'),
      },
      {
        isEnable:
          !!booking?.tipCard &&
          Number(booking?.tipAmount || 0) > 0 &&
          !(
            booking?.tipCard?.id === booking?.card?.id ||
            booking?.tipCard?.wallet?.type === booking?.card?.wallet?.type
          ),
        renderContent: () => <PaymentMethodTip booking={booking} />,
        title: t('activities.tipPaymentType'),
      },
      {
        content: getBookingTotal,
        isEnable: true,
        title: t('booking.totalIncludingTax'),
      },
    ],
    [booking, t, enableTooltip, getBookingTotal, isShowSort],
  );

  const filteredBookingInfo = useMemo(
    () => bookingInfo.filter(item => item.isEnable),
    [bookingInfo],
  );

  return (
    <WView
      borderColor={Colors.neutral150}
      borderRadius={8}
      borderTopWidth={1}
      color={Colors.white}
      justifyCenter
      pHoz={16}
      pTop={12}
      style={style}>
      {filteredBookingInfo.map(item => (
        <BookingInfoItem item={item} key={item.title} />
      ))}
    </WView>
  );
}

export default InformationPayment;

const styles = StyleSheet.create({
  walletImg: {height: 16, width: 36},
  walletImgAndroid: {height: 28, width: 48},
});
