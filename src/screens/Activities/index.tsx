/* eslint-disable react-hooks/exhaustive-deps */
import {SafeAreaView} from 'react-native';
import React, {useEffect} from 'react';

import {useIsFocused, useNavigation} from '@react-navigation/native';
import ListActivities from './components/ListActivities';
import {GlobalStyle} from '@themes/GlobalStyle';
import WText from '@components/WText';
import {Colors, Icons} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {WImage, WTouchable, WView} from '@components/index';
import {EKeyTabActivity} from '@themes/Enums';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

function Activities() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {setIsFocusActivities} = useBookingGlobalStatus();
  const isFocused = useIsFocused();

  useEffect(() => {
    if (isFocused) {
      setIsFocusActivities(true);
    } else {
      setIsFocusActivities(false);
    }
    return () => {
      setIsFocusActivities(false);
    };
  }, [isFocused]);

  const navigateHistory = () =>
    navigation.navigate('ActivitiesHistory', {isTopBarEnable: false});

  return (
    <SafeAreaView style={GlobalStyle.container}>
      <WView alignCenter justifyBetween mHoz={16} row>
        <WText color={Colors.neutral900} marginVertical={8} type="semiBold16">
          {t('activities.index')}
        </WText>
        <WTouchable onPress={navigateHistory}>
          <WImage size={20} source={Icons.icHistory} />
        </WTouchable>
      </WView>
      <WView fill mTop={12}>
        <WText
          color={Colors.neutral850}
          marginBottom={8}
          marginHorizontal={16}
          type="semiBold14">
          {t('activities.recent')}
        </WText>
        <ListActivities type={EKeyTabActivity.Recent} />
      </WView>
    </SafeAreaView>
  );
}

export default Activities;
