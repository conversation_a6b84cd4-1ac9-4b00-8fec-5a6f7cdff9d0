/* eslint-disable max-lines */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable import/named */
import {
  Animated,
  Easing,
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
} from 'react-native';
import React, {useEffect, useMemo, useRef} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/icomoon';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';
import {isEmpty} from 'lodash-es';
import HTMLtoPDF from 'react-native-html-to-pdf';
import Share from 'react-native-share';
import RNFS from 'react-native-fs';
import FileViewer from 'react-native-file-viewer';
import moment from 'moment';
import InformationPayment from './components/InformationPayment';
import {NavHeader, WImage, WText, WTouchable, WView} from '@components/index';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  useBookingsServiceBookingControllerGetBooking,
  useBookingsServiceBookingControllerGetBookingOfMeKey,
  useBookingsServiceBookingControllerGetBookingReceipt,
  useDriverServiceDriverControllerGetDriver,
} from '@queries';
import {ActivitiesDetailProps} from '@global';
import {Colors, Icons, Images} from '@themes/index';
import {
  formatBookingTime,
  formatNumberPrice,
  getCarData,
  showErrorAlert,
} from '@utils/Tools';
import {TranslationKeys} from '@generated/translationKeys';
import {
  BOOKING_STATUS,
  BOOKING_TYPE,
  FILE_NAME_RECEIPT,
  FORMAT_DATE_TIME_PICKER,
  IS_IOS,
  REASON_LIST,
} from '@themes/Constants';
import BookingAddress from '@screens/Booking/components/BookingAddress';
import {BookingEntity, CancelReason, CreateBookingWithCodeDto} from '@requests';
import {useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import Spin from '@components/Spin';
import useLoading from '@utils/hooks/useLoading';
import {getDownloadPermissionAndroid} from '@utils/permission';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';

function ActivitiesDetail() {
  const {item, isBooked} = (useRoute().params || {}) as ActivitiesDetailProps;
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const {showLoading, dismissLoading} = useLoading();

  const [_bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [_currentBooking, setCurrentBooking] = useGlobalState('currentBooking');
  const {setCurrentStatus} = useBookingGlobalStatus();

  const progress = useRef(new Animated.Value(0)).current;

  const {
    data: bookingDetail,
    isFetching,
    refetch,
  } = useBookingsServiceBookingControllerGetBooking(
    {
      id: item?.id as string,
    },
    undefined,
    {
      enabled: !!item?.id,
    },
  );

  const {data: dataDriver} = useDriverServiceDriverControllerGetDriver(
    {
      id: bookingDetail?.data?.driverId,
    },
    undefined,
    {
      enabled: !!bookingDetail?.data?.driverId,
    },
  );

  const {data: dataReceipt, isLoading} =
    useBookingsServiceBookingControllerGetBookingReceipt(
      {
        id: bookingDetail?.data?.id || '',
      },
      [bookingDetail?.data?.id],
      {
        enabled: bookingDetail?.data?.status === BookingEntity.status.COMPLETED,
      },
    );

  const driver = dataDriver?.data || {};
  const detail = (bookingDetail?.data || {}) as BookingEntity;
  const car = getCarData(detail?.vehicleType);
  const bookingStatusData =
    BOOKING_STATUS?.[detail?.status] || BOOKING_STATUS.ONGOING;
  const isRateAndTip =
    detail?.reviewStatus === BookingEntity.reviewStatus.PENDING &&
    detail?.status === BookingEntity.status.COMPLETED;
  const isCancelBooking = detail?.status === BookingEntity.status.CANCELLED;
  const isRebook =
    detail?.status === BookingEntity.status.CANCELLED ||
    detail?.status === BookingEntity.status.COMPLETED;

  const isSchedule = detail?.status === BookingEntity.status.SCHEDULED;

  const reasonTitle = t(
    REASON_LIST.find(ele => ele?.id === detail?.cancelReason?.reason)
      ?.label as TranslationKeys,
  );
  const noteReasonCancel = useMemo(() => {
    if (reasonTitle?.toUpperCase() === CancelReason.reason.OTHER) {
      return detail?.cancelReason?.note?.trim() || reasonTitle;
    }
    if (
      detail?.cancelReason?.reason?.toUpperCase() ===
      CancelReason.reason.CANCELLED_BY_CENTER
    ) {
      return t('cancelBooking.cancelledByCenter') || reasonTitle;
    }
    return reasonTitle ?? '';
  }, [reasonTitle, detail?.cancelReason?.note]);

  const copyToClipboard = () => {
    Clipboard.setString(String(detail?.bookingCode || ''));
    Toast.show({
      bottomOffset: 24,
      position: 'bottom',
      props: {title: t('referral.copy')},
      type: 'success',
      visibilityTime: 2000,
    });
  };

  const onPressRebook = () => {
    if (isBooked) {
      return showErrorAlert({message: t('error.inAnotherBooking')});
    }
    const bookingWithCodeParams: CreateBookingWithCodeDto = {
      amount: 0,
      codeId: '',
      date:
        detail?.vehicleType === BookingEntity.vehicleType.ACCESSIBLE_VAN
          ? moment().add(24, 'hours').format(FORMAT_DATE_TIME_PICKER)
          : null,
      destinationLocation: {
        address: detail?.destinationAddress,
        geo: {
          latitude: detail?.destinationLocation?.[1] || 0,
          longitude: detail?.destinationLocation?.[0] || 0,
        },
      },
      originLocation: {
        address: detail?.originAddress,
        geo: {
          latitude: detail?.originLocation?.[1] || 0,
          longitude: detail?.originLocation?.[0] || 0,
        },
      },
      vehicleType: detail?.vehicleType,
    };
    setCurrentBooking({id: undefined} as any);
    setCurrentStatus(BookingEntity.status.CONFIRMED_ADDRESS);
    setBookingWithCodeParams(bookingWithCodeParams);
    navigation.goBack();
    setTimeout(() => {
      navigation.navigate('BookingMaps', {isTopBarEnable: false});
    }, 800);
  };
  const onCancel = () => {
    navigation.navigate('CancelReasonList', {
      bookingId: detail?.id || '',
      isTopBarEnable: false,
    });
  };

  const onPressRateAndTip = () => {
    setTimeout(() => {
      navigation.navigate('RatingDriver', {
        bookingDetail: item as BookingEntity,
        isActivitiesFlow: true,
        onClose: handleClose,
      });
    }, 100);
  };

  const handleClose = () => {
    setTimeout(() => {
      refetch();
    }, 300);
    queryClient.refetchQueries({
      queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
      type: 'all',
    });
  };

  useEffect(() => {
    Animated.loop(
      Animated.timing(progress, {
        duration: 1500,
        easing: Easing.linear,
        toValue: 3,
        useNativeDriver: false,
      }),
    ).start();
  }, []);

  const renderBtnCopy = () => (
    <WTouchable onPress={copyToClipboard}>
      <Icon name="Outline-Copy" size={24} />
    </WTouchable>
  );

  const renderInfoRefund = () => {
    if (!detail?.refundTag) return;
    const isRefundFull = detail?.refundTag === BookingEntity.refundTag.REFUNDED;
    return (
      <WView
        borderRadius={12}
        color={Colors.neutral100}
        gap={8}
        mTop={16}
        padding={16}>
        <WText color={Colors.neutral850} type="semiBold12">
          {isRefundFull
            ? t('activities.refundTitleFull')
            : t('activities.refundTitlePartial')}
        </WText>
        <WText color={Colors.neutral850} type="regular12">
          {t('activities.refundDesc', {
            value: formatNumberPrice(detail?.refundAmount),
          })}
        </WText>
      </WView>
    );
  };

  const renderCardCancelOrSchedule = () => {
    if (!detail) return null;

    const {status} = detail;
    const isCancelOrSchedule = [
      BookingEntity.status.CANCELLED,
      BookingEntity.status.SCHEDULED,
    ].includes(status);
    const isStatusCancel = status === BookingEntity.status.CANCELLED;

    const renderCancel = () => (
      <>
        <WText type="semiBold12">{t('activities.cancelTitle')}</WText>
        <WText marginTop={8} type="regular12">
          {t('activities.cancelContent')}
        </WText>
        <WView mTop={8} row>
          <WText type="medium12">{t('activities.cancelledReason')}</WText>
          <WText fill marginLeft={4} type="regular12">
            {noteReasonCancel}
            {detail?.cancelReason?.isAutoCancelled
              ? ` ${t('cancelBooking.autoCancellation')}`
              : ''}
          </WText>
        </WView>
      </>
    );

    const renderSchedule = () => (
      <WView gap={8}>
        <WText color={Colors.neutral850} type="semiBold12">
          {t('activities.rideConfirmed')}
        </WText>
        <WText color={Colors.neutral850} type="regular12">
          {t('activities.rideConfirmedDesc')}
        </WText>
        <WView gap={8} mTop={16} row>
          {[0, 1, 2].map(i => (
            <Animated.View
              key={i}
              style={[
                styles.bar,
                {
                  backgroundColor: progress.interpolate({
                    extrapolate: 'clamp',
                    inputRange: [i, i + 1],
                    outputRange: [Colors.neutral400, Colors.success500],
                  }),
                },
              ]}
            />
          ))}
        </WView>
      </WView>
    );

    if (isCancelOrSchedule) {
      return (
        <WView
          borderRadius={12}
          color={Colors.neutral100}
          mTop={16}
          padding={16}>
          {isStatusCancel ? renderCancel() : renderSchedule()}
        </WView>
      );
    }

    return null;
  };

  const renderPickupDestination = () => (
    <WView>
      <WView alignCenter justifyBetween mTop={16} row>
        <WView alignCenter row>
          <WView
            alignCenter
            borderRadius={8}
            color={Colors.neutral100}
            h={52}
            justifyCenter
            w={52}>
            <WImage
              h={20}
              resizeMode="contain"
              source={car?.image || Images.appLogo}
              w={38}
            />
          </WView>
          <WView mLeft={6}>
            <WText type="medium14">{formatBookingTime(detail?.date)}</WText>
            <WText color={Colors.neutral600} marginTop={4} type="regular12">
              {t(car?.name as TranslationKeys)}
            </WText>
          </WView>
        </WView>
        {!!detail?.status && (
          <WView
            alignCenter
            borderRadius={100}
            color={bookingStatusData?.backgroundColor}
            mTop={8}
            pHoz={12}
            pVer={4}
            row>
            <WView
              borderRadius={3}
              color={bookingStatusData?.dotColor}
              h={6}
              w={6}
            />
            <WText
              color={bookingStatusData?.textColor}
              marginLeft={6}
              type="medium12">
              {t(bookingStatusData?.title as TranslationKeys)}
            </WText>
          </WView>
        )}
      </WView>
      <BookingAddress
        destinationAddress={detail?.destinationAddress?.name || ''}
        originAddress={detail?.originAddress?.name || ''}
      />
    </WView>
  );

  const renderInfoDriver = () => {
    const driverName = `${driver?.firstName ?? ''} ${driver?.lastName ?? ''}`;
    const vehicleNumber = detail?.vehicle?.vehicleNumber
      ? `#${detail?.vehicle?.vehicleNumber}`
      : '';
    if (isEmpty(driver)) return null;

    return (
      <WView
        borderBottomWidth={1}
        borderColor={Colors.neutral200}
        gap={16}
        pBottom={16}>
        <WView
          alignCenter
          borderColor={Colors.neutral200}
          borderTopWidth={1}
          gap={16}
          justifyBetween
          mTop={16}
          pTop={16}
          row>
          <WView alignCenter fill row>
            <Image source={Images.defaultAvatar} style={styles.driverImg} />
            <WView mLeft={12}>
              <WText type="medium14">{driverName}</WText>
              <WView alignCenter gap={10} mTop={2} row>
                <WView alignCenter gap={2} row>
                  <WText color={Colors.neutral600} type="regular12">
                    {driver?.avgRating?.toFixed(1) || 'N/A'}
                  </WText>
                  <Icon color={Colors.starYellow} name="Fill-Star" size={12} />
                </WView>

                {detail?.vehicle?.licensePlateNumber && (
                  <WView alignCenter gap={10} row>
                    <WView color={Colors.neutral200} h={16} w={1} />
                    <WText color={Colors.neutral600} type="regular12">
                      {detail?.vehicle?.licensePlateNumber}
                    </WText>
                  </WView>
                )}

                {detail?.vehicle?.vehicleNumber && (
                  <WView alignCenter gap={10} row>
                    <WView color={Colors.neutral200} h={16} w={1} />
                    <WText color={Colors.neutral600} type="regular12">
                      {vehicleNumber}
                    </WText>
                  </WView>
                )}
              </WView>
            </WView>
          </WView>
        </WView>
        {isRateAndTip && (
          <WTouchable
            alignCenter
            borderColor={Colors.neutral300}
            borderRadius={8}
            borderWidth={1}
            gap={8}
            onPress={onPressRateAndTip}
            pHoz={16}
            pVer={12}
            row>
            <Icon color={Colors.neutral900} name="Outline-Star" size={16} />
            <WText center color={Colors.neutral900} type="medium14">
              {t('activities.rateOrTipDriver')}
            </WText>
          </WTouchable>
        )}
        {detail?.review?.rating && (
          <WView gap={8}>
            <WView gap={5} row selfCenter>
              {new Array(detail?.review?.rating)
                .fill(detail?.review?.rating)
                ?.map((_, index) => (
                  <Icon
                    color={Colors.primary}
                    key={index}
                    name="Fill-Star"
                    size={16}
                  />
                ))}
            </WView>
            {detail?.review?.note && (
              <WText color={Colors.neutral600} type="regular12">
                {detail?.review?.note || ''}
              </WText>
            )}
          </WView>
        )}
      </WView>
    );
  };

  const renderPaymentAndNote = () => (
    <WView>
      <WView color={Colors.white} justifyCenter pTop={12}>
        <InformationPayment
          booking={detail}
          isShowSort={!isSchedule}
          style={[styles.mHor0, !isCancelBooking && styles.borderTop0]}
        />
      </WView>
      {detail?.note && (
        <WView
          borderColor={Colors.neutral600}
          borderRadius={6}
          borderWidth={1}
          h={56}
          mBottom={8}
          pHoz={4}
          pVer={8}>
          <WText type="regular14">{detail?.note}</WText>
        </WView>
      )}
    </WView>
  );

  const generatePDF = async (htmlContent: string) => {
    try {
      const options = {
        directory: 'Documents',
        fileName: FILE_NAME_RECEIPT,
        html: htmlContent,
      };

      const pdf = await HTMLtoPDF.convert(options);
      const pdfPath = pdf.filePath;
      return pdfPath;
    } catch (error) {
      return null;
    }
  };

  const handlePreview = async (htmlContent: string) => {
    const filePath = await generatePDF(htmlContent);
    if (filePath) {
      FileViewer.open(filePath);
    }
  };

  const handleDownload = async (htmlContent: string) => {
    try {
      const filePath = await generatePDF(htmlContent);
      showLoading();
      if (IS_IOS) {
        const shareOptions = {
          title: t('activities.sharePdf'),
          type: 'application/pdf',
          url: `file://${filePath}`,
        };
        await Share.open(shareOptions);
      } else {
        const isPermission = await getDownloadPermissionAndroid();
        if (isPermission) {
          const fileExists = await RNFS.exists(filePath);
          if (!fileExists) {
            return;
          }
          const destPath = `${RNFS.DownloadDirectoryPath}/${FILE_NAME_RECEIPT}.pdf`;
          await RNFS.copyFile(filePath, destPath);
          setTimeout(() => {
            Toast.show({
              bottomOffset: 24,
              position: 'top',
              props: {title: t('activities.downLoadSuccess')},
              type: 'success',
              visibilityTime: 2000,
            });
          }, 400);
        }
      }
    } catch (error) {
      // error
    } finally {
      dismissLoading();
    }
  };

  const handleExportInvoice = async () => {
    showErrorAlert({
      buttons: [
        {
          style: 'cancel',
          text: t('button.cancel'),
        },
        {
          onPress: () => handleDownload(String(dataReceipt?.data) || ''),
          text: t('activities.downloadPdf'),
        },
        {
          onPress: () => handlePreview(String(dataReceipt?.data) || ''),
          text: t('activities.previewPdf'),
        },
      ],
      message: t('activities.chooseOption'),
      title: t('activities.pdfOption'),
    });
  };

  const renderButton = () => {
    const isSchedule = detail?.status === BookingEntity.status.SCHEDULED;
    const labelHelp = `${t('button.help')}?`;
    const isShowBtnExport = !!dataReceipt?.data;

    const navigateNeedHelp = () => {
      const isRemovePayment =
        detail?.type === BOOKING_TYPE.BUSINESS ||
        detail.paymentMethodType === BookingEntity.paymentMethodType.CASH;

      navigation.navigate('ModalNeedHelp', {
        bookingId: detail?.id ?? '',
        isRemovePayment,
      });
    };

    return (
      <WView gap={16} mVer={16}>
        {!isSchedule && (
          <WTouchable center onPress={navigateNeedHelp}>
            <WText color={Colors.neutral600} type="regular14">
              {labelHelp}
            </WText>
          </WTouchable>
        )}
        <Spin loading={isLoading}>
          <WView gap={12} row>
            {isShowBtnExport && (
              <WTouchable
                borderColor={Colors.neutral300}
                borderRadius={8}
                borderWidth={1}
                center
                fill
                gap={8}
                onPress={handleExportInvoice}
                row>
                <WImage size={16} source={Icons.icExport} />
                <WText color={Colors.neutral700} type="medium14">
                  {t('activities.exportInvoice')}
                </WText>
              </WTouchable>
            )}

            {isRebook && (
              <WTouchable
                borderRadius={8}
                center
                color={Colors.neutral900}
                fill
                h={46}
                onPress={onPressRebook}
                pHoz={5}>
                <WText center color={Colors.white} type="medium14">
                  {t('activities.rebookThisRide')}
                </WText>
              </WTouchable>
            )}
          </WView>
        </Spin>

        {isSchedule && (
          <WTouchable
            borderRadius={8}
            center
            color={Colors.primary100}
            h={46}
            onPress={onCancel}>
            <WText color={Colors.primary} type="medium14">
              {t('button.cancelSchedule')}
            </WText>
          </WTouchable>
        )}
      </WView>
    );
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader
        renderRightButton={renderBtnCopy}
        title={`${t('booking.bookingID')} ${detail?.bookingCode || ''}`}
      />
      <ScrollView contentContainerStyle={styles.content}>
        <Spin loading={isFetching}>
          <WView fill>
            {renderInfoRefund()}
            {renderCardCancelOrSchedule()}
            {renderPickupDestination()}
            {renderInfoDriver()}
            {renderPaymentAndNote()}
          </WView>
          {renderButton()}
        </Spin>
      </ScrollView>
    </SafeAreaView>
  );
}

export default ActivitiesDetail;
const styles = StyleSheet.create({
  bar: {
    borderRadius: 8,
    flex: 1,
    height: 4,
  },
  borderTop0: {borderTopWidth: 0},
  content: {
    flexGrow: 1,
    marginHorizontal: 16,
  },
  driverImg: {
    borderColor: Colors.neutral200,
    borderRadius: 24,
    borderWidth: 1,
    height: 43,
    width: 43,
  },
  mHor0: {marginHorizontal: -16},
});
