import React from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import ListActivities from './components/ListActivities';
import {GlobalStyle} from '@themes/GlobalStyle';
import NavHeader from '@components/NavHeader';
import {WView} from '@components/index';
import {EKeyTabActivity} from '@themes/Enums';

function ActivitiesHistory() {
  const {top} = useSafeAreaInsets();
  return (
    <WView pTop={top} style={GlobalStyle.container}>
      <NavHeader title="History" />
      <WView fill mTop={12}>
        <ListActivities type={EKeyTabActivity.History} />
      </WView>
    </WView>
  );
}

export default ActivitiesHistory;
