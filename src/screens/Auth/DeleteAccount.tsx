import {useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {SafeAreaView, StyleSheet, TextInput} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {NavHeader, WButton, WText, WTextInput, WView} from '@components';
import {Colors} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import CheckBoxView from './components/CheckBoxView';
import {
  useNotificationTokensServiceNotificationTokenControllerDeleteNotificationToken,
  useUsersServiceUserControllerDeleteMe,
} from '@queries';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {queryClient} from '@react-query/queryClient';
import {useGlobalState} from '@react-query/clientStateManage';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '../../themes/Constants';
import MixPanelSdk from '@utils/mixPanelSdk';

function DeleteAccount() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const [fcmData] = useGlobalState('fcmData');
  const [userData] = useGlobalState('userData');

  const [isConfirm, setIsConfirm] = useState(false);
  const [isUnderstand, setIsUnderstand] = useState(false);

  const {mutate: deleteMe} = useUsersServiceUserControllerDeleteMe();
  const {mutate: deleteNotificationToken} =
    useNotificationTokensServiceNotificationTokenControllerDeleteNotificationToken();

  const onDeleteAccount = () => {
    if (fcmData?.id) {
      deleteNotificationToken({
        id: fcmData?.id,
      });
    }
    deleteMe(undefined, {
      onSuccess: () => {
        AsyncStorage.clear();
        queryClient.clear();
        MixPanelSdk.logEvent({
          eventName: 'account_delete',
          eventValues: {
            email: userData?.user?.email,
            phoneNumber: userData?.user?.phoneNumber,
            userName: userData?.user?.fullName,
          },
        });
        navigation.reset({routes: [{name: 'Login'}]});
      },
    });
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        style={styles.container}>
        <NavHeader title={t('deleteAccount.title')} />
        <WView mHoz={16} mTop={28}>
          <WText lineHeight={22} type="regular14">
            {t('deleteAccount.content1')}
          </WText>
          <WText lineHeight={22} type="medium14">
            {t('deleteAccount.content2')}
          </WText>
          <WView mHoz={8} mTop={4}>
            <WView row alignItems="flex-start">
              <WText type="regular14">{`•`}</WText>
              <WText lineHeight={22} type="regular14" marginLeft={10}>
                {t('deleteAccount.content3')}
              </WText>
            </WView>
            <WView row alignItems="flex-start">
              <WText type="regular14">{`•`}</WText>
              <WText lineHeight={22} type="regular14" marginLeft={10}>
                {t('deleteAccount.content4')}
              </WText>
            </WView>
          </WView>
          <WText type="medium14" lineHeight={22} marginTop={12}>
            {t('deleteAccount.content5')}
          </WText>
          <WText type="regular14" lineHeight={22}>
            {t('deleteAccount.content6')}
          </WText>
          <WView
            mTop={12}
            borderRadius={12}
            padding={16}
            color={Colors.white}
            style={GlobalStyle.shadow}>
            <WText type="regular14" marginBottom={6}>
              {t('deleteAccount.reason')}
            </WText>
            <TextInput
              allowFontScaling={false}
              multiline
              onChangeText={() => {}}
              placeholder={t('deleteAccount.enterReason')}
              style={styles.input}
              textAlignVertical={'top'}
            />
            <CheckBoxView
              title={t('deleteAccount.confirm1')}
              onPress={() => {
                setIsUnderstand(!isUnderstand);
              }}
              isCheck={isUnderstand}
              style={{marginTop: 16}}
            />
            <CheckBoxView
              title={t('deleteAccount.confirm2')}
              onPress={() => {
                setIsConfirm(!isConfirm);
              }}
              isCheck={isConfirm}
              style={{marginVertical: 16}}
            />
            <WButton
              disabled={!isUnderstand || !isConfirm}
              onPress={onDeleteAccount}
              label={t('button.confirmDelete')}
            />
          </WView>
        </WView>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
}

export default DeleteAccount;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  input: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral600,
    borderRadius: 8,
    borderWidth: 1,
    height: SCREEN_HEIGHT / 7,
    marginTop: 'auto',
    marginVertical: 12,
    padding: 15,
    paddingTop: 15,
    width: SCREEN_WIDTH - 64,
  },
});
