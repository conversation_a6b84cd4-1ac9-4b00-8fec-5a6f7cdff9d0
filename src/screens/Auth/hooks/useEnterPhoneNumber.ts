import {useFormik} from 'formik';
import {useTranslation} from 'react-i18next';
import * as Yup from 'yup';
import {Linking} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {PHONE_REGEX, POLICY_VERIFY_PHONE} from '@themes/Constants';
import {numericText, showErrorAlert} from '@utils/Tools';

export const useEnterPhoneNumber = () => {
  const {t, i18n} = useTranslation();
  const navigation = useNavigation();

  const formik = useFormik({
    initialValues: {
      phoneNumber: '',
    },
    onSubmit: ({phoneNumber}) => handleSubmit(phoneNumber),
    validationSchema: Yup.object({
      phoneNumber: Yup.string()
        .matches(PHONE_REGEX, t('validate.phoneNumber.invalid'))
        .required(t('validate.phoneNumber.required')),
    }),
  });

  const handleOpenPolicy = () => {
    const language = i18n.language as 'en' | 'fr';
    const policyUrl = POLICY_VERIFY_PHONE[language];
    Linking.canOpenURL(policyUrl).then(supported => {
      if (supported) {
        Linking.openURL(policyUrl);
      } else {
        showErrorAlert({
          message: t('error.invalidLink'),
        });
      }
    });
  };

  const handleChangePhoneNumber = (text: string) => {
    formik.setFieldValue('phoneNumber', numericText(text));
  };

  const handleSubmit = (phoneNumber: string) => {
    //todo
    console.log(phoneNumber);
    navigation.navigate('OtpVerificationV2', {
      isTopBarEnable: false,
    });
  };

  return {
    formik,
    handleChangePhoneNumber,
    handleOpenPolicy,
  };
};
