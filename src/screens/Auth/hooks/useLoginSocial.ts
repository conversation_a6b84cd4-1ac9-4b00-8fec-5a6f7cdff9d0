import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {useEffect} from 'react';
import Config from 'react-native-config';
import {Alert} from 'react-native';
import appleAuth from '@invertase/react-native-apple-authentication';
import {SOCIAL} from '@themes/Constants';

export const useLoginSocial = () => {
  useEffect(() => {
    GoogleSignin.configure({
      forceCodeForRefreshToken: true,
      hostedDomain: '',
      offlineAccess: false,
      webClientId: Config.GOOGLE_WEB_CLIENT_ID,
    });
  }, []);

  const onLoginGoogle = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const data = await GoogleSignin.signIn();
      if (data?.idToken) {
        //todo
      }
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        return;
      }
      if (error.code === statusCodes.IN_PROGRESS) {
        return;
      }
      if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        Alert.alert('error.googleLogin');
      } else {
        Alert.alert(error?.message);
      }
    }
  };

  const onLoginApple = async () => {
    try {
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      });
      if (!appleAuthRequestResponse.identityToken) {
        return;
      }
      const {identityToken, fullName} = appleAuthRequestResponse;
      const _userName = `${fullName?.givenName || ''} ${
        fullName?.familyName || ''
      }`?.trim();

      if (identityToken) {
        //todo
      }
    } catch (error: any) {
      if (error?.code === appleAuth.Error.CANCELED) {
        return;
      }
      return;
    }
  };

  const onLoginSocial = (type: SOCIAL) => {
    if (type === SOCIAL.APPLE) {
      onLoginApple();
    } else {
      onLoginGoogle();
    }
  };
  return {
    onLoginSocial,
  };
};
