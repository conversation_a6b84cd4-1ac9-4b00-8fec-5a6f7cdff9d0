import {useNavigation} from '@react-navigation/native';

export const useLoginV2 = () => {
  const navigation = useNavigation();

  const navigateToLogin = () => {
    navigation.navigate('Login', {isTopBarEnable: false});
  };

  const navigateToEnterPhone = () => {
    navigation.navigate('EnterPhoneNumber', {isTopBarEnable: false});
  };

  return {
    navigateToEnterPhone,
    navigateToLogin,
  };
};
