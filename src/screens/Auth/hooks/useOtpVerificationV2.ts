import {useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';

export const useOtpVerificationV2 = () => {
  const navigation = useNavigation();
  const [otpCode, setOtpCode] = useState('');
  const [timer, setTimer] = useState(60);
  const [isCounting, setIsCounting] = useState(true);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isCounting && timer > 0) {
      interval = setInterval(() => {
        setTimer(prevTimer => {
          if (prevTimer <= 1) {
            setIsCounting(false);
            return 0;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isCounting, timer]);

  const handleResendCode = () => {
    if (!isCounting) {
      setTimer(60);
      setIsCounting(true);
      // TODO: Add API call to resend OTP
    }
  };

  const handleOtpChange = (code: string | undefined) => {
    setOtpCode(code || '');
  };

  const handleOtpSubmit = (code: string) => {
    console.log('OTP submitted:', code);
    navigation.navigate('UpdateInformation', {isTopBarEnable: false});
  };

  return {
    handleOtpChange,
    handleOtpSubmit,
    handleResendCode,
    isCounting,
    otpCode,
    timer,
  };
};
