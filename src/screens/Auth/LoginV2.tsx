import {StyleSheet, View} from 'react-native';
import React from 'react';
import LottieView from 'lottie-react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import SocialLoginButtons from './components/SocialLoginButtons';
import {useLoginV2} from './hooks/useLoginV2';
import {LOTTIE} from '@themes/Lottie';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import {WButton, WText, WTouchable, WView} from '@components/index';
import {Colors} from '@themes/index';
import {GlobalStyle} from '@themes/GlobalStyle';

function LoginV2() {
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {navigateToLogin, navigateToEnterPhone} = useLoginV2();

  return (
    <View>
      <LottieView
        autoPlay
        loop={true}
        source={LOTTIE.introLogin}
        style={{height: SCREEN_HEIGHT, width: SCREEN_WIDTH}}
      />
      <WView
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        color={Colors.white}
        pHoz={16}
        pVer={24}
        style={[styles.container, GlobalStyle.shadowCard]}>
        <WView gap={8} mBottom={24}>
          <WText color={Colors.neutral850} type="semiBold20">
            {t('login.getStarted')}
          </WText>
          <WText color={Colors.neutral875} type="regular16">
            {t('login.createAccount')}
          </WText>
        </WView>
        <WButton
          label={t('login.continuePhoneNumber')}
          onPress={navigateToEnterPhone}
        />
        <SocialLoginButtons />
        <WTouchable
          alignCenter
          mBottom={bottom}
          mTop={24}
          onPress={navigateToLogin}>
          <WText
            color={Colors.neutral850}
            textDecorationLine="underline"
            type="regular14">
            {t('login.loginInWithEmail')}
          </WText>
        </WTouchable>
      </WView>
    </View>
  );
}

export default LoginV2;
const styles = StyleSheet.create({
  container: {
    bottom: 0,
    position: 'absolute',
    width: '100%',
  },
});
