/* eslint-disable react-native/no-raw-text */
import {Image, SafeAreaView, StyleSheet} from 'react-native';
import React from 'react';
import {useTranslation} from 'react-i18next';
import Header from './components/Header';
import CustomOtpInput from './components/CustomOtpInput';
import {useOtpVerificationV2} from './hooks/useOtpVerificationV2';
import {WText, WView} from '@components/index';
import {Colors, Images} from '@themes/index';
import {formatTimeMinuteSecond} from '@utils/Tools';
import {GlobalStyle} from '@themes/GlobalStyle';
import {SCREEN_WIDTH} from '@themes/Constants';

function OtpVerificationV2() {
  const {t} = useTranslation();
  const {
    handleOtpChange,
    handleOtpSubmit,
    handleResendCode,
    isCounting,
    // otpCode,
    timer,
  } = useOtpVerificationV2();

  return (
    <SafeAreaView style={GlobalStyle.container}>
      <Header title={t('login.otpVerification')} />

      <WView mHoz={16} mTop={24}>
        <WView style={styles.backgroundImg}>
          <Image
            resizeMode="contain"
            source={Images.loginBackground}
            style={{
              height: SCREEN_WIDTH / 2,
              width: SCREEN_WIDTH / 2,
            }}
          />
        </WView>
        <WText color={Colors.neutral700} type="regular14">
          {t('login.enterOtp')} <WText type="semiBold14">(+1) 905123456</WText>
        </WText>

        <WView mTop={32}>
          <CustomOtpInput
            autoFocusOnLoad={true}
            onChange={handleOtpChange}
            onSubmit={handleOtpSubmit}
          />
        </WView>

        <WView center mTop={32}>
          <WView alignCenter center row>
            <WText
              color={isCounting ? Colors.neutral500 : Colors.neutral875}
              type="medium14">
              {isCounting
                ? t('button.resendCodeIn')
                : t('login.didNotReceiveCode')}
            </WText>

            <WText
              color={isCounting ? Colors.neutral900 : Colors.blue700}
              disabled={isCounting}
              onPress={handleResendCode}
              type="medium14">
              {isCounting
                ? ` ${formatTimeMinuteSecond(timer)}`
                : ` ${t('button.resend')}`}
            </WText>
          </WView>
        </WView>
      </WView>
    </SafeAreaView>
  );
}

export default OtpVerificationV2;
const styles = StyleSheet.create({
  backgroundImg: {
    opacity: 0.5,
    position: 'absolute',
    right: -16,
    top: -40,
  },
});
