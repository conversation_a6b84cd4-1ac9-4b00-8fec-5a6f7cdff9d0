import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React, {useRef} from 'react';
import {SafeAreaView, StyleSheet, TextInput} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import * as Yup from 'yup';
import {useUsersServiceUserControllerUpdatePass} from '@queries';
import {UpdateUserPasswordDto} from '@requests';
import {NavHeader, WButton, WTextInput, WView} from '@components';
import {PASSWORD_REGEX} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

const INITIAL_FORMIK = {
  oldPassword: '',
  password: '',
};

function ChangePassword() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const passRef = useRef<TextInput>(null);

  const {mutate: changePassword} = useUsersServiceUserControllerUpdatePass();

  const onChangePassword = (data: UpdateUserPasswordDto) => {
    changePassword(
      {
        requestBody: data,
      },
      {
        onSuccess: () => {
          navigation.goBack();
        },
      },
    );
  };
  const onSubmitEditing = (field: string) => {
    switch (field) {
      case 'oldPassword':
        passRef.current?.focus();
        break;
      default:
        break;
    }
  };
  return (
    <Formik
      initialValues={INITIAL_FORMIK}
      onSubmit={values => onChangePassword(values)}
      validationSchema={Yup.object({
        oldPassword: Yup.string()
          // .matches(PASSWORD_REGEX, t('validate.password.invalid'))
          .required(t('validate.password.required')),
        password: Yup.string()
          .matches(PASSWORD_REGEX, t('validate.password.invalid'))
          .required(t('validate.password.required')),
      })}>
      {({
        handleChange,
        handleSubmit,
        handleBlur,
        errors,
        values,
        isValid,
        touched,
      }) => (
        <SafeAreaView style={GlobalStyle.flex1}>
          <KeyboardAwareScrollView
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            style={styles.container}>
            <NavHeader title={t('changePassword.title')} />
            <WView mHoz={16} mTop={32}>
              <WTextInput
                defaultValue={values?.oldPassword}
                errorMessage={errors?.oldPassword}
                isSecure
                onBlur={handleBlur('oldPassword')}
                onChangeText={handleChange('oldPassword')}
                onSubmitEditing={() => onSubmitEditing('oldPassword')}
                required
                returnKeyType="next"
                title={t('changePassword.currentPassword')}
                touched={touched?.oldPassword}
              />
              <WTextInput
                containerStyle={styles.inputContainer}
                defaultValue={values?.password}
                errorMessage={errors?.password}
                isSecure
                onBlur={handleBlur('password')}
                onChangeText={handleChange('password')}
                onSubmitEditing={handleSubmit}
                ref={passRef}
                required
                returnKeyType="done"
                title={t('changePassword.newPassword')}
                touched={touched?.password}
              />
            </WView>
          </KeyboardAwareScrollView>
          <WButton
            disabled={!isValid || !values?.oldPassword || !values?.password}
            label={t('button.saveChange')}
            onPress={handleSubmit}
            style={styles.submitBtn}
          />
        </SafeAreaView>
      )}
    </Formik>
  );
}

export default ChangePassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  inputContainer: {marginTop: 16},
  submitBtn: {marginBottom: 16, marginHorizontal: 16},
});
