/* eslint-disable react-hooks/exhaustive-deps */
import {ActivityIndicator} from 'react-native';
import React, {useEffect} from 'react';
import {useNavigation} from '@react-navigation/native';
import {
  useUsersServiceUserControllerGetUser,
  useUsersServiceUserControllerUpdateReferredUser,
} from '@queries';
import MixPanelSdk from '@utils/mixPanelSdk';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useGlobalState} from '@react-query/clientStateManage';
import {WView} from '@components/index';
import {Colors} from '@themes/index';

function ReferralCode() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [loyalPointConfig] = useGlobalState('loyalPointConfig');

  const {data: user, isPending} =
    useUsersServiceUserControllerGetUser(undefined);
  const [referralCode] = useGlobalState('referralCode');

  const {mutate: mutateReferral} =
    useUsersServiceUserControllerUpdateReferredUser();
  const handleOpenReferralCode = () => {
    const onApply = (code?: string) => {
      mutateReferral(
        {
          requestBody: {
            referredUserCode: code || '',
          },
        },
        {
          onSuccess: () => {
            navigation.goBack();
            const eventValues = {
              email: user?.data?.email,
              phoneNumber: user?.data?.phoneNumber,
              referralCode: code,
              userName: user?.data?.fullName,
            };

            MixPanelSdk.logEvent({
              eventName: 'referral_code_submit',
              eventValues: {
                ...eventValues,
                referralCode,
              },
            });
            navigation.reset({routes: [{name: 'Main'}]});
          },
        },
      );
    };
    setTimeout(() => {
      navigation.navigate('ActionModal', {
        cancelLabel: t('button.skip'),
        confirmLabel: t('button.submit'),
        content: t('referral.socialContent', {
          count: loyalPointConfig?.signUpReferencePoint || 0,
        }),
        defaultInputValue: referralCode,
        enableCloseOnMask: false,
        hasInput: true,
        onApply: (data?: string) => onApply(data),
        onCancel: () => {
          navigation.goBack();
          navigation.reset({routes: [{name: 'Main'}]});
        },
        placeholder: t('referral.enterReferralCode'),
        title: t('referral.enterReferralCode'),
        validateType: 'referralCode',
      });
    }, 300);
  };

  useEffect(() => {
    if (user?.data?.email) {
      handleOpenReferralCode();
    }
  }, [user?.data?.email]);

  return (
    <WView center color={Colors.background} fill>
      {isPending && <ActivityIndicator color={Colors.primary} size={'large'} />}
    </WView>
  );
}

export default ReferralCode;
