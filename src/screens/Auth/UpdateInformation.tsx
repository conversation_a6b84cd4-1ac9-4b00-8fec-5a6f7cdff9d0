import {SafeAreaView} from 'react-native';
import React from 'react';
import {useTranslation} from 'react-i18next';
import Header from './components/Header';
import {useUpdateInformation} from './hooks/useUpdateInformation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {WButton, WText, WView} from '@components/index';
import {Colors} from '@themes/index';
import WAnimatedInput from '@components/WAnimatedInput';

function UpdateInformation() {
  const {t} = useTranslation();
  const {} = useUpdateInformation();
  return (
    <SafeAreaView style={GlobalStyle.container}>
      <Header title={t('login.updateAccount')} />
      <WView fill mHoz={16}>
        <WText color={Colors.neutral700} type="regular14">
          {t('login.enterRealName')}
        </WText>

        <WView fill gap={12} mTop={24}>
          <WAnimatedInput label={t('login.yourName')} required />
          <WAnimatedInput label={t('login.yourEmail')} />
          <WAnimatedInput label={t('login.referralCode')} />
        </WView>
        <WButton label={t('button.finish')} />
      </WView>
    </SafeAreaView>
  );
}

export default UpdateInformation;
