import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import IonIcon from 'react-native-vector-icons/Ionicons';
import Toast from 'react-native-toast-message';
import {formatNumberLocalized, showErrorAlert} from '@utils/Tools';
import {
  ILoginResponse,
  OpenAPI,
  ResendCodeInputDto,
  UserEntity,
} from '@requests';
import {SCREEN_WIDTH} from '@themes/Constants';
import {CodeInputForm, WText, WView} from '@components';
import {
  useAuthServiceAuthControllerConfirmRegister,
  useAuthServiceAuthControllerRenewOtp,
  useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig,
  useUsersServiceUserControllerGetUser,
  useUsersServiceUserControllerGetUserKey,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {Colors, Images} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {VerifyCodeRouteProps} from '@global';
import MixPanelSdk from '@utils/mixPanelSdk';
import AppsFlyer from '@utils/appsFlyerSdk';

interface VerifyCodeProps {
  route: RouteProp<VerifyCodeRouteProps, 'VerifyCode'>;
}

const FORM_FORGOT_PASSWORD_TYPE = {
  code: 'code',
  email: 'email',
  password: 'password',
};

function VerifyCode({route}: VerifyCodeProps) {
  const {referralCode} = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [formType, setFormType] = useState(FORM_FORGOT_PASSWORD_TYPE.email);
  const [__, setUserData] = useGlobalState('userData');

  const {data: pointConfig} =
    useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig();

  const {
    mutate: confirmRegister,
    isError,
    data: loginRes,
  } = useAuthServiceAuthControllerConfirmRegister({
    onSuccess: (res: {data: ILoginResponse}) => {
      OpenAPI.TOKEN = res?.data?.accessToken;
    },
  });

  const {
    data: user,
    isFetching,
    isSuccess,
  } = useUsersServiceUserControllerGetUser(
    [`verifyCode_${useUsersServiceUserControllerGetUserKey}`],
    {
      enabled: !!loginRes?.data?.accessToken,
    },
  );

  const {mutate: renewOTP} = useAuthServiceAuthControllerRenewOtp();

  useEffect(() => {
    if (user?.data && isSuccess) {
      setUserData({
        accessToken: loginRes?.data?.accessToken as string,
        refreshToken: loginRes?.data?.refreshToken as string,
        user: user?.data as UserEntity,
      });
      OpenAPI.TOKEN = loginRes?.data?.accessToken;
      if (pointConfig?.data?.signUpReferencePoint > 0 && referralCode) {
        Toast.show({
          position: 'top',
          props: {
            content: t('referral.referralBonusUnlockedContent', {
              count: formatNumberLocalized(
                pointConfig?.data?.signUpReferencePoint,
              ),
            }),
            title: t('referral.referralBonusUnlocked'),
          },
          topOffset: 10,
          type: 'success',
          visibilityTime: 4000,
        });
      }
      trackEventRegisterUser();
      navigation.reset({routes: [{name: 'Main'}]});
    }
  }, [isFetching]);

  const trackEventRegisterUser = () => {
    const eventValues = {
      email: user?.data?.email,
      phoneNumber: user?.data?.phoneNumber,
      userName: user?.data?.fullName,
    };
    AppsFlyer.logEvent({
      eventName: 'user_signup',
      eventValues,
    });
    MixPanelSdk.logEvent({
      eventName: 'user_signup',
      eventValues,
    });
    if (referralCode) {
      MixPanelSdk.logEvent({
        eventName: 'referral_code_submit',
        eventValues: {
          ...eventValues,
          referralCode,
        },
      });
    }
  };

  const onConfirmRegister = (code: string) => {
    if (formType === FORM_FORGOT_PASSWORD_TYPE.email) {
      setFormType(FORM_FORGOT_PASSWORD_TYPE.code);
    }
    if (formType === FORM_FORGOT_PASSWORD_TYPE.code) {
      setFormType(FORM_FORGOT_PASSWORD_TYPE.password);
    }
    confirmRegister({
      requestBody: {
        email: route?.params?.email || '',
        verifyCode: code,
      },
    });
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        style={styles.container}>
        <WView fill padding={16}>
          <WView justifyContent="flex-end" row>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral150}
                h={40}
                w={40}>
                <IonIcon
                  color={Colors.neutral600}
                  name="close-outline"
                  size={20}
                />
              </WView>
            </TouchableWithoutFeedback>
          </WView>

          <WView mTop={40}>
            <WView style={styles.backgroundImg}>
              <Image
                resizeMode="contain"
                source={Images.loginBackground}
                style={{
                  height: SCREEN_WIDTH / 2,
                  width: SCREEN_WIDTH / 2,
                }}
              />
            </WView>
            <WText marginBottom={8} type="medium34">
              {t('verifyCode.title')}
            </WText>
          </WView>
          <WView mTop={16}>
            <WText type="regular14">{t('verifyCode.content')}</WText>
            <CodeInputForm
              autoFocusOnLoad={true}
              onSubmit={code => {
                onConfirmRegister(code);
              }}
              style={styles.codeInputContainer}
            />
            {isError && (
              <WText color={Colors.primary900} marginTop={8} type="regular14">
                {t('verifyCode.expiredCode')}
              </WText>
            )}
          </WView>
          <TouchableOpacity
            onPress={() => {
              // onConfirmRegister();
            }}
            style={styles.confirmBtn}>
            <WText color={Colors.white} type="semiBold14">
              {t('button.confirm')}
            </WText>
          </TouchableOpacity>
          <TouchableOpacity
            hitSlop={GlobalStyle.hitSlop}
            onPress={() => {
              renewOTP(
                {
                  requestBody: {
                    action: ResendCodeInputDto.action.REGISTER,
                    email: route?.params?.email || '',
                  },
                },
                {
                  onSuccess: () => {
                    showErrorAlert({
                      message: t('error.codeSend'),
                    });
                  },
                },
              );
            }}
            style={styles.resendCodeBtn}>
            <WText type="semiBold16">{t('button.resendCode')}</WText>
          </TouchableOpacity>
        </WView>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
}

export default VerifyCode;

const styles = StyleSheet.create({
  backgroundImg: {
    position: 'absolute',
    right: -16,
    top: -40,
  },
  codeInputContainer: {marginTop: 16},
  confirmBtn: {
    alignItems: 'center',
    backgroundColor: Colors.neutral875,
    borderRadius: 8,
    height: 46,
    justifyContent: 'center',
    marginVertical: 16,
    minWidth: 80,
  },
  container: {
    flex: 1,
  },
  resendCodeBtn: {alignSelf: 'center'},
});
