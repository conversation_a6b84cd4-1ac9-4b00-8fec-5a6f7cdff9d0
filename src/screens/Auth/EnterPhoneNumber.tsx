/* eslint-disable react-native/no-raw-text */
import {SafeAreaView, StyleSheet} from 'react-native';
import React from 'react';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Image} from 'react-native';
import Header from './components/Header';
import {useEnterPhoneNumber} from './hooks/useEnterPhoneNumber';
import {WButton, WImage, WText, WTextInput, WView} from '@components/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors, Icons, Images} from '@themes/index';
import {CANADA_PHONE_CODE, SCREEN_WIDTH} from '@themes/Constants';

function EnterPhoneNumber() {
  const {t} = useTypeSafeTranslation();

  const {formik, handleOpenPolicy, handleChangePhoneNumber} =
    useEnterPhoneNumber();

  const renderPhone = () => (
    <WView
      alignCenter
      borderRadius={10}
      color={Colors.neutral150}
      gap={8}
      h={47}
      padding={12}
      row>
      <WImage size={24} source={Icons.icCanada} />
      <WText color={Colors.neutral900} type="medium16">
        {CANADA_PHONE_CODE}
      </WText>
      <Ionicons
        color={Colors.neutral900}
        name="chevron-down-outline"
        size={16}
      />
    </WView>
  );

  return (
    <SafeAreaView>
      <Header title={t('login.enterPhoneNumber')} />
      <WView gap={16} mHoz={16}>
        <WView style={styles.backgroundImg}>
          <Image
            resizeMode="contain"
            source={Images.loginBackground}
            style={{
              height: SCREEN_WIDTH / 2,
              width: SCREEN_WIDTH / 2,
            }}
          />
        </WView>
        <WText color={Colors.neutral700} type="regular14">
          {t('login.toCreateANew')}
        </WText>

        <WView gap={8} row>
          {renderPhone()}
          <WTextInput
            clearButtonMode="while-editing"
            containerStyle={styles.containerInput}
            errorMessage={formik.errors?.phoneNumber}
            keyboardType="phone-pad"
            onBlur={formik.handleBlur('phoneNumber')}
            onChangeText={handleChangePhoneNumber}
            placeholder={t('booking.phonePlaceholder')}
            touched={formik.touched?.phoneNumber}
            value={formik.values.phoneNumber}
          />
        </WView>

        <WButton label={t('button.continue')} onPress={formik.handleSubmit} />
        <WText
          color={Colors.neutral700}
          lineHeight={16}
          marginTop={12}
          type="medium12">
          {t('verifyPhone.policy')}
          <WText
            color={Colors.primary}
            lineHeight={16}
            onPress={handleOpenPolicy}
            type="semiBold12">
            {t('verifyPhone.here')}
          </WText>
          .
        </WText>
      </WView>
    </SafeAreaView>
  );
}

export default EnterPhoneNumber;
const styles = StyleSheet.create({
  backgroundImg: {
    opacity: 0.5,
    position: 'absolute',
    right: -16,
    top: -40,
  },
  containerInput: {
    flex: 1,
  },
});
