import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React, {useMemo, useRef, useState} from 'react';
import {
  Alert,
  Image,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import IonIcon from 'react-native-vector-icons/Ionicons';
import * as Yup from 'yup';
import {showErrorAlert} from '../../utils/Tools';
import PasswordGuideLine from './components/PasswordGuideLine';
import {
  ForgotPasswordInputDto,
  ResendCodeInputDto,
  VerifyCodeInputDto,
} from '@requests';
import {
  useAuthServiceAuthControllerForgotPassword,
  useAuthServiceAuthControllerRenewOtp,
  useAuthServiceAuthControllerResetPassword,
  useAuthServiceAuthControllerVerifyOtp,
} from '@queries';
import {
  LOWER_CASE_REGEX,
  NUMBER_CASE_REGEX,
  PASSWORD_REGEX,
  SCREEN_WIDTH,
  SPECIAL_CHARACTER_REGEX,
  UPPER_CASE_REGEX,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors, Images} from '@themes';
import {CodeInputForm, WButton, WText, WTextInput, WView} from '@components';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

const INITIAL_FORMIK = {
  code: '',
  email: '',
  password: '',
};
interface FormProps {
  [key: string]: any;
}

const FORM_FORGOT_PASSWORD_TYPE = {
  code: 'code',
  email: 'email',
  password: 'password',
};

function ForgotPassword() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const passRef = useRef<TextInput>(null);
  const [formType, setFormType] = useState(FORM_FORGOT_PASSWORD_TYPE.email);

  const isCodeForm = formType === FORM_FORGOT_PASSWORD_TYPE.code;
  const isEmailForm = formType === FORM_FORGOT_PASSWORD_TYPE.email;
  const isPasswordForm = formType === FORM_FORGOT_PASSWORD_TYPE.password;

  const defaultSchema = Yup.object({});

  const emailSchema = Yup.string()
    .email(t('validate.email.invalid'))
    .required(t('validate.email.required'));

  const passwordSchema = Yup.string()
    .matches(PASSWORD_REGEX, t('validate.password.invalid'))
    .required(t('validate.password.required'));

  const dynamicSchema: FormProps = {
    code: undefined,
    email: emailSchema,
    password: passwordSchema,
  };

  const validationSchema = useMemo(
    () =>
      defaultSchema.shape({
        [formType]: dynamicSchema[formType],
      }),
    [formType],
  );

  const {mutate: forgotPassword, variables: forgotPasswordVariables} =
    useAuthServiceAuthControllerForgotPassword({
      onSuccess: () => {
        setFormType(FORM_FORGOT_PASSWORD_TYPE.code);
      },
    });

  const {
    mutate: verifyOtp,
    variables,
    isError,
  } = useAuthServiceAuthControllerVerifyOtp({
    onSuccess: () => {
      setFormType(FORM_FORGOT_PASSWORD_TYPE.password);
    },
  });

  const {mutate: resetPassword} = useAuthServiceAuthControllerResetPassword({
    onSuccess: () => {
      navigation.goBack();
    },
  });

  const {mutate: renewOtp} = useAuthServiceAuthControllerRenewOtp();

  const onForgotPassword = (data: any) => {
    if (isEmailForm) {
      return forgotPassword({
        requestBody: {
          ...data,
          userType: ForgotPasswordInputDto.userType.CUSTOMER,
        },
      });
    }
    if (isCodeForm) {
      return verifyOtp({
        requestBody: {
          action: VerifyCodeInputDto.action.RESET_PASSWORD,
          email: data?.email?.toLocaleLowerCase(),
          verifyCode: data?.code,
        },
      });
    }
    resetPassword({
      requestBody: {
        email:
          forgotPasswordVariables?.requestBody?.email?.toLocaleLowerCase() ||
          '',
        password: data?.password,
        verifyCode: variables?.requestBody?.verifyCode || '',
      },
    });
  };

  const onSubmitEditing = (field: string) => {
    switch (field) {
      case 'email':
        passRef.current?.focus();
        break;
      default:
        break;
    }
  };

  const onResendCode = () => {
    renewOtp(
      {
        requestBody: {
          action: ResendCodeInputDto.action.RESET_PASSWORD,
          email:
            forgotPasswordVariables?.requestBody?.email?.toLocaleLowerCase() ||
            '',
        },
      },
      {
        onSuccess: () => {
          showErrorAlert({message: t('error.codeSend')});
        },
      },
    );
  };

  return (
    <Formik
      initialValues={INITIAL_FORMIK}
      onSubmit={values => onForgotPassword(values)}
      validationSchema={validationSchema}>
      {({
        handleChange,
        handleSubmit,
        setFieldValue,
        handleBlur,
        touched,
        errors,
        values,
      }) => {
        const passwordError = !values?.password ? errors?.password : '';
        return (
          <SafeAreaView style={GlobalStyle.flex1}>
            <KeyboardAwareScrollView
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
              style={styles.container}>
              <WView fill padding={16}>
                <WView alignCenter justifyBetween row>
                  <Image source={Images.appLogo} />
                  <TouchableWithoutFeedback
                    onPress={() => {
                      navigation.goBack();
                    }}>
                    <WView
                      borderRadius={20}
                      center
                      color={Colors.neutral150}
                      h={40}
                      w={40}>
                      <IonIcon
                        color={Colors.neutral600}
                        name="close-outline"
                        size={20}
                      />
                    </WView>
                  </TouchableWithoutFeedback>
                </WView>

                <WView mTop={40}>
                  <WView style={styles.backgroundImg}>
                    <Image
                      resizeMode="contain"
                      source={Images.loginBackground}
                      style={{
                        height: SCREEN_WIDTH / 2,
                        width: SCREEN_WIDTH / 2,
                      }}
                    />
                  </WView>
                  <WText marginBottom={8} type="medium34">
                    {t('forgotPassword.title')}
                  </WText>
                </WView>
                {(isEmailForm || isPasswordForm) && (
                  <WView style={styles.content}>
                    <WTextInput
                      errorMessage={
                        isPasswordForm ? passwordError : errors?.email
                      }
                      onBlur={handleBlur(isPasswordForm ? 'password' : 'email')}
                      onChangeText={handleChange(
                        isPasswordForm ? 'password' : 'email',
                      )}
                      onSubmitEditing={() => onSubmitEditing('email')}
                      returnKeyType="next"
                      title={
                        isPasswordForm
                          ? t('forgotPassword.newPassword')
                          : t('login.emailTitle')
                      }
                      touched={touched?.email}
                    />
                    {!!values?.password && !!errors?.password && (
                      <WView>
                        <PasswordGuideLine
                          isValid={values?.password?.length >= 8}
                          label={t('validate.password.min')}
                        />
                        <PasswordGuideLine
                          isValid={PASSWORD_REGEX.test(values?.password)}
                          label={t('validate.password.shouldContain')}
                        />
                        <WView mLeft={20}>
                          <PasswordGuideLine
                            isValid={LOWER_CASE_REGEX.test(values?.password)}
                            label={t('validate.password.lowerCase')}
                            labelStyle={styles.mLeft8}
                          />
                          <PasswordGuideLine
                            isValid={UPPER_CASE_REGEX.test(values?.password)}
                            label={t('validate.password.upperCase')}
                            labelStyle={styles.mLeft8}
                          />
                          <PasswordGuideLine
                            isValid={NUMBER_CASE_REGEX.test(values?.password)}
                            label={t('validate.password.number')}
                            labelStyle={styles.mLeft8}
                          />
                          <PasswordGuideLine
                            isValid={SPECIAL_CHARACTER_REGEX.test(
                              values?.password,
                            )}
                            label={t('validate.password.specialCharacters')}
                            labelStyle={styles.mLeft8}
                          />
                        </WView>
                      </WView>
                    )}
                  </WView>
                )}
                {isCodeForm && (
                  <WView mTop={16}>
                    <WText type="regular14">
                      {t('forgotPassword.content')}
                    </WText>
                    <CodeInputForm
                      autoFocusOnLoad={true}
                      onChange={verifyCode => {
                        setFieldValue('code', verifyCode);
                      }}
                      onSubmit={verifyCode => {
                        setFieldValue('code', verifyCode);
                        verifyOtp({
                          requestBody: {
                            action: VerifyCodeInputDto.action.RESET_PASSWORD,
                            email: values?.email?.toLocaleLowerCase(),
                            verifyCode,
                          },
                        });
                      }}
                      style={styles.mTop16}
                    />
                    {isError && (
                      <WText
                        color={Colors.primary900}
                        marginTop={8}
                        type="regular14">
                        {t('verifyCode.expiredCode')}
                      </WText>
                    )}
                  </WView>
                )}
                <WButton
                  disabled={
                    isEmailForm
                      ? false
                      : values?.code?.length < 6 || !values?.code
                  }
                  label={t('button.confirm')}
                  onPress={() => {
                    handleSubmit();
                  }}
                  style={styles.confirmBtn}
                />
                {isCodeForm && (
                  <TouchableOpacity
                    hitSlop={GlobalStyle.hitSlop}
                    onPress={onResendCode}
                    style={styles.resendCodeBtn}>
                    <WText type="semiBold16">{t('button.resendCode')}</WText>
                  </TouchableOpacity>
                )}
              </WView>
            </KeyboardAwareScrollView>
          </SafeAreaView>
        );
      }}
    </Formik>
  );
}

export default ForgotPassword;

const styles = StyleSheet.create({
  backgroundImg: {
    position: 'absolute',
    right: -16,
    top: -40,
  },
  confirmBtn: {
    marginVertical: 16,
  },
  container: {
    flex: 1,
  },
  content: {paddingTop: 16},
  mLeft8: {marginLeft: 8},
  mTop16: {marginTop: 16},
  resendCodeBtn: {alignSelf: 'center'},
});
