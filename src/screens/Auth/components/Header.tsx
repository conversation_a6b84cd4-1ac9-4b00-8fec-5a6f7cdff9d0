import React from 'react';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import WText from '@components/WText';
import {Colors} from '@themes/index';
import {WTouchable, WView} from '@components/index';

interface Props {
  title: string;
}

function Header({title}: Props) {
  const navigation = useNavigation();
  return (
    <WView gap={12} mBottom={16} mHoz={16}>
      <WTouchable onPress={navigation.goBack}>
        <Ionicons name="chevron-back-outline" size={20} />
      </WTouchable>
      <WText color={Colors.neutral850} type="semiBold24">
        {title}
      </WText>
    </WView>
  );
}

export default Header;
