import React from 'react';
import {StyleProp, ViewStyle} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '@themes';
import {WText, WView} from '@components';

interface PasswordGuideLineProps {
  isValid?: boolean;
  label?: string;
  labelStyle?: StyleProp<ViewStyle>;
}

function PasswordGuideLine({
  isValid,
  label,
  labelStyle,
}: PasswordGuideLineProps) {
  return (
    <WView alignCenter mVer={4} row>
      <Icon
        color={isValid ? Colors.success : Colors.neutral500}
        name={isValid ? 'Fill-CheckCircle' : 'Outline-XCircle'}
        size={20}
      />
      <WText
        color={isValid ? Colors.success : Colors.neutral700}
        marginLeft={4}
        style={labelStyle}
        type="regular12">
        {label}
      </WText>
    </WView>
  );
}

export default PasswordGuideLine;
