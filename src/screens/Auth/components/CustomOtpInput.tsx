/* eslint-disable react-native/no-color-literals */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  StyleProp,
  ViewStyle,
} from 'react-native';
import WText from '@components/WText';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors} from '@themes/index';
import {IS_ANDROID, SCREEN_WIDTH} from '@themes/Constants';

interface CustomOtpInputProps {
  autoFocusOnLoad?: boolean;
  onSubmit?: (code: string) => void;
  style?: StyleProp<ViewStyle>;
  onChange?: (code: string | undefined) => void;
  messageError?: string;
}

function CustomOtpInput({
  autoFocusOnLoad,
  onSubmit,
  onChange,
  style,
  messageError,
  ...props
}: CustomOtpInputProps) {
  const codeInput = useRef<TextInput>(null);
  const [code, setCode] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(autoFocusOnLoad ? 0 : -1);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const onFocusTextInput = () => {
    setFocusedIndex(code.length);
    codeInput.current?.focus();
  };

  useEffect(() => {
    if (autoFocusOnLoad) {
      onFocusTextInput();
    }
  }, []);

  const onChangeText = (inputCode: string) => {
    setCode(inputCode);
    setFocusedIndex(inputCode.length);

    if (inputCode.length === 6 && !isSubmitted) {
      setIsSubmitted(true);
      onSubmit?.(inputCode);
      Keyboard.dismiss();
      setFocusedIndex(-1);
    } else {
      setIsSubmitted(false);
    }

    onChange?.(inputCode);
  };

  const onBlurInput = () => {
    setFocusedIndex(-1);
  };

  const onFocusInput = () => {
    setFocusedIndex(code.length);
  };

  const renderInput = (position: number) => {
    const isFocused = position === focusedIndex;
    const hasValue = code[position];
    const isError = !!messageError;

    return (
      <View
        key={position}
        style={[
          styles.inputBox,
          isFocused && styles.inputBoxFocused,
          isError && styles.inputBoxError,
        ]}>
        <WText
          color={hasValue ? Colors.grey1 : Colors.neutral500}
          type="semiBold20">
          {code[position] || ''}
        </WText>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <TextInput
        {...props}
        allowFontScaling={false}
        autoFocus={autoFocusOnLoad}
        keyboardType={IS_ANDROID ? 'numeric' : 'number-pad'}
        maxLength={6}
        onBlur={onBlurInput}
        onChangeText={onChangeText}
        onFocus={onFocusInput}
        ref={codeInput}
        selectionColor="transparent"
        style={styles.hiddenInput}
        textContentType="oneTimeCode"
        value={code}
      />
      <TouchableWithoutFeedback
        hitSlop={GlobalStyle.hitSlop}
        onPress={onFocusTextInput}>
        <View style={styles.inputCollection}>
          {Array.from({length: 6}, (_, index) => renderInput(index))}
        </View>
      </TouchableWithoutFeedback>
      {messageError && (
        <WText color={Colors.primary900} marginTop={8} type="regular14">
          {messageError}
        </WText>
      )}
    </View>
  );
}

export default CustomOtpInput;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    alignSelf: 'center',
    width: '100%',
  },
  hiddenInput: {
    color: 'transparent',
    height: 0,
    opacity: 0,
    position: 'absolute',
    width: 0,
  },
  inputBox: {
    alignItems: 'center',
    backgroundColor: Colors.neutral150,
    borderRadius: 12,
    height: 60,
    justifyContent: 'center',
    width: (SCREEN_WIDTH - 80) / 6,
    ...GlobalStyle.shadowSoft,
  },
  inputBoxError: {
    borderColor: Colors.primary900,
    borderWidth: 2,
  },
  inputBoxFocused: {
    backgroundColor: Colors.white,
    ...GlobalStyle.shadow,
  },
  inputCollection: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'space-between',
    width: '100%',
  },
});
