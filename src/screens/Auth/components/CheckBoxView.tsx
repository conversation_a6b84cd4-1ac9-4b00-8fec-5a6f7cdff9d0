import React from 'react';
import {StyleProp, TouchableOpacity, ViewStyle} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '@themes';
import {WText, WView} from '@components';

interface CheckBoxViewProps {
  isCheck?: boolean;
  title?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}

function CheckBoxView({isCheck, title, onPress, style}: CheckBoxViewProps) {
  return (
    <WView alignItems="flex-start" row style={style}>
      <TouchableOpacity activeOpacity={0.8} onPress={onPress}>
        <Icon
          color={isCheck ? Colors.neutral800 : Colors.neutral600}
          name={isCheck ? 'Fill-CheckSquare' : 'Outline-Square'}
          size={24}
        />
      </TouchableOpacity>
      <WText fill lineHeight={22} marginLeft={6} type="regular14">
        {title}
      </WText>
    </WView>
  );
}

export default CheckBoxView;
