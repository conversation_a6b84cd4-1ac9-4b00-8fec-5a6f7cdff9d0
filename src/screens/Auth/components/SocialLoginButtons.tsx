import appleAuth from '@invertase/react-native-apple-authentication';
import React from 'react';
import {Image, StyleSheet, TouchableOpacity} from 'react-native';
import {useLoginSocial} from '../hooks/useLoginSocial';
import {IS_IOS, SOCIAL} from '@themes/Constants';
import {Colors, Images} from '@themes';
import {WView} from '@components';
import Divider from '@components/Divider';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

function SocialLoginButtons() {
  const {t} = useTypeSafeTranslation();
  const {onLoginSocial} = useLoginSocial();

  return (
    <WView pTop={24}>
      <Divider label={t('login.or')} />
      <WView gap={12} mTop={12} row>
        {IS_IOS && appleAuth.isSupported && (
          <TouchableOpacity
            onPress={() => onLoginSocial(SOCIAL.APPLE)}
            style={styles.socialBtn}>
            <Image resizeMode="contain" source={Images.appleLogo} />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={() => onLoginSocial(SOCIAL.GOOGLE)}
          style={styles.socialBtn}>
          <Image resizeMode="contain" source={Images.googleLogo} />
        </TouchableOpacity>
      </WView>
    </WView>
  );
}
SocialLoginButtons.propTypes = {};

const styles = StyleSheet.create({
  socialBtn: {
    alignItems: 'center',
    borderColor: Colors.neutral400,
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 12,
  },
});

export default SocialLoginButtons;
