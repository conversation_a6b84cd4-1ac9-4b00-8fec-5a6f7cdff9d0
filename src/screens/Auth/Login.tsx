import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React, {useEffect, useRef} from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import * as Yup from 'yup';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {useTranslation} from 'react-i18next';
import {ILoginResponse, OpenAPI, UpdateLocaleDto, UserEntity} from '@requests';
import {GlobalStyle} from '@themes/GlobalStyle';
import {
  useAuthServiceAuthControllerLogin,
  useUsersServiceUserControllerGetUser,
  useUsersServiceUserControllerGetUserKey,
  useUsersServiceUserControllerUpdateLocale,
} from '@queries';
import {SCREEN_WIDTH} from '@themes/Constants';
import {Colors, Images} from '@themes';
import {useGlobalState} from '@react-query/clientStateManage';
import {WText, WTextInput, WView} from '@components';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {setCalendarLanguage} from '@utils/Tools';
import MixPanelSdk from '@utils/mixPanelSdk';

const INITIAL_FORMIK = {
  email: __DEV__ ? '<EMAIL>' : '',
  password: __DEV__ ? 'Huysonthanh1#' : '',
};

function Login() {
  const {t} = useTypeSafeTranslation();
  const {i18n} = useTranslation();
  const [__, setLanguage] = useGlobalState('language');
  const navigation = useNavigation();
  const passRef = useRef<TextInput>(null);

  const [_, setUserData] = useGlobalState('userData');
  const [isSkipIntro] = useGlobalState('isSkipIntro');

  const {mutate: login, data: loginRes} = useAuthServiceAuthControllerLogin({
    onSuccess: async (data: {data: ILoginResponse}) => {
      OpenAPI.TOKEN = data?.data?.accessToken;
    },
  });
  const {mutate: updateLocale} = useUsersServiceUserControllerUpdateLocale();

  const {
    data: user,
    isFetching,
    isSuccess,
  } = useUsersServiceUserControllerGetUser(
    [useUsersServiceUserControllerGetUserKey],
    {
      enabled: !!loginRes?.data?.accessToken,
    },
  );

  const handleUpdateLanguage = async () => {
    const language = user?.data?.locale || 'en';
    OpenAPI.HEADERS = {['Accept-Language']: language};
    await i18n.changeLanguage(language);
    setLanguage(language);
    updateLocale({
      requestBody: {
        locale: language as UpdateLocaleDto.locale,
      },
    });
    setCalendarLanguage(language);
    MixPanelSdk.logEvent({
      eventName: 'user_login',
      eventValues: {
        email: user?.data?.email,
        phoneNumber: user?.data?.phoneNumber,
        userName: user?.data?.fullName,
      },
    });
    setTimeout(() => {
      navigation.reset({routes: [{name: 'Main'}]});
    }, 300);
  };

  useEffect(() => {
    if (user?.data && isSuccess) {
      setUserData({
        accessToken: loginRes?.data?.accessToken as string,
        refreshToken: loginRes?.data?.refreshToken as string,
        user: user?.data as UserEntity,
      });
      OpenAPI.TOKEN = loginRes?.data?.accessToken;
      handleUpdateLanguage();
    }
  }, [isFetching]);

  const onLogin = (data: any) => {
    login({
      requestBody: {
        ...data,
        email: data?.email?.toLocaleLowerCase(),
      },
    });
  };

  const onSubmitEditing = (field: string) => {
    switch (field) {
      case 'email':
        passRef.current?.focus();
        break;
      default:
        break;
    }
  };

  return (
    <Formik
      initialValues={INITIAL_FORMIK}
      onSubmit={values => onLogin(values)}
      validationSchema={Yup.object({
        email: Yup.string()
          .email(t('validate.email.invalid'))
          .required(t('validate.email.required')),
        password: Yup.string().required(t('validate.password.required')),
      })}>
      {({handleChange, handleSubmit, handleBlur, touched, errors, values}) => (
        <SafeAreaView style={GlobalStyle.flex1}>
          <KeyboardAwareScrollView
            contentContainerStyle={styles.flexGrow}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            style={styles.container}>
            <WView fill padding={16}>
              <WView alignCenter justifyBetween row>
                <Image source={Images.appLogo} />
                {!isSkipIntro && (
                  <TouchableWithoutFeedback
                    onPress={() => {
                      navigation.goBack();
                    }}>
                    <WView
                      borderRadius={20}
                      center
                      color={Colors.neutral150}
                      h={40}
                      w={40}>
                      <IonIcon
                        color={Colors.neutral600}
                        name="close-outline"
                        size={20}
                      />
                    </WView>
                  </TouchableWithoutFeedback>
                )}

                <TouchableWithoutFeedback
                  onPress={() =>
                    navigation.navigate('LoginV2', {isTopBarEnable: false})
                  }>
                  <WText>heheh</WText>
                </TouchableWithoutFeedback>
              </WView>

              <WView mTop={40}>
                <WView style={styles.backgroundImg}>
                  <Image
                    resizeMode="contain"
                    source={Images.loginBackground}
                    style={{
                      height: SCREEN_WIDTH / 2,
                      width: SCREEN_WIDTH / 2,
                    }}
                  />
                </WView>
                <WText marginBottom={8} type="medium34">
                  {t('login.title')}
                </WText>
              </WView>
              <WView style={styles.content}>
                <WTextInput
                  defaultValue={values?.email}
                  errorMessage={errors?.email}
                  keyboardType="email-address"
                  onBlur={handleBlur('email')}
                  onChangeText={handleChange('email')}
                  onSubmitEditing={() => onSubmitEditing('email')}
                  returnKeyType="next"
                  title={t('login.emailTitle')}
                  touched={touched?.email}
                />
                <WTextInput
                  containerStyle={styles.inputContainer}
                  defaultValue={values?.password}
                  errorMessage={errors?.password}
                  isSecure
                  onBlur={handleBlur('password')}
                  onChangeText={handleChange('password')}
                  onSubmitEditing={handleSubmit}
                  ref={passRef}
                  returnKeyType="done"
                  title={t('login.passwordTitle')}
                  touched={touched?.password}
                />
              </WView>
              <TouchableOpacity
                hitSlop={GlobalStyle.hitSlop}
                onPress={() => {
                  navigation.navigate('ForgotPassword', {
                    isTopBarEnable: false,
                  });
                }}
                style={styles.forgotBtn}>
                <WText type="medium14" underLine>
                  {t('forgotPassword.title')}
                </WText>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  handleSubmit();
                }}
                style={styles.loginBtn}>
                <WText color={Colors.white} type="semiBold14">
                  {t('button.login')}
                </WText>
              </TouchableOpacity>
            </WView>
          </KeyboardAwareScrollView>
        </SafeAreaView>
      )}
    </Formik>
  );
}

export default Login;

const styles = StyleSheet.create({
  backgroundImg: {
    position: 'absolute',
    right: -16,
    top: -40,
  },
  container: {
    flex: 1,
  },
  content: {paddingVertical: 16},
  flexGrow: {flexGrow: 1},
  forgotBtn: {
    alignSelf: 'flex-start',
    marginBottom: 24,
  },
  inputContainer: {marginTop: 16},
  loginBtn: {
    alignItems: 'center',
    backgroundColor: Colors.neutral875,
    borderRadius: 8,
    height: 46,
    justifyContent: 'center',
    minWidth: 80,
  },
});
