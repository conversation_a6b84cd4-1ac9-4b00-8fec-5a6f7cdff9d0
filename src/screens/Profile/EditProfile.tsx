import {RouteProp, useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import {Formik} from 'formik';
import moment from 'moment';
import React, {useRef, useState} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import * as Yup from 'yup';
import {
  useGlobalState,
  UserDataProps,
} from '../../react-query/clientStateManage';
import {NavHeader, WButton, WText, WTextInput, WView} from '@components';
import {TranslationKeys} from '@generated/translationKeys';
import {EditProfileRouteProps, GenderProps} from '@global';
import {
  useUsersServiceUserControllerGetUserKey,
  useUsersServiceUserControllerUpdateMe,
} from '@queries';
import {queryClient} from '@react-query/queryClient';
import {UpdateUserDto, UserEntity} from '@requests';
import {Colors} from '@themes';
import {
  DEFAULT_FORMAT_DATE,
  FORMAT_DOB,
  GENDER_DATA,
  PHONE_REGEX,
} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {formatTimeLocale} from '@utils/Tools';

interface EditProfileProps {
  route: RouteProp<EditProfileRouteProps, 'EditProfile'>;
}

function EditProfile({route}: EditProfileProps) {
  const {user, isVerifyPhone} = route?.params || {};
  const defaultGender = GENDER_DATA.find(ele => ele?.id === user?.gender);
  const {mutate: updateMe} = useUsersServiceUserControllerUpdateMe();
  const navigation = useNavigation();
  const [userData, setUserData] = useGlobalState('userData');

  const emailRef = useRef<TextInput>(null);
  const phoneNumberRef = useRef<TextInput>(null);
  const [gender, setGender] = useState<GenderProps | undefined>(defaultGender);
  const [birthday, setBirthday] = useState<Date | undefined>(
    user?.birthday ? moment(user?.birthday).toDate() : undefined,
  );

  const {t} = useTypeSafeTranslation();

  const onUpdateProfile = (values: UpdateUserDto) => {
    updateMe(
      {
        requestBody: {
          birthday: dayjs(birthday).format(DEFAULT_FORMAT_DATE),
          fullName: values?.fullName,
          gender: gender?.id,
          ...(user?.phoneNumber !== values?.phoneNumber && {
            phoneNumber: values?.phoneNumber,
          }),
        },
      },
      {
        onSuccess(values) {
          setUserData({
            ...userData,
            user: values?.data as UserEntity,
          } as UserDataProps);
          queryClient.refetchQueries({
            queryKey: [useUsersServiceUserControllerGetUserKey],
            type: 'active',
          });
          navigation.goBack();
        },
      },
    );
  };

  const INITIAL_FORMIK = {
    email: user?.email,
    fullName: user?.fullName,
    phoneNumber: user?.phoneNumber?.includes('NA') ? '' : user?.phoneNumber,
  };

  const onSubmitEditing = (field: string) => {
    switch (field) {
      case 'fullName':
        emailRef.current?.focus();
        break;
      case 'email':
        phoneNumberRef.current?.focus();
        break;
      default:
        break;
    }
  };

  return (
    <Formik
      initialValues={INITIAL_FORMIK}
      onSubmit={values => onUpdateProfile(values)}
      validationSchema={Yup.object({
        email: Yup.string()
          .email(t('validate.email.invalid'))
          .required(t('validate.email.required')),
        fullName: Yup.string().required(t('validate.fullName.required')),
        phoneNumber: Yup.string()
          .matches(PHONE_REGEX, t('validate.phoneNumber.invalid'))
          .required(t('validate.phoneNumber.required')),
      })}>
      {({handleChange, handleSubmit, handleBlur, touched, errors, values}) => (
        <SafeAreaView style={styles.container}>
          <ScrollView>
            <NavHeader title={t('profile.editPersonalInformation')} />
            <WView mTop={10} pHoz={16}>
              <WTextInput
                containerStyle={styles.mTop16}
                defaultValue={values?.fullName}
                errorMessage={errors?.fullName}
                onBlur={handleBlur('fullName')}
                onChangeText={handleChange('fullName')}
                onSubmitEditing={() => onSubmitEditing('fullName')}
                required
                returnKeyType="next"
                title={t('profile.name')}
                touched={touched?.fullName}
              />
              <WTextInput
                containerStyle={styles.mTop16}
                defaultValue={values?.email}
                editable={false}
                errorMessage={errors?.email}
                onBlur={handleBlur('email')}
                onChangeText={handleChange('email')}
                onSubmitEditing={() => onSubmitEditing('email')}
                ref={emailRef}
                required
                returnKeyType="next"
                title={t('profile.email')}
                touched={touched?.email}
              />
              <WTextInput
                containerStyle={styles.mTop16}
                defaultValue={values?.phoneNumber}
                editable={!isVerifyPhone}
                errorMessage={errors?.phoneNumber}
                onBlur={handleBlur('phoneNumber')}
                onChangeText={handleChange('phoneNumber')}
                placeholder={t('profile.phoneNumber')}
                ref={phoneNumberRef}
                required
                returnKeyType="next"
                title={t('profile.phoneNumber')}
                touched={touched?.phoneNumber}
              />
              <WView mTop={16}>
                <WText marginBottom={6} type="regular14">
                  {t('profile.gender')}
                </WText>
                <TouchableWithoutFeedback
                  onPress={() => {
                    navigation.navigate('GenderModal', {
                      defaultGender: gender,
                      onApply: (item: GenderProps) => {
                        setGender(item);
                        navigation.goBack();
                      },
                    });
                  }}>
                  <WView
                    alignCenter
                    borderColor={Colors.neutral600}
                    borderRadius={10}
                    borderWidth={1}
                    justifyBetween
                    pHoz={16}
                    pVer={12}
                    row>
                    <WText
                      color={
                        gender?.title ? Colors.neutral900 : Colors.neutral600
                      }
                      type="regular16">
                      {t(gender?.title as TranslationKeys) ||
                        t('profile.selectGender')}
                    </WText>
                    <Icon name="Outline-CaretDown" size={20} />
                  </WView>
                </TouchableWithoutFeedback>
              </WView>
              <WView mTop={16}>
                <WText marginBottom={6} type="regular14">
                  {t('profile.dob')}
                </WText>
                <TouchableWithoutFeedback
                  onPress={() => {
                    navigation.navigate('DateTimePickerModal', {
                      defaultDate: birthday,
                      onApply: date => {
                        setBirthday(date);
                        navigation.goBack();
                      },
                    });
                  }}>
                  <WView
                    alignCenter
                    borderColor={Colors.neutral600}
                    borderRadius={10}
                    borderWidth={1}
                    justifyBetween
                    pHoz={16}
                    pVer={12}
                    row>
                    <WText
                      capitalize
                      color={birthday ? Colors.neutral900 : Colors.neutral600}
                      type="regular16">
                      {birthday
                        ? formatTimeLocale(birthday, FORMAT_DOB)
                        : t('profile.selectDob')}
                    </WText>
                    <Icon name="Outline-CaretDown" size={20} />
                  </WView>
                </TouchableWithoutFeedback>
              </WView>
            </WView>
          </ScrollView>
          <WButton
            label={t('button.saveChange')}
            onPress={() => {
              handleSubmit();
            }}
            style={styles.saveBtn}
          />
        </SafeAreaView>
      )}
    </Formik>
  );
}

export default EditProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mTop16: {
    marginTop: 16,
  },
  saveBtn: {marginBottom: 20, marginHorizontal: 16, marginTop: 'auto'},
});
