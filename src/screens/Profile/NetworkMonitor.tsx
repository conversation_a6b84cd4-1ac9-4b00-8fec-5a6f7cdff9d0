import React, {useState} from 'react';
import NetworkLogger from 'react-native-network-logger';
import {WTextInput, WView} from '@components';

function NetworkMonitor() {
  const [showLogger, setShowLogger] = useState(false);
  if (!showLogger) {
    return (
      <WView padding={16}>
        <WTextInput
          onChangeText={pass => {
            if (pass === 'Taxiloyal@123') {
              setShowLogger(true);
            }
          }}
          title="Password"
        />
      </WView>
    );
  }
  return (
    <WView fill>
      <NetworkLogger sort="desc" theme={'dark'} />
    </WView>
  );
}

export default NetworkMonitor;
