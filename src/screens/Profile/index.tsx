import React, {useEffect, useMemo} from 'react';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import Ionicons from 'react-native-vector-icons/Ionicons';
import InfoView from './components/InfoView';
import AvatarUser from '@components/AvatarUser';
import {NavHeader, WText, WTouchable, WView} from '@components';
import {TranslationKeys} from '@generated/translationKeys';
import {
  useUsersServiceUserControllerGetUser,
  useUsersServiceUserControllerIsVerifyPhoneNumber,
  useUsersServiceUserControllerUpdateMe,
} from '@queries';
import {useGlobalState, UserDataProps} from '@react-query/clientStateManage';
import {UserEntity} from '@requests';
import {Colors} from '@themes';
import {FORMAT_DOB} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import useUpload from '@utils/hooks/useUpload';
import {capitalizeFirstLetter, formatTimeLocale} from '@utils/Tools';

function Profile() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  const [userData, setUserData] = useGlobalState('userData');

  const {data: user, refetch} = useUsersServiceUserControllerGetUser();
  const {mutate: updateMe} = useUsersServiceUserControllerUpdateMe();
  const {data: dataIsVerifyPhone, refetch: refetchVerifyPhone} =
    useUsersServiceUserControllerIsVerifyPhoneNumber();

  const {showPickerImageActions} = useUpload({
    onSuccess: (image: {pathFile: string | undefined}) => {
      updateMe(
        {
          requestBody: {
            avatarUrl: image?.pathFile,
          },
        },
        {
          onSuccess: () => {
            refetch()?.then(response => {
              setUserData({
                ...userData,
                user: response?.data?.data as UserEntity,
              } as UserDataProps);
            });
          },
        },
      );
    },
  });

  const handleVerify = () => {
    navigation.navigate('VerifyPhoneModal', {
      defaultPhoneNumber: user?.data?.phoneNumber ?? '',
    });
  };

  useEffect(() => {
    if (isFocused) refetchVerifyPhone();
  }, [isFocused]);

  const renderStatus = (isVerify = true) => (
    <WTouchable
      alignCenter
      borderRadius={4}
      color={isVerify ? Colors.success100 : Colors.danger200}
      disabled={isVerify}
      gap={2}
      onPress={handleVerify}
      pHoz={8}
      pVer={2}
      row>
      <WText color={isVerify ? Colors.success : Colors.error} type="medium12">
        {isVerify ? t('button.verified') : t('button.verifyNow')}
      </WText>
      {!isVerify && (
        <Ionicons
          color={Colors.error}
          name="chevron-forward-outline"
          size={10}
        />
      )}
    </WTouchable>
  );

  const userInfo = useMemo(
    () => [
      {
        content: user?.data?.fullName || '-',
        title: t('profile.name'),
      },
      {
        renderContent: () => (
          <WView alignCenter gap={16} row>
            <WText type="regular14">{user?.data?.email}</WText>
            {renderStatus()}
          </WView>
        ),
        title: t('profile.email'),
      },
      {
        renderContent: () => (
          <WView alignCenter gap={16} row>
            <WText type="regular14">
              {user?.data?.phoneNumber?.includes('NA')
                ? 'NA'
                : user?.data?.phoneNumber || '-'}
            </WText>
            {renderStatus(Boolean(dataIsVerifyPhone?.data) ?? false)}
          </WView>
        ),
        title: t('profile.phoneNumber'),
      },
      {
        content: user?.data?.gender
          ? t(`profile.${user?.data?.gender?.toLowerCase()}` as TranslationKeys)
          : '-',
        title: t('profile.gender'),
      },
      {
        content: user?.data?.birthday
          ? capitalizeFirstLetter(
              String(formatTimeLocale(user?.data?.birthday, FORMAT_DOB)),
            )
          : '-',
        title: t('profile.dob'),
      },
    ],
    [user, dataIsVerifyPhone?.data],
  );
  return (
    <SafeAreaView style={styles.container}>
      <NavHeader title={t('setting.profileDetails')} />

      <WView
        alignCenter
        borderBottomColor={Colors.neutral200}
        borderBottomWidth={1}
        mHoz={16}
        mTop={26}
        pBottom={8}
        row>
        <WText type="medium14">{t('profile.avatar')}</WText>
        <TouchableOpacity
          hitSlop={GlobalStyle.hitSlop}
          onPress={showPickerImageActions}
          style={styles.editGeneralInfo}>
          <Icon
            color={Colors.primary}
            name="Outline-PencilSimple"
            size={20}
            style={styles.editIcon}
          />

          <WText color={Colors.primary} marginLeft={4} type="medium14">
            {t('button.edit')}
          </WText>
        </TouchableOpacity>
      </WView>

      <AvatarUser
        avatarUrl={user?.data?.avatarUrl}
        fullName={user?.data?.fullName}
        size={82}
        style={styles.avatar}
      />

      <WView
        alignCenter
        borderBottomColor={Colors.neutral200}
        borderBottomWidth={1}
        mHoz={16}
        mTop={16}
        pBottom={8}
        row>
        <WText type="medium14">{t('profile.generalInformation')}</WText>
        <TouchableOpacity
          hitSlop={GlobalStyle.hitSlop}
          onPress={() => {
            navigation.navigate('EditProfile', {
              isTopBarEnable: false,
              isVerifyPhone: Boolean(dataIsVerifyPhone?.data),
              user: user?.data as UserEntity,
            });
          }}
          style={styles.editGeneralInfo}>
          <Icon color={Colors.primary} name="Outline-PencilSimple" size={20} />
          <WText color={Colors.primary} marginLeft={4} type="medium14">
            {t('button.edit')}
          </WText>
        </TouchableOpacity>
      </WView>

      {userInfo.map(item => (
        <InfoView
          content={item?.content}
          key={item?.title}
          renderContent={item?.renderContent}
          title={item?.title}
        />
      ))}
    </SafeAreaView>
  );
}

export default Profile;

const styles = StyleSheet.create({
  avatar: {alignSelf: 'center', marginVertical: 16},
  container: {
    flex: 1,
  },
  editGeneralInfo: {
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 'auto',
  },
  editIcon: {marginLeft: 'auto'},
});
