import React, {ReactNode} from 'react';
import {WText, WView} from '@components';

interface InfoViewProps {
  title?: string;
  content?: string;
  renderContent?: () => ReactNode;
}

function InfoView({title, content, renderContent}: InfoViewProps) {
  return (
    <WView justifyBetween padding={16} row>
      <WText type="medium14">{title}</WText>
      {renderContent ? (
        renderContent()
      ) : (
        <WText type="regular14">{content}</WText>
      )}
    </WView>
  );
}

export default InfoView;
