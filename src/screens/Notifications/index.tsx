/* eslint-disable react-hooks/exhaustive-deps */
import React, {useEffect} from 'react';
import {SafeAreaView, StyleSheet, useWindowDimensions} from 'react-native';
import {
  Route,
  SceneRendererProps,
  TabBar,
  TabBarProps,
  TabView,
} from 'react-native-tab-view';
import {ListNotification} from './components';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors} from '@themes';
import {WText, WView} from '@components';
import {EKeyTabNotification} from '@themes/Constants';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  useNotificationsServiceNotificationControllerTotalUnseenNotifications,
  useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
} from '@queries';

function Notifications() {
  const {t} = useTypeSafeTranslation();
  const layout = useWindowDimensions();
  const [index, setIndex] = React.useState(0);

  const [navigateTab, setNavigateTab] = useGlobalState('navigateTab');

  const {data} =
    useNotificationsServiceNotificationControllerTotalUnseenNotifications(
      [
        useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
      ],
      {
        refetchOnMount: true,
        refetchOnWindowFocus: true,
      },
    );

  const routes = React.useMemo(
    () => [
      {key: EKeyTabNotification.GENERAL, title: t('notification.general')},
      {key: EKeyTabNotification.PROMOTION, title: t('notification.promotion')},
    ],
    [t],
  );

  const resetTabIfNeeded = () => {
    if (navigateTab) {
      setTimeout(() => {
        setIndex(Number(navigateTab));
        setNavigateTab('');
      }, 300);
    }
  };

  useEffect(() => {
    resetTabIfNeeded();
  }, [navigateTab]);

  const renderScene = ({
    route,
  }: SceneRendererProps & {
    route: {key: EKeyTabNotification; title: string};
  }) => {
    switch (route.key) {
      case EKeyTabNotification.GENERAL:
        return <ListNotification type={EKeyTabNotification.GENERAL} />;
      case EKeyTabNotification.PROMOTION:
        return <ListNotification type={EKeyTabNotification.PROMOTION} />;
      default:
        break;
    }
  };

  const renderTabBar = (props: TabBarProps<Route>) => (
    <TabBar
      {...props}
      indicatorStyle={{backgroundColor: Colors.primary}}
      renderLabel={({route, focused}) => {
        const countUnseen =
          route.key === EKeyTabNotification.GENERAL
            ? data?.data?.general
            : data?.data?.promotion;
        return (
          <WView alignCenter gap={4} row>
            <WText
              color={focused ? Colors.primary : Colors.neutral600}
              type="medium14">
              {route.title}
            </WText>
            {countUnseen > 0 && (
              <WView
                borderRadius={20}
                center
                color={Colors.error}
                h={16}
                row
                w={16}>
                <WText color={Colors.white} type="medium10">
                  {countUnseen}
                </WText>
              </WView>
            )}
          </WView>
        );
      }}
      style={{backgroundColor: Colors.white}}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <WView padding={16}>
        <WText type="medium16">{t('notification.title')}</WText>
      </WView>
      <TabView
        initialLayout={{width: layout.width}}
        navigationState={{index, routes}}
        onIndexChange={setIndex}
        renderScene={renderScene}
        renderTabBar={renderTabBar}
      />
    </SafeAreaView>
  );
}

export default Notifications;

const styles = StyleSheet.create({
  container: {backgroundColor: Colors.white, flex: 1},
});
