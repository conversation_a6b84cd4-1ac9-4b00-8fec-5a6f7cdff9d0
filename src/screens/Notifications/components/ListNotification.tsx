import moment from 'moment';
import React, {useCallback, useRef, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import {Swipeable} from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/icomoon';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import Toast from 'react-native-toast-message';
import {
  NotificationEntity,
  NotificationsService,
  SeenAllNotificationsDto,
} from '@requests';
import {useLoadMoreQuery} from '@utils/hooks/useLoadMoreQuery';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  useNotificationsServiceNotificationControllerDeleteNotification,
  useNotificationsServiceNotificationControllerGetNotificationsKey,
  useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
  useNotificationsServiceNotificationControllerSeenAllNotification,
  useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
  useNotificationsServiceNotificationControllerUpdateNotification,
} from '@queries';
import {
  EKeyTabNotification,
  SCREEN_WIDTH,
  STR_TIME_FORMAT,
  TIME_NOT_24H_FORMAT,
} from '@themes/Constants';
import {Colors, Images} from '@themes';
import {WText, WTouchable, WView} from '@components';
import 'moment/locale/fr';
import {getTimeNotify, refetchQueriesByKeys} from '@utils/Tools';
import Icons from '@themes/Icons';
import {useAppState} from '@utils/hooks/useAppState';

interface Props {
  type: EKeyTabNotification;
}

function ListNotification({type}: Props) {
  const {t} = useTypeSafeTranslation();
  const {i18n} = useTranslation();
  const navigation = useNavigation();
  const [currentOpenIndex, setCurrentOpenIndex] = useState<
    number | undefined
  >();

  const {
    data: notifications,
    refetch,
    loadMore,
    loading,
    isFetching,
    isFetchingNextPage,
    isRefetching,
  } = useLoadMoreQuery({
    enabled: !!type,
    query: (offset: number) =>
      NotificationsService.notificationControllerGetNotifications(
        10,
        offset,
        type,
      ),
    queryKey: [
      useNotificationsServiceNotificationControllerGetNotificationsKey,
      type,
    ],
  });

  const {mutate: readMessage} =
    useNotificationsServiceNotificationControllerUpdateNotification();

  const {mutate: deleteMessage} =
    useNotificationsServiceNotificationControllerDeleteNotification();

  const {mutate: readAllMessage} =
    useNotificationsServiceNotificationControllerSeenAllNotification();

  const refetchAPIHasUnseen = useCallback(() => {
    refetchQueriesByKeys([
      useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
      useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
    ]);
  }, []);

  useAppState({
    appActiveHandler: () => {
      refetch();
      refetchAPIHasUnseen();
    },
  });

  const handleDeleteNotification = useCallback(
    (id: string) =>
      deleteMessage(
        {
          id,
        },
        {
          onSuccess() {
            refetch();
            refetchAPIHasUnseen();
          },
        },
      ),
    [deleteMessage, refetch, refetchAPIHasUnseen],
  );
  const renderLeftActions = (id: string) => (
    <TouchableWithoutFeedback
      onPress={() => handleDeleteNotification(id ?? '')}>
      <WView
        alignCenter
        color={Colors.danger700}
        justifyCenter
        w={SCREEN_WIDTH * 0.2}>
        <Icon color={Colors.white} name="Outline-Trash" size={24} />
      </WView>
    </TouchableWithoutFeedback>
  );

  const refsArray = useRef<Swipeable[]>([]);

  const handleSwipeableRef = (ref: Swipeable, index: number) => {
    refsArray.current[index] = ref;
  };

  const handleSwipeableWillOpen = (index: number) => () => {
    if (currentOpenIndex === undefined || currentOpenIndex === index) {
      setCurrentOpenIndex(index);
    } else {
      refsArray.current?.[currentOpenIndex]?.close();
      setCurrentOpenIndex(index);
    }
  };

  const onRefresh = () => {
    refetch();
    refetchAPIHasUnseen();
  };

  const onReadMessage = (item: NotificationEntity) => {
    if (!item?.isSeen) {
      readMessage(
        {
          id: item?.id,
          requestBody: {
            isSeen: true,
          },
        },
        {
          onSuccess() {
            refetchAPIHasUnseen();
            refetch();
          },
        },
      );
    }
    if (type === EKeyTabNotification.PROMOTION) {
      navigation.navigate('NotificationDetail', {
        headerTitle: t('notification.promotion'),
        id: item?.id ?? '',
      });
    }
  };

  const handleReadAll = useCallback(() => {
    readAllMessage(
      {
        requestBody: {
          type: type as unknown as SeenAllNotificationsDto.type,
        },
      },
      {
        onSuccess: () => {
          Toast.show({
            bottomOffset: 24,
            position: 'top',
            props: {
              title:
                type === EKeyTabNotification.GENERAL
                  ? t('notification.readGeneral')
                  : t('notification.readPromotion'),
            },
            type: 'success',
            visibilityTime: 2000,
          });
          refetch();
          refetchAPIHasUnseen();
        },
      },
    );
  }, [readAllMessage, refetch, refetchAPIHasUnseen, t, type]);

  const renderItem = ({
    item,
    index,
  }: {
    item: NotificationEntity;
    index: number;
  }) => {
    let isStick = index === 0;

    if (index !== 0) {
      isStick = !moment(notifications?.[index]?.sendTime).isSame(
        moment(notifications?.[index - 1]?.sendTime),
        'dates',
      );
    }

    return (
      <WView>
        {isStick && (
          <WView alignCenter justifyBetween row>
            <WView color="white" padding={16}>
              <WText capitalize type="regular12">
                {getTimeNotify({
                  createdAt: item?.sendTime,
                  locale: i18n.language,
                })}
              </WText>
            </WView>
            {index === 0 && (
              <WTouchable
                mRight={16}
                onPress={handleReadAll}
                pHoz={12}
                pVer={4}>
                <Image source={Icons.icRead} style={styles.icRead} />
              </WTouchable>
            )}
          </WView>
        )}
        <Swipeable
          containerStyle={{backgroundColor: Colors.danger700}}
          friction={0.5}
          onSwipeableWillOpen={handleSwipeableWillOpen(index)}
          overshootRight={false}
          ref={ref => handleSwipeableRef(ref as Swipeable, index)}
          renderRightActions={() => renderLeftActions(item?.id)}
          rightThreshold={SCREEN_WIDTH * 0.2}>
          <TouchableWithoutFeedback onPress={() => onReadMessage(item)}>
            <WView
              color={item?.isSeen ? Colors.white : Colors.neutral100}
              pHoz={16}
              pVer={12}
              row>
              <WView
                alignCenter
                borderRadius={20}
                color={item?.isSeen ? Colors.neutral150 : Colors.neutral900}
                h={38}
                justifyCenter
                w={38}>
                <Icon
                  color={item?.isSeen ? Colors.neutral700 : Colors.white}
                  name="Outline-BellSimpleRinging"
                  size={20}
                />
              </WView>
              <WView fill mLeft={8}>
                <WView alignCenter row>
                  <WText type="medium14">{item?.title}</WText>
                  <WText
                    color={Colors.neutral700}
                    marginLeft={'auto'}
                    type="regular12">
                    {item?.sendTime &&
                      moment(item?.sendTime).format(TIME_NOT_24H_FORMAT)}
                  </WText>
                </WView>
                <WText fill marginTop={4} numberOfLine={2} type="regular14">
                  {item?.sortMessage || item?.message || ''}
                </WText>
              </WView>
            </WView>
          </TouchableWithoutFeedback>
        </Swipeable>
      </WView>
    );
  };

  const renderEmpty = () => {
    if (loading || isFetching || isFetchingNextPage) {
      return (
        <WView alignCenter fill justifyCenter>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </WView>
      );
    }
    return (
      <WView alignCenter fill justifyCenter>
        <Image source={Images.emptyNotification} />
        <WText color={Colors.neutral700} type="regular14">
          {t('empty.notification')}
        </WText>
      </WView>
    );
  };

  const renderFooter = () => {
    if (
      (loading || isFetching || isFetchingNextPage) &&
      notifications?.length > 0 &&
      !isRefetching
    ) {
      return (
        <WView h={40}>
          <ActivityIndicator color={Colors.primary} size={'small'} />
        </WView>
      );
    }
    return null;
  };

  return (
    <FlatList
      ListEmptyComponent={renderEmpty}
      ListFooterComponent={renderFooter}
      contentContainerStyle={styles.flatList}
      data={notifications || []}
      keyExtractor={(item: any, index: number) =>
        item.id ? item.id.toString() : index.toString()
      }
      onEndReached={loadMore}
      refreshControl={
        <RefreshControl
          colors={[Colors.primary]}
          onRefresh={onRefresh}
          refreshing={false}
          tintColor={Colors.primary}
        />
      }
      renderItem={renderItem}
    />
  );
}

export default ListNotification;

const styles = StyleSheet.create({
  flatList: {flexGrow: 1},
  icRead: {
    height: 20,
    width: 20,
  },
});
