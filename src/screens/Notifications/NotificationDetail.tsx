/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-restricted-imports */
import {RouteProp} from '@react-navigation/native';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
import React, {useEffect} from 'react';
import {Linking, SafeAreaView, StyleSheet} from 'react-native';
import QuillEditor from 'react-native-cn-quill';
import {WText, WView} from '@components/index';
import Spin from '@components/Spin';
import {NotificationDetailRouteProps} from '@global';
import {
  useNotificationsServiceNotificationControllerGetNotification,
  useNotificationsServiceNotificationControllerUpdateNotification,
} from '@queries';
import {SCREEN_HEIGHT, SCREEN_WIDTH, STR_TIME_FORMAT} from '@themes/Constants';
import {Colors} from '@themes/index';
import 'moment/locale/fr';
import {getTimeNotify} from '@utils/Tools';

interface BookingCancelProps {
  route: RouteProp<NotificationDetailRouteProps, 'NotificationDetail'>;
}

function NotificationDetail({route}: BookingCancelProps) {
  const {i18n} = useTranslation();
  const {id} = route?.params || {};
  const editorRef = React.useRef<QuillEditor>(null);
  const {
    data: dataDetail,
    isLoading,
    isFetching,
  } = useNotificationsServiceNotificationControllerGetNotification(
    {id},
    undefined,
    {
      enabled: !!id,
    },
  );

  const {mutate: readMessage} =
    useNotificationsServiceNotificationControllerUpdateNotification();

  useEffect(() => {
    if (!dataDetail?.data?.isSeen && dataDetail?.data?.id) {
      readMessage({
        id: dataDetail?.data?.id,
        requestBody: {
          isSeen: true,
        },
      });
    }
  }, [dataDetail?.data?.isSeen]);

  const {title, sendTime, message} = dataDetail?.data || {};

  return (
    <SafeAreaView style={styles.container}>
      <Spin loading={isLoading || isFetching}>
        <WView gap={16} mHoz={16} pBottom={26} pTop={12}>
          {!!title && (
            <WText color={Colors.neutral875} type="semiBold22">
              {title}
            </WText>
          )}

          <WView justifyBetween row>
            <WText capitalize color={Colors.neutral700} type="regular14">
              {getTimeNotify({createdAt: sendTime, locale: i18n.language})}
            </WText>
            <WText color={Colors.neutral700} type="regular14">
              {sendTime && moment(sendTime).format(STR_TIME_FORMAT)}
            </WText>
          </WView>
        </WView>
        <WView fill mTop={-20}>
          <QuillEditor
            initialHtml={message || ''}
            ref={editorRef}
            style={styles.editor}
            webview={{
              onLoad: () => {
                editorRef.current?.enable(false);
              },
              onShouldStartLoadWithRequest: request => {
                const {url, isTopFrame} = request || {};

                if (isTopFrame && url?.startsWith('http')) {
                  Linking.openURL(url);
                  return false;
                }

                return true;
              },
            }}
          />
        </WView>
      </Spin>
    </SafeAreaView>
  );
}

export default NotificationDetail;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    flex: 1,
  },
  editor: {
    flex: 1,
    height: SCREEN_HEIGHT,
    padding: 0,
    width: SCREEN_WIDTH,
  },
});
