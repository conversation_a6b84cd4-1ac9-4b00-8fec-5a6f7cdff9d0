import {
  <PERSON>,
  SafeAreaView,
  Sc<PERSON>View,
  Share,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import React, {useState} from 'react';
import Icon from 'react-native-vector-icons/icomoon';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';
import {isEmpty} from 'lodash-es';
import Config from 'react-native-config';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig,
  useUsersServiceUserControllerGetReferralSummary,
  useUsersServiceUserControllerGetSuccessfulReferrals,
} from '@queries';
import {SuccessfulReferralEntity} from '@requests';
import {CollapseHeader, NavHeader, WButton, WText, WView} from '@components';
import {Colors, Images} from '@themes';
import {SCREEN_WIDTH} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import {formatNumberLocalized} from '@utils/Tools';

function ReferFriends() {
  const {t} = useTypeSafeTranslation();
  const [userData] = useGlobalState('userData');
  const [showListReferral, setShowListReferral] = useState(true);

  const code = userData?.user?.referralCode;
  const referralLink = `${Config.REFER_FRIEND}${code}`;

  const {data: pointConfig} =
    useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig();

  const {data: totalReferral} =
    useUsersServiceUserControllerGetReferralSummary();

  const {data: listReferral} =
    useUsersServiceUserControllerGetSuccessfulReferrals({
      limit: 100,
      offset: 0,
    });

  const onShareLink = async () => {
    const message = `EH! Need a ride? Check out Taxi Loyal. It’s super easy to use and you can get awesome discounts. Use my referral code: ${code} and my referral link: ${referralLink} to sign up and grab some coupons. Enjoy!`;
    try {
      const result = await Share.share({
        message,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      // return showErrorAlert({ message: error.message });
    }
  };

  const copyToClipboard = () => {
    Clipboard.setString(code as string);
    Toast.show({
      bottomOffset: 24,
      position: 'bottom',
      props: {title: t('referral.copy')},
      type: 'success',
      visibilityTime: 2000,
    });
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader title={t('setting.referFriends')} />

      <ScrollView style={styles.content}>
        <WView>
          <Image source={Images.referBanner} style={{width: SCREEN_WIDTH}} />
          <WView style={styles.totalView}>
            <WText color={Colors.primary} type="semiBold18">
              {t('referral.title')}
            </WText>
            <WText lineHeight={20} marginTop={8} type="regular14">
              {t('referral.content', {
                count: formatNumberLocalized(
                  pointConfig?.data?.referNewFriendPoint,
                ),
              })}
            </WText>
          </WView>
        </WView>

        <WView
          alignCenter
          borderColor={Colors.neutral300}
          borderRadius={8}
          borderWidth={1}
          color={Colors.neutral100}
          justifyCenter
          mHoz={16}
          mTop={16}
          padding={16}>
          <WText color={Colors.neutral600} type="regular12">
            {t('referral.myReferralCode')}
          </WText>
          <TouchableOpacity
            onPress={copyToClipboard}
            style={styles.copyCodeBtn}>
            <WText marginRight={4} type="semiBold20">
              {code}
            </WText>
            <Icon name="Outline-Copy" size={24} />
          </TouchableOpacity>
        </WView>
        <WView
          alignCenter
          borderColor={Colors.neutral300}
          borderRadius={8}
          borderWidth={1}
          color={Colors.neutral100}
          justifyCenter
          mHoz={16}
          mTop={16}
          pHoz={16}
          pVer={12}
          row>
          <WText fill numberOfLine={1} type="regular14">
            {referralLink}
          </WText>
          <WButton
            label={t('button.shareLink')}
            labelType="medium14"
            onPress={onShareLink}
            style={styles.shareBtn}
          />
        </WView>
        <WView style={styles.totalContent}>
          <WView fill>
            <WText type="semiBold20">
              {formatNumberLocalized(totalReferral?.data?.referralCount || 0)}
            </WText>
            <WText color={Colors.neutral600} type="regular12">
              {t('referral.successfulReferral')}
            </WText>
          </WView>
          <WView color={Colors.neutral200} h={'100%'} w={1} />
          <WView fill mLeft={16}>
            <WText type="semiBold20">
              {formatNumberLocalized(totalReferral?.data?.totalPoints || 0)}
            </WText>
            <WText color={Colors.neutral600} type="regular12">
              {t('referral.totalPointsEarned')}
            </WText>
          </WView>
        </WView>
        {!isEmpty(listReferral?.data?.items) && (
          <>
            <CollapseHeader
              isOpen={showListReferral}
              onPress={() => {
                setShowListReferral(!showListReferral);
              }}
              title={t('referral.successfulReferral')}
            />
            {showListReferral &&
              listReferral?.data?.items?.map(
                (ele: SuccessfulReferralEntity, index: number) => {
                  const pointLabel = `${formatNumberLocalized(ele?.point)} ${t(
                    'referral.points',
                  )}`;
                  return (
                    <WView
                      alignCenter
                      justifyBetween
                      key={`${ele?.userName} + ${index}`}
                      mHoz={16}
                      mTop={8}
                      row>
                      <WText type="regular14">{ele?.userName}</WText>
                      <WText color={Colors.neutral600} type="regular14">
                        {pointLabel}
                      </WText>
                    </WView>
                  );
                },
              )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

export default ReferFriends;

const styles = StyleSheet.create({
  content: {marginTop: 10},
  copyCodeBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4,
  },
  shareBtn: {height: 34, marginLeft: 12, paddingHorizontal: 6},
  totalContent: {
    borderColor: Colors.neutral300,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
  },
  totalView: {
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    right: SCREEN_WIDTH * 0.1,
    top: 0,
    width: SCREEN_WIDTH * 0.5,
  },
});
