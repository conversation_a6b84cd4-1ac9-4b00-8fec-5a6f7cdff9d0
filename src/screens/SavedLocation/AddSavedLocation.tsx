import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {
  Keyboard,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {AddSavedLocationRouteProps} from 'global';
import {TranslationKeys} from '@generated/translationKeys';
import {SavedLocationDto} from '@requests';
import {queryClient} from '@react-query/queryClient';
import {
  useUsersServiceUserControllerAddSavedLocation,
  useUsersServiceUserControllerDeleteDepartmentGroup,
  useUsersServiceUserControllerGetSavedLocationKey,
  useUsersServiceUserControllerUpdateSavedLocation,
} from '@queries';
import {NavHeader, WButton, WText, WTextInput, WView} from '@components';
import {PopTo} from '@navigation/utils';
import {useGlobalState} from '@react-query/clientStateManage';
import {Colors} from '@themes';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface AddSavedLocationProps {
  route: RouteProp<AddSavedLocationRouteProps, 'AddSavedLocation'>;
}
const asterisk = '*';

function AddSavedLocation({route}: AddSavedLocationProps) {
  const {isEdit, location, isFromBooking} = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [data] = useGlobalState('userData');

  const [savedLocationData, setSavedLocationData] = useState<SavedLocationDto>(
    location || data?.location,
  );
  const [locationName, setLocationName] = useState(savedLocationData?.address);

  const isDefaultSavedLocation =
    savedLocationData?.type === SavedLocationDto.type.HOME ||
    savedLocationData?.type === SavedLocationDto.type.WORK;

  const {mutate: onSaveLocation} =
    useUsersServiceUserControllerAddSavedLocation();

  const {mutate: onUpdateLocation} =
    useUsersServiceUserControllerUpdateSavedLocation();

  const {mutate: onDeleteLocation} =
    useUsersServiceUserControllerDeleteDepartmentGroup();

  const onUpdateSavedLocation = () => {
    onUpdateLocation(
      {
        id: location?.id as string,
        requestBody: {
          address: savedLocationData?.address,
          coordinates: savedLocationData?.coordinates,
          name: isDefaultSavedLocation ? savedLocationData?.name : locationName,
          type: isDefaultSavedLocation
            ? (location?.type as SavedLocationDto.type)
            : SavedLocationDto.type.NORMAL,
        },
      },
      {
        onSuccess() {
          queryClient.refetchQueries({
            queryKey: [useUsersServiceUserControllerGetSavedLocationKey],
            type: 'active',
          });
          navigation.goBack();
        },
      },
    );
  };

  const onAddSavedLocation = () => {
    onSaveLocation(
      {
        requestBody: {
          address: savedLocationData?.address,
          coordinates: savedLocationData?.coordinates,
          name: isDefaultSavedLocation ? savedLocationData?.name : locationName,
          type: isDefaultSavedLocation
            ? savedLocationData?.type
            : SavedLocationDto.type.NORMAL,
        },
      },
      {
        onSuccess: () => {
          queryClient.refetchQueries({
            queryKey: [useUsersServiceUserControllerGetSavedLocationKey],
            type: 'active',
          });
          PopTo(2);
        },
      },
    );
  };

  return (
    <SafeAreaView style={GlobalStyle.flex1}>
      <NavHeader
        title={
          isEdit
            ? t('savedLocation.title.editLocation')
            : t('savedLocation.title.add')
        }
      />
      <TouchableWithoutFeedback
        onPress={() => {
          Keyboard.dismiss();
        }}>
        <WView fill>
          <WView mHoz={16} mTop={24}>
            <WTextInput
              defaultValue={
                isDefaultSavedLocation
                  ? t(
                      `savedLocation.${savedLocationData?.type?.toLowerCase()}` as TranslationKeys,
                    )
                  : savedLocationData?.name
              }
              editable={!isDefaultSavedLocation}
              onChangeText={text => {
                setLocationName(text);
              }}
              required
              returnKeyType="done"
              title={t('profile.name')}
            />
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                if (isEdit || isFromBooking) {
                  navigation.navigate('SearchSavedLocation', {
                    isEdit: true,
                    isTopBarEnable: false,
                    location,
                    onApply: locationRes => {
                      setSavedLocationData(locationRes);
                      navigation.goBack();
                    },
                  });
                } else {
                  navigation.goBack();
                }
              }}>
              <WView mTop={16}>
                <WView row>
                  <WText marginBottom={6} type="regular14">
                    {t('signUp.addressTitle')}
                  </WText>
                  <WText
                    color={Colors.primary900}
                    marginBottom={6}
                    marginLeft={2}
                    type="regular14">
                    {asterisk}
                  </WText>
                </WView>
                <WView style={styles.addressContainer}>
                  <WText fill numberOfLine={1} type="regular16">
                    {savedLocationData?.address || t('signUp.addressTitle')}
                  </WText>
                </WView>
              </WView>
            </TouchableOpacity>
          </WView>
          <WView row style={styles.addLocationBtn}>
            {isEdit && (
              <WButton
                disabled={false}
                label={t('button.delete')}
                labelColor={Colors.primary}
                onPress={() => {
                  onDeleteLocation(
                    {
                      id: location?.id as string,
                    },
                    {
                      onSuccess: () => {
                        queryClient.refetchQueries({
                          queryKey: [
                            useUsersServiceUserControllerGetSavedLocationKey,
                          ],
                          type: 'active',
                        });
                        navigation.goBack();
                      },
                    },
                  );
                }}
                style={styles.cancelBtn}
              />
            )}
            <WButton
              disabled={false}
              label={isEdit ? t('button.save') : t('button.addLocation')}
              onPress={() => {
                if (isEdit) {
                  onUpdateSavedLocation();
                } else {
                  onAddSavedLocation();
                }
              }}
              style={styles.saveBtn}
            />
          </WView>
        </WView>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
}

export default AddSavedLocation;

const styles = StyleSheet.create({
  addLocationBtn: {
    marginBottom: 12,
    marginTop: 'auto',
  },
  addressContainer: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderColor: Colors.neutral600,
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: 'row',
    height: 50,
    paddingHorizontal: 16,
  },
  cancelBtn: {
    backgroundColor: Colors.primary100,
    borderColor: Colors.primary300,
    borderWidth: 1,
    flex: 1,
    marginHorizontal: 16,
    marginRight: 0,
  },
  mTop16: {marginTop: 16},
  saveBtn: {flex: 1, marginHorizontal: 16},
});
