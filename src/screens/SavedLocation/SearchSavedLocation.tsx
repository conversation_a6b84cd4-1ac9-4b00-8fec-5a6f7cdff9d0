import {RouteProp, useNavigation} from '@react-navigation/native';
import {debounce} from 'lodash-es';
import React, {useState} from 'react';
import {
  InputAccessoryView,
  Keyboard,
  Platform,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {useGlobalState} from '@react-query/clientStateManage';
import {formatLocationData} from '@utils/Tools';
import {SearchSavedLocationRouteProps} from '@global';
import {useUsersServiceUserControllerGetRecentLocation} from '@queries';
import {SavedLocationDto, SavedLocationEntity} from '@requests';
import {WText, WView} from '@components';
import NavHeader from '@components/NavHeader';
import SearchLocationResult from '@components/SearchLocationResult';
import {TranslationKeys} from '@generated/translationKeys';
import {Colors, Fonts} from '@themes';
import {
  CURRENT_USER_LOCATION,
  DEFAULT_SAVED_LOCATIONS,
  DISTANCE,
  GOOGLE_MAPS_API_KEY,
  INPUT_ACCESSORY_SAVED_LOCATION_ID,
  MIN_SEARCH_CHARACTER,
  SEARCH_TYPE,
} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

interface SearchSavedLocationProps {
  route: RouteProp<SearchSavedLocationRouteProps, 'SearchSavedLocation'>;
}

function SearchSavedLocation({route}: SearchSavedLocationProps) {
  const {onApply, isEdit, location} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const [useData] = useGlobalState('userData');

  const defaultGeoLocation = useData?.location || CURRENT_USER_LOCATION;

  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [googleAPILocationRes, setGoogleAPILocationRes] =
    useState<Response | null>();

  const {data: recentLocation} =
    useUsersServiceUserControllerGetRecentLocation();

  const searchGoogleAPILocation = async (textQuery: string) => {
    setLoading(true);
    const urlPlaces = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(
      textQuery,
    )}&region=CA&radius=${DISTANCE}&key=${GOOGLE_MAPS_API_KEY}`;
    try {
      const response = await fetch(urlPlaces);
      return await response.json();
    } catch (err) {
      if (err instanceof Error) {
        // showErrorAlert({messageCode: 'NETWORK_ERROR'});
      }
    } finally {
      setLoading(false);
    }
  };

  const onSearch = debounce(async (input: string) => {
    if (input?.length < MIN_SEARCH_CHARACTER) {
      return;
    }
    setSearchValue(input);
    try {
      const response = await searchGoogleAPILocation(input);
      setGoogleAPILocationRes(response?.results);
    } catch (err) {
      if (err instanceof Error) {
        // showErrorAlert({message: err?.message});
      }
    }
  }, 500);

  const onSelectLocationItem = (item?: any) => {
    if (isEdit) {
      onApply?.({
        address: item?.address?.address || item?.address?.name,
        coordinates: item?.geo,
        name: item?.address?.name,
        type: location?.type,
      } as SavedLocationEntity);
    } else {
      navigation.navigate('AddSavedLocation', {
        isTopBarEnable: false,
        location: {
          address: item?.address?.address || item?.address?.name,
          coordinates: item?.geo,
          name: item?.address?.name,
          type: location?.type,
        } as SavedLocationEntity,
      });
    }
  };

  const isDefaultSavedLocation =
    location?.type === SavedLocationDto.type.HOME ||
    location?.type === SavedLocationDto.type.WORK;

  const getLocationData = () => {
    if (searchValue) {
      return googleAPILocationRes;
    }
  };

  const searchLocationResultData = getLocationData();

  const defaultSavedLocation = DEFAULT_SAVED_LOCATIONS.find(
    ele => ele?.id === location?.type,
  );

  const onSelectLocationOnMap = () => {
    navigation.navigate('LocationMarkerSelector', {
      currentSearchType: SEARCH_TYPE.SAVED_LOCATION,
      isTopBarEnable: false,
      location: location?.id
        ? formatLocationData(location)
        : defaultGeoLocation,
      onConfirm: item => {
        navigation.goBack();
        onSelectLocationItem(item);
      },
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <NavHeader
        title={t(
          (isDefaultSavedLocation
            ? defaultSavedLocation?.title
            : 'savedLocation.title.add') as TranslationKeys,
        )}
      />
      <WView
        alignCenter
        borderRadius={8}
        color={Colors.white}
        mHoz={16}
        mTop={16}
        padding={16}
        row
        style={GlobalStyle.shadowSoft}>
        <WView
          alignCenter
          borderRadius={10}
          color={Colors.primary}
          h={20}
          justifyCenter
          mRight={16}
          w={20}>
          <Icon color={Colors.white} name="Outline-HouseLine" size={13} />
        </WView>
        <TextInput
          allowFontScaling={false}
          inputAccessoryViewID={INPUT_ACCESSORY_SAVED_LOCATION_ID}
          onChangeText={onSearch}
          placeholder={t('savedLocation.enterLocation')}
          placeholderTextColor={Colors.neutral500}
          selectionColor={Colors.primary}
          style={styles.locationInput}
        />
      </WView>
      <SearchLocationResult
        data={
          searchValue ? searchLocationResultData : recentLocation?.data?.items
        }
        hasSearchText={!!searchValue}
        loading={loading}
        onPressItem={onSelectLocationItem}
        onTouch={() => {
          Keyboard.dismiss();
        }}
        style={styles.searchResult}
      />
      <SafeAreaView style={styles.chooseOnMapContainer}>
        <WView
          color={Colors.white}
          h={3}
          style={[styles.shadowView, GlobalStyle.shadowButton]}
          w={'100%'}
        />
        <TouchableWithoutFeedback onPress={onSelectLocationOnMap}>
          <WView color={Colors.white} row style={styles.chooseOnMapWrapper}>
            <Icon
              color={Colors.neutral500}
              name="Outline-MapTrifold"
              size={20}
            />
            <WText color={Colors.neutral700} marginLeft={8} type="regular14">
              {t('locationPicker.title')}
            </WText>
          </WView>
        </TouchableWithoutFeedback>
      </SafeAreaView>
      {Platform.OS === 'ios' && (
        <InputAccessoryView nativeID={INPUT_ACCESSORY_SAVED_LOCATION_ID}>
          <TouchableWithoutFeedback onPress={onSelectLocationOnMap}>
            <SafeAreaView style={[styles.bottomView, GlobalStyle.shadow]}>
              <Icon
                color={Colors.neutral500}
                name="Outline-MapTrifold"
                size={20}
              />
              <WText color={Colors.neutral700} marginLeft={8} type="regular14">
                {t('selectDestination.chooseOnMap')}
              </WText>
            </SafeAreaView>
          </TouchableWithoutFeedback>
        </InputAccessoryView>
      )}
    </SafeAreaView>
  );
}

export default SearchSavedLocation;

const styles = StyleSheet.create({
  bottomView: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    flexDirection: 'row',
    height: 50,
    justifyContent: 'center',
    paddingVertical: 8,
  },
  chooseOnMapContainer: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    height: 50,
    justifyContent: 'center',
    paddingVertical: 8,
    zIndex: 999,
  },
  chooseOnMapWrapper: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 1,
  },
  container: {
    backgroundColor: Colors.white,
    flex: 1,
  },
  locationInput: {
    flex: 1,
    fontFamily: Fonts.type.regular,
    fontSize: 14,
    paddingVertical: 4,
  },
  searchResult: {
    backgroundColor: Colors.white,
    marginTop: 24,
  },
  shadowView: {position: 'absolute', top: 0, zIndex: 1},
});
