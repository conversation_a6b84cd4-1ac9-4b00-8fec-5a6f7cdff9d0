import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {useNavigation} from '@react-navigation/native';
import {SavedLocationDto} from '../../../api/requests';
import useTypeSafeTranslation from '../../../utils/hooks/useTypeSafeTranslation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {DEFAULT_SAVED_LOCATION_TYPE} from '@themes/Constants';
import {Colors} from '@themes';
import {WText, WView} from '@components';

interface SavedLocationItemProps {
  name?: string;
  address?: string;
  type?: string;
  onEdit?: () => void;
  onPress?: () => void;
}

function SavedLocationItem({
  name,
  address,
  type,
  onEdit,
  onPress,
}: SavedLocationItemProps) {
  const {t} = useTypeSafeTranslation();
  const isDefaultSavedLocation =
    type === SavedLocationDto.type.HOME || type === SavedLocationDto.type.WORK;

  const _onEdit = () => {
    onEdit?.();
  };
  const _onPress = () => {
    onPress?.();
  };

  return (
    <TouchableWithoutFeedback onPress={_onPress}>
      <WView alignCenter padding={16} row>
        <WView
          alignCenter
          borderRadius={8}
          color={isDefaultSavedLocation ? Colors.primary100 : Colors.neutral100}
          h={48}
          justifyCenter
          w={48}>
          {isDefaultSavedLocation ? (
            <Icon
              color={Colors.primary}
              name={
                type === SavedLocationDto.type.HOME
                  ? 'Outline-HouseLine'
                  : 'Outline-Briefcase'
              }
              size={32}
            />
          ) : (
            <Icon color={Colors.neutral850} name="Outline-MapPin" size={32} />
          )}
        </WView>
        <WView fill justifyCenter mLeft={16}>
          <WText lineHeight={24} type="medium16">
            {t(name)}
          </WText>
          {!!address && (
            <WText
              color={Colors.neutral600}
              fill
              numberOfLine={2}
              type="regular12">
              {address}
            </WText>
          )}
        </WView>
        <TouchableOpacity hitSlop={GlobalStyle.hitSlop} onPress={_onEdit}>
          <Icon
            color={Colors.neutral500}
            name={address ? 'Outline-PencilSimpleLine' : 'Outline-Plus'}
            size={20}
          />
        </TouchableOpacity>
      </WView>
    </TouchableWithoutFeedback>
  );
}

export default SavedLocationItem;

const styles = StyleSheet.create({
  editIcon: {marginLeft: 'auto'},
});
