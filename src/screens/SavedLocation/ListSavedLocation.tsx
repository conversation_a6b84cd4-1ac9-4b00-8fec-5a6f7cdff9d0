import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, ListRenderItem, SafeAreaView, StyleSheet} from 'react-native';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {isEmpty} from 'lodash-es';
import SavedLocationItem from './components/SavedLocationItem';
import {useUsersServiceUserControllerGetSavedLocation} from '@queries';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {SavedLocationDto, SavedLocationEntity} from '@requests';
import {ListSavedLocationRouteProps} from '@global';
import {TranslationKeys} from '@generated/translationKeys';
import {NavHeader, WButton} from '@components';
import {Colors} from '@themes';
import {DEFAULT_SAVED_LOCATIONS} from '@themes/Constants';

interface ListSavedLocationProps {
  route: RouteProp<ListSavedLocationRouteProps, 'ListSavedLocation'>;
}

function ListSavedLocation({route}: ListSavedLocationProps) {
  const {onApply, isFromBooking} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const keyExtractor = useCallback((_: any, index: number) => index, []);
  const navigation = useNavigation();
  const {
    data: savedLocations,
    isFetching,
    isSuccess,
  } = useUsersServiceUserControllerGetSavedLocation();

  const [savedLocationsState, setSavedLocationsState] = useState(
    DEFAULT_SAVED_LOCATIONS,
  );

  useEffect(() => {
    if (isSuccess) {
      setSavedLocationsState(
        savedLocationsState.map(item => {
          const defaultLocation = savedLocations?.data?.items?.find(
            (ele: SavedLocationEntity) => ele?.type == item?.type,
          );
          return {
            ...item,
            address: defaultLocation?.address,
            coordinates: defaultLocation?.coordinates,
            id: defaultLocation?.id,
            name: defaultLocation?.name,
          };
        }),
      );
    }
  }, [isFetching, savedLocations]);

  const formatSavedLocation = (data: SavedLocationEntity[]) => {
    if (isEmpty(data)) {
      return [];
    }
    return data?.filter(
      (ele: SavedLocationEntity) =>
        !(
          ele?.type === SavedLocationDto.type.HOME ||
          ele?.type === SavedLocationDto.type.WORK
        ),
    );
  };

  const renderItem: ListRenderItem<SavedLocationEntity> = ({item}) => {
    const isDefaultSavedLocation =
      item?.type === SavedLocationDto.type.HOME ||
      item?.type === SavedLocationDto.type.WORK;

    return (
      <SavedLocationItem
        address={item?.address}
        name={
          isDefaultSavedLocation
            ? t(`savedLocation.${item?.type?.toLowerCase()}` as TranslationKeys)
            : item?.name
        }
        onEdit={() => {
          if (!item?.address) {
            navigation.navigate('SearchSavedLocation', {
              isTopBarEnable: false,
              location: item,
            });
          } else {
            navigation.navigate('AddSavedLocation', {
              isEdit: !!item?.address,
              isFromBooking,
              isTopBarEnable: false,
              location: item,
            });
          }
        }}
        onPress={() => {
          if (!item?.address) {
            return navigation.navigate('SearchSavedLocation', {
              isTopBarEnable: false,
              location: item,
            });
          }
          if (isFromBooking) {
            onApply?.(item);
          }
        }}
        type={item?.type}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <NavHeader title={t('savedLocation.title.default')} />
      <FlatList
        contentContainerStyle={styles.flatList}
        data={
          [
            ...savedLocationsState,
            ...formatSavedLocation(savedLocations?.data?.items),
          ] as SavedLocationEntity[]
        }
        keyExtractor={keyExtractor}
        renderItem={renderItem}
      />
      <WButton
        label={t('button.addLocation')}
        onPress={() => {
          navigation.navigate('SearchSavedLocation', {
            isTopBarEnable: false,
          });
        }}
        style={styles.addBtn}
      />
    </SafeAreaView>
  );
}

export default ListSavedLocation;

const styles = StyleSheet.create({
  addBtn: {marginBottom: 12, marginHorizontal: 16},
  container: {
    backgroundColor: Colors.white,
    flex: 1,
  },
  flatList: {marginTop: 16},
});
