/* eslint-disable react-native/no-raw-text */
import {ImageSourcePropType, ScrollView} from 'react-native';
import React from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import {WButton, WImage, WText, WTouchable, WView} from '@components/index';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors, Images} from '@themes/index';
import {useGlobalState} from '@react-query/clientStateManage';
import {useUsersServiceUserControllerIsVerifyPhoneNumber} from '@queries';
import {getLocalizedString, showErrorAlert} from '@utils/Tools';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';
import {SCREEN_WIDTH} from '@themes/Constants';

function BirthDay40Year() {
  const {t} = useTypeSafeTranslation();
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation();

  const [userData] = useGlobalState('userData');
  const {currentUserBooking} = useBookingGlobalStatus();

  const {refetch: refetchVerifyPhone} =
    useUsersServiceUserControllerIsVerifyPhoneNumber(undefined, {
      enabled: false,
    });

  const onUpdatePhoneNumber = () => {
    navigation.navigate('VerifyPhoneModal', {
      defaultPhoneNumber: userData?.user?.phoneNumber,
      onApply: () => {
        navigation.navigate('SelectDestination', {
          isTopBarEnable: false,
        });
      },
    });
  };

  const onGoToBooking = async () => {
    const dataVerify = await refetchVerifyPhone();
    const isVerifyPhone = dataVerify?.data?.data;
    if (!userData?.user?.phoneNumber || !isVerifyPhone) {
      return onUpdatePhoneNumber();
    }

    if (currentUserBooking?.id) {
      return showErrorAlert({message: t('error.inAnotherBooking')});
    }

    navigation.navigate('SelectDestination', {
      isSchedule: false,
      isTopBarEnable: false,
    });
  };

  const onGotoLoyalOne = () => {
    navigation.navigate('Subscription', {
      hidePayment: true,
      isTopBarEnable: false,
    });
  };

  const onGotoReferFriends = () => {
    navigation.navigate('ReferFriends', {
      isTopBarEnable: false,
    });
  };

  return (
    <WView style={GlobalStyle.flex1}>
      <ScrollView>
        <WView fill gap={16} pBottom={50} pHoz={16} pTop={20}>
          <WText color={Colors.neutral875} type="semiBold20">
            {t('birthday40Year.title')}
          </WText>

          <WView justifyBetween row>
            <WText color={Colors.neutral700} type="regular14">
              {t('birthday40Year.date')}
            </WText>
            <WText color={Colors.neutral700} type="regular14">
              {t('birthday40Year.time')}
            </WText>
          </WView>

          <WView>
            <WText type="bold14">{t('birthday40Year.mainTitle')}</WText>
            <WText lineHeight={22} type="regular14">
              {t('birthday40Year.mainDescription')}
            </WText>
          </WView>

          <WView>
            <WText type="bold14">{t('birthday40Year.discountTitle')}</WText>
            <WText lineHeight={22} type="regular14">
              {t('birthday40Year.discountDescription')}
            </WText>
          </WView>

          <WTouchable gap={4} onPress={onGoToBooking}>
            <WImage
              h={SCREEN_WIDTH / 2.91}
              source={
                getLocalizedString(
                  Images.bookNowBirthdayEn,
                  Images.bookNowBirthdayFr,
                ) as ImageSourcePropType
              }
              w={'100%'}
            />
            <WText center color={Colors.colorLink} type="regular14">
              {t('birthday40Year.bookNowButton')}
            </WText>
          </WTouchable>

          <WView>
            <WText type="bold14">
              {t('birthday40Year.subscriptionTitle')}{' '}
              <WText type="regular14">
                {t('birthday40Year.subscriptionOriginalPrice')}
              </WText>
            </WText>
            <WText lineHeight={22} type="regular14">
              {t('birthday40Year.subscriptionDescription')}
              {'\n'}
              {t('birthday40Year.subscriptionBenefit1')}
              {'\n'}
              {t('birthday40Year.subscriptionBenefit2')}
              {'\n'}
              {t('birthday40Year.subscriptionBenefit3')}
              {'\n'}
              {t('birthday40Year.subscriptionBenefit4')}
            </WText>
          </WView>

          <WTouchable gap={4} onPress={onGotoLoyalOne}>
            <WImage
              h={SCREEN_WIDTH / 2.91}
              source={
                getLocalizedString(
                  Images.loyalOneBirthdayEn,
                  Images.loyalOneBirthdayFr,
                ) as ImageSourcePropType
              }
              w={'100%'}
            />
            <WText center color={Colors.colorLink} type="regular14">
              {t('birthday40Year.tryNowButton')}
            </WText>
          </WTouchable>

          <WView>
            <WText type="bold14">{t('birthday40Year.referralTitle')}</WText>
            <WText lineHeight={22} type="regular14">
              {t('birthday40Year.referralDescription')}
              <WText type="bold14">
                {t('birthday40Year.referralDescription2')}
              </WText>
              {t('birthday40Year.referralDescription3')}
            </WText>
          </WView>

          <WTouchable gap={4} onPress={onGotoReferFriends}>
            <WImage
              h={SCREEN_WIDTH / 2.91}
              source={
                getLocalizedString(
                  Images.referFriendBirthdayEn,
                  Images.referFriendBirthdayFr,
                ) as ImageSourcePropType
              }
              w={'100%'}
            />
            <WText center color={Colors.colorLink} type="regular14">
              {t('birthday40Year.earnPointsButton')}
            </WText>
          </WTouchable>

          <WText type="regular14">{t('birthday40Year.hurryMessage')}</WText>
        </WView>
      </ScrollView>

      <WView
        borderTopColor={Colors.neutral200}
        borderTopWidth={0.5}
        mBottom={bottom}
        pHoz={16}
        pTop={12}>
        <WButton label={t('button.bookNow')} onPress={onGoToBooking} />
      </WView>
    </WView>
  );
}

export default BirthDay40Year;
