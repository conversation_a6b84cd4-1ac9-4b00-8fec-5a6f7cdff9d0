/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import {useNavigation} from '@react-navigation/native';
import React, {useRef, useState} from 'react';
import {
  Image,
  LayoutAnimation,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {WButton, WText, WView} from '@components';
import {useGlobalState} from '@react-query/clientStateManage';
import {Colors, Images} from '@themes';
import {SCREEN_WIDTH} from '@themes/Constants';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';

const PROGRESS_BAR_WIDTH = (SCREEN_WIDTH - 72) / 4;

function Intro() {
  const navigation = useNavigation();
  const [_, setIsSkipIntro] = useGlobalState('isSkipIntro');
  const {t} = useTypeSafeTranslation();
  const scrollRef = useRef<ScrollView>(null);
  const [slideNum, setSlideNum] = useState(0);

  const INTRO_SLIDES = [
    {
      description: t('intro.introContent1'),
      id: '1',
      image: Images.intro1,
      title: t('intro.introTitle1'),
    },
    {
      description: t('intro.introContent2'),
      id: '2',
      image: Images.intro2,
      title: t('intro.introTitle2'),
    },
    {
      description: t('intro.introContent3'),
      id: '3',
      image: Images.intro3,
      title: t('intro.introTitle3'),
    },
    {
      description: t('intro.introContent4'),
      id: '4',
      image: Images.intro4,
      title: t('intro.introTitle4'),
    },
  ];

  const onScrollEnd = (e: {
    nativeEvent: {
      contentOffset: {x: number};
      layoutMeasurement: {width: number};
    };
  }) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    const {x} = e.nativeEvent.contentOffset;
    const {width} = e.nativeEvent.layoutMeasurement;
    const pageNum = Math.floor(x / width);
    setSlideNum(pageNum);
  };

  const handleNext = () => {
    setSlideNum(slideNum + 1);
    scrollRef?.current?.scrollTo({
      x: (slideNum + 1) * SCREEN_WIDTH,
    });
  };

  return (
    <SafeAreaView
      style={[
        GlobalStyle.flex1,
        {backgroundColor: Colors.white, paddingTop: 12},
      ]}>
      <WView alignCenter mBottom={35} mHoz={16} row selfCenter>
        {[0, 1, 2, 3]?.map((_, i) => (
          <WView
            borderRadius={8}
            color={i === slideNum ? Colors.primary : Colors.neutral100}
            h={4}
            key={i}
            mRight={8}
            w={PROGRESS_BAR_WIDTH}
          />
        ))}
      </WView>
      <ScrollView
        horizontal
        onMomentumScrollEnd={onScrollEnd}
        pagingEnabled
        ref={scrollRef}
        scrollEventThrottle={1}
        showsHorizontalScrollIndicator={false}
        style={GlobalStyle.flex1}>
        {INTRO_SLIDES.map((slide: any, key: number) => (
          <View key={key} style={styles.introContainer}>
            <Image
              resizeMode="contain"
              source={slide.image}
              style={{
                width: SCREEN_WIDTH - 32,
              }}
            />
            <View style={[GlobalStyle.flex1, styles.contentWrapper]}>
              <WText center type="semiBold20">
                {slide.title}
              </WText>
              <WText center lineHeight={22} marginTop={10} type="regular14">
                {slide.description}
              </WText>
            </View>
          </View>
        ))}
      </ScrollView>
      {slideNum < 3 && (
        <TouchableOpacity onPress={handleNext} style={styles.btnSkip}>
          <WText type="semiBold14">{t('button.next')}</WText>
        </TouchableOpacity>
      )}
      <WButton
        label={t('button.getStarted')}
        onPress={() => {
          setIsSkipIntro(true);
          navigation.navigate('Login');
        }}
        style={styles.introBtn}
      />
    </SafeAreaView>
  );
}

export default Intro;

const styles = StyleSheet.create({
  btnSkip: {
    alignSelf: 'center',
    marginBottom: 16,
  },
  contentWrapper: {
    justifyContent: 'center',
    marginHorizontal: 24,
  },
  introBtn: {
    marginBottom: 12,
    marginHorizontal: 16,
    width: SCREEN_WIDTH - 32,
  },
  introContainer: {
    alignItems: 'center',
    width: SCREEN_WIDTH,
  },
});
