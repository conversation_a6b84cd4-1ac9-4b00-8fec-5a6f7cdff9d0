import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigationRef as navigation} from '../navigation/utils';
import {queryClient} from '../react-query/queryClient';
import {GLOBAL_STATE_KEY_PREFIX} from '../themes/Constants';
import {EScreenDeepLink} from '@themes/Enums';

interface ProcessDeepLinkProps {
  deep_link_value: string;
  code: string;
  scr: string;
}

const DeepLinkHandler = {
  async processDeepLink({deep_link_value, code, scr}: ProcessDeepLinkProps) {
    const data = await AsyncStorage.getItem('REACT_QUERY_OFFLINE_CACHE');
    const token = JSON.parse(String(data))?.clientState?.queries.find(
      (ele: {queryHash: string | string[]}) =>
        ele?.queryHash?.includes('userData'),
    )?.state?.data?.accessToken;

    if (scr === EScreenDeepLink.ShareRoute) {
      return navigation.navigate(scr, {
        bookingId: decodeURIComponent(code) || '',
        isTopBarEnable: false,
      });
    }

    if (scr === EScreenDeepLink.Subscription) {
      if (token) {
        return navigation.navigate('Subscription', {
          hidePayment: true,
          isTopBarEnable: false,
        });
      }
      return navigation.navigate('Login');
    }

    //handle navigate
    if (code) {
      if (token) {
        return;
      }
      const stateKey = [GLOBAL_STATE_KEY_PREFIX, 'referralCode'];
      queryClient.setQueryData(stateKey, code);
      navigation.navigate('Register', {isTopBarEnable: false});
    }

    switch (deep_link_value) {
      case 'home':
        break;
      default:
        break;
    }
  },
};

export default DeepLinkHandler;
