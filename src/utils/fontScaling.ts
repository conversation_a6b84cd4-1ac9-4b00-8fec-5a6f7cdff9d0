import {Platform} from 'react-native';
import {SCREEN_WIDTH} from '@themes/Constants';

export const scaleFontSize = (size: number, factor = 0.5): number => {
  const baseWidth = 375;

  const screenRatio = SCREEN_WIDTH / baseWidth;

  const scaleFactor = 1 + (screenRatio - 1) * factor;

  const calculatedSize = Math.round(size * scaleFactor);

  const platformAdjustment = Platform.OS === 'android' ? 0.95 : 1;

  return Math.max(
    10,
    Math.min(calculatedSize * platformAdjustment, size * 1.5),
  );
};

export const scaleLineHeight = (fontSize: number, multiplier = 1.2): number =>
  Math.round(scaleFontSize(fontSize) * multiplier);

export const getResponsiveTextStyle = (
  fontSize: number,
  fontFactor = 0.5,
  lineHeightMultiplier = 1.2,
): any => {
  const scaledFontSize = scaleFontSize(fontSize, fontFactor);

  return {
    fontSize: scaledFontSize,
    lineHeight: Math.round(scaledFontSize * lineHeightMultiplier),
    ...(Platform.OS === 'android' && {
      includeFontPadding: false,
      textAlignVertical: 'center',
    }),
  };
};
