/* eslint-disable import/named */
/* eslint-disable max-lines */
import notifee, {
  EventType,
  NotificationAndroid,
  NotificationIOS,
} from '@notifee/react-native';
import dayjs from 'dayjs';
import 'dayjs/locale/en-au';
import 'dayjs/locale/vi';
import duration from 'dayjs/plugin/duration';
import i18next from 'i18next';
import {isArray, isEmpty} from 'lodash-es';
import moment from 'moment';
import {Alert, AlertButton, Linking, Platform} from 'react-native';
import {LocaleConfig} from 'react-native-calendars';
import Config from 'react-native-config';
import Toast from 'react-native-toast-message';
import CryptoJS from 'crypto-js';
import {
  CoordinatesDto,
  CouponEntity,
  LocationDto,
  MyCouponEntity,
  SavedLocationDto,
} from '../api/requests';
import {CustomToastProps} from '../components/Modal/CustomToast';
import {Images} from '../themes';
import {
  CARS,
  DEFAULT_FORMAT_DATE,
  FORMAT_DOB,
  FORMAT_NOTIFICATION_DATE,
  IS_ANDROID,
  TIME_NOT_24H_FORMAT,
} from '../themes/Constants';
import {navigate} from '@navigation/utils';
import {queryClient} from '@react-query/queryClient';
import {
  useNotificationsServiceNotificationControllerGetNotificationsKey,
  useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
  useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
} from '@queries';
import {NotificationDetailProps, ScreenOptions} from '@global';

interface IGetTimeProps {
  createdAt: Date | string;
  locale: string;
  format?: string;
}

dayjs.extend(duration);

export const onRedirectToStore = () => {
  const IOS_LINK = 'https://apps.apple.com/us/app/taxi-loyal/id6503342274';
  const ANDROID_LINK =
    'https://play.google.com/store/apps/details?id=com.taxiloyal.passenger';

  const url = Platform.select({
    android: ANDROID_LINK,
    ios: IOS_LINK,
  });
  try {
    Linking.canOpenURL(url ?? '').then(supported => {
      if (supported) {
        Linking.openURL(url ?? '');
      } else {
        showErrorAlert({
          message: i18next.t('error.invalidLink'),
        });
      }
    });
  } catch (error) {
    //TODO
  }
};

export function getLocalizedString(en: string, fr: string) {
  const {language} = i18next;
  if (language === 'en') return en;
  if (language === 'fr') return fr;
  return en;
}

export function plainToFlattenObject(object: any) {
  const result: {[key: string]: any} = {};

  function flatten(obj: any, prefix = '') {
    Object.keys(obj).forEach(key => {
      if (typeof obj[key] === 'object') {
        flatten(obj[key], `${prefix}${key}.`);
      } else {
        result[`${prefix}${key}`] = obj[key];
      }
    });
  }

  flatten(object);

  return result;
}

export const zipObjectDeep = (keys: any, values: any) =>
  keys.reduce((acc: any, key: any, idx: any) => {
    acc[key] = values[idx];
    return acc;
  }, {});

export const memoize = (fn: any) => {
  const cache = new Map();
  const cached = function (this: any, val: any) {
    return cache.has(val)
      ? cache.get(val)
      : cache.set(val, fn.call(this, val)) && cache.get(val);
  };
  cached.cache = cache;
  return cached;
};

export const finalizeLocation = location => {
  if (!location) {
    return {
      coordinate: {
        lat: null,
        lng: null,
      },
      formattedAddress: null,
    };
  }
  const finalPlace = {
    coordinate: location?.position || location?.geometry?.location,
    formattedAddress: location?.formattedAddress || location?.formatted_address,
  };
  return finalPlace;
};

export const uuidv4 = () => {
  const randomId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
  return randomId.replace(/[xy]/g, c => {
    // eslint-disable-next-line no-bitwise
    const r = (Math.random() * 16) | 0,
      // eslint-disable-next-line no-bitwise
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export const formatDuration = (seconds = 0, date?: string) => {
  const currentTime = moment(date);
  const newTime = currentTime.add(seconds, 'seconds');
  return newTime.format(TIME_NOT_24H_FORMAT);
};

export const formatTime = (secondNumber = 0) => {
  const duration = moment.duration(secondNumber, 'seconds');
  const minutes = duration.minutes();
  const hours = duration.hours();
  let estTime = minutes;
  if (minutes > 60) {
    estTime = hours;
  }

  if (hours > 0) {
    return i18next.t('booking.hour', {count: hours});
  }

  if (secondNumber < 60) {
    return i18next.t('booking.minute', {count: 1});
  }

  return i18next.t('booking.minute', {count: estTime});
};

export const formatDistance = (distance = 0): string => {
  if (isNaN(distance) || distance <= 0) {
    return '0 km';
  }

  const distanceInKm = distance / 1000;

  const formattedDistance = distanceInKm.toLocaleString('en-US', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });

  return `${formattedDistance} km`;
};

export const formatBookingTime = (time?: Date | string) => {
  if (!time) {
    return '';
  }
  return `${formatTimeLocale(
    time ?? '',
    TIME_NOT_24H_FORMAT,
  )} • ${capitalizeFirstLetter(formatTimeLocale(time ?? '', FORMAT_DOB))}`;
};

export const formatLocationData = (data: any): LocationDto => {
  const {geometry, name, formatted_address, location, address, coordinates} =
    data || {};

  const res: LocationDto = {
    address: {
      address: address || formatted_address || name,
      name,
    },
    geo: {
      latitude:
        geometry?.location?.lat || location?.[1] || coordinates?.latitude,
      longitude:
        geometry?.location?.lng || location?.[0] || coordinates?.longitude,
    },
  };
  return res;
};

export const getMinMaxPrice = data => {
  if (isEmpty(data)) {
    return;
  }
  const maxAmount = Math.max(...data.map(item => item.amount));
  const minAmount = Math.min(...data.map(item => item.amount));

  return {
    maxAmount,
    minAmount,
  };
};

export const getCarData = (vehicleType: string) => {
  if (!vehicleType || !CARS.find(ele => ele.id === vehicleType)) {
    return;
  }
  return CARS.find(ele => ele.id === vehicleType);
};

export const checkSavedLocationType = (
  array: any[],
  type: SavedLocationDto.type,
) => {
  if (!isArray(array)) {
    return false;
  }
  return array?.some(item => item.type === type);
};

const toRadians = (degrees: number) => degrees * (Math.PI / 180);

export const calculateDistance = (
  startPoint: CoordinatesDto,
  endPoint: CoordinatesDto,
) => {
  const earthRadiusKm = 6371; // Radius of the Earth in kilometers
  const lat1 = toRadians(startPoint?.latitude);
  const lat2 = toRadians(endPoint?.latitude);
  const deltaLat = toRadians(endPoint?.latitude - startPoint?.latitude);
  const deltaLng = toRadians(endPoint?.longitude - startPoint?.longitude);

  const a =
    Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(lat1) *
      Math.cos(lat2) *
      Math.sin(deltaLng / 2) *
      Math.sin(deltaLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = earthRadiusKm * c;
  return distance || 0; // Distance in kilometers
};

export const calculateTravelTime = (
  startPoint: CoordinatesDto,
  endPoint: CoordinatesDto,
) => {
  // Calculate the distance between points
  const distance = calculateDistance(startPoint, endPoint);

  // Calculate travel time based on fixed speed
  const speedKmPerHour = 20; // 40 km/h
  const travelTimeHours = distance / speedKmPerHour;
  const travelTimeSeconds = travelTimeHours * 3600; // Convert hours to seconds

  // Return travel time in seconds
  return travelTimeSeconds || 0;
};

// Function to convert degrees to radians
const toRadiansOfCar = (degrees: number) => (degrees * Math.PI) / 180;

export const calculateDirection = (
  currentLocation: CoordinatesDto,
  nextLocation: CoordinatesDto,
) => {
  if (
    currentLocation?.latitude === undefined ||
    nextLocation?.latitude === undefined ||
    nextLocation === undefined
  ) {
    return 0;
  }
  // Unpack the coordinates
  const {latitude: lat1, longitude: long1} = currentLocation;
  const {latitude: lat2, longitude: long2} = nextLocation;

  // Convert latitude and longitude values from degrees to radians
  const lat1Rad = toRadiansOfCar(lat1);
  const long1Rad = toRadiansOfCar(long1);
  const lat2Rad = toRadiansOfCar(lat2);
  const long2Rad = toRadiansOfCar(long2);

  // Calculate the differences in coordinates
  const delta_long = long2Rad - long1Rad;

  // Calculate the angle using arctan2 and convert it from radians to degrees
  const y = Math.sin(delta_long) * Math.cos(lat2Rad);
  const x =
    Math.cos(lat1Rad) * Math.sin(lat2Rad) -
    Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(delta_long);
  const angle_rad = Math.atan2(y, x);
  const angle_deg = (angle_rad * 180) / Math.PI;

  // Ensure angle is between 0 and 360 degrees
  const direction = (angle_deg + 360) % 360;

  return direction;
};

export const displayNotification = async ({
  title,
  body,
  data,
  androidConfig = {},
  iosConfig = {},
}: {
  title: string;
  body: string;
  data: any;
  androidConfig?: NotificationAndroid;
  iosConfig?: NotificationIOS;
}) => {
  Toast.show({
    bottomOffset: 24,
    position: 'top',
    props: {
      content: body,
      image: Images.simpleLogo,
      showCloseIcon: false,
      title,
    } as CustomToastProps['props'],
    swipeable: false,
    topOffset: 10,
    type: 'success',
    visibilityTime: 4000,
  });
  /*
   * try {
   *   const channelId = await notifee.createChannel({
   *     id: 'default',
   *     importance: AndroidImportance.HIGH,
   *     name: 'Default Channel',
   *     visibility: AndroidVisibility.PUBLIC,
   *   });
   *   await notifee.displayNotification({
   *     android: {
   *       channelId,
   *       importance: AndroidImportance.HIGH,
   *       largeIcon: 'ic_launcher',
   *       pressAction: {
   *         id: channelId,
   *         launchActivity: channelId,
   *       },
   *       smallIcon: 'ic_notification',
   *       visibility: AndroidVisibility.PUBLIC,
   *       ...androidConfig,
   *     },
   *     body,
   *     data,
   *     ios: {
   *       ...iosConfig,
   *     },
   *     title,
   *   });
   * } catch (error) {}
   */
};

enum NotificationType {
  PROMOTION = 'PROMOTION',
  GENERAL = 'GENERAL',
}

export async function handleNavigateOnClick() {
  notifee.onForegroundEvent(({type, detail}: any) => {
    if (
      type === EventType.PRESS &&
      detail?.notification?.data?.type === NotificationType.PROMOTION
    ) {
      navigate<ScreenOptions & NotificationDetailProps>('NotificationDetail', {
        headerTitle: i18next.t('notification.promotion'),
        id: detail?.notification?.data?.id,
      });
    }
  });
}

export const handleNavigateNotification = ({
  remoteMessage,
  isInApp,
}: {
  remoteMessage: any;
  isInApp: boolean;
}) => {
  const notifyData =
    typeof remoteMessage?.data === 'string'
      ? JSON.parse(remoteMessage?.data)
      : remoteMessage?.data;

  setTimeout(() => {
    refetchQueriesByKeys([
      useNotificationsServiceNotificationControllerGetNotificationsKey,
      useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
      useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
    ]);
  }, 500);

  if (isInApp) {
    const title = remoteMessage?.notification?.title;
    const body = remoteMessage?.notification?.body;

    displayNotification({
      body,
      data: notifyData,
      iosConfig: {
        sound: 'defaultNotification.wav',
      },
      title,
    });
  } else if (
    remoteMessage?.data?.type === NotificationType.PROMOTION &&
    IS_ANDROID
  ) {
    navigate<ScreenOptions & NotificationDetailProps>('NotificationDetail', {
      headerTitle: i18next.t('notification.promotion'),
      id: remoteMessage?.data?.id ?? '',
    });
  }
};

export const sortByLocationType = (
  locations: {geometry: {location_type: string}; address_components: any}[],
): {
  geometry: {location_type: string};
  address_components: any;
  formatted_address?: string;
}[] => {
  const locationTypePriority: {[key: string]: number} = {
    APPROXIMATE: 3,
    GEOMETRIC_CENTER: 1,
    RANGE_INTERPOLATED: 2,
    ROOFTOP: 0,
  };

  const customCompare = (
    location1: {geometry: {location_type: string}},
    location2: {geometry: {location_type: string}},
  ): number => {
    const type1 = location1.geometry.location_type;
    const type2 = location2.geometry.location_type;

    return locationTypePriority[type1] - locationTypePriority[type2];
  };

  locations.sort(customCompare);

  return locations;
};

// eslint-disable-next-line max-params
function quadraticBezierCurve(p1, p2, controlPoint, numPoints) {
  const points = [];
  const step = 1 / (numPoints - 1);

  for (let t = 0; t <= 1; t += step) {
    const x =
      (1 - t) ** 2 * p1[0] + 2 * (1 - t) * t * controlPoint[0] + t ** 2 * p2[0];
    const y =
      (1 - t) ** 2 * p1[1] + 2 * (1 - t) * t * controlPoint[1] + t ** 2 * p2[1];
    const coord = {latitude: x, longitude: y};
    points.push(coord);
  }

  return points;
}

const calculateControlPoint = (p1, p2) => {
  const d = Math.sqrt((p2[0] - p1[0]) ** 2 + (p2[1] - p1[1]) ** 2);
  const h = d * 2;
  const w = d / 2;
  const x_m = (p1[0] + p2[0]) / 2;
  const y_m = (p1[1] + p2[1]) / 2;
  const x_c =
    x_m +
    ((h * (p2[1] - p1[1])) /
      (2 * Math.sqrt((p2[0] - p1[0]) ** 2 + (p2[1] - p1[1]) ** 2))) *
      (w / d);
  const y_c =
    y_m -
    ((h * (p2[0] - p1[0])) /
      (2 * Math.sqrt((p2[0] - p1[0]) ** 2 + (p2[1] - p1[1]) ** 2))) *
      (w / d);
  const controlPoint = [x_c, y_c];
  return controlPoint;
};

export const getPoints = places => {
  const p1 = [places[0].latitude, places[0].longitude];
  const p2 = [places[1].latitude, places[1].longitude];
  const controlPoint = calculateControlPoint(p1, p2);

  return quadraticBezierCurve(p1, p2, controlPoint, 100);
};

export const getDiscountPrice = (price: number, coupon: CouponEntity) => {
  if (!coupon) {
    return price || 0;
  }
  if (coupon?.type === CouponEntity.type.PERCENTAGE) {
    const maxDiscountPrice = coupon?.maxDiscountLimit || 0;
    const discountPrice = (price * coupon.value) / 100;
    const lastDiscountPrice =
      discountPrice > maxDiscountPrice && maxDiscountPrice > 0
        ? maxDiscountPrice
        : discountPrice;

    return price - lastDiscountPrice || 0;
  }
  return price - coupon?.value || 0;
};

export const getIOSVersion = async () => {
  const appID = Config.APP_ID || '1548403212';
  if (!appID) {
    throw new Error('iosStoreURL is invalid.');
  }
  const response = await fetch(
    `https://itunes.apple.com/lookup?id=${appID}&country=vn&${new Date().getTime()}`,
    {
      headers: {
        ['cache-control']: 'no-cache',
      },
    },
  )
    .then(r => r.text())
    .then(r => JSON.parse(r));

  if (!response || !response.results || response.results.length === 0) {
    throw new Error(`appID(${appID}) is not released.`);
  }
  return response.results[0].version;
};

const getAndroidVersion = async () => {
  const bundleId = Config.BUNDLE_ID;
  const android_link = `https://play.google.com/store/apps/details?id=${bundleId}`;
  const response = await fetch(android_link).then(r => {
    if (r.status === 200) {
      return r.text();
    }
    throw new Error('androidStoreURL is invalid.');
  });
  const matches = response.match(/\[\[\[['"]((\d+\.)+\d+)['"]\]\],/);
  return matches?.[1];
};

export const getStoreVersion = async () => {
  if (Config.APP_ENV_NAME !== 'RELEASE') {
    return '9.9.9';
  }
  if (Platform.OS === 'ios') {
    return getIOSVersion();
  }
  return getAndroidVersion();
};

export const getBannerDirection = (point1, point2) => {
  if (!point1 || !point2) {
    return 'bottom-right';
  }
  if (point2.latitude > point1.latitude) {
    if (point2.longitude > point1.longitude) {
      return 'top-right';
    }
    if (point2.longitude < point1.longitude) {
      return 'top-left';
    }
    return 'top-left';
  }
  if (point2.latitude < point1.latitude) {
    if (point2.longitude > point1.longitude) {
      return 'bottom-right';
    }
    if (point2.longitude < point1.longitude) {
      return 'bottom-left';
    }
    return 'bottom-left';
  }
  /*
   * if (point2.longitude > point1.longitude) {
   *   return 'right';
   * }
   * if (point2.longitude < point1.longitude) {
   *   return 'left';
   * }
   */
  return 'bottom-left';
};

export const showErrorAlert = ({
  title,
  message,
  buttons,
}: {
  title?: string;
  message: string;
  buttons?: AlertButton[];
}) => Alert.alert((title || Config.APP_NAME) as string, message, buttons);

export const setCalendarLanguage = (language: string) => {
  LocaleConfig.locales.fr = {
    dayNames: [
      'Dimanche',
      'Lundi',
      'Mardi',
      'Mercredi',
      'Jeudi',
      'Vendredi',
      'Samedi',
    ],
    dayNamesShort: ['Dim.', 'Lun.', 'Mar.', 'Mer.', 'Jeu.', 'Ven.', 'Sam.'],
    monthNames: [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ],
    monthNamesShort: [
      'Janv.',
      'Févr.',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juil.',
      'Août',
      'Sept.',
      'Oct.',
      'Nov.',
      'Déc.',
    ],
  };
  LocaleConfig.defaultLocale = language === 'en' ? '' : 'fr';
};

export const getRoutesName = (routes: any[]) => {
  if (routes?.length > 0) {
    const listRouteName = routes?.map(item => item?.name);
    return listRouteName;
  }
  return [];
};
export const removeQueryParameters = (url?: string) => {
  if (!url) {
    return;
  }
  return url?.split('?')?.[0]?.replace('http://', 'https://');
};
export function sortByIsValid(arr: MyCouponEntity[]) {
  if (!arr || arr?.length === 0) {
    return [];
  }
  return arr?.sort((a: MyCouponEntity, b: MyCouponEntity) => {
    if (a.isValid === b.isValid) {
      return 0;
    }
    return a.isValid ? -1 : 1;
  });
}

export const getCouponValueTitle = (couponData: CouponEntity) => {
  if (!couponData) {
    return '';
  }
  if (couponData?.type === CouponEntity.type.PERCENTAGE) {
    if (couponData?.maxDiscountLimit) {
      return `${i18next.t('coupon.discountPercent', {
        count: couponData?.value,
      })} ${i18next.t('coupon.upTo', {
        count: formatNumberPrice(
          couponData?.maxDiscountLimit,
        ) as unknown as number,
      })} `;
    }
    return i18next.t('coupon.discountPercent', {count: couponData?.value});
  }
  return i18next.t('coupon.discountAmount', {
    count: formatNumberPrice(couponData?.value) as unknown as number,
  });
};

export const getTimeNotify = ({
  createdAt,
  locale = 'en',
  format = FORMAT_NOTIFICATION_DATE,
}: IGetTimeProps) => {
  if (!createdAt) {
    return;
  }
  return moment(createdAt).locale(locale).format(format);
};

export const checkMoreThanOrEqualNumbersDaysFromNow = ({
  endDate,
  number = 7,
}: {
  endDate: Date | string;
  number?: number;
}) => {
  const result = dayjs().diff(dayjs(endDate), 'day') >= number;
  return result;
};

let isAlertVisible = false;
export const showAlertOnce = ({
  message,
  title,
}: {
  title?: string;
  message: string;
}) => {
  if (!isAlertVisible) {
    isAlertVisible = true;
    showErrorAlert({
      buttons: [
        {
          onPress: () => {
            isAlertVisible = false;
          },
          text: i18next.t('button.ok'),
        },
      ],
      message,
      title,
    });
  }
};

export const capitalizeFirstLetter = (value: string) => {
  if (!value) return;
  return value?.charAt(0).toUpperCase() + value?.slice(1);
};

export const formatTimeLocale = (
  time: string | Date,
  format = DEFAULT_FORMAT_DATE,
): string => {
  if (!time) return '';
  return moment(time)
    .locale(i18next.language ?? 'en')
    .format(format);
};

export const checkSvgInUrlStrict = (url: string): boolean => {
  if (!url) return false;
  const svgRegex = /\.svg(?:\?.*)?$/i;
  return svgRegex.test(url);
};

export const generateEncryptedId = (data: string) => {
  const secretKey = Config?.CRYPTO_SECRET_KEY ?? '';
  return CryptoJS.AES.encrypt(data, secretKey).toString();
};

export const decryptEncryptedId = (encryptedData: string) => {
  const secretKey = Config?.CRYPTO_SECRET_KEY ?? '';
  const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
  return bytes.toString(CryptoJS.enc.Utf8);
};

export function formatNumberLocalized(input = 0): string {
  const numberInput = Number(input);

  if (isNaN(numberInput) || numberInput === 0) {
    return '0';
  }

  return numberInput.toLocaleString('en-US', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });
}

export const formatNumberPrice = (input = 0): string => {
  const numberInput = Number(input) / 100;

  if (isNaN(numberInput) || numberInput <= 0) {
    return '$0';
  }

  const formattedNumber = numberInput.toLocaleString('en-US', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });

  return `$${formattedNumber}`;
};

export const formatTimeMinuteSecond = (time: number): string => {
  if (!time) return '';
  const minutes = Math.floor(time / 60);
  const seconds = time % 60;
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(
    2,
    '0',
  )}`;
};

export const numericText = (text: string) => {
  if (!text) return;
  const numericText = text?.replace(/[^0-9]/g, '');
  return numericText;
};

export const refetchQueriesByKeys = (
  keys: string[],
  type: 'all' | 'active' | 'inactive' = 'all',
) => {
  keys.forEach(key => {
    queryClient.refetchQueries({queryKey: [key], type});
  });
};
