/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-raw-text */
import i18next from 'i18next';
import {Al<PERSON>, Linking} from 'react-native';
import React from 'react';
import {Colors} from '@themes/index';
import {WText, WView} from '@components/index';
import {MAIL_SUPPORT} from '@themes/Constants';

interface ShowErrorAlertProps {
  isInternetIgnore?: boolean;
  messageCode?: string;
  title?: string;
  message?: string;
  hasOption?: boolean;
  pressTitle?: string;
  isCancelable?: boolean;
  onPress?: () => void;
  onCancel?: () => void;
}

export const showErrorAlert = ({
  isInternetIgnore,
  messageCode,
  title = 'Taxi Loyal' || i18next.t('error.title'),
  message = i18next.t('error.description'),
  hasOption = false,
  isCancelable = true,
  pressTitle = i18next.t('button.OK'),
  onPress = () => {
    //TODO
  },
  onCancel = () => {
    //TODO
  },
}: ShowErrorAlertProps) => {
  setTimeout(() => {
    if (messageCode && messageCode === 'NETWORK_ERROR' && !isInternetIgnore) {
      /*
       * return Navigation.showOverlay({
       *   component: {
       *     name: 'ConfirmAlert',
       *     options: {
       *       overlay: {
       *         interceptTouchOutside: true,
       *       },
       *       layout: {
       *         componentBackgroundColor: 'transparent',
       *       },
       *     },
       *     passProps: {
       *       title: i18next.t('error.title'),
       *       description: i18next.t('error.networkError'),
       *       submitButtonTitle: i18next.t('button.retry'),
       *       onSubmit: () => {
       *         onCancel && onCancel();
       *       },
       *     },
       *   },
       * });
       */
    }
    const alertContent = hasOption
      ? [
          {onPress: onCancel, text: i18next.t('button.cancel')},
          {onPress, text: pressTitle},
        ]
      : [{onPress: onCancel, text: i18next.t('button.OK')}];

    return Alert.alert(
      title,
      message?.split('GraphQL error:')?.pop() || message,
      alertContent,
      {
        cancelable: isCancelable,
      },
    );
  }, 200);
};

export const showMessage = ({
  title = 'Smartos Booking' || i18next.t('error.title'),
  message = i18next.t('error.description'),
  hasOption = false,
  pressTitle = i18next.t('button.OK'),
  onPress = () => {
    //TODO
  },
  onCancel = () => {
    //TODO
  },
}: ShowErrorAlertProps) => {
  setTimeout(() => {
    const alertContent = hasOption
      ? [
          {onPress: onCancel, text: i18next.t('button.cancel')},
          {onPress, text: pressTitle},
        ]
      : [{onPress: onCancel, text: i18next.t('button.OK')}];

    return Alert.alert(title, message, alertContent, {
      cancelable: true,
    });
  }, 200);
};

const handleMailPress = async () => {
  const email = `mailto:${MAIL_SUPPORT}`;
  const canOpen = await Linking.canOpenURL(email);
  if (canOpen) {
    Linking.openURL(email);
  } else {
    showErrorAlert({
      message: `${i18next.t('deactivated.cannotOpenURL')}`,
    });
  }
};
export const showContentDeactivated = (
  <WView>
    <WText
      color={Colors.neutral875}
      marginTop={16}
      textAlign="center"
      type="medium14">
      {`${i18next.t('deactivated.description')}`}
      <WText
        color={Colors.primary}
        onPress={handleMailPress}
        style={{textDecorationLine: 'underline'}}
        type="medium14">
        {` ${MAIL_SUPPORT}`}
      </WText>
    </WText>
  </WView>
);
