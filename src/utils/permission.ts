/* eslint-disable react-native/split-platform-components */
import {PermissionsAndroid, Platform} from 'react-native';
import i18next from 'i18next';

export const getDownloadPermissionAndroid = async () => {
  try {
    if (Number(Platform.Version) >= 33) {
      return true;
    }
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      {
        buttonNegative: i18next.t('button.cancel'),
        buttonPositive: i18next.t('button.ok'),
        message: i18next.t('permissions.saveFile'),
        title: i18next.t('permissions.fileDownload'),
      },
    );
    if (granted === PermissionsAndroid.RESULTS.GRANTED) return true;
  } catch (err) {
    //TODO
  }
};
