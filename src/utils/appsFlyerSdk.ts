import {Platform} from 'react-native';
import appsFlyer from 'react-native-appsflyer';

import Config from 'react-native-config';
import {isReleaseMode} from '../themes/Constants';
import DeepLinkHandler from './deeplinkHandler';

const guestUserId = 'guest';

const handleDeepLinkResponse = (response: any) => {
  const responseData = response?.data;
  DeepLinkHandler.processDeepLink(responseData);
};

appsFlyer.setOneLinkCustomDomains(
  [Config.APPSFLYER_DOMAIN || ''],
  () => {
    console.log('success custom domain');
  },
  error => {
    console.log('fail custom domain', error);
  },
);

/*
 * https://github.com/AppsFlyerSDK/appsflyer-react-native-plugin/blob/master/Docs/Guides.md#deferred-deep-linking
 * These MUST be registered to complete initialization
 */
/*
 * appsFlyer.onAppOpenAttribution(res => {
 *   handleDeepLinkResponse(res);
 * });
 */

/*
 * appsFlyer.onDeepLink(res => {
 *   handleDeepLinkResponse(res);
 * });
 */

appsFlyer.initSdk({
  appId: Config.APPSFLYER_APP_ID,
  devKey: Config.APPSFLYER_DEV_KEY || '',
  isDebug: false,
  onDeepLinkListener: true,
  onInstallConversionDataListener: true,
  timeToWaitForATTUserAuthorization: 10,
});

const appsFlyerEventPrefix = 'af_';

const AppsFlyer = {
  clearUserInfo() {
    appsFlyer.setCustomerUserId(guestUserId);
  },

  init() {
    appsFlyer.setCustomerUserId(guestUserId);
  },

  async logEvent({eventName = '', eventValues = {}}) {
    if (!isReleaseMode) {
      return;
    }

    const params = {...eventValues, source: Platform.OS};

    /*
     * if (store.getState().auth.user?.id) {
     *   params = {...params, user_id: store.getState().auth.user?.id};
     * }
     */
    await appsFlyer.logEvent(appsFlyerEventPrefix + eventName, params);
  },

  updateUserInfo({userId}: {userId: string | number}) {
    if (userId) {
      appsFlyer.setCustomerUserId(
        typeof userId === 'string' ? userId : userId.toString(),
      );
    }
  },
};

export default AppsFlyer;
