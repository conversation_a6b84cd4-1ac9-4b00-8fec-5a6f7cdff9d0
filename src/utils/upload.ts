import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import ImageCropPicker from 'react-native-image-crop-picker';
import i18next from 'i18next';
import {ActionSheetIOS, Platform} from 'react-native';
import DialogAndroid from 'react-native-dialogs';
import ImageResizer from '@bam.tech/react-native-image-resizer';
import {requestAllowCamera, requestAllowPhoto} from './permission-helper';
import {showErrorAlert} from './Tools';

const showMenuOptions = ({
  data,
  onSelectItem,
  labelKey = 'title',
  idKey = 'id',
}) => {
  const {title, items, cancelLabel} = data;
  if (Platform.OS === 'ios') {
    const cancelLabelIOS = cancelLabel || i18next.t('button.cancel');

    const labels = items?.map((e: {[x: string]: any}) => e[labelKey]);
    ActionSheetIOS.showActionSheetWithOptions(
      {
        cancelButtonIndex: items.length,
        destructiveButtonIndex: -1,
        options: [...labels, cancelLabelIOS],
        title,
      },
      index => {
        if (index === items.length) {
          return;
        }
        onSelectItem(index);
      },
    );
  } else {
    DialogAndroid.showPicker(title, null, {
      idKey,
      items,
      labelKey,
      negativeText: i18next.t('button.cancel'),
      positiveText: i18next.t('button.ok'),
      type: DialogAndroid.listPlain,
    }).then((result: any) => {
      const {action, selectedItem} = result;
      if (action === 'actionSelect') {
        const index = items.findIndex(
          (e: {[x: string]: string}) => e[idKey] === selectedItem[idKey],
        );
        if (index >= 0) {
          onSelectItem(index);
        }
      }
    });
  }
};

export interface ErrorProps {
  code: string;
  message: string;
}
interface UploadProps {
  onSuccess: (res: FileProps) => void;
  maxSizeMB?: number;
}

export interface FileProps {
  name?: string;
  filename?: string;
  path: string;
  mime: string;
  uri?: string;
  type?: string;
}

const WIDTH_CAMERA = 800;
const HEIGHT_CAMERA = 800;
const FILE_MB = 1000000.0;
const IMAGE_MB = 1024;
const TYPE_FILE = 'file';
const DEFAULT_MB = 3;

const checkMaximumSize = (
  file: {size: number},
  maxSize: number,
  type?: string,
) => {
  const fileSize =
    type === TYPE_FILE
      ? file?.size / FILE_MB
      : file?.size / (IMAGE_MB * IMAGE_MB);
  if (fileSize < maxSize) {
    return true;
  }
  /*
   * showErrorAlert({
   *   message: i18next.t(
   *     type === TYPE_FILE ? 'info.fileMaximumSize' : 'info.imageMaximumSize',
   *     {maxSize},
   *   ),
   * });
   */
};

export const chooseFile = async ({
  selectFile,
  maxSizeMB = DEFAULT_MB,
}: {
  selectFile: (file: FileProps) => void;
  maxSizeMB?: number;
}) => {
  try {
    const file: DocumentPickerResponse = await DocumentPicker.pick({
      type: [DocumentPicker.types.allFiles],
    });
    if (file && checkMaximumSize(file, maxSizeMB, TYPE_FILE)) {
      selectFile({
        ...file,
        filename: file?.name,
        mime: file?.type,
        path: file?.uri,
      });
    }
  } catch (err) {
    if (DocumentPicker.isCancel(err as ErrorProps)) {
      showErrorAlert({message: err?.message});
    } else {
      throw err;
    }
  }
};

export const choosePhoto = ({
  onSuccess,
  maxSizeMB = DEFAULT_MB,
}: UploadProps) => {
  const PICKER_OPTIONS = [
    {id: 1, title: i18next.t('picker.openCamera')},
    {id: 2, title: i18next.t('picker.openImages')},
  ];
  return showMenuOptions({
    data: {
      items: PICKER_OPTIONS,
      title: '',
    },
    onSelectItem: async (index: number) => {
      switch (index) {
        case 0:
          try {
            const image = await ImageCropPicker.openCamera({
              compressImageMaxHeight: HEIGHT_CAMERA,
              compressImageMaxWidth: WIDTH_CAMERA,
            });
            if (checkMaximumSize(image, maxSizeMB)) {
              const resizedImage = await ImageResizer.createResizedImage(
                image.path,
                WIDTH_CAMERA,
                HEIGHT_CAMERA,
                'JPEG',
                50,
              );

              onSuccess({
                ...image,
                path: resizedImage.uri,
                uri: resizedImage.uri,
              });
            }
          } catch (error) {
            if ((error as ErrorProps).code !== 'E_PICKER_CANCELLED') {
              await requestAllowCamera();
            }
          }
          break;
        case 1:
          try {
            const image = await ImageCropPicker.openPicker({
              compressImageMaxHeight: HEIGHT_CAMERA,
              compressImageMaxWidth: WIDTH_CAMERA,
              mediaType: 'photo',
            });
            if (checkMaximumSize(image, maxSizeMB)) {
              const resizedImage = await ImageResizer.createResizedImage(
                image.path,
                WIDTH_CAMERA,
                HEIGHT_CAMERA,
                'JPEG',
                50,
              );

              onSuccess({
                ...image,
                path: resizedImage.uri,
                uri: resizedImage.uri,
              });
            }
          } catch (error) {
            if ((error as ErrorProps).code !== 'E_PICKER_CANCELLED') {
              await requestAllowPhoto();
            }
          }
          break;
        default:
          break;
      }
    },
  });
};

export const uploadFileToS3 = async ({
  type,
  uri,
  presignedUrlS3,
  onSuccessUpload,
}: {
  type: string;
  uri: string;
  presignedUrlS3: any;
  onSuccessUpload: (value: any) => void;
}) => {
  const xhr = new XMLHttpRequest();
  xhr.open('PUT', presignedUrlS3?.uploadUrl);
  xhr.setRequestHeader('Content-Type', type);
  xhr.send({uri});
  xhr.onreadystatechange = () => {
    if (xhr.readyState === 4 && xhr.status === 200) {
      onSuccessUpload?.(presignedUrlS3);
    } else if (
      xhr.readyState === 4 &&
      (xhr.status === 400 || xhr.status === 403)
    ) {
      /*
       * showErrorAlert({
       *   message: 'xhr',
       *   messageCode: `${i18next.t('error')}!`,
       * });
       */
    }
  };
};
