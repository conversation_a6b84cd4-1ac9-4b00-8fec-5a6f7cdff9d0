import AsyncStorage from '@react-native-async-storage/async-storage';
import {STORAGE_KEY} from 'src/constants/storageKeys';

export const hasBookingTipBeenShown = async (
  bookingCode: number | undefined,
): Promise<boolean> => {
  if (!bookingCode) return false;

  try {
    const shownBookingCodes = await AsyncStorage.getItem(
      STORAGE_KEY.SHOWN_BOOKING_CODES,
    );
    if (!shownBookingCodes) return false;

    const parsedCodes = JSON.parse(shownBookingCodes) as number[];
    return parsedCodes.includes(bookingCode);
  } catch (error) {
    return false;
  }
};

export const markBookingTipAsShown = async (
  bookingCode: number | undefined,
): Promise<void> => {
  if (!bookingCode) return;

  try {
    const shownBookingCodes = await AsyncStorage.getItem(
      STORAGE_KEY.SHOWN_BOOKING_CODES,
    );
    let parsedCodes: number[] = [];

    if (shownBookingCodes) {
      parsedCodes = JSON.parse(shownBookingCodes) as number[];
    }

    if (!parsedCodes.includes(bookingCode)) {
      parsedCodes.push(bookingCode);
      await AsyncStorage.setItem(
        STORAGE_KEY.SHOWN_BOOKING_CODES,
        JSON.stringify(parsedCodes),
      );
    }
  } catch (error) {
    //error
  }
};
