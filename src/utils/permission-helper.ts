import {Platform, Linking, Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  request,
  check,
  PERMISSIONS,
  RESULTS,
  Permission,
} from 'react-native-permissions';
import i18next from 'i18next';

const CAMERA_FIRSTIME_REQUESTED = 'CAMERA_FIRSTIME_REQUESTED';
const PHOTO_FIRSTIME_REQUESTED = 'PHOTO_FIRSTIME_REQUESTED';

export async function storeData(key: string, value: string) {
  try {
    if (value) {
      await AsyncStorage.setItem(key, value);
    }
  } catch (e) {
    // saving error
  }
}

export async function getData(key: string) {
  try {
    const value = await AsyncStorage.getItem(key);
    if (value !== null) {
      return value;
    }
    return null;
  } catch (e) {
    return null;
  }
}

export async function requestAllowLocation() {
  try {
    await request(
      Platform.select({
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      }) as Permission,
    );
  } catch (error) {
    Alert.alert('Error', error.message);
  }
}

export async function checkAccessLocation() {
  return await check(
    Platform.select({
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    }) as Permission,
  );
}

export const requestAllowCameraRoll = async () =>
  await request(
    Platform.select({
      android:
        Number(Platform.Version) >= 33
          ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
          : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
    }) as Permission,
  );

export async function requestAllowPhoto() {
  try {
    const result = await requestAllowCameraRoll();
    if (
      result === RESULTS.UNAVAILABLE ||
      result === RESULTS.BLOCKED ||
      result === RESULTS.DENIED
    ) {
      const isFirstTimeRequested = await getData(PHOTO_FIRSTIME_REQUESTED);

      isFirstTimeRequested
        ? Alert.alert('Warning', i18next.t('permissions.requestPhoto'), [
            {
              onPress: () => Linking.openSettings(),
              text: i18next.t('button.confirm'),
            },
            {
              text: i18next.t('button.cancel'),
            },
          ])
        : request(
            Platform.select({
              android: PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
              ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
            }) as Permission,
          );

      if (!isFirstTimeRequested) {
        await storeData(PHOTO_FIRSTIME_REQUESTED, 'true');
      }
      return result;
    }

    if (result !== RESULTS.GRANTED) {
      Alert.alert('Warning', i18next.t('permissions.requestPhoto'), [
        {
          onPress: () => Linking.openSettings(),
          text: i18next.t('button.confirm'),
        },
        {
          text: i18next.t('button.cancel'),
        },
      ]);
    }
    return result;
  } catch (error) {
    Alert.alert('Error', error.message);
  }
}

export async function requestAllowCamera() {
  try {
    const result = await check(
      Platform.select({
        android: PERMISSIONS.ANDROID.CAMERA,
        ios: PERMISSIONS.IOS.CAMERA,
      }) as Permission,
    );
    if (result === RESULTS.UNAVAILABLE) {
      const isFirstTimeRequested = await getData(CAMERA_FIRSTIME_REQUESTED);
      isFirstTimeRequested
        ? Alert.alert('Warning', i18next.t('permissions.requestPhoto'), [
            {
              onPress: () => Linking.openSettings(),
              text: i18next.t('button.confirm'),
            },
            {
              text: i18next.t('button.cancel'),
            },
          ])
        : request(
            Platform.select({
              android: PERMISSIONS.ANDROID.CAMERA,
              ios: PERMISSIONS.IOS.CAMERA,
            }) as Permission,
          );

      if (!isFirstTimeRequested) {
        await storeData(CAMERA_FIRSTIME_REQUESTED, 'true');
      }

      return result;
    }
    if (result !== RESULTS.GRANTED) {
      Alert.alert('Warning', i18next.t('permissions.requestPhoto'), [
        {
          onPress: () => Linking.openSettings(),
          text: i18next.t('button.confirm'),
        },
        {
          text: i18next.t('button.cancel'),
        },
      ]);
    }
    return result;
  } catch (error) {
    Alert.alert('Error', error.message);
  }
}
