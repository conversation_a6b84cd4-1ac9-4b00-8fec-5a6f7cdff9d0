import i18next from 'i18next';
import moment from 'moment';
import {SubscriptionPlanEntity} from '@requests';
import {formatNumberPrice} from '@utils/Tools';
import {TZ_OTTAWA} from '@themes/Constants';

/**
 * Calculate monthly price after discount
 * @param plan The subscription plan object
 * @param discountType Type of discount (PERCENTAGE or AMOUNT)
 * @param discountValue Value of discount
 * @returns Formatted monthly price as string
 */
export function calculateMonthlyPrice(
  plan: any,
  discountType: string,
  discountValue: number,
): string {
  let finalPrice = plan?.amount / 100;

  if (discountType === 'PERCENTAGE') {
    finalPrice = finalPrice - (finalPrice * discountValue) / 100;
  } else {
    finalPrice = finalPrice - discountValue / 100;
  }

  const monthlyPrice = finalPrice / 12;

  return monthlyPrice.toFixed(2);
}

export function getMinAmount(
  plans: SubscriptionPlanEntity[],
): number | undefined {
  if (!plans?.length) return undefined;

  return Math.min(...plans.map(plan => plan.amount));
}

interface DiscountInfo {
  validTo: string;
  type: 'PERCENTAGE' | 'AMOUNT';
  value: number;
  discountText: string;
  planType: string;
  isYearly: boolean;
  labelDiscount: string;
}

interface PlanPriceInfo {
  originalPrice: string;
  discountedPrice?: string;
  discountLabel?: string;
  equivalentPrice?: string;
  hasDiscount: boolean;
  finalAmount: number;
}

/**
 * Process discount information and check if it's valid
 * @param plan The subscription plan object
 * @returns Processed discount information or null if discount is not valid
 */
export function processDiscount(plan: any): DiscountInfo | null {
  if (!plan.discount || plan?.discount?.status !== 'ACTIVE') return null;

  const {validTo, type, value, validFrom} = plan?.discount || {};

  const isYearly =
    plan.recurringType === SubscriptionPlanEntity.recurringType.YEARLY_1;
  const planType = isYearly
    ? i18next.t('subscription.yearly')
    : i18next.t('subscription.monthly');

  const now = moment().tz(TZ_OTTAWA);
  const endDate = moment.utc(validTo).tz(TZ_OTTAWA, true);
  const startDate = moment.utc(validFrom).tz(TZ_OTTAWA, true);

  const diff = startDate.diff(now);

  if (diff > 0 || endDate.isBefore(now) || endDate.isSame(now)) return null;

  const discountText = type === 'PERCENTAGE' ? `${value}%` : `$${value / 100}`;

  const labelDiscount =
    type === 'PERCENTAGE'
      ? i18next.t('subscription.saveValue', {value: discountText})
      : i18next.t('subscription.getDiscount', {value: discountText});

  return {
    discountText,
    isYearly,
    labelDiscount,
    planType,
    type,
    validTo,
    value,
  };
}

export function calculatePlanPrices(
  plan: SubscriptionPlanEntity,
  t: any,
  isDisabledDiscount?: boolean,
): PlanPriceInfo {
  const discountInfo = processDiscount(plan);

  const originalPrice = formatNumberPrice(plan?.amount);
  let discountedPrice = originalPrice;
  let discountLabel = '';
  let equivalentPrice = '';

  if (!plan?.discount || !discountInfo || isDisabledDiscount) {
    return {
      finalAmount: plan?.amount,
      hasDiscount: false,
      originalPrice,
    };
  }

  const {type, value, isYearly} = discountInfo;
  let finalAmount = 0;

  if (type === 'PERCENTAGE') {
    const discountAmount = (plan.amount * value) / 100;
    discountedPrice = formatNumberPrice(plan.amount - discountAmount);
    discountLabel = `${value}% ${t('subscription.off')}`;
    finalAmount = plan.amount - discountAmount;
  } else {
    discountedPrice = formatNumberPrice(plan.amount - value);
    finalAmount = plan.amount - value;
    discountLabel = `$${value / 100} ${t('subscription.off')}`;
  }

  if (isYearly) {
    const monthlyPrice = calculateMonthlyPrice(plan, type, value);
    equivalentPrice = `${t(
      'subscription.equivalentToOnly',
    )} $${monthlyPrice}/ ${t('subscription.month')}`;
  }

  return {
    discountLabel,
    discountedPrice,
    equivalentPrice,
    finalAmount,
    hasDiscount: true,
    originalPrice,
  };
}

export const getPlanName = (
  recurringType?: SubscriptionPlanEntity.recurringType,
  isLowerCase?: boolean,
) => {
  const isYearly =
    recurringType === SubscriptionPlanEntity.recurringType.YEARLY_1;
  const planType = isYearly
    ? i18next.t('subscription.yearly')
    : i18next.t('subscription.monthly');

  return isLowerCase ? planType.toLowerCase() : planType ?? '';
};
