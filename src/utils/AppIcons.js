import Ionicons from 'react-native-vector-icons/Ionicons';
import TalashIcon from 'react-native-vector-icons/icomoon';
import {Colors} from '../themes';

const replaceSuffixPattern = /--(active|big|small|very-big)/g;
const icons = {
  'ic-setting-2': [24, Colors.primary],
  'ic-bold-setting-2': [24, Colors.primary],
  'ic-home-3': [24, Colors.grey1],
  'ic-bold-home-3': [24, Colors.grey1],
};

const iconsMap = {};
const iconsLoaded = new Promise(resolve => {
  // @ts-ignore
  new Promise.all(
    Object.keys(icons).map(iconName => {
      // @ts-ignore
      switch (icons[iconName][2]) {
        case 'ionicons':
          return Ionicons.getImageSource(
            iconName.replace(replaceSuffixPattern, ''),
            // @ts-ignore
            icons[iconName][0],
            // @ts-ignore
            icons[iconName][1],
          );
        default:
          return TalashIcon.getImageSource(
            iconName.replace(replaceSuffixPattern, ''),
            // @ts-ignore
            icons[iconName][0],
            // @ts-ignore
            icons[iconName][1],
          );
      }
    }),
    // @ts-ignore
  ).then(sources => {
    Object.keys(icons).forEach((iconName, idx) => {
      // @ts-ignore
      iconsMap[iconName] = sources[idx];
    });

    // Call resolve (and we are done)
    resolve(true);
  });
});

export {iconsMap, iconsLoaded};
