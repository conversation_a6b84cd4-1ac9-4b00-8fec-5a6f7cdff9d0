import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {STORAGE_KEY} from 'src/constants/storageKeys';
import {TZ_OTTAWA} from '@themes/Constants';

// Initialize dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

const POPUP_COOLDOWN_DAYS = 10;

export const savePopupBannerClosedDate = async (): Promise<void> => {
  try {
    // Use Ottawa timezone for the current date
    const currentDate = dayjs().tz(TZ_OTTAWA).toISOString();
    await AsyncStorage.setItem(
      STORAGE_KEY.POPUP_BANNER_CLOSED_DATE,
      currentDate,
    );
  } catch (error) {
    //error
  }
};

export const shouldShowPopupBanner = async (): Promise<boolean> => {
  try {
    const closedDateStr = await AsyncStorage.getItem(
      STORAGE_KEY.POPUP_BANNER_CLOSED_DATE,
    );

    if (!closedDateStr) {
      return true;
    }

    const closedDate = dayjs(closedDateStr).tz(TZ_OTTAWA);
    const currentDate = dayjs().tz(TZ_OTTAWA);

    const daysPassed = currentDate.diff(closedDate, 'day');

    const shouldShow = daysPassed >= POPUP_COOLDOWN_DAYS;

    return shouldShow;
  } catch (error) {
    return true;
  }
};
