import moment from 'moment';
import {useMemo} from 'react';
import {EDaySpecial} from '@themes/Enums';
import {LOTTIE} from '@themes/Lottie';

export const useSpecialDay = () => {
  const today = moment();

  const data = useMemo(
    () => [
      {
        banner: LOTTIE.noel,
        bottomHome: LOTTIE.noelTab,
        endDay: moment({day: 25, month: 11}).endOf('day'),
        id: 1,
        key: EDaySpecial.Noel,
        startDay: moment({day: 18, month: 11}).startOf('day'),
      },
      {
        banner: LOTTIE.newYear,
        bottomHome: null,
        endDay: moment({day: 7, month: 0})
          .endOf('day')
          .year(today.year() + 1),
        id: 2,
        key: EDaySpecial.NewYear,
        startDay: moment({day: 26, month: 11}).startOf('day'),
      },
      {
        banner: LOTTIE.valentine,
        bottomHome: LOTTIE.valentineTab,
        endDay: moment({day: 14, month: 1}).endOf('day'),
        id: 3,
        key: EDaySpecial.Valentine,
        startDay: moment({day: 4, month: 1}).startOf('day'),
      },
    ],
    [today],
  );

  const specialDay = data.find(
    day =>
      today.isSameOrAfter(day.startDay, 'day') &&
      today.isSameOrBefore(day.endDay, 'day'),
  );

  return {
    dataSpecial: specialDay,
    specialDay: specialDay ? specialDay.key : EDaySpecial.Normal,
  };
};
