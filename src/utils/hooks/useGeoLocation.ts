import {useEffect} from 'react';
import {PermissionsAndroid, Platform} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import {PERMISSIONS, check} from 'react-native-permissions';
import {GOOGLE_MAPS_API_KEY} from '../../themes/Constants';
import {sortByLocationType} from '../Tools';
import {UserDataProps, useGlobalState} from '@react-query/clientStateManage';
/*
 * import {useReactiveVar} from '@apollo/client';
 * import {geoLocationVar} from '../../graphql/Cache';
 */

/*
 * const component = {
 *   name: 'SelectDistrict',
 *   passProps: {},
 *   options: {
 *     topBar: {
 *       visible: false,
 *       drawBehind: true,
 *       elevation: 0,
 *       background: {
 *         color: Colors.white,
 *       },
 *       noBorder: false,
 *     },
 *     layout: {
 *       componentBackgroundColor: 'transparent',
 *       backgroundColor: 'transparent',
 *     },
 *     statusBar: {
 *       drawBehind: true,
 *     },
 *     modalPresentationStyle:
 *       Platform.OS === 'android' ? 'overCurrentContext' : 'overFullScreen',
 *     animations: {
 *       showModal: {
 *         enabled: false,
 *       },
 *       dismissModal: {
 *         enabled: false,
 *       },
 *     },
 *   },
 * };
 */

const useLocationPermission = () => {
  const [data, setData] = useGlobalState('userData');

  const getAddressFromCoordinates = async ({
    latitude,
    longitude,
  }: {
    latitude: number;
    longitude: number;
  }) =>
    new Promise((resolve, reject) => {
      fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${latitude},${longitude}&key=${GOOGLE_MAPS_API_KEY}`,
      )
        .then(response => response.json())
        .then(responseJson => {
          if (responseJson.status === 'OK') {
            const sortedData = sortByLocationType(responseJson.results);
            const shortAddressName = sortedData?.[0]?.formatted_address;
            resolve(shortAddressName);
          }
          reject('not found');
        })
        .catch(error => {
          reject(error);
        });
    });

  // const userPosition = useReactiveVar(geoLocationVar);
  useEffect(() => {
    checkLocationPermission();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkLocationPermission = async () => {
    try {
      if (Platform.OS === 'ios') {
        const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
        if (
          result === 'denied' ||
          result === 'unavailable'
          // && !userPosition
        ) {
          requestLocationPermission();
        }
        if (result === 'granted') {
          Geolocation.getCurrentPosition(
            async currentPosition => {
              if (
                currentPosition?.coords?.latitude !==
                  data?.location?.geo?.latitude ||
                currentPosition?.coords?.longitude !==
                  data?.location?.geo?.longitude
              ) {
                await getAddressFromCoordinates(currentPosition?.coords)
                  .then((address: string) => {
                    const addressArray = address?.split(', ');
                    setData({
                      ...(data as UserDataProps),
                      location: {
                        address: {
                          address,
                          name: `${addressArray?.[0]} ${addressArray?.[1]}`,
                        },
                        geo: currentPosition?.coords,
                      },
                    });
                  })
                  .catch(error => console.error('An error occurred:', error));
              }
            },
            () => {
              /*
               * if (!NavigationDetector.userPosition) {
               *   Navigation.showModal({ component });
               * }
               */
            },
          );
        }
      } else if (Platform.OS === 'android') {
        const result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
        if (result === 'denied' && !data?.location?.geo) {
          requestLocationPermission();
        }
        if (result === 'granted') {
          Geolocation.getCurrentPosition(
            async currentPosition => {
              if (
                currentPosition?.coords?.latitude !==
                  data?.location?.geo?.latitude ||
                currentPosition?.coords?.longitude !==
                  data?.location?.geo?.longitude
              ) {
                getAddressFromCoordinates(currentPosition?.coords)
                  .then((address: string) => {
                    const addressArray = address?.split(', ');
                    setData({
                      ...(data as UserDataProps),
                      location: {
                        address: {
                          address,
                          name: `${addressArray?.[0]} ${addressArray?.[1]}`,
                        },
                        geo: currentPosition?.coords,
                      },
                    });
                  })
                  .catch(error => console.error('An error occurred:', error));
              }
            },
            () => {
              /*
               * if (!NavigationDetector.userPosition) {
               *   Navigation.showModal({ component });
               * }
               */
            },
          );
        }
      }
    } catch (error) {}
  };

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'ios') {
        const result = await Geolocation.requestAuthorization('whenInUse');
        /*
         * if (result === 'unavailable' || result === 'limited') {
         *   if (!NavigationDetector.userPosition) {
         *     // Navigation.showModal({ component });
         *   }
         * }
         */
        if (result === 'denied' || result === 'disabled') {
          /*
           * if (!NavigationDetector.userPosition) {
           *   Navigation.showModal({ component });
           * }
           */
          setData({
            ...(data as UserDataProps),
            location: undefined,
          });
        }
        if (result === 'granted' || result === 'restricted') {
          Geolocation.getCurrentPosition(
            async currentPosition => {
              if (
                currentPosition?.coords?.latitude !==
                  data?.location?.geo?.latitude ||
                currentPosition?.coords?.longitude !==
                  data?.location?.geo?.longitude
              ) {
                getAddressFromCoordinates(currentPosition?.coords)
                  .then((address: string) => {
                    const addressArray = address?.split(', ');
                    setData({
                      ...(data as UserDataProps),
                      location: {
                        address: {
                          address,
                          name: `${addressArray?.[0]} ${addressArray?.[1]}`,
                        },
                        geo: currentPosition?.coords,
                      },
                    });
                  })
                  .catch(error => console.error('An error occurred:', error));
              }
            },
            () => {
              /*
               * if (!NavigationDetector.userPosition) {
               *   Navigation.showModal({ component });
               * }
               */
            },
          );
        }
      } else if (Platform.OS === 'android') {
        const result = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (result === 'denied' || result === 'never_ask_again') {
          /*
           * if (!NavigationDetector.userPosition) {
           *   Navigation.showModal({ component });
           * }
           */
          setData({
            ...(data as UserDataProps),
            location: undefined,
          });
        } else {
          Geolocation.getCurrentPosition(
            async currentPosition => {
              if (
                currentPosition?.coords?.latitude !==
                  data?.location?.geo?.latitude ||
                currentPosition?.coords?.longitude !==
                  data?.location?.geo?.longitude
              ) {
                await getAddressFromCoordinates(currentPosition?.coords)
                  .then((address: string) => {
                    const addressArray = address?.split(', ');
                    setData({
                      ...(data as UserDataProps),
                      location: {
                        address: {
                          address,
                          name: `${addressArray?.[0]} ${addressArray?.[1]}`,
                        },
                        geo: currentPosition?.coords,
                      },
                    });
                  })
                  .catch(error => console.error('An error occurred:', error));
              }
            },
            () => {
              /*
               * if (!NavigationDetector.userPosition) {
               *   Navigation.showModal({ component });
               * }
               */
            },
          );
        }
      }
    } catch (error) {}
  };

  return {data, requestLocationPermission};
};

export {useLocationPermission};
