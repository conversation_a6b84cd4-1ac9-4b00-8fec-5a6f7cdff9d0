import {isEmpty} from 'lodash-es';
import {UserSubscriptionEntity} from '@requests';
import {useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscription} from '@queries';

const useSubscription = () => {
  const {data, refetch} =
    useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscription();
  const mySubscription = data?.data as UserSubscriptionEntity;

  let isSubscribe = !!mySubscription?.renewDate;

  const isFirstTimeBuySubscription = isEmpty(data?.data);
  if (isFirstTimeBuySubscription) {
    isSubscribe = false;
  }
  return {
    data: mySubscription,
    isFirstTimeBuySubscription,
    isSubscribe,
    refetch,
  };
};

export default useSubscription;
