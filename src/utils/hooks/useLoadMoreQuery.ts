import {QueryKey, useInfiniteQuery} from '@tanstack/react-query';

const LIMIT_NUMBER = 10;
// const MINUTE = 1000 * 60;

export const useLoadMoreQuery = ({
  query,
  queryKey,
  enabled = true,
  limit = LIMIT_NUMBER,
}: {
  query: any;
  queryKey: QueryKey;
  enabled?: boolean;
  limit?: number;
}) => {
  let totalItems = 0;
  let currentOffset = 0;

  const {
    data,
    isFetchingNextPage,
    isFetching,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
  } = useInfiniteQuery({
    enabled,
    gcTime: 0,
    getNextPageParam: (lastPage: any) => {
      const {offset, total} = lastPage?.data?.meta || {};
      totalItems = total;
      if (offset < total) {
        currentOffset = offset + limit;
        return offset + limit;
      }
      return undefined;
    },
    initialPageParam: 0,
    persister: undefined,
    queryFn: async ({pageParam = 0}) => {
      const res = await query(pageParam);
      return res;
    },
    queryKey,
    retry: false,
    staleTime: 0,
  });

  const loadMore = () => {
    if (!isFetchingNextPage && data) {
      fetchNextPage();
    }
  };

  return {
    currentOffset,
    data: !data?.pages?.[0]?.data
      ? []
      : data?.pages?.flatMap(page => page?.data?.items),
    isFetching,
    isFetchingNextPage,
    isRefetching,
    loadMore,
    loading: isLoading,
    refetch,
    totalItems,
  };
};
