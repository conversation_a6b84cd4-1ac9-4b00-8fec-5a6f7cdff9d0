import {useState, useEffect} from 'react';

const useTypewriter = (text: string, shouldStart = true, loop = false) => {
  const typingSpeed = 30;
  const startDelay = 300;
  const loopDelay = 2000;

  const [displayText, setDisplayText] = useState('\u200B'.repeat(text.length));
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    setDisplayText('\u200B'.repeat(text.length));
    setCurrentIndex(0);
    setIsTyping(false);
    setIsDeleting(false);
    setHasStarted(false);
  }, [text]);

  useEffect(() => {
    if (shouldStart && !hasStarted) {
      setHasStarted(true);
    }
    if (!shouldStart) {
      setDisplayText('\u200B'.repeat(text.length));
      setCurrentIndex(0);
      setIsTyping(false);
      setIsDeleting(false);
      setHasStarted(false);
    }
  }, [shouldStart, hasStarted]);

  useEffect(() => {
    if (!hasStarted) return;

    let timeout: NodeJS.Timeout;

    if (!isTyping && currentIndex === 0 && !isDeleting) {
      setDisplayText('\u200B'.repeat(text.length));
      timeout = setTimeout(() => {
        setIsTyping(true);
      }, startDelay);
      return () => clearTimeout(timeout);
    }

    if (isTyping && !isDeleting) {
      if (currentIndex < text.length) {
        timeout = setTimeout(() => {
          const visiblePart = text.substring(0, currentIndex + 1);
          const invisiblePart = '\u200B'.repeat(
            text.length - (currentIndex + 1),
          );
          setDisplayText(visiblePart + invisiblePart);
          setCurrentIndex(currentIndex + 1);
        }, typingSpeed);
      } else if (loop) {
        timeout = setTimeout(() => {
          setIsDeleting(true);
        }, loopDelay);
      }
      return () => clearTimeout(timeout);
    }

    if (isDeleting && loop) {
      if (currentIndex > 0) {
        timeout = setTimeout(() => {
          const visiblePart = text.substring(0, currentIndex - 1);
          const invisiblePart = '\u200B'.repeat(
            text.length - (currentIndex - 1),
          );
          setDisplayText(visiblePart + invisiblePart);
          setCurrentIndex(currentIndex - 1);
        }, typingSpeed / 2);
      } else {
        setIsDeleting(false);
        setIsTyping(false);
      }
      return () => clearTimeout(timeout);
    }
  }, [
    text,
    typingSpeed,
    currentIndex,
    isTyping,
    isDeleting,
    loop,
    loopDelay,
    startDelay,
    hasStarted,
  ]);

  return displayText;
};

export default useTypewriter;
