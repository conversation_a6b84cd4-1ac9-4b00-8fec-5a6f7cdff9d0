/* eslint-disable @typescript-eslint/prefer-optional-chain */
/* eslint-disable max-lines */
/* eslint-disable react-hooks/exhaustive-deps */
import {useNavigation, useRoute} from '@react-navigation/native';
import {isEmpty} from 'lodash-es';
import {useEffect, useMemo, useRef, useState} from 'react';
import {LayoutAnimation, Linking} from 'react-native';
import MapView from 'react-native-maps';
import {io, Socket} from 'socket.io-client';
import Config from 'react-native-config';
import NetInfo from '@react-native-community/netinfo';
import firestore from '@react-native-firebase/firestore';
import useTypeSafeTranslation from './useTypeSafeTranslation';
import {useAppState} from './useAppState';
import {
  calculateDirection,
  calculateDistance,
  calculateTravelTime,
  formatTime,
  showErrorAlert,
} from '@utils/Tools';
import {BookingEntity, CoordinatesDto} from '@requests';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  useBookingsServiceBookingSharingControllerGetBookingByEncryptedId,
  useBookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedId,
} from '@queries';
import {IS_ANDROID, SOCKET_EMIT_EVENT} from '@themes/Constants';
import {IRealTime, ShareRouteProps} from '@global';
import Icons from '@themes/Icons';

export const useShareRoute = () => {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const {bookingId = ''} = (useRoute()?.params || {}) as ShareRouteProps;

  const [_, setIsShareRoute] = useGlobalState('isShareRoute');
  const [maxWaitingTime, setMaxWaitingTime] = useGlobalState('maxWaitingTime');
  const [currentWaitingTime, setCurrentWaitingTime] =
    useGlobalState('currentWaitingTime');
  const [userData] = useGlobalState('userData');

  const waitTime = parseFloat((maxWaitingTime || 0).toFixed(0));
  const defaultPercent = currentWaitingTime
    ? `${((waitTime - currentWaitingTime) / waitTime) * 100}%`
    : '0%';

  const [progressPercent, setProgressPercent] = useState(defaultPercent);
  const [carDegree, setCarDegree] = useState(0);
  const [currentDriverLocation, setCurrentDriverLocation] =
    useState<CoordinatesDto>();
  const [listCarLocation, setListCarLocation] = useState<CoordinatesDto[]>([]);
  const [socketState, setSocketState] = useState<Socket>();
  const [socketConnected, setSocketConnected] = useState(false);

  const hasFirstRun = useRef(false);
  const mapRef = useRef<MapView>(null);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const {
    data,
    refetch: refetchBooking,
    isError,
  } = useBookingsServiceBookingSharingControllerGetBookingByEncryptedId(
    {
      encryptedId: bookingId,
    },
    undefined,
    {
      enabled: !!bookingId,
    },
  );

  const booking = data?.data;

  const {data: driverLocation, isFetching: isFetchingCurrentLocation} =
    useBookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedId(
      {
        encryptedId: bookingId,
      },
      undefined,
      {
        enabled: !!booking?.bookingCode,
        refetchIntervalInBackground: true,
        refetchOnWindowFocus: false,
      },
    );

  const isClientOnBoard =
    booking?.status === BookingEntity.status.CLIENT_ON_BOARD;
  const isArrivalAtClient =
    booking?.status === BookingEntity.status.ARRIVAL_AT_CLIENT;
  const isVehicleConfirmed =
    booking?.status === BookingEntity.status.VEHICLE_CONFIRMED;
  const isCompleted = booking?.status === BookingEntity.status.COMPLETED;
  const isAmountMoneyReceived =
    booking?.status === BookingEntity.status.AMOUNT_MONEY_RECEIVED;

  const isLookingForDriver = [
    BookingEntity.status.AWAITING_CONFIRMED_VEHICLE,
    BookingEntity.status.DEMAND_CREATION,
  ].includes(booking?.status as BookingEntity.status);

  const isCancelled = booking?.status === BookingEntity.status.CANCELLED;

  const isWaitingForPickup =
    [
      BookingEntity.status.VEHICLE_CONFIRMED,
      BookingEntity.status.ARRIVAL_AT_CLIENT,
    ].includes(booking?.status as BookingEntity.status) &&
    !isEmpty(driverLocation?.data?.currentLocation);

  const isShowPolyline =
    [
      BookingEntity.status.CONFIRMED_ADDRESS,
      BookingEntity.status.CLIENT_ON_BOARD,
      BookingEntity.status.COMPLETED,
      BookingEntity.status.AMOUNT_MONEY_RECEIVED,
    ].includes(booking?.status as BookingEntity.status) &&
    booking?.originLocation;

  const isShowDestination =
    ![
      BookingEntity.status.VEHICLE_CONFIRMED,
      BookingEntity.status.AWAITING_CONFIRMED_VEHICLE,
      BookingEntity.status.ARRIVAL_AT_CLIENT,
    ].includes(booking?.status as BookingEntity.status) &&
    !isEmpty(booking?.destinationLocation);

  const isCompleteOrigin = [
    BookingEntity.status.CLIENT_ON_BOARD,
    BookingEntity.status.COMPLETED,
    BookingEntity.status.AMOUNT_MONEY_RECEIVED,
  ].includes(booking?.status as BookingEntity.status);

  const isCompleteDestination = [
    BookingEntity.status.COMPLETED,
    BookingEntity.status.AMOUNT_MONEY_RECEIVED,
  ].includes(booking?.status as BookingEntity.status);

  const isShowMinAway = [
    BookingEntity.status.VEHICLE_CONFIRMED,
    BookingEntity.status.CLIENT_ON_BOARD,
  ].includes(booking?.status as BookingEntity.status);

  const isCompletedOrReceived = [
    BookingEntity.status.COMPLETED,
    BookingEntity.status.AMOUNT_MONEY_RECEIVED,
  ].includes(booking?.status as BookingEntity.status);

  const goBack = () => {
    if (userData?.accessToken) {
      navigation.reset({routes: [{name: 'Main'}]});
    } else {
      navigation.reset({routes: [{name: 'Login'}]});
    }
    setIsShareRoute(false);
  };

  const onNavigateMaps = () => {
    if (mapRef.current) {
      if (booking?.status === BookingEntity.status.AWAITING_CONFIRMED_VEHICLE) {
        mapRef.current?.animateCamera({
          center: {
            latitude: booking?.originLocation?.[0],
            longitude: booking?.originLocation?.[1],
          },
          zoom: 16,
        });
      } else {
        mapRef.current.fitToSuppliedMarkers(['marker1', 'marker2', 'marker3'], {
          animated: true,
          edgePadding: {bottom: 400, left: 100, right: 100, top: 100},
        });
      }
    }
  };

  const calculateProgress = (seconds: number) =>
    ((waitTime - seconds) / waitTime) * 100;

  const startTimer = () => {
    let seconds = currentWaitingTime || waitTime;
    intervalRef.current = setInterval(() => {
      const percent = calculateProgress(seconds);
      seconds -= 5;

      if (seconds > 0) {
        setCurrentWaitingTime(seconds);
      } else {
        clearTimer();
        LayoutAnimation.linear();
        setProgressPercent('100%');
      }

      setProgressPercent(`${percent.toFixed(2)}%`);
      LayoutAnimation.linear();
    }, 5000);
  };

  const handleSetCarDegree = () => {
    if (!isFetchingCurrentLocation) {
      if (driverLocation?.data?.currentLocation?.latitude) {
        if (!currentDriverLocation) {
          setCurrentDriverLocation(driverLocation?.data?.currentLocation);
        }
        const newListCar = [
          ...listCarLocation,
          driverLocation?.data?.currentLocation,
        ];
        if (newListCar?.length < 2) {
          setCarDegree(calculateDirection(newListCar?.[0], newListCar?.[1]));
          setListCarLocation(newListCar);
        } else {
          setCarDegree(
            calculateDirection(
              listCarLocation?.[1],
              driverLocation?.data?.currentLocation,
            ),
          );
          setListCarLocation([
            listCarLocation?.[1],
            driverLocation?.data?.currentLocation,
          ]);
        }
      }
    }
  };

  const minAway = useMemo(() => {
    const location = isClientOnBoard
      ? {
          latitude: booking?.destinationLocation?.[1],
          longitude: booking?.destinationLocation?.[0],
        }
      : {
          latitude: booking?.originLocation?.[1],
          longitude: booking?.originLocation?.[0],
        };

    return parseFloat(
      (
        calculateTravelTime(location, driverLocation?.data?.currentLocation) ||
        0
      ).toFixed(0),
    );
  }, [
    booking?.destinationLocation,
    booking?.originLocation,
    driverLocation?.data?.currentLocation,
    isClientOnBoard,
  ]);

  const distance = useMemo(
    () =>
      calculateDistance(
        {
          latitude: booking?.originLocation?.[1],
          longitude: booking?.originLocation?.[0],
        },
        {
          latitude: booking?.destinationLocation?.[1],
          longitude: booking?.destinationLocation?.[0],
        },
      ),
    [booking],
  );

  const waitTimeTitle = `${formatTime(minAway)} ${t('activities.away')}`;

  const titleModal = useMemo(() => {
    const titles = {
      [BookingEntity.status.AWAITING_CONFIRMED_VEHICLE]: t(
        'shareRoute.lookingForDriver',
      ),
      [BookingEntity.status.DEMAND_CREATION]: t('shareRoute.lookingForDriver'),
      [BookingEntity.status.VEHICLE_CONFIRMED]: t(
        'shareRoute.waitingForPickup',
      ),
      [BookingEntity.status.ARRIVAL_AT_CLIENT]: t('shareRoute.driverArrived'),
      [BookingEntity.status.CLIENT_ON_BOARD]: t('shareRoute.onTheWay'),
      [BookingEntity.status.COMPLETED]: t('shareRoute.arrived'),
      [BookingEntity.status.AMOUNT_MONEY_RECEIVED]: t('shareRoute.arrived'),
      [BookingEntity.status.CANCELLED]: t('shareRoute.bookingCancel'),
    };
    return `${
      ![
        BookingEntity.status.ARRIVAL_AT_CLIENT,
        BookingEntity.status.CANCELLED,
      ].includes(booking?.status)
        ? booking?.user?.fullName
        : ''
    } ${titles[booking?.status as keyof typeof titles] || ''}`;
  }, [booking, t]);

  const getLineDashPattern = (distance = 0) => {
    const defaultLineDashPattern = [10, 10];

    if (IS_ANDROID) {
      return [18, 18];
    }
    if (distance > 1) {
      return [distance * 18, distance * 18];
    }
    return defaultLineDashPattern;
  };

  const navigateActionModal = (isSuccess = false) => {
    navigation.navigate('ActionModal', {
      confirmLabel: t('button.backToHomepage'),
      content: isSuccess
        ? t('shareRoute.descriptionSuccess')
        : t('shareRoute.descriptionFailed'),
      enableCloseOnMask: false,
      hasCancelBtn: false,
      onApply: () => {
        setIsShareRoute(false);
        goBack();
      },
      title: isSuccess
        ? t('shareRoute.titleSuccess')
        : t('shareRoute.titleFailed'),
      ...(isSuccess && {
        iconSize: 54,
        image: Icons.icSuccess,
      }),
    });
  };

  const onNavigateError = () => {
    if (booking?.status) {
      if (
        [
          BookingEntity.status.COMPLETED,
          BookingEntity.status.CANCELLED,
        ]?.includes(booking?.status) &&
        !hasFirstRun.current
      ) {
        navigateActionModal(false);
      }
      hasFirstRun.current = true;
    }
    if (isError && !hasFirstRun.current) {
      navigateActionModal(false);
      hasFirstRun.current = true;
    }
  };

  const onCallUser = () => {
    const phone = booking?.user?.phoneNumber || 0;
    Linking.canOpenURL(`tel:${phone}`).then(supported => {
      if (supported) {
        Linking.openURL(`tel:${phone}`);
      } else {
        showErrorAlert({message: t('error.invalidLink')});
      }
    });
  };

  useEffect(() => {
    setTimeout(() => {
      onNavigateMaps();
    }, 1500);
  }, [booking]);

  useEffect(() => {
    onNavigateError();
  }, [booking?.bookingCode, isError]);

  useEffect(() => {
    handleSetCarDegree();
  }, [isFetchingCurrentLocation]);

  const clearTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  useEffect(() => {
    setIsShareRoute(true);
    return () => {
      setIsShareRoute(false);
    };
  }, []);

  useEffect(() => {
    if (waitTime) {
      startTimer();
    }
    return () => clearTimer();
  }, [waitTime]);

  const handleBookingUpdate = (data: IRealTime) => {
    const {status, waitingTime} = data;
    if (bookingId) {
      refetchBooking();
    }

    if (status === BookingEntity.status.VEHICLE_CONFIRMED) {
      setMaxWaitingTime(waitingTime);
    }
    if (status === BookingEntity.status.COMPLETED) {
      navigateActionModal(true);
      setCurrentWaitingTime(0);
    }
  };

  useEffect(() => {
    if (!bookingId) {
      if (socketState) {
        socketState.disconnect();
        setSocketState(undefined);
        setSocketConnected(false);
      }
      return;
    }

    const socket: Socket = io(`${Config.SOCKET_URL}/booking-sharing`, {
      forceNew: true,
      query: {
        encryptedId: decodeURIComponent(bookingId),
      },
      reconnectionAttempts: 1,
      transports: ['websocket'],
    });

    setSocketState(socket);

    socket.on('connect', () => {
      setSocketConnected(true);
    });

    socket.on('disconnect', () => {
      setSocketConnected(false);
    });

    socket.on(SOCKET_EMIT_EVENT.CHANGE_STATUS, handleBookingUpdate);

    return () => {
      socket.off(SOCKET_EMIT_EVENT.CHANGE_STATUS);
      socket.disconnect();
      setSocketState(undefined);
      setSocketConnected(false);
    };
  }, [bookingId]);

  useEffect(() => {
    if (!bookingId || socketConnected) return;

    const unsubscribe = firestore()
      .collection('book_sharings')
      .doc(encodeURIComponent(bookingId))
      .onSnapshot(docSnapshot => {
        if (!docSnapshot || !docSnapshot?.exists) return;
        const data = docSnapshot.data() as IRealTime;
        handleBookingUpdate(data);
      });

    return () => unsubscribe();
  }, [bookingId, socketConnected]);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      if (state.isConnected && bookingId && socketState) {
        socketState.connect();
      }
    });

    return () => unsubscribe();
  }, [bookingId]);

  useAppState({
    appActiveHandler: () => {
      refetchBooking();
    },
  });

  return {
    booking,
    carDegree,
    distance,
    driverLocation,
    getLineDashPattern,
    goBack,
    isAmountMoneyReceived,
    isArrivalAtClient,
    isCancelled,
    isClientOnBoard,
    isCompleteDestination,
    isCompleteOrigin,
    isCompleted,
    isCompletedOrReceived,
    isLookingForDriver,
    isShowDestination,
    isShowMinAway,
    isShowPolyline,
    isVehicleConfirmed,
    isWaitingForPickup,
    mapRef,
    onCallUser,
    progressPercent,
    titleModal,
    waitTimeTitle,
  };
};
