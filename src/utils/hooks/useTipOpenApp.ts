import {useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import {useEffect, useState} from 'react';
import {BookingEntity} from '@requests';
import {
  hasBookingTipBeenShown,
  markBookingTipAsShown,
} from '@utils/shownBookingTracker';

interface Props {
  dataLastCompleted: BookingEntity;
}

let tipShownInSession = false;

export const useTipOpenApp = ({dataLastCompleted}: Props) => {
  const navigation = useNavigation();
  const [hasShownTip, setHasShownTip] = useState(false);
  const [isFirstMount, setIsFirstMount] = useState(true);
  const [hasCheckedStorage, setHasCheckedStorage] = useState(false);

  const {reviewStatus, status, endDate, bookingCode} = dataLastCompleted ?? {};

  const isRateAndTip =
    reviewStatus === BookingEntity.reviewStatus.PENDING &&
    status === BookingEntity.status.COMPLETED;

  const isWithinOneHour = () => {
    if (!endDate) return false;

    const rideEndTime = dayjs(endDate);
    const currentTime = dayjs();
    const diffInMinutes = currentTime.diff(rideEndTime, 'minute');

    return diffInMinutes >= 0 && diffInMinutes <= 60;
  };

  useEffect(() => {
    if (bookingCode && !hasCheckedStorage) {
      hasBookingTipBeenShown(bookingCode).then(hasBeenShown => {
        setHasShownTip(hasBeenShown);
        setHasCheckedStorage(true);
      });
    }
  }, [bookingCode, hasCheckedStorage]);

  useEffect(
    () => () => {
      setIsFirstMount(false);
    },
    [],
  );

  const onGotoTip = async () => {
    if (tipShownInSession || hasShownTip) return;

    if (!isFirstMount) return;

    if (bookingCode && !hasCheckedStorage) {
      const hasBeenShown = await hasBookingTipBeenShown(bookingCode);
      setHasShownTip(hasBeenShown);
      setHasCheckedStorage(true);

      if (hasBeenShown) return;
    }

    if (isRateAndTip && isWithinOneHour()) {
      tipShownInSession = true;
      setHasShownTip(true);

      if (bookingCode) {
        await markBookingTipAsShown(bookingCode);
      }

      navigation.navigate('RatingDriver', {
        bookingDetail: dataLastCompleted,
        isActivitiesFlow: true,
        onClose: () => navigation.goBack(),
      });
    }
    tipShownInSession = true;
  };

  return {
    isFirstMount,
    isRateAndTip,
    isWithinOneHour: isWithinOneHour(),
    onGotoTip,
  };
};
