import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';
import {useBookingsServiceBookingControllerGetBooking} from '@queries';
import {BookingEntity} from '@requests';
import Images from '@themes/Images';

interface Props {
  bookingId: string;
  handleClear: () => void;
}
export const useTipBooking = ({bookingId, handleClear}: Props) => {
  const {t} = useTranslation();
  const navigation = useNavigation();
  const {refetch: refetchDetail} =
    useBookingsServiceBookingControllerGetBooking(
      {
        id: bookingId,
      },
      undefined,
      {
        enabled: !!bookingId,
      },
    );

  const onCloseModal = (points: number) => {
    navigation.navigate('ActionModal', {
      cancelLabel: t('button.dismiss'),
      confirmLabel: t('button.viewLoyalPoints'),
      content: t('rating.earnPointFirstRide', {
        count: points || 0,
      }),
      iconSize: 84,
      image: Images.roundLogo,
      onApply: () => {
        navigation.goBack();
        navigation.navigate('LoyalPoint', {
          isTopBarEnable: false,
        });
      },
      title: t('rating.earnPointFirstRideTitle', {
        count: points || 0,
      }),
    });
  };

  const handleNavigateRating = (booking: BookingEntity) => {
    navigation.navigate('RatingDriver', {
      bookingDetail: booking,
      onClose: () => {
        handleClear();
        if (Number(booking?.awardedFirstRide) > 0) {
          return onCloseModal(Number(booking?.awardedFirstRide));
        }
      },
    });
  };

  const handleRateAndTip = () => {
    setTimeout(() => {
      refetchDetail().then(res =>
        handleNavigateRating(res?.data?.data as BookingEntity),
      );
    }, 4000);
  };

  return {
    handleRateAndTip,
  };
};
