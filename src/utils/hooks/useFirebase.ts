import messaging from '@react-native-firebase/messaging';
import {useCallback, useEffect, useState} from 'react';
import {Linking, Platform} from 'react-native';
import deviceInfo from 'react-native-device-info';
import Toast from 'react-native-toast-message';
import {handleNavigateNotification, handleNavigateOnClick} from '../Tools';
import {useAppState} from './useAppState';
import useTypeSafeTranslation from './useTypeSafeTranslation';
import {Images} from '@themes';
import {CustomToastProps} from '@components/Modal/CustomToast';
import {NotificationTokenEntity} from '@requests';
import {queryClient} from '@react-query/queryClient';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  useNotificationTokensServiceNotificationTokenControllerCreateNotificationToken,
  useUsersServiceUserControllerGetUserKey,
} from '@queries';
import {IS_IOS} from '@themes/Constants';

let cacheFcmToken = '';
let RETRY_COUNT = 0;
const NO_DRIVER = 'No Drivers Nearby';
const REFERRAL = 'referral';

const useFirebase = () => {
  const [userData] = useGlobalState('userData');
  const [currentListScreen] = useGlobalState('currentListScreen');
  const {t} = useTypeSafeTranslation();

  const token = userData?.accessToken;
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [_, setFcmData] = useGlobalState('fcmData');

  const updatePoint = () => {
    queryClient.refetchQueries({
      queryKey: [useUsersServiceUserControllerGetUserKey],
      type: 'all',
    });
  };

  const {mutate: createInstallation} =
    useNotificationTokensServiceNotificationTokenControllerCreateNotificationToken();

  const registerDevice = async () => {
    if (Platform.OS === 'ios') {
      try {
        if (!messaging().isDeviceRegisteredForRemoteMessages) {
          await messaging().registerDeviceForRemoteMessages();
        }
      } catch (error) {
        console.log('💩: -> error', error);
      }
    }
  };

  const getToken = useCallback(async () => {
    if (token) {
      try {
        if (
          messaging().isDeviceRegisteredForRemoteMessages &&
          Platform.OS === 'ios'
        ) {
          await messaging().registerDeviceForRemoteMessages();
        }

        const fcmToken = await messaging().getToken();
        console.log('💩: getToken -> fcmToken', fcmToken);
        if (fcmToken !== cacheFcmToken) {
          cacheFcmToken = fcmToken;
          setIsEnabled(true);
          createInstallation(
            {
              requestBody: {
                deviceIdentity: await deviceInfo.getUniqueId(),
                fcmToken,
                platform: Platform.OS.toUpperCase(),
              },
            },
            {
              onSuccess(data) {
                setFcmData(data?.data as NotificationTokenEntity);
              },
            },
          );
        }
      } catch (error) {
        RETRY_COUNT = RETRY_COUNT + 1;
        if (RETRY_COUNT < 3) {
          setTimeout(() => {
            getToken();
          }, 2000);
        }
        return;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token]);
  // }, []);

  const deleteToken = async () => {
    setIsEnabled(false);
    await messaging().deleteToken();
    await messaging().unregisterDeviceForRemoteMessages();
  };

  const requestUserPermission = useCallback(async () => {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      if (enabled) {
        getToken();
      } else {
        deleteToken();
      }
    } else {
      deleteToken();
    }
  }, [getToken]);

  const checkUserPermission = useCallback(async () => {
    const authStatus = await messaging().hasPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
      requestUserPermission();
    } else {
      getToken();
    }
  }, [getToken, requestUserPermission]);

  // in app
  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      if (
        currentListScreen?.includes('BookingMaps') &&
        remoteMessage?.notification?.title === NO_DRIVER
      ) {
        return;
      }
      if (remoteMessage?.notification?.title === NO_DRIVER) {
        return Toast.show({
          bottomOffset: 24,
          position: 'top',
          props: {
            content: remoteMessage?.notification?.body,
            image: Images.simpleLogo,
            showCloseIcon: false,
            title: t('booking.noDriversNearby'),
          } as CustomToastProps['props'],
          swipeable: false,
          topOffset: 10,
          type: 'success',
          visibilityTime: 4000,
        });
      }

      if (
        remoteMessage?.notification?.title?.toLowerCase()?.includes(REFERRAL)
      ) {
        updatePoint();
      }
      handleNavigateNotification({
        isInApp: true,
        remoteMessage,
      });
      // alert('A new FCM message arrived!', JSON.stringify(remoteMessage));
    });

    return unsubscribe;
  }, [currentListScreen]);

  useEffect(() => {
    // Assume a message-notification contains a "type" property in the data payload of the screen to open
    messaging().onNotificationOpenedApp(remoteMessage => {
      handleNavigateNotification({
        isInApp: false,
        remoteMessage,
      });
    });

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          handleNavigateNotification({
            isInApp: false,
            remoteMessage,
          });
        }
      });

    IS_IOS && handleNotificationHandlersIOS();
    messaging().setBackgroundMessageHandler(
      () => new Promise((resolve: any) => resolve()),
    );
  }, []);

  const handleNotificationHandlersIOS = async () => {
    await handleNavigateOnClick();
  };

  useEffect(() => {
    if (token) {
      registerDevice();
      checkUserPermission();
    }
  }, [checkUserPermission, token]);

  const toggleNotificationSetting = () => {
    Linking.openSettings();
  };

  useAppState({
    appActiveHandler: () => {
      if (token) {
        registerDevice();
        checkUserPermission();
      }
    },
  });

  return {
    deleteToken,
    getToken,
    isEnabled,
    toggleNotificationSetting,
  };
};

export default useFirebase;
