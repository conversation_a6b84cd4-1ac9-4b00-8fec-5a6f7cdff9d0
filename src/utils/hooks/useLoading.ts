import Toast from 'react-native-toast-message';

const useLoading = () => {
  const showLoading = () => {
    Toast.show({
      bottomOffset: -10,
      position: 'bottom',
      swipeable: false,
      topOffset: 10,
      type: 'loading',
      visibilityTime: 9000,
    });
  };
  const dismissLoading = () => {
    Toast.hide();
  };
  return {
    dismissLoading,
    showLoading,
  };
};

export default useLoading;
