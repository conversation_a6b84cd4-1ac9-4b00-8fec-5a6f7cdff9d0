import {useState} from 'react';
import {Alert} from 'react-native';
import {chooseFile, choosePhoto, FileProps, uploadFileToS3} from '../upload';
import {CreateUploadPresignedUrlDto} from '../../api/requests';
import {useStoragesServiceStorageControllerGenerateS3PresignedUrl} from '@queries';

interface UseUploadProps {
  onSuccess: (res: any, type?: FileProps) => void;
  maxSizeMB?: number;
}

const useUpload = ({onSuccess, maxSizeMB}: UseUploadProps) => {
  const [loading, setLoading] = useState(false);
  const {mutate: presignedUrlS3} =
    useStoragesServiceStorageControllerGenerateS3PresignedUrl();

  const showPickerImageActions = () => {
    choosePhoto({
      maxSizeMB,
      onSuccess: (files: FileProps) => appendFilesToS3(files),
    });
  };

  const pickerFileActions = async () => {
    chooseFile({
      maxSizeMB,
      selectFile: (files: FileProps) => appendFilesToS3(files),
    });
  };

  const appendFilesToS3 = async (file: FileProps) => {
    try {
      setLoading(true);
      presignedUrlS3(
        {
          requestBody: {
            fileName:
              file?.filename?.replace(/^.*[\\\/]/, '') ||
              // eslint-disable-next-line no-useless-escape
              file?.path?.replace(/^.*[\\\/]/, '') ||
              // eslint-disable-next-line no-useless-escape
              file?.uri?.replace(/^.*[\\\/]/, '') ||
              '',
            fileType: file?.mime || file?.type || '',
            folderPrefix: CreateUploadPresignedUrlDto.folderPrefix.AVATARS,
          },
        },
        {
          onSuccess: res => {
            const configUpload = res?.data;
            uploadFileToS3({
              onSuccessUpload: value => {
                onSuccess?.(value, file);
                setLoading(false);
              },
              presignedUrlS3: configUpload,
              type: file.mime,
              uri: file.path,
            });
          },
        },
      );
    } catch (errorData) {
      return Alert.alert('error');
    }
  };
  return {loadingImage: loading, pickerFileActions, showPickerImageActions};
};

export default useUpload;
