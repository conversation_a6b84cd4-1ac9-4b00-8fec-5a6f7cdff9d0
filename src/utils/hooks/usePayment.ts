/* eslint-disable @typescript-eslint/no-empty-function */
import {
  confirmPayment,
  confirmPlatformPayPayment,
  isPlatformPaySupported,
  PlatformPay,
  usePlatformPay,
} from '@stripe/stripe-react-native';
import Config from 'react-native-config';
import {Platform} from 'react-native';
import {useEffect, useState} from 'react';
import {showErrorAlert} from '../Tools';
import useTypeSafeTranslation from './useTypeSafeTranslation';
import useLoading from './useLoading';
import {
  useUsersServiceUserControllerListCards,
  useUsersServiceUserControllerListCardsKey,
} from '@queries';
import {BookingEntity, CardEntity} from '@requests';
import {isReleaseMode, WALLET} from '@themes/Constants';

interface UsePaymentType {
  onSuccess?: (booking?: BookingEntity) => void;
  onError?: (err?: any) => void;
}

const IS_TEST_ENV = !isReleaseMode;

const usePayment = ({
  onSuccess = () => {},
  onError = () => {},
}: UsePaymentType = {}) => {
  const {t} = useTypeSafeTranslation();
  const {showLoading, dismissLoading} = useLoading();
  const [isWalletSupported, setIsWalletSupported] = useState(false);
  const [defaultPaymentMethod, setDefaultPaymentMethod] =
    useState<CardEntity>();
  const [loading, setLoading] = useState(false);

  const {
    data: listCard,
    isFetching,
    isLoading,
  } = useUsersServiceUserControllerListCards([
    useUsersServiceUserControllerListCardsKey,
  ]);

  useEffect(() => {
    const defaultPaymentMethod = listCard?.data.find(
      (ele: CardEntity) => ele.isDefault,
    );
    const walletData = isWalletSupported ? WALLET : undefined;
    setDefaultPaymentMethod(
      defaultPaymentMethod || listCard?.data?.[0] || walletData,
    );
  }, [isFetching, listCard, isLoading, isWalletSupported]);

  const checkPlatformPaySupport = async () => {
    try {
      if (Platform.OS === 'android') {
        const googlePaySupportedResponse = await isPlatformGooglePaySupported({
          googlePay: {testEnv: IS_TEST_ENV},
        });
        setIsWalletSupported(googlePaySupportedResponse);
      } else {
        const applePaySupportedResponse = await isPlatformPaySupported();
        setIsWalletSupported(applePaySupportedResponse);
      }
    } catch (error) {
      showErrorAlert({message: t('error.500')});
    }
  };

  useEffect(() => {
    checkPlatformPaySupport();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPlatformPaySupported]);

  const {
    isPlatformPaySupported: isPlatformGooglePaySupported,
    confirmPlatformPayPayment: confirmPlatformGooglePayPayment,
  } = usePlatformPay();

  const applePay = async (
    clientSecret: string,
    productName: string,
    booking?: BookingEntity,
  ) => {
    const amount = booking?.amount || 0;
    setLoading(true);
    showLoading();
    const {error} = await confirmPlatformPayPayment(clientSecret, {
      applePay: {
        cartItems: [
          {
            amount: (amount / 100).toFixed(2),
            label: productName,
            paymentType: PlatformPay.PaymentType.Immediate,
          },
          {
            amount: (amount / 100).toFixed(2),
            label: `140689 Canada Limitee (Via ${Config.APP_NAME})`,
            paymentType: PlatformPay.PaymentType.Immediate,
          },
        ],
        currencyCode: 'CAD',
        merchantCountryCode: 'CA',
        requiredBillingContactFields: [PlatformPay.ContactField.PhoneNumber],
        requiredShippingAddressFields: [PlatformPay.ContactField.PostalAddress],
      },
    });
    dismissLoading();
    if (error) {
      onError?.(error);
      showErrorAlert({message: error?.message});
      setLoading(false);
    } else {
      onSuccess?.(booking);
      setLoading(false);
    }
  };

  const googlePay = async (clientSecret: string, booking?: BookingEntity) => {
    showLoading();
    setLoading(true);
    const {error} = await confirmPlatformGooglePayPayment(clientSecret, {
      googlePay: {
        allowCreditCards: true,
        amount: booking?.amount,
        billingAddressConfig: {
          format: PlatformPay.BillingAddressFormat.Full,
          isPhoneNumberRequired: true,
          isRequired: true,
        },
        currencyCode: 'CAD',
        merchantCountryCode: 'CA',
        merchantName: 'Taxi Loyal',
        testEnv: IS_TEST_ENV,
      },
    });
    dismissLoading();
    if (error) {
      onError?.(error);
      showErrorAlert({message: error?.message});
      setLoading(false);
    } else {
      onSuccess?.(booking);
      setLoading(false);
    }
  };

  const payByCard = async (
    paymentIntent: string,
    cardId: string,
    booking?: BookingEntity,
  ) => {
    showLoading();
    setLoading(true);
    const {error} = await confirmPayment(paymentIntent, {
      paymentMethodData: {
        paymentMethodId: cardId,
      },
      paymentMethodType: 'Card',
    });
    dismissLoading();
    if (!error) {
      onSuccess?.(booking);
      setLoading(false);
    } else {
      onError?.(error);
      showErrorAlert({message: error?.message});
      setLoading(false);
    }
  };

  return {
    applePay,
    defaultPaymentMethod,
    googlePay,
    isWalletSupported,
    loading,
    payByCard,
  };
};

export default usePayment;
