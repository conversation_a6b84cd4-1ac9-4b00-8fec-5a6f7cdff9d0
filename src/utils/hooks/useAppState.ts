import {useEffect, useRef} from 'react';
import {AppState, AppStateStatus} from 'react-native';

export function useAppState({
  appActiveHandler,
}: {
  appActiveHandler: () => void;
}) {
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    function onChange(newState: AppStateStatus) {
      if (
        appState.current.match(/inactive|background/) &&
        newState === 'active'
      ) {
        appActiveHandler?.();
      }

      appState.current = newState;
    }

    const subscription = AppState.addEventListener('change', onChange);

    return () => {
      subscription.remove();
    };
  }, [appActiveHandler]);
}
