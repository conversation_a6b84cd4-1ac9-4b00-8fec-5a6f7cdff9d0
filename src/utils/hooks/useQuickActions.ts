/* eslint-disable react-hooks/exhaustive-deps */
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useEffect} from 'react';
import {DeviceEventEmitter, NativeModules} from 'react-native';
import QuickActions, {ShortcutItem} from 'react-native-quick-actions';
import {useNavigation} from '@react-navigation/native';
import useTypeSafeTranslation from './useTypeSafeTranslation';
import {BookingEntity} from '@requests';
import {
  useBookingsServiceBookingControllerGetCurrentBooking,
  useUsersServiceUserControllerGetUserKey,
  useUsersServiceUserControllerUpdateMe,
} from '@queries';
import {showErrorAlert} from '@utils/Tools';
import {IS_IOS, LANGUAGE, SHORT_CUT_ITEMS} from '@themes/Constants';
import {ETypeQuickAction} from '@themes/Enums';
import {useGlobalState, UserDataProps} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import {IShortcutItem} from '@global';

export const useQuickActions = () => {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const [userData, setUserData] = useGlobalState('userData');

  const {refetch} = useBookingsServiceBookingControllerGetCurrentBooking([], {
    enabled: false,
  });

  const {mutate: updateMe} = useUsersServiceUserControllerUpdateMe();

  const onUpdatePhoneNumber = () => {
    navigation.navigate('ActionModal', {
      cancelLabel: t('button.cancel'),
      confirmLabel: t('button.bookingNow'),
      content: t('booking.updatePhoneContent'),
      hasInput: true,
      onApply: phone => {
        updateMe(
          {
            requestBody: {
              phoneNumber: phone,
            },
          },
          {
            onSuccess: () => {
              navigation.goBack();
              queryClient.refetchQueries({
                queryKey: [useUsersServiceUserControllerGetUserKey],
                type: 'all',
              });
              setUserData({
                ...userData,
                user: {
                  ...userData?.user,
                  phoneNumber: phone,
                },
              } as UserDataProps);

              navigation.navigate('SelectDestination', {
                isTopBarEnable: false,
              });
            },
          },
        );
      },
      onCancel: () => {
        navigation.goBack();
      },
      placeholder: t('booking.updatePhonePlaceholder'),
      title: t('booking.updatePhone'),
      validateType: 'phone',
    });
  };

  const processShortcut = async (item: ShortcutItem) => {
    if (item) {
      const data = await AsyncStorage.getItem('REACT_QUERY_OFFLINE_CACHE');
      const userData = JSON.parse(String(data))?.clientState?.queries.find(
        (ele: {queryHash: string | string[]}) =>
          ele?.queryHash?.includes('userData'),
      )?.state?.data;

      if (item?.type === ETypeQuickAction.BookARide) {
        if (!userData?.accessToken) {
          return;
        }
        if (!userData?.user?.phoneNumber) {
          return onUpdatePhoneNumber();
        }

        setTimeout(() => {
          refetch()?.then(res => {
            if (
              res?.data?.data === null ||
              res?.data?.data?.status === BookingEntity.status.COMPLETED ||
              res?.data?.data?.status === BookingEntity.status.CANCELLED
            ) {
              return navigation.navigate('SelectDestination', {
                isSchedule: false,
                isTopBarEnable: false,
              });
            }
            return showErrorAlert({message: t('error.inAnotherBooking')});
          });
        }, 300);
      }
    }
  };

  const getDeviceLanguage = () => {
    const appLanguage = IS_IOS
      ? NativeModules.SettingsManager.settings.AppleLocale ||
        NativeModules.SettingsManager.settings.AppleLanguages[0]
      : NativeModules.I18nManager.localeIdentifier;

    const deviceLanguage =
      appLanguage.search(/-|_/g) !== -1 ? appLanguage.slice(0, 2) : appLanguage;
    if (['fr', 'en'].includes(deviceLanguage)) {
      return deviceLanguage;
    }
    return undefined;
  };

  const getLocalizedShortcutItems = (items: IShortcutItem[]): IShortcutItem[] =>
    items.map(item => ({
      ...item,
      title: getDeviceLanguage() == LANGUAGE.FR ? item.titleFr : item.titleEn,
    }));

  useEffect(() => {
    const localizedShortcutItems = getLocalizedShortcutItems(SHORT_CUT_ITEMS);
    QuickActions.setShortcutItems(localizedShortcutItems as ShortcutItem[]);
    QuickActions.popInitialAction().then(item => {
      if (item) {
        processShortcut(item);
      }
    });

    DeviceEventEmitter.addListener(
      'quickActionShortcut',
      (item: ShortcutItem) => {
        processShortcut(item);
      },
    );

    return () => {
      QuickActions.clearShortcutItems();
      DeviceEventEmitter.removeAllListeners();
    };
  }, []);
};
