import {useEffect, useState} from 'react';
import {AppState, AppStateStatus} from 'react-native';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {TZ_OTTAWA} from '@themes/Constants';

// Initialize dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

const EVENT_START = dayjs.tz('2025-05-20 00:00:00', TZ_OTTAWA).valueOf();
const EVENT_DURATION_DAYS = 40;
const MILLISECONDS_IN_DAY = 24 * 60 * 60 * 1000;
const EVENT_END = EVENT_START + EVENT_DURATION_DAYS * MILLISECONDS_IN_DAY;

export const useBirthdayEvent = () => {
  const [isHappening, setIsHappening] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  const calculateTimeRemaining = () => {
    const now = dayjs().tz(TZ_OTTAWA).valueOf();
    const isCurrentlyHappening = now >= EVENT_START && now < EVENT_END;
    setIsHappening(isCurrentlyHappening);

    if (isCurrentlyHappening) {
      const remainingTime = Math.max(0, EVENT_END - now);

      const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );
      const minutes = Math.floor(
        (remainingTime % (1000 * 60 * 60)) / (1000 * 60),
      );
      const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

      setTimeRemaining({days, hours, minutes, seconds});
    }
  };

  useEffect(() => {
    calculateTimeRemaining();

    const interval = setInterval(calculateTimeRemaining, 1000);

    const appStateSubscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (nextAppState === 'active') {
          calculateTimeRemaining();
        }
      },
    );

    return () => {
      clearInterval(interval);
      appStateSubscription.remove();
    };
  }, []);

  return {
    isHappening,
    timeRemaining,
  };
};
