/* eslint-disable max-lines */
/* eslint-disable react-hooks/exhaustive-deps */
import {useEffect, useState} from 'react';
// eslint-disable-next-line no-restricted-imports
import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import {Platform} from 'react-native';
import {getCouponValueTitle, getDiscountPrice} from '../Tools';
import useLoading from './useLoading';
import usePayment from './usePayment';
import useTypeSafeTranslation from './useTypeSafeTranslation';
import {TranslationKeys} from '@generated/translationKeys';
import {CouponProps} from '@global';
import {PopTo} from '@navigation/utils';
import {
  useBookingsServiceBookingControllerCreateBookingWithCode,
  useBookingsServiceBookingControllerCreateBookingWithPayment,
  useBookingsServiceBookingControllerCreateEstimation,
  useBookingsServiceBookingControllerGetBookingOfMeK<PERSON>,
  useBookingsServiceBookingControllerGetCurrentBooking<PERSON>ey,
  useUsersServiceEmployeeCodeControllerGetCodesOfUser,
  useUsersServiceUserControllerListCards,
  useUsersServiceUserControllerListCardsKey,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';
import {queryClient} from '@react-query/queryClient';
import {
  BookingEntity,
  CardEntity,
  CoordinatesDto,
  CouponEntity,
  CreateBookingWithCodeDto,
  CreateBookingWithPaymentDto,
  EmployeeCodeEntity,
  EstimateAmountEntity,
  LocationDto,
} from '@requests';
import {
  BOOKING_TYPE,
  CARS,
  CASH,
  FORMAT_DATE_TIME_PICKER,
  WALLET,
} from '@themes/Constants';
import MixPanelSdk from '@utils/mixPanelSdk';
import AppsFlyer from '@utils/appsFlyerSdk';
import {LOTTIE} from '@themes/Lottie';
import {ERRORS} from '@themes/Errors';
import {useBookingGlobalStatus} from 'src/contexts/BookingProvider';
import {ISelectedCar} from '@interface/booking';

const DEFAULT_DATE = moment().toISOString();

interface UseBookingProps {
  onBookCar?: (data: BookingEntity) => void;
  bookingType?: string;
}

const useBooking = ({onBookCar, bookingType}: UseBookingProps) => {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const {showLoading, dismissLoading} = useLoading();

  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [_, setCurrentBooking] = useGlobalState('currentBooking');
  const [userData] = useGlobalState('userData');
  const [_currentPayment, setCurrentPayment] = useGlobalState('currentPayment');
  const [selectedCar, setSelectedCar] = useState<ISelectedCar>(
    CARS?.[0] as ISelectedCar,
  );
  const [business, setBusiness] = useState<EmployeeCodeEntity | undefined>();
  const [coupon, setCoupon] = useState<CouponEntity | undefined>();
  const [card, setCard] = useState<CardEntity>();
  const [showWarningBanner, setShowWarningBanner] = useState(false);
  const [showWarningSchedule, setShowWarningSchedule] = useState(false);
  const [note, setNote] = useState<string>('');
  const [bookingId, setBookingId] = useState<string>('');
  const [tipAmount, setTipAmount] = useState<number | null>(null);
  const [showWarningPrice, setShowWarningPrice] = useState(false);
  const [showWarningFixedFare, setShowWarningFixedFare] = useState(false);
  const [showWarningRideUpdated, setShowWarningRideUpdated] = useState(false);

  const [displayCars, setDisplayCars] = useState<ISelectedCar[]>([]);
  const [hasSelectedInitialCar, setHasSelectedInitialCar] = useState(false);

  const {refetchAndUpdate} = useBookingGlobalStatus();

  const {
    data: listCard,
    isFetching,
    isLoading,
  } = useUsersServiceUserControllerListCards([
    useUsersServiceUserControllerListCardsKey,
  ]);

  const {
    data: listBusiness,
    isFetching: isFetchingBusiness,
    isLoading: isLoadingBusiness,
  } = useUsersServiceEmployeeCodeControllerGetCodesOfUser({
    limit: 1,
    offset: 0,
    order: 'createdAt:desc',
  });

  const onSetNote = (text: string) => {
    setNote(text);
  };

  useEffect(() => {
    setBusiness(listBusiness?.data?.items?.[0]);
  }, [isFetchingBusiness, listBusiness, isLoadingBusiness]);

  const handleBookingNavigation = (
    data: BookingEntity & {originalPrice: number},
    params?: CreateBookingWithCodeDto,
  ) => {
    if (params?.date) {
      setCurrentBooking({id: ''} as BookingEntity);
      navigation.navigate('Main');
    } else {
      onBookCar?.({...data, amount: data?.originalPrice || data?.amount});
      const eventValues = {
        carSelect: data?.vehicleType,
        email: data?.user?.email,
        phoneNumber: data?.user?.phoneNumber,
        rideValue: Number(data?.amount) / 100 || 0,
        userName: data?.user?.fullName,
      };
      MixPanelSdk.logEvent({
        eventName: 'booking_success',
        eventValues,
      });
      AppsFlyer.logEvent({
        eventName: 'booking_success',
        eventValues,
      });

      if (coupon?.value) {
        MixPanelSdk.logEvent({
          eventName: 'coupon_apply',
          eventValues: {
            ...(coupon?.title && {couponName: coupon?.title}),
            ...(coupon?.value && {
              couponValue: Number(coupon?.value) / 100,
            }),
            email: userData?.user?.email,
            phoneNumber: userData?.user?.phoneNumber,
            userName: userData?.user?.fullName,
          },
        });
      }
    }

    setTimeout(() => {
      refetchAndUpdate();
      queryClient.refetchQueries({
        queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
        type: 'all',
      });
    }, 3000);
    dismissLoading();
  };
  const {applePay, googlePay, payByCard, isWalletSupported} = usePayment({
    onSuccess: bookingData => {
      if (bookingData) {
        handleBookingNavigation(bookingData as any, bookingWithCodeParams);
      }
    },
  });

  useEffect(() => {
    if (!card) {
      const defaultPaymentMethod = listCard?.data.find(
        (ele: CardEntity) => ele.isDefault,
      );
      const walletData = isWalletSupported ? WALLET : undefined;
      setCard(defaultPaymentMethod || listCard?.data?.[0] || walletData);
    }
  }, [isFetching, listCard, isLoading, isWalletSupported]);

  const {data: bookingEstimation, mutate: createBookingEstimation} =
    useBookingsServiceBookingControllerCreateEstimation({
      onError(error: any) {
        if (error?.body?.message?.[0] === 'The location is not supported') {
          return PopTo(2);
        }
      },
      onSuccess: async (data: {
        data: {estimateAmounts: EstimateAmountEntity[]};
      }) => {
        const matchedEstimate = data?.data?.estimateAmounts?.find(
          item => item?.vehicleType === selectedCar?.vehicleType,
        );
        setSelectedCar({
          ...selectedCar,
          amount:
            data?.data?.estimateAmounts?.find(
              ele => ele?.vehicleType === (selectedCar?.id || CARS?.[0]?.id),
            )?.amount ?? 0,
          isFixedFare:
            matchedEstimate?.fixedFare?.isAllowCoupon === false || false,
          isScheduled: matchedEstimate?.isScheduled || false,
        });
      },
    });

  const {mutate: createBookingWithPayment} =
    useBookingsServiceBookingControllerCreateBookingWithPayment({
      onError: err => {
        priceHasChanged(err);
      },
      onSuccess: bookingWithPayment => {
        setCurrentBooking(bookingWithPayment?.data as BookingEntity);
      },
    });

  const {mutate: createBookingWithCode} =
    useBookingsServiceBookingControllerCreateBookingWithCode({
      onSuccess: bookingWithPayment => {
        setCurrentBooking(bookingWithPayment?.data as BookingEntity);
      },
    });

  useEffect(() => {
    if (
      bookingWithCodeParams?.destinationLocation &&
      bookingWithCodeParams?.originLocation
    ) {
      const {destinationLocation, originLocation, date} =
        bookingWithCodeParams || {};
      createBookingEstimation({
        requestBody: {
          ...(date && {
            date: moment(date).toISOString(),
          }),
          destination: destinationLocation?.geo,
          origin: originLocation?.geo,
        },
      });
    }
  }, [
    bookingWithCodeParams?.date,
    bookingWithCodeParams?.originLocation,
    bookingWithCodeParams?.destinationLocation,
  ]);

  const priceHasChanged = (error: any) => {
    if (error?.body?.messageCode === ERRORS.PRICE_HAS_CHANGED) {
      navigation.navigate('ActionModal', {
        confirmLabel: t('button.gotIt'),
        content: t('booking.priceUpdateDesc'),
        enableCloseOnMask: false,
        hasCancelBtn: false,
        iconSize: 100,
        lottieJson: LOTTIE.reload,
        onApply: () => {
          navigation.goBack();
          const {destinationLocation, originLocation, date} =
            bookingWithCodeParams || {};
          createBookingEstimation({
            requestBody: {
              ...(date && {
                date: moment(date).toISOString(),
              }),
              destination: destinationLocation?.geo as CoordinatesDto,
              origin: originLocation?.geo as CoordinatesDto,
            },
          });
        },
        title: t('booking.priceUpdateTitle'),
      });
    }
  };

  const checkAvailableCoupon = () => {
    if (!coupon) {
      return true;
    }
    if (
      !coupon?.vehicleTypes?.includes(
        selectedCar?.id as BookingEntity.vehicleType,
      ) &&
      !coupon?.vehicleTypes?.includes(BookingEntity.vehicleType.ALL)
    ) {
      return false;
    }
    if (card?.id === CASH.id) {
      return false;
    }
    const paymentType =
      card?.id === CASH.id
        ? CreateBookingWithPaymentDto.paymentMethodType.CASH
        : CreateBookingWithPaymentDto.paymentMethodType.CARD;
    if (
      !coupon?.paymentMethods?.includes('ALL') &&
      !coupon?.paymentMethods?.includes(paymentType)
    ) {
      return false;
    }

    return true;
  };

  const isAvailable = checkAvailableCoupon();

  useEffect(() => {
    setShowWarningBanner(!isAvailable);
    const unsubscribe = setTimeout(() => {
      setShowWarningBanner(false);
    }, 5500);

    return () => {
      clearTimeout(unsubscribe);
    };
  }, [card, selectedCar, coupon]);

  useEffect(() => {
    setShowWarningFixedFare(selectedCar?.isFixedFare || false);
    if (selectedCar?.isFixedFare) {
      setCoupon(undefined);
    }
    const unsubscribe = setTimeout(() => {
      setShowWarningFixedFare(false);
    }, 5500);

    return () => {
      clearTimeout(unsubscribe);
    };
  }, [selectedCar?.id, selectedCar?.isFixedFare]);

  useEffect(() => {
    setShowWarningSchedule(selectedCar?.isScheduled || false);
    const unsubscribe = setTimeout(() => {
      setShowWarningSchedule(false);
    }, 5500);

    return () => {
      clearTimeout(unsubscribe);
    };
  }, [selectedCar.isScheduled]);

  // set display cars
  useEffect(() => {
    if (bookingEstimation?.data) {
      const {estimateAmounts} = bookingEstimation?.data || {};

      const availableCars = CARS.map(car => {
        const matchedEstimate = estimateAmounts?.find(
          item => item?.vehicleType === car?.vehicleType,
        );
        if (matchedEstimate) {
          return {
            ...car,
            isFixedFare: matchedEstimate?.fixedFare?.isAllowCoupon === false,
            isScheduled: matchedEstimate?.isScheduled || false,
          };
        }
        return null;
      }).filter(Boolean) as ISelectedCar[];

      setDisplayCars(availableCars);
    }
  }, [bookingEstimation?.data?.estimateAmounts]);

  // set initial car
  useEffect(() => {
    if (hasSelectedInitialCar || !displayCars?.length) return;

    const selectedCar =
      displayCars.find(car => car?.id === bookingWithCodeParams?.vehicleType) ??
      displayCars[0];

    if (!selectedCar) return;

    const estimate = bookingEstimation?.data?.estimateAmounts?.find(
      ele => ele?.vehicleType === selectedCar.id,
    );

    setSelectedCar({
      ...selectedCar,
      amount: estimate?.amount,
    });

    const carIsScheduled = selectedCar?.isScheduled || estimate?.isScheduled;
    if (carIsScheduled) {
      setShowWarningSchedule(true);
      setTimeout(() => {
        setShowWarningSchedule(false);
      }, 5000);

      const bookingTime = moment(
        bookingWithCodeParams?.date,
        FORMAT_DATE_TIME_PICKER,
      );
      const currentTimePlus24h = moment().add(24, 'hours');

      if (!bookingTime.isValid() || bookingTime.isBefore(currentTimePlus24h)) {
        const updatedParams = {
          ...bookingWithCodeParams,
          date: currentTimePlus24h.format(FORMAT_DATE_TIME_PICKER),
        };
        setBookingWithCodeParams(updatedParams as CreateBookingWithCodeDto);
      }
    }

    setHasSelectedInitialCar(true);
  }, [
    hasSelectedInitialCar,
    displayCars,
    bookingWithCodeParams?.vehicleType,
    bookingWithCodeParams?.date,
    bookingEstimation?.data?.estimateAmounts,
  ]);

  const onBook = () => {
    if (bookingType === BOOKING_TYPE.PERSONAL) {
      if (!card) {
        return navigation.navigate('ActionModal', {
          cancelLabel: t('button.discard'),
          confirmBtnStyle: {flex: 2},
          confirmLabel: t('button.addPaymentMethod'),
          content: t('booking.cardPaymentShouldUsedContent'),
          enableCloseOnMask: false,
          onApply: () => {
            navigation.goBack();
            goToPaymentList();
          },
          title: t('booking.cardPaymentShouldUsed'),
        });
      }
      setCurrentPayment(card);

      createBookingWithPayment(
        {
          requestBody: {
            ...(coupon?.id && isAvailable && {couponId: coupon?.id}),
            amount: selectedCar?.amount || 0,
            date: bookingWithCodeParams?.date
              ? moment(bookingWithCodeParams?.date).toISOString()
              : undefined,
            destinationLocation:
              bookingWithCodeParams?.destinationLocation as LocationDto,
            ...(note && {note}),
            originLocation:
              bookingWithCodeParams?.originLocation as LocationDto,
            paymentMethodType:
              card?.id === CASH.id
                ? CreateBookingWithPaymentDto.paymentMethodType.CASH
                : CreateBookingWithPaymentDto.paymentMethodType.CARD,
            tipAmount: tipAmount ?? null,
            vehicleType:
              selectedCar?.vehicleType as CreateBookingWithPaymentDto.vehicleType,
          },
        },
        {
          onError: (error: any) => {
            const {destinationLocation, originLocation, date} =
              bookingWithCodeParams || {};
            createBookingEstimation({
              requestBody: {
                ...(date && {
                  date: moment(date).toISOString(),
                }),
                destination: destinationLocation?.geo as CoordinatesDto,
                origin: originLocation?.geo as CoordinatesDto,
              },
            });
            if (error?.body?.message?.[0] === 'Coupon not found') {
              setCoupon(undefined);
            }

            if (error?.body?.messageCode === ERRORS.VEHICLE_TYPE_NOT_FOUND) {
              setShowWarningRideUpdated(true);
              setTimeout(() => {
                setShowWarningRideUpdated(false);
              }, 5000);
            }
            if (error?.body?.messageCode === ERRORS.INVALID_SCHEDULED_TIME) {
              setShowWarningRideUpdated(true);
              setTimeout(() => {
                setShowWarningRideUpdated(false);
              }, 5000);
              setBookingWithCodeParams({
                ...bookingWithCodeParams,
                date: moment().add(1, 'day').format(FORMAT_DATE_TIME_PICKER),
              } as CreateBookingWithCodeDto);
            }
          },
          onSuccess: async res => {
            const lastAmount = getDiscountPrice(
              selectedCar?.amount || 0,
              coupon as CouponEntity,
            );

            if (lastAmount > 0 && card?.id !== CASH.id) {
              const {paymentIntent} = res?.data?.paymentData || {};

              const calculateAmount = {
                ...res?.data,
                amount: getLastAmount,
                originalPrice: res?.data?.amount,
              } as BookingEntity & {originalPrice: number};
              if (card?.id === WALLET.id) {
                if (Platform.OS === 'ios') {
                  return applePay(
                    paymentIntent,
                    `${t(selectedCar?.name as TranslationKeys)}`,
                    calculateAmount,
                  );
                }
                return googlePay(paymentIntent, calculateAmount);
              }
              showLoading();
              payByCard(paymentIntent, card?.id, calculateAmount);
            } else {
              handleBookingNavigation(
                res?.data as BookingEntity & {
                  originalPrice: number;
                },
                bookingWithCodeParams,
              );
            }
          },
        },
      );
    } else {
      if (!business) {
        return navigation.navigate('ActionModal', {
          cancelLabel: t('button.discard'),
          confirmLabel: t('button.addBusiness'),
          content: t('booking.businessAdditionRequiredContent'),
          enableCloseOnMask: false,
          onApply: () => {
            navigation.goBack();
            goToBusinessList();
          },
          title: t('booking.businessAdditionRequired'),
        });
      }
      createBookingWithCode(
        {
          requestBody: {
            ...(coupon?.id && {couponId: coupon?.id}),
            ...bookingWithCodeParams,
            amount: selectedCar?.amount,
            codeId: business?.code?.id || bookingWithCodeParams?.codeId,
            date: bookingWithCodeParams?.date
              ? moment(bookingWithCodeParams?.date).toISOString()
              : undefined,
            ...(note && {note}),
            vehicleType: selectedCar?.vehicleType,
          } as CreateBookingWithCodeDto,
        },
        {
          onError: (error: any) => {
            if (error?.body?.message?.[0] === 'Coupon not found') {
              setCoupon(undefined);
            }
          },
          onSuccess: ({data: bookingWithCodeRes}) => {
            queryClient.refetchQueries({
              queryKey: [
                useBookingsServiceBookingControllerGetCurrentBookingKey,
              ],
              type: 'all',
            });

            if (bookingWithCodeParams?.date) {
              navigation.navigate('Main');
            } else {
              onBookCar?.(bookingWithCodeRes as BookingEntity);
            }
          },
        },
      );
    }
  };

  const goToPaymentList = () => {
    navigation.navigate('ListPayment', {
      defaultPayment: card,
      isFromBooking: true,
      isTopBarEnable: false,
      onApply: (res: CardEntity) => {
        navigation.goBack();
        setCard(res);
        /*
         * if (res.id === CASH.id) {
         *   setCoupon(undefined);
         * }
         */
      },
    });
  };

  const goToBusinessList = () => {
    navigation.navigate('ListBusiness', {
      headerTitle: t('business.selectYourBusiness'),
      isFromBooking: true,
      isTopBarEnable: false,
      onApplyCode: businessRes => {
        navigation.goBack();
        setBusiness(businessRes);
      },
      selectedBusiness: business,
    });
  };

  const couponValueTitle = getCouponValueTitle(coupon as CouponEntity);

  const onGoToCouponList = () => {
    navigation.navigate('Coupon', {
      bookingParams: {
        amount: selectedCar?.amount,
        bookingType: 'PERSONAL',
        date: bookingWithCodeParams?.date
          ? moment(bookingWithCodeParams?.date).toISOString()
          : DEFAULT_DATE,
        destinationLatitude:
          bookingWithCodeParams?.destinationLocation?.geo?.latitude,
        destinationLongitude:
          bookingWithCodeParams?.destinationLocation?.geo?.longitude,
        note,
        originLatitude: bookingWithCodeParams?.originLocation?.geo?.latitude,
        originLongitude: bookingWithCodeParams?.originLocation?.geo?.longitude,
        paymentMethod:
          card?.id === CASH.id
            ? CreateBookingWithPaymentDto.paymentMethodType.CASH
            : CreateBookingWithPaymentDto.paymentMethodType.CARD,
        vehicleType:
          selectedCar?.vehicleType as CreateBookingWithPaymentDto.vehicleType,
      } as CouponProps['bookingParams'],
      coupon: {...coupon, isValid: isAvailable} as CouponEntity,
      isDisabled: selectedCar?.isFixedFare,
      isFromBooking: true,
      isTopBarEnable: false,
      onApply: (item?: CouponEntity) => {
        navigation.goBack();
        setCoupon(item);
        MixPanelSdk.logEvent({
          eventName: 'coupon_redeem',
          eventValues: {
            ...(item?.title && {couponName: item?.title}),
            ...(item?.value && {
              couponValue: Number(item?.value) / 100,
            }),
            email: userData?.user?.email,
            phoneNumber: userData?.user?.phoneNumber,
            userName: userData?.user?.fullName,
          },
        });
      },
    });
  };

  const onGotoBeforeTip = () => {
    navigation.navigate('BeforeTipModal', {
      amount: selectedCar?.amount || 0,
      isTopBarEnable: false,
      onApply: (amount: number) => {
        setTipAmount(amount);
        navigation.goBack();
      },
      valueDefault: tipAmount as number,
    });
  };

  const afterDiscount = getDiscountPrice(
    selectedCar?.amount || 0,
    coupon as CouponEntity,
  );

  const getLastAmount =
    (afterDiscount < 0
      ? 0
      : getDiscountPrice(selectedCar?.amount || 0, coupon as CouponEntity)) +
    Number(tipAmount);

  useEffect(() => {
    if (tipAmount) {
      setShowWarningPrice(true);
      setTimeout(() => {
        setShowWarningPrice(false);
      }, 5000);
    }

    if (afterDiscount !== selectedCar?.amount) {
      setShowWarningPrice(true);
      setTimeout(() => {
        setShowWarningPrice(false);
      }, 5000);
    }
  }, [tipAmount, afterDiscount, selectedCar?.amount]);

  return {
    bookingEstimation,
    bookingId,
    business,
    card,
    coupon,
    couponValueTitle,
    displayCars,
    getLastAmount,
    goToBusinessList,
    goToPaymentList,
    isAvailable,
    note,
    onBook,
    onGoToCouponList,
    onGotoBeforeTip,
    onSetNote,
    selectedCar,
    setBookingId,
    setCoupon,
    setSelectedCar,
    showWarningBanner,
    showWarningFixedFare,
    showWarningPrice,
    showWarningRideUpdated,
    showWarningSchedule,
    tipAmount,
  };
};

export default useBooking;
