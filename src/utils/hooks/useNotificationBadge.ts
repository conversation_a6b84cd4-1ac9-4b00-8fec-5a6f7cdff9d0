import {useEffect} from 'react';
import notifee from '@notifee/react-native';
import {
  useNotificationsServiceNotificationControllerTotalUnseenNotifications,
  useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
} from '@queries';
import {useGlobalState} from '@react-query/clientStateManage';

export const useNotificationBadge = () => {
  const [userData] = useGlobalState('userData');
  const {data} =
    useNotificationsServiceNotificationControllerTotalUnseenNotifications(
      [
        useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
      ],
      {
        enabled: !!userData?.accessToken,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
      },
    );

  useEffect(() => {
    if (data?.data) {
      const general = data.data.general ?? 0;
      const promotion = data.data.promotion ?? 0;
      const badgeCount = general + promotion;
      notifee.setBadgeCount(badgeCount);
    }
  }, [data]);

  return null;
};
