import {Mixpanel} from 'mixpanel-react-native';
import Config from 'react-native-config';
import appsFlyer from 'react-native-appsflyer';
import {EStatusMixpanel} from '@themes/Enums';

const trackAutomaticEvents = false;
const mixpanel = new Mixpanel(
  Config?.MIXPANEL_API_KEY || '',
  trackAutomaticEvents,
);
mixpanel.init();

let mediaSource = '';

const MixPanelSdk = {
  async initAppsFlyer() {
    try {
      const distinctID = await mixpanel.getDistinctId();

      await appsFlyer.initSdk({
        appId: Config?.APPSFLYER_APP_ID || '',
        devKey: Config?.APPSFLYER_DEV_KEY || '',
        isDebug: false,
        onDeepLinkListener: true,
        onInstallConversionDataListener: true,
        timeToWaitForATTUserAuthorization: 10,
      });

      appsFlyer.setCustomerUserId(distinctID);

      appsFlyer.onInstallConversionData(res => {
        if (res) {
          if (res.data.af_status === EStatusMixpanel.NonOrganic) {
            mediaSource = res?.data?.media_source;
          } else {
            mediaSource = res?.data?.af_status;
          }
        }
      });
    } catch (error) {
      // error
    }
  },

  async logEvent({eventName = '', eventValues = {}}) {
    try {
      mixpanel.track(eventName, {...eventValues, source: mediaSource});
    } catch (error) {
      // error
    }
  },
};

export default MixPanelSdk;
