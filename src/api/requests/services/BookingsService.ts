/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelBookingDto } from '../models/CancelBookingDto';
import type { CheckWorkingAreaDto } from '../models/CheckWorkingAreaDto';
import type { CreateBookingWithCodeDto } from '../models/CreateBookingWithCodeDto';
import type { CreateBookingWithPaymentDto } from '../models/CreateBookingWithPaymentDto';
import type { CreateEstimationDto } from '../models/CreateEstimationDto';
import type { CreateReviewDto } from '../models/CreateReviewDto';
import type { IBaseResponse } from '../models/IBaseResponse';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BookingsService {

  /**
   * Create a estimation for booking
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerCreateEstimation(
    requestBody: CreateEstimationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/bookings/estimation',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Create a booking with an employee of the business
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerCreateBookingWithCode(
    requestBody: CreateBookingWithCodeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/bookings/with-code',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Create a personal booking with payment
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerCreateBookingWithPayment(
    requestBody: CreateBookingWithPaymentDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/bookings/payment',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get nearby vehicles
   * @param latitude
   * @param longitude
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetNearbyVehicles(
    latitude: number,
    longitude: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings/nearby-vehicles',
      query: {
        'latitude': latitude,
        'longitude': longitude,
      },
    });
  }

  /**
   * Get a booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBooking(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get a public booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBookingPublic(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings/{id}/public',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get current location of a booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetCurrentLocation(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings/{id}/current-locations',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Review a booking
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerReviewBooking(
    id: string,
    requestBody: CreateReviewDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/bookings/{id}/review',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Cancel a booking
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerCancelBooking(
    id: string,
    requestBody: CancelBookingDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/bookings/{id}/cancel',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of booking of me
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param q
   * @param status
   * @param statuses
   * @param filterType
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBookingOfMe(
    limit: number = 10,
    offset: number,
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    status?: 'DRAFT' | 'SCHEDULED' | 'DEMAND_CREATION' | 'TO_BE_DISTRIBUTED' | 'CONFIRMED_ADDRESS' | 'AWAITING_CONFIRMED_VEHICLE' | 'VEHICLE_CONFIRMED' | 'ARRIVAL_AT_CLIENT' | 'CLIENT_ON_BOARD' | 'AMOUNT_MONEY_RECEIVED' | 'COMPLETED' | 'NO_LOAD' | 'CANCELLATION_REQUEST' | 'CANCELLED' | null,
    statuses?: string,
    filterType?: 'CURRENT_BOOKING' | 'COMPLETED' | 'CANCELLED' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/bookings',
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'status': status,
        'statuses': statuses,
        'filterType': filterType,
      },
    });
  }

  /**
   * Get current booking of a user
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetCurrentBooking(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/current-bookings',
    });
  }

  /**
   * Get list of booking of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param q
   * @param status
   * @param departmentId
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBookingOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    status?: string | null,
    departmentId?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/bookings',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'status': status,
        'departmentId': departmentId,
        'order': order,
      },
    });
  }

  /**
   * Get list of bookings of a code
   * @param id
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param q
   * @param status
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBookingsOfCode(
    id: string,
    limit: number = 10,
    offset: number,
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    status?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/codes/{id}/bookings',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'status': status,
        'order': order,
      },
    });
  }

  /**
   * Check working areas
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerCheckWorkingArea(
    requestBody: CheckWorkingAreaDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/bookings/check-working-areas',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get receipt file of a booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBookingReceipt(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/{id}/receipt',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get admin receipt file of a booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingControllerGetBookingReceiptAdmin(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/{id}/admin-receipt',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get list of booking of a user
   * @param id
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param q
   * @param status
   * @param statuses
   * @param filterType
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetBookingOfUser(
    id: string,
    limit: number = 10,
    offset: number,
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    status?: 'DRAFT' | 'SCHEDULED' | 'DEMAND_CREATION' | 'TO_BE_DISTRIBUTED' | 'CONFIRMED_ADDRESS' | 'AWAITING_CONFIRMED_VEHICLE' | 'VEHICLE_CONFIRMED' | 'ARRIVAL_AT_CLIENT' | 'CLIENT_ON_BOARD' | 'AMOUNT_MONEY_RECEIVED' | 'COMPLETED' | 'NO_LOAD' | 'CANCELLATION_REQUEST' | 'CANCELLED' | null,
    statuses?: string,
    filterType?: 'CURRENT_BOOKING' | 'COMPLETED' | 'CANCELLED' | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/{id}/bookings',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'status': status,
        'statuses': statuses,
        'filterType': filterType,
        'order': order,
      },
    });
  }

  /**
   * Get statistics of a user
   * @param id
   * @param fromDate
   * @param toDate
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetStatisticsOfUser(
    id: string,
    fromDate: string,
    toDate: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/{id}/statistics',
      path: {
        'id': id,
      },
      query: {
        'fromDate': fromDate,
        'toDate': toDate,
      },
    });
  }

  /**
   * Get statistics of a business
   * @param businessId
   * @param fromDate
   * @param toDate
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetStatisticsOfBusiness(
    businessId: string,
    fromDate: string,
    toDate: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/statistics',
      path: {
        'businessId': businessId,
      },
      query: {
        'fromDate': fromDate,
        'toDate': toDate,
      },
    });
  }

  /**
   * Get list of bookings
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param q
   * @param type
   * @param businessIds
   * @param statuses
   * @param vehicleTypes
   * @param paymentMethodType
   * @param refundTag
   * @param reasons
   * @param zones
   * @param vehicleIds
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetBookings(
    limit: number = 10,
    offset: number,
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    type?: string | null,
    businessIds?: string,
    statuses?: string,
    vehicleTypes?: string,
    paymentMethodType?: any,
    refundTag?: any,
    reasons?: string,
    zones?: string,
    vehicleIds?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings',
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'type': type,
        'businessIds': businessIds,
        'statuses': statuses,
        'vehicleTypes': vehicleTypes,
        'paymentMethodType': paymentMethodType,
        'refundTag': refundTag,
        'reasons': reasons,
        'zones': zones,
        'vehicleIds': vehicleIds,
        'order': order,
      },
    });
  }

  /**
   * Get list of bookings
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param q
   * @param type
   * @param businessIds
   * @param statuses
   * @param vehicleTypes
   * @param paymentMethodType
   * @param refundTag
   * @param reasons
   * @param zones
   * @param vehicleIds
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetBookingsFilter(
    limit: number = 10,
    offset: number,
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    type?: string | null,
    businessIds?: string,
    statuses?: string,
    vehicleTypes?: string,
    paymentMethodType?: any,
    refundTag?: any,
    reasons?: string,
    zones?: string,
    vehicleIds?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings-filter',
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'type': type,
        'businessIds': businessIds,
        'statuses': statuses,
        'vehicleTypes': vehicleTypes,
        'paymentMethodType': paymentMethodType,
        'refundTag': refundTag,
        'reasons': reasons,
        'zones': zones,
        'vehicleIds': vehicleIds,
        'order': order,
      },
    });
  }

  /**
   * Export booking history
   * @param fromDate
   * @param toDate
   * @param q
   * @param type
   * @param businessIds
   * @param statuses
   * @param vehicleTypes
   * @param paymentMethodType
   * @param refundTag
   * @param reasons
   * @param zones
   * @param vehicleIds
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerExportBookings(
    fromDate?: string | null,
    toDate?: string | null,
    q?: string | null,
    type?: string | null,
    businessIds?: string,
    statuses?: string,
    vehicleTypes?: string,
    paymentMethodType?: any,
    refundTag?: any,
    reasons?: string,
    zones?: string,
    vehicleIds?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings-exports',
      query: {
        'fromDate': fromDate,
        'toDate': toDate,
        'q': q,
        'type': type,
        'businessIds': businessIds,
        'statuses': statuses,
        'vehicleTypes': vehicleTypes,
        'paymentMethodType': paymentMethodType,
        'refundTag': refundTag,
        'reasons': reasons,
        'zones': zones,
        'vehicleIds': vehicleIds,
        'order': order,
      },
    });
  }

  /**
   * Get list of booking of employee
   * @param id
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetBookingOfEmployee(
    id: string,
    limit: number = 10,
    offset: number,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/employees/{id}/bookings',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Get trips of a booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetTrips(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings/{id}/trips',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get progresses of a booking
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bookingSuperAdminControllerGetBookingProgresses(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/bookings/{id}/progresses',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get a booking by encrypted ID
   * @param encryptedId
   * @returns any
   * @throws ApiError
   */
  public static bookingSharingControllerGetBookingByEncryptedId(
    encryptedId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/booking-sharing/encrypted',
      query: {
        'encryptedId': encryptedId,
      },
    });
  }

  /**
   * Get current location of a booking by encrypted ID
   * @param encryptedId
   * @returns any
   * @throws ApiError
   */
  public static bookingSharingControllerGetCurrentLocationByEncryptedId(
    encryptedId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/booking-sharing/encrypted/current-locations',
      query: {
        'encryptedId': encryptedId,
      },
    });
  }

}
