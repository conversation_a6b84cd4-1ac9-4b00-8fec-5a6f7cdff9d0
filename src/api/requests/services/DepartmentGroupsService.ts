/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateDepartmentGroupDto } from '../models/CreateDepartmentGroupDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateDepartmentGroupDto } from '../models/UpdateDepartmentGroupDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class DepartmentGroupsService {

  /**
   * Get list of group of a department
   * @param departmentId
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static departmentGroupControllerGetGroupsOfDepartment(
    departmentId: string,
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/departments/{departmentId}/groups',
      path: {
        'departmentId': departmentId,
      },
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Create a department group
   * @param departmentId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentGroupControllerCreateDepartmentGroup(
    departmentId: string,
    requestBody: CreateDepartmentGroupDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/departments/{departmentId}/groups',
      path: {
        'departmentId': departmentId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update a department group
   * @param id
   * @param departmentId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentGroupControllerUpdateDepartmentGroup(
    id: string,
    departmentId: string,
    requestBody: UpdateDepartmentGroupDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/departments/{departmentId}/groups/{id}',
      path: {
        'id': id,
        'departmentId': departmentId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a department group
   * @param id
   * @param departmentId
   * @returns any
   * @throws ApiError
   */
  public static departmentGroupControllerDeleteDepartmentGroup(
    id: string,
    departmentId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/departments/{departmentId}/groups/{id}',
      path: {
        'id': id,
        'departmentId': departmentId,
      },
    });
  }

}
