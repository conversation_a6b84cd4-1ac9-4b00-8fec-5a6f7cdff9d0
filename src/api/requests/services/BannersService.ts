/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { BannerParamDto } from '../models/BannerParamDto';
import type { DisplayBannerDto } from '../models/DisplayBannerDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateStatusBannerDto } from '../models/UpdateStatusBannerDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BannersService {

  /**
   * Get list value of in app navigation
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerGetListInAppNavigationValue(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/banners/target',
    });
  }

  /**
   * Create a new banner
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerCreateBanner(
    requestBody: BannerParamDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/banners',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list banner
   * @param limit
   * @param offset
   * @param q
   * @param isActive
   * @param isDisplay
   * @param navigationType
   * @param createdById
   * @param fromDate
   * @param toDate
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerGetListBanner(
    limit: number = 10,
    offset: number,
    q?: string | null,
    isActive?: boolean | null,
    isDisplay?: boolean | null,
    navigationType?: 'URL' | 'IN_APP' | null,
    createdById?: string | null,
    fromDate?: string | null,
    toDate?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/banners',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'isActive': isActive,
        'isDisplay': isDisplay,
        'navigationType': navigationType,
        'createdById': createdById,
        'fromDate': fromDate,
        'toDate': toDate,
        'order': order,
      },
    });
  }

  /**
   * Update banner displayed
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerUpdateDisplayBanner(
    requestBody: Array<DisplayBannerDto>,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/banners/display',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update status banner
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerUpdateBannerStatus(
    id: string,
    requestBody: UpdateStatusBannerDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/banners/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a banner
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerDeleteBanner(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/banners/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update banner content
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerUpdateBanner(
    id: string,
    requestBody: BannerParamDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/banners/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get banner detail
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static bannerAdminControllerGetBannerDetail(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/banners/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get banner displayed of the current user
   * @returns any
   * @throws ApiError
   */
  public static bannerControllerGetBannerDisplayed(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/user-banners',
    });
  }

}
