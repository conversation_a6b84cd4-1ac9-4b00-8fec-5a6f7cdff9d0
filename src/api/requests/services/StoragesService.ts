/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateUploadExcelPresignedUrlDto } from '../models/CreateUploadExcelPresignedUrlDto';
import type { CreateUploadPresignedUrlDto } from '../models/CreateUploadPresignedUrlDto';
import type { IBaseResponse } from '../models/IBaseResponse';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class StoragesService {

  /**
   * Create a Presigned URL to upload file
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static storageControllerGenerateS3PresignedUrl(
    requestBody: CreateUploadPresignedUrlDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/storages/upload-presigned-url',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Create a Presigned URL to upload Excel file
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static storageControllerGenerateS3PresignedUrlForExcel(
    requestBody: CreateUploadExcelPresignedUrlDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/storages/upload-presigned-url-excel',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
