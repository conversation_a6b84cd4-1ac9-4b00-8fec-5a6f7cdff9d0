/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateDepartmentRoleDto } from '../models/CreateDepartmentRoleDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateDepartmentRoleDto } from '../models/UpdateDepartmentRoleDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class DepartmentRolesService {

  /**
   * Get list of role of a group
   * @param departmentGroupId
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static departmentRoleControllerGetRolesOfDepartmentGroup(
    departmentGroupId: string,
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/department-groups/{departmentGroupId}/roles',
      path: {
        'departmentGroupId': departmentGroupId,
      },
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Create a department role
   * @param departmentGroupId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentRoleControllerCreateDepartmentRole(
    departmentGroupId: string,
    requestBody: CreateDepartmentRoleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/department-groups/{departmentGroupId}/roles',
      path: {
        'departmentGroupId': departmentGroupId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update a department role
   * @param id
   * @param departmentGroupId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentRoleControllerUpdateDepartmentRole(
    id: string,
    departmentGroupId: string,
    requestBody: UpdateDepartmentRoleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/department-groups/{departmentGroupId}/roles/{id}',
      path: {
        'id': id,
        'departmentGroupId': departmentGroupId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a department role
   * @param id
   * @param departmentGroupId
   * @returns any
   * @throws ApiError
   */
  public static departmentRoleControllerDeleteDepartmentRole(
    id: string,
    departmentGroupId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/department-groups/{departmentGroupId}/roles/{id}',
      path: {
        'id': id,
        'departmentGroupId': departmentGroupId,
      },
    });
  }

}
