/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateRefundPaymentDto } from '../models/CreateRefundPaymentDto';
import type { CreateSupportTicketDto } from '../models/CreateSupportTicketDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { ResolveTicketDto } from '../models/ResolveTicketDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class SupportTicketService {

  /**
   * Create support ticket from user
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static supportTicketControllerCreateSupportTicket(
    requestBody: CreateSupportTicketDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/support-tickets',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list support ticket
   * @param limit
   * @param offset
   * @param q
   * @param status
   * @param issueType
   * @param resolvedById
   * @param bookingId
   * @param solvedDateFrom
   * @param solvedDateTo
   * @param receivedDateFrom
   * @param receivedDateTo
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static supportTicketAdminControllerGetListSupportTicket(
    limit: number = 10,
    offset: number,
    q?: string | null,
    status?: 'OPEN' | 'CLOSED' | null,
    issueType?: 'PAYMENT_ISSUE' | 'LOST_ITEM' | 'OTHER' | null,
    resolvedById?: string | null,
    bookingId?: string,
    solvedDateFrom?: string | null,
    solvedDateTo?: string | null,
    receivedDateFrom?: string | null,
    receivedDateTo?: string | null,
    order?: string | null,
  ): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/support-tickets',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'status': status,
        'issueType': issueType,
        'resolvedById': resolvedById,
        'bookingId': bookingId,
        'solvedDateFrom': solvedDateFrom,
        'solvedDateTo': solvedDateTo,
        'receivedDateFrom': receivedDateFrom,
        'receivedDateTo': receivedDateTo,
        'order': order,
      },
    });
  }

  /**
   * Get list of issue reason
   * @returns any
   * @throws ApiError
   */
  public static supportTicketControllerGetListOfIssueReason(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/support-tickets/issue-type',
    });
  }

  /**
   * Get list support progress of booking
   * @param bookingId
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static supportTicketAdminControllerGetListSupportProgressOfBooking(
    bookingId: string,
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/support-tickets/progress/{bookingId}',
      path: {
        'bookingId': bookingId,
      },
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Create refund payment
   * @param bookingId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static supportTicketAdminControllerCreateRefundPayment(
    bookingId: string,
    requestBody: CreateRefundPaymentDto,
  ): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/support-tickets/refund/{bookingId}',
      path: {
        'bookingId': bookingId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * get support ticket detail
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static supportTicketAdminControllerGetSupportTicketDetail(
    id: string,
  ): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/support-tickets/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * mark ticket as resolved
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static supportTicketAdminControllerResolveTicket(
    id: string,
    requestBody: ResolveTicketDto,
  ): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/support-tickets/resolve/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
