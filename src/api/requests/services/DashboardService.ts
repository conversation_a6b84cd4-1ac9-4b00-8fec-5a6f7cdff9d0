/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { IBaseResponse } from '../models/IBaseResponse';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class DashboardService {

  /**
   * Summary the overall dashboard data
   * @param startTime
   * @param endTime
   * @param bookingType
   * @param zone
   * @param filterType
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetOverallDashBoard(
    startTime: string,
    endTime: string,
    bookingType?: 'PERSONAL' | 'BUSINESS' | null,
    zone?: string | null,
    filterType?: 'LAST_1_HOUR' | 'LAST_6_HOURS' | 'TODAY' | 'YESTERDAY' | 'LAST_4_WEEKS' | 'LAST_3_MONTHS' | 'LAST_12_MONTHS' | 'ALL_TIME' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/overalls',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'bookingType': bookingType,
        'zone': zone,
        'filterType': filterType,
      },
    });
  }

  /**
   * Summary the overall dashboard data of business
   * @param startTime
   * @param endTime
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetOverallDashBoardOfBusiness(
    startTime: string,
    endTime: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/businesses/{businessId}/overalls',
      path: {
        'businessId': businessId,
      },
      query: {
        'startTime': startTime,
        'endTime': endTime,
      },
    });
  }

  /**
   * Get top business
   * @param startTime
   * @param endTime
   * @param limit
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetTopBusiness(
    startTime: string,
    endTime: string,
    limit: number = 5,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/top-businesses',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'limit': limit,
      },
    });
  }

  /**
   * Get top users
   * @param startTime
   * @param endTime
   * @param limit
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetTopUsers(
    startTime: string,
    endTime: string,
    limit: number = 5,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/top-users',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'limit': limit,
      },
    });
  }

  /**
   * Get the last bookings
   * @param startTime
   * @param endTime
   * @param bookingType
   * @param limit
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetLastBookings(
    startTime: string,
    endTime: string,
    bookingType: any,
    limit: number = 5,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/last-bookings',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'limit': limit,
        'bookingType': bookingType,
      },
    });
  }

  /**
   * Get the booking charts
   * @param startTime
   * @param endTime
   * @param bookingType
   * @param zone
   * @param filterType
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetReportBookingChart(
    startTime: string,
    endTime: string,
    bookingType?: 'PERSONAL' | 'BUSINESS' | null,
    zone?: string | null,
    filterType?: 'LAST_1_HOUR' | 'LAST_6_HOURS' | 'TODAY' | 'YESTERDAY' | 'LAST_4_WEEKS' | 'LAST_3_MONTHS' | 'LAST_12_MONTHS' | 'ALL_TIME' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/charts/booking',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'bookingType': bookingType,
        'zone': zone,
        'filterType': filterType,
      },
    });
  }

  /**
   * Get the revenue charts
   * @param startTime
   * @param endTime
   * @param bookingType
   * @param zone
   * @param filterType
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetReportBookingRevenueChart(
    startTime: string,
    endTime: string,
    bookingType?: 'PERSONAL' | 'BUSINESS' | null,
    zone?: string | null,
    filterType?: 'LAST_1_HOUR' | 'LAST_6_HOURS' | 'TODAY' | 'YESTERDAY' | 'LAST_4_WEEKS' | 'LAST_3_MONTHS' | 'LAST_12_MONTHS' | 'ALL_TIME' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/charts/revenue',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'bookingType': bookingType,
        'zone': zone,
        'filterType': filterType,
      },
    });
  }

  /**
   * Get the booking source charts
   * @param startTime
   * @param endTime
   * @param filterType
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetReportBookingSourceChart(
    startTime: string,
    endTime: string,
    filterType?: 'LAST_1_HOUR' | 'LAST_6_HOURS' | 'TODAY' | 'YESTERDAY' | 'LAST_4_WEEKS' | 'LAST_3_MONTHS' | 'LAST_12_MONTHS' | 'ALL_TIME' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/charts/booking-source',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'filterType': filterType,
      },
    });
  }

  /**
   * Get the tracking booking source charts
   * @param startTime
   * @param endTime
   * @param filterType
   * @returns any
   * @throws ApiError
   */
  public static dashBoardControllerGetTrackingBookingSourceChart(
    startTime: string,
    endTime: string,
    filterType?: 'LAST_1_HOUR' | 'LAST_6_HOURS' | 'TODAY' | 'YESTERDAY' | 'LAST_4_WEEKS' | 'LAST_3_MONTHS' | 'LAST_12_MONTHS' | 'ALL_TIME' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/dashboard/charts/tracking-booking-source',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'filterType': filterType,
      },
    });
  }

}
