/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { IBaseResponse } from '../models/IBaseResponse';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class VersionAppService {

  /**
   * Check version info
   * @param version
   * @param platform
   * @returns any
   * @throws ApiError
   */
  public static versionAppControllerCheckVersion(
    version: string,
    platform: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/version-app/check-version',
      query: {
        'version': version,
        'platform': platform,
      },
    });
  }

}
