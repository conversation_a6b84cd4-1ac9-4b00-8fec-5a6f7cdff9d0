/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelSubscriptionDto } from '../models/CancelSubscriptionDto';
import type { CreateDiscountDto } from '../models/CreateDiscountDto';
import type { CreateSubscriptionCouponDto } from '../models/CreateSubscriptionCouponDto';
import type { CreateSubscriptionDto } from '../models/CreateSubscriptionDto';
import type { CreateSubscriptionV2Dto } from '../models/CreateSubscriptionV2Dto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { SubscribeSubscriptionDto } from '../models/SubscribeSubscriptionDto';
import type { SubscriptionCouponMetricEntity } from '../models/SubscriptionCouponMetricEntity';
import type { UpdatePositionsCouponDto } from '../models/UpdatePositionsCouponDto';
import type { UpdateSubscriptionCardDto } from '../models/UpdateSubscriptionCardDto';
import type { UpdateSubscriptionCouponDto } from '../models/UpdateSubscriptionCouponDto';
import type { UpdateSubscriptionDto } from '../models/UpdateSubscriptionDto';
import type { UpdateSubscriptionStatusDto } from '../models/UpdateSubscriptionStatusDto';
import type { UpdateSubscriptionV2Dto } from '../models/UpdateSubscriptionV2Dto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class SubscriptionsService {

  /**
   * Get list of subscriptions on app
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static subscriptionControllerGetAppSubscriptions(
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/app/subscriptions',
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Get a subscription on app
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static subscriptionControllerGetAppSubscription(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/app/subscriptions/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get list of coupons of subscription on app
   * @param id
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static subscriptionControllerGetCouponsOfSubscription(
    id: string,
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/app/subscriptions/{id}/coupons',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * User subscribe a subscription
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionControllerSubscribeSubscription(
    requestBody: SubscribeSubscriptionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/app/subscriptions/subscribe',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * User change plan of a subscription
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionControllerChangePlanSubscription(
    id: string,
    requestBody: SubscribeSubscriptionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/app/subscriptions/{id}/change-plan',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get current subscription of me
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerGetCurrentSubscription(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/subscriptions',
    });
  }

  /**
   * Get list coupon of my subscription
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerGetCurrentSubscriptionCoupons(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/subscriptions/coupons',
    });
  }

  /**
   * Get card of my subscription
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerGetCurrentSubscriptionPaymentMethod(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/subscriptions/cards',
    });
  }

  /**
   * Update card of my subscription
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerUpdateSubscriptionCard(
    requestBody: UpdateSubscriptionCardDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/subscriptions/cards',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list subscription transaction of me
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerGetSubscriptionTransactionOfMe(
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/subscriptions/transactions',
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Get a subscription transaction
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerGetSubscriptionTransaction(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/subscriptions/transactions/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Cancel renew a subscription
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerCancelRenewSubscription(
    requestBody: CancelSubscriptionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/subscriptions/cancel',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Resubscribe a subscription
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeControllerReSubscribeSubscription(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/subscriptions/resubscribe',
    });
  }

  /**
   * Get list of subscriptions of system
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetSubscriptions(
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions',
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Create a subscription of system
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerCreateSubscription(
    requestBody: CreateSubscriptionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/subscriptions',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list filter of subscriptions of system
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetSubscriptionsFilter(
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/filter',
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Summary the overall subscription data
   * @param startTime
   * @param endTime
   * @param subscriptionId
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetOverallDashBoard(
    startTime: string,
    endTime: string,
    subscriptionId?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/overalls',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'subscriptionId': subscriptionId,
      },
    });
  }

  /**
   * Summary the subscription chart
   * @param startTime
   * @param endTime
   * @param timezone
   * @param subscriptionId
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetSubscriptionDashboardChart(
    startTime: string,
    endTime: string,
    timezone: string,
    subscriptionId?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/charts',
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'subscriptionId': subscriptionId,
        'timezone': timezone,
      },
    });
  }

  /**
   * Get a subscription of system
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetSubscription(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a subscription of a system
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerUpdateSubscription(
    id: string,
    requestBody: UpdateSubscriptionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/subscriptions/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a subscription of system
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerDeleteSubscription(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/subscriptions/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status of a subscription
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerUpdateUserStatus(
    id: string,
    requestBody: UpdateSubscriptionStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/subscriptions/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of coupon statistics of subscription
   * @param id
   * @param q
   * @param couponId
   * @param fromDate
   * @param toDate
   * @returns SubscriptionCouponMetricEntity
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetCouponStatisticsOfSubscription(
    id: string,
    q?: string,
    couponId?: string,
    fromDate?: string,
    toDate?: string,
  ): CancelablePromise<SubscriptionCouponMetricEntity> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/coupon-statistics',
      path: {
        'id': id,
      },
      query: {
        'q': q,
        'couponId': couponId,
        'fromDate': fromDate,
        'toDate': toDate,
      },
    });
  }

  /**
   * Get list of coupon logs of subscription
   * @param id
   * @param q
   * @param couponId
   * @param fromDate
   * @param toDate
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetCouponLogsOfSubscription(
    id: string,
    q?: string,
    couponId?: string,
    fromDate?: string,
    toDate?: string,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/coupon-logs',
      path: {
        'id': id,
      },
      query: {
        'q': q,
        'couponId': couponId,
        'fromDate': fromDate,
        'toDate': toDate,
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get list of coupons of subscription
   * @param id
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetCouponsOfSubscription(
    id: string,
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/coupons',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Create a coupon of subscription
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerCreateCouponOfSubscription(
    id: string,
    requestBody: CreateSubscriptionCouponDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/subscriptions/{id}/coupons',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a coupon of subscription
   * @param id
   * @param couponId
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetCouponOfSubscription(
    id: string,
    couponId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/coupons/{couponId}',
      path: {
        'id': id,
        'couponId': couponId,
      },
    });
  }

  /**
   * Update a coupon of a subscription
   * @param id
   * @param couponId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerUpdateCouponOfSubscription(
    id: string,
    couponId: string,
    requestBody: UpdateSubscriptionCouponDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/subscriptions/{id}/coupons/{couponId}',
      path: {
        'id': id,
        'couponId': couponId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a coupon of subscription
   * @param id
   * @param couponId
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerDeleteCouponOfSubscription(
    id: string,
    couponId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/subscriptions/{id}/coupons/{couponId}',
      path: {
        'id': id,
        'couponId': couponId,
      },
    });
  }

  /**
   * Update a coupon of a subscription
   * @param id
   * @param couponId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerUpdatePositionsOfCouponOfSubscription(
    id: string,
    couponId: string,
    requestBody: UpdatePositionsCouponDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/subscriptions/{id}/coupons/{couponId}/positions',
      path: {
        'id': id,
        'couponId': couponId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Create a discount for plan of subscription
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerCreateDiscountOfSubscriptionPlan(
    id: string,
    requestBody: CreateDiscountDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/subscriptions/plans/{id}/discounts',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get discounts for plans of subscription
   * @param id
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetDiscountsOfSubscription(
    id: string,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/discounts',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get statistics of a subscription
   * @param id
   * @param startTime
   * @param endTime
   * @param subscriptionId
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetSubscriptionStatistics(
    id: string,
    startTime: string,
    endTime: string,
    subscriptionId?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/statistics',
      path: {
        'id': id,
      },
      query: {
        'startTime': startTime,
        'endTime': endTime,
        'subscriptionId': subscriptionId,
      },
    });
  }

  /**
   * Get transactions of a subscription
   * @param id
   * @param q
   * @param action
   * @param status
   * @param recurringType
   * @param fromDate
   * @param toDate
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetTransactionsOfSubscription(
    id: string,
    q?: string,
    action?: 'RENEW' | 'NEW' | 'CANCEL',
    status?: 'PURCHASED' | 'DECLINED',
    recurringType?: 'MONTHLY_1' | 'YEARLY_1',
    fromDate?: string,
    toDate?: string,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/{id}/transactions',
      path: {
        'id': id,
      },
      query: {
        'q': q,
        'action': action,
        'status': status,
        'recurringType': recurringType,
        'fromDate': fromDate,
        'toDate': toDate,
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get list of coupon log activities of subscription
   * @param id
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerGetCouponLogActivities(
    id: string,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/subscriptions/coupon/{id}/log-activities',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get list subscription transaction of user
   * @param id
   * @param stripePaymentIntentId
   * @param subscriptionId
   * @param fromDate
   * @param toDate
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static userSubscriptionControllerGetSubscriptionTransactions(
    id: string,
    stripePaymentIntentId?: string | null,
    subscriptionId?: string | null,
    fromDate?: string | null,
    toDate?: string | null,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/{id}/subscription-transactions',
      path: {
        'id': id,
      },
      query: {
        'stripePaymentIntentId': stripePaymentIntentId,
        'subscriptionId': subscriptionId,
        'fromDate': fromDate,
        'toDate': toDate,
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Create a subscription of system
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerV2CreateSubscriptionV2(
    requestBody: CreateSubscriptionV2Dto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v2/subscriptions',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update a subscription of a system
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionSuperAdminControllerV2UpdateSubscriptionV2(
    id: string,
    requestBody: UpdateSubscriptionV2Dto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v2/subscriptions/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get current subscription of me
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeV2ControllerGetCurrentSubscription(
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v2/users/me/subscriptions',
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get card of my subscription
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v2/users/me/subscriptions/{id}/cards',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update card of my subscription
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeV2ControllerUpdateSubscriptionCard(
    id: string,
    requestBody: UpdateSubscriptionCardDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v2/users/me/subscriptions/{id}/cards',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Cancel renew a subscription
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeV2ControllerCancelRenewSubscription(
    id: string,
    requestBody: CancelSubscriptionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v2/users/me/subscriptions/{id}/cancel',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Resubscribe a subscription
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static subscriptionMeV2ControllerReSubscribeSubscription(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v2/users/me/subscriptions/{id}/resubscribe',
      path: {
        'id': id,
      },
    });
  }

}
