/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateAdminOfBusinessDto } from '../models/CreateAdminOfBusinessDto';
import type { CreateEmployeeDto } from '../models/CreateEmployeeDto';
import type { CreateEmployeeOfBusinessDto } from '../models/CreateEmployeeOfBusinessDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateAdminOfBusinessDto } from '../models/UpdateAdminOfBusinessDto';
import type { UpdateEmployeeDto } from '../models/UpdateEmployeeDto';
import type { UpdateEmployeeOfBusinessDto } from '../models/UpdateEmployeeOfBusinessDto';
import type { UpdateEmployeeStatusDto } from '../models/UpdateEmployeeStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class EmployeesService {

  /**
   * Get list of employee of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param q
   * @param departmentId
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerGetEmployeesOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    q?: string | null,
    departmentId?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/employees',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'departmentId': departmentId,
        'order': order,
      },
    });
  }

  /**
   * Create a employee of a business
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerCreateEmployeeOfBusiness(
    businessId: string,
    requestBody: CreateEmployeeOfBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses/{businessId}/employees',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a employee of a business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerGetDepartmentOfBusiness(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/employees/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Update a employee of a business
   * @param id
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerUpdateEmployeeOfBusiness(
    id: string,
    businessId: string,
    requestBody: UpdateEmployeeOfBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/employees/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a employee of a business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerDeleteEmployeeOfBusiness(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}/employees/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Get list of employee of a department
   * @param departmentId
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerGetEmployeesOfDepartment(
    departmentId: string,
    limit: number = 10,
    offset: number,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/departments/{departmentId}/employees',
      path: {
        'departmentId': departmentId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Create a employee of a department
   * @param departmentId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerCreateEmployee(
    departmentId: string,
    requestBody: CreateEmployeeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/departments/{departmentId}/employees',
      path: {
        'departmentId': departmentId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a employee of a department
   * @param id
   * @param departmentId
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerGetDepartment(
    id: string,
    departmentId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/departments/{departmentId}/employees/{id}',
      path: {
        'id': id,
        'departmentId': departmentId,
      },
    });
  }

  /**
   * Update a employee of a department
   * @param id
   * @param departmentId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerUpdateEmployee(
    id: string,
    departmentId: string,
    requestBody: UpdateEmployeeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/departments/{departmentId}/employees/{id}',
      path: {
        'id': id,
        'departmentId': departmentId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a employee of a department
   * @param id
   * @param departmentId
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerDeleteEmployee(
    id: string,
    departmentId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/departments/{departmentId}/employees/{id}',
      path: {
        'id': id,
        'departmentId': departmentId,
      },
    });
  }

  /**
   * Get list of admins of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param q
   * @param roleId
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerGetAdminsOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    q?: string | null,
    roleId?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/admins',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'roleId': roleId,
        'order': order,
      },
    });
  }

  /**
   * Create an admin of a business
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerCreateAdminOfBusiness(
    businessId: string,
    requestBody: CreateAdminOfBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses/{businessId}/admins',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get an admin of a business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerGetAdminOfBusiness(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/admins/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Delete an admin of a business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerDeleteAdminOfBusiness(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}/admins/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Update an admin of a business
   * @param id
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerUpdateAdminOfBusiness(
    id: string,
    businessId: string,
    requestBody: UpdateAdminOfBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/admins/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update status of a employee
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static employeeControllerUpdateEmployeeStatus(
    id: string,
    requestBody: UpdateEmployeeStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/employees/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
