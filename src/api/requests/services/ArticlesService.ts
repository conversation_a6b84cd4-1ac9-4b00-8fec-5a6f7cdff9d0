/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ArticleParamDto } from '../models/ArticleParamDto';
import type { CreateArticleDto } from '../models/CreateArticleDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateDisplayArticleDto } from '../models/UpdateDisplayArticleDto';
import type { UpdateStatusArticleDto } from '../models/UpdateStatusArticleDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class ArticlesService {

  /**
   * Create a new article
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static articleControllerCreateArticle(
    requestBody: CreateArticleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/articles',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list article
   * @param limit
   * @param offset
   * @param titleEn
   * @param isActive
   * @param isDisplay
   * @param createdById
   * @param fromDate
   * @param toDate
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static articleControllerGetListArticle(
    limit: number = 10,
    offset: number,
    titleEn?: string,
    isActive?: boolean,
    isDisplay?: boolean,
    createdById?: string,
    fromDate?: string,
    toDate?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/articles',
      query: {
        'limit': limit,
        'offset': offset,
        'titleEn': titleEn,
        'isActive': isActive,
        'isDisplay': isDisplay,
        'createdById': createdById,
        'fromDate': fromDate,
        'toDate': toDate,
        'order': order,
      },
    });
  }

  /**
   * Update display of an article
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static articleControllerUpdateDisplayArticle(
    requestBody: Array<UpdateDisplayArticleDto>,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/articles/display',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update an article
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static articleControllerUpdateArticle(
    id: string,
    requestBody: ArticleParamDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/articles/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete an article
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static articleControllerDeleteArticle(
    id: string,
  ): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/articles/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static articleControllerGetArticleDetail(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/articles/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status of an article
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static articleControllerUpdateArticleStatus(
    id: string,
    requestBody: UpdateStatusArticleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/articles/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list displayed article
   * @returns any
   * @throws ApiError
   */
  public static articleControllerGetArticleDisplayed(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/articles/displayed',
    });
  }

}
