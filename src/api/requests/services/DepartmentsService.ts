/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateDepartmentDto } from '../models/CreateDepartmentDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateDepartmentDto } from '../models/UpdateDepartmentDto';
import type { UpdateDepartmentStatusDto } from '../models/UpdateDepartmentStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class DepartmentsService {

  /**
   * Get list of departments of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param name
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static departmentControllerGetDepartmentsOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    name?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/departments',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'name': name,
        'order': order,
      },
    });
  }

  /**
   * Create a department
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentControllerCreateDepartment(
    businessId: string,
    requestBody: CreateDepartmentDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses/{businessId}/departments',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a department
   * @param departmentId
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static departmentControllerGetDepartment(
    departmentId: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/departments/{departmentId}',
      path: {
        'departmentId': departmentId,
        'businessId': businessId,
      },
    });
  }

  /**
   * Update a department
   * @param businessId
   * @param departmentId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentControllerUpdateDepartment(
    businessId: string,
    departmentId: string,
    requestBody: UpdateDepartmentDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/departments/{departmentId}',
      path: {
        'businessId': businessId,
        'departmentId': departmentId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a department
   * @param departmentId
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static departmentControllerDeleteEmployee(
    departmentId: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}/departments/{departmentId}',
      path: {
        'departmentId': departmentId,
        'businessId': businessId,
      },
    });
  }

  /**
   * Update status of a department
   * @param departmentId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static departmentControllerUpdateDepartmentStatus(
    departmentId: string,
    requestBody: UpdateDepartmentStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/departments/{departmentId}/status',
      path: {
        'departmentId': departmentId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
