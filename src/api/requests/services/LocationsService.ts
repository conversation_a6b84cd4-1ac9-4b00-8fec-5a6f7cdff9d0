/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateLocationDto } from '../models/CreateLocationDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateLocationDto } from '../models/UpdateLocationDto';
import type { UpdateLocationStatusDto } from '../models/UpdateLocationStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class LocationsService {

  /**
   * Create a location
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static locationControllerCreateLocation(
    requestBody: CreateLocationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/locations',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of location
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static locationControllerGetList(
    limit: number = 10,
    offset: number,
    q?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/locations',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Get list location by place ids
   * @param placeIds A JSON string representing an array of placeIds, e.g., ["placeId1","placeId2","placeId3"]
   * @returns any
   * @throws ApiError
   */
  public static locationControllerGetLocationsByPlaceIds(
    placeIds?: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/locations/by-place-ids',
      query: {
        'placeIds': placeIds,
      },
    });
  }

  /**
   * Get location details
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static locationControllerGetLocation(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/locations/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a location
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static locationControllerUpdateLocation(
    id: string,
    requestBody: UpdateLocationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/locations/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a location
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static locationControllerDeleteLocation(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/locations/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status of location
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static locationControllerUpdateStatusOfLocation(
    id: string,
    requestBody: UpdateLocationStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/locations/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
