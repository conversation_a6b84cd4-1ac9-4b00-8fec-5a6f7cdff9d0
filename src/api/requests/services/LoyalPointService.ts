/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateLoyalPointConfigDto } from '../models/UpdateLoyalPointConfigDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class LoyalPointService {

  /**
   * Get loyal point config
   * @returns any
   * @throws ApiError
   */
  public static loyalPointControllerGetLoyalPointConfig(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/loyal-point-configs',
    });
  }

  /**
   * Update loyal point config
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static loyalPointControllerUpdateLoyalPointConfig(
    requestBody: UpdateLoyalPointConfigDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/loyal-point-configs',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list loyal point rewards
   * @returns any
   * @throws ApiError
   */
  public static loyalPointControllerGetLoyalPointRewards(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/loyal-point-configs/rewards',
    });
  }

  /**
   * Get list of points of me
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static loyalPointUserControllerGetPoints(
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/points',
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get loyal point config in app
   * @returns any
   * @throws ApiError
   */
  public static loyalPointPublicControllerGetAppLoyalPointConfig(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/loyal-point-configs/app',
    });
  }

}
