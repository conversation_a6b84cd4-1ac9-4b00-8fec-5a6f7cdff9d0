/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateCodeDto } from '../models/CreateCodeDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateCodeStatusDto } from '../models/UpdateCodeStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class CodesService {

  /**
   * Get list of codes of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param code
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static codeControllerGetCodesOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    code?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/codes',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'code': code,
        'order': order,
      },
    });
  }

  /**
   * Create a code
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static codeControllerCreateCode(
    businessId: string,
    requestBody: CreateCodeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses/{businessId}/codes',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of available codes of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param code
   * @returns any
   * @throws ApiError
   */
  public static codeControllerGetAvailableCodesOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    code?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/codes/available',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'code': code,
      },
    });
  }

  /**
   * Update status of code
   * @param businessId
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static codeControllerUpdateStatusOfCode(
    businessId: string,
    id: string,
    requestBody: UpdateCodeStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/codes/{id}/status',
      path: {
        'businessId': businessId,
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get code detail of business
   * @param businessId
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static codeControllerGetCodeOfBusiness(
    businessId: string,
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/codes/{id}',
      path: {
        'businessId': businessId,
        'id': id,
      },
    });
  }

  /**
   * Delete a code of business
   * @param businessId
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static codeControllerDeleteCodeOfBusiness(
    businessId: string,
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}/codes/{id}',
      path: {
        'businessId': businessId,
        'id': id,
      },
    });
  }

}
