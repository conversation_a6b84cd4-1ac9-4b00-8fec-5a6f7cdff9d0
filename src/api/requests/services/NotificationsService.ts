/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateNotificationDto } from '../models/CreateNotificationDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { SeenAllNotificationsDto } from '../models/SeenAllNotificationsDto';
import type { UpdateNotificationDto } from '../models/UpdateNotificationDto';
import type { UpdateStatusNotificationDto } from '../models/UpdateStatusNotificationDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class NotificationsService {

  /**
   * Get list of notifications
   * @param limit
   * @param offset
   * @param type
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerGetNotifications(
    limit: number = 10,
    offset: number,
    type?: 'GENERAL' | 'PROMOTION' | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/notifications',
      query: {
        'limit': limit,
        'offset': offset,
        'type': type,
        'order': order,
      },
    });
  }

  /**
   * Create a notification
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerCreateNotification(
    requestBody: CreateNotificationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/notifications',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a notification
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerGetNotification(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/notifications/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Delete a notification
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerDeleteNotification(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/notifications/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Read a notification
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerUpdateNotification(
    id: string,
    requestBody: UpdateNotificationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/notifications/{id}/seen',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Read all notifications
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerSeenAllNotification(
    requestBody: SeenAllNotificationsDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/notifications/seen',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Check if there are new notifications for the user
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerHasUnseenNotifications(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/notifications/check/has-unseen',
    });
  }

  /**
   * Count total unseen notifications for the user
   * @returns any
   * @throws ApiError
   */
  public static notificationControllerTotalUnseenNotifications(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/notifications/check/unseen',
    });
  }

  /**
   * Get list of notifications of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param notificationType
   * @param fromDate
   * @param toDate
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerGetBusinessNotifications(
    businessId: string,
    limit: number = 10,
    offset: number,
    notificationType?: any,
    fromDate?: string | null,
    toDate?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/notifications',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'notificationType': notificationType,
        'fromDate': fromDate,
        'toDate': toDate,
        'order': order,
      },
    });
  }

  /**
   * Get a notification of business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerGetBusinessNotification(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/notifications/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Delete a notification of business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerDeleteNotification(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}/notifications/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Read a notification of business
   * @param id
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerUpdateNotification(
    id: string,
    businessId: string,
    requestBody: UpdateNotificationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/notifications/{id}/seen',
      path: {
        'id': id,
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of notifications by admin
   * @param limit
   * @param offset
   * @param notificationType
   * @param fromDate
   * @param toDate
   * @param createdByIds
   * @param sendTimeFrom
   * @param sendTimeTo
   * @param status
   * @param q
   * @param type
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerGetNotifications(
    limit: number = 10,
    offset: number,
    notificationType?: any,
    fromDate?: string | null,
    toDate?: string | null,
    createdByIds?: string,
    sendTimeFrom?: string | null,
    sendTimeTo?: string | null,
    status?: 'COMPLETED' | 'SCHEDULED' | 'CANCELLED' | 'REMOVED' | 'IN_PROGRESS' | null,
    q?: string | null,
    type?: 'GENERAL' | 'PROMOTION' | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/admin/notifications',
      query: {
        'limit': limit,
        'offset': offset,
        'notificationType': notificationType,
        'fromDate': fromDate,
        'toDate': toDate,
        'createdByIds': createdByIds,
        'sendTimeFrom': sendTimeFrom,
        'sendTimeTo': sendTimeTo,
        'status': status,
        'q': q,
        'type': type,
        'order': order,
      },
    });
  }

  /**
   * Get a notification
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerGetNotification(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/admin/notifications/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status notification by admin
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static notificationAdminControllerUpdateStatusNotification(
    id: string,
    requestBody: UpdateStatusNotificationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/admin/notifications/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
