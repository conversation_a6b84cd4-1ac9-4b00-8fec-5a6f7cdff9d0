/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateBusinessBillToDto } from '../models/CreateBusinessBillToDto';
import type { CreateBusinessDto } from '../models/CreateBusinessDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateBusinessDto } from '../models/UpdateBusinessDto';
import type { UpdateBusinessStatusDto } from '../models/UpdateBusinessStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BusinessesService {

  /**
   * Get a business by id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static businessControllerGetBusiness(
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}',
      path: {
        'businessId': businessId,
      },
    });
  }

  /**
   * Update a business
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static businessControllerUpdateBusiness(
    businessId: string,
    requestBody: UpdateBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a business
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static businessSuperAdminControllerDeleteBusiness(
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}',
      path: {
        'businessId': businessId,
      },
    });
  }

  /**
   * Get bill-tos list of business
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static businessControllerGetBillToOfBusiness(
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/bill-tos',
      path: {
        'businessId': businessId,
      },
    });
  }

  /**
   * Create a bill-to
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static businessControllerCreateBillTo(
    businessId: string,
    requestBody: CreateBusinessBillToDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses/{businessId}/bill-tos',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of business
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static businessSuperAdminControllerGetBusinesses(
    limit: number = 10,
    offset: number,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Create a business
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static businessSuperAdminControllerCreateDepartmentGroup(
    requestBody: CreateBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list filter of business
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static businessSuperAdminControllerGetBusinessesFilter(
    limit: number = 10,
    offset: number,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses-filter',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Update status of a business
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static businessSuperAdminControllerUpdateBusinessStatus(
    businessId: string,
    requestBody: UpdateBusinessStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/status',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Export business customers
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static businessSuperAdminControllerExportBusinessCustomers(
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses-exports',
      query: {
        'q': q,
        'order': order,
      },
    });
  }

}
