/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateCouponDto } from '../models/CreateCouponDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateCouponDto } from '../models/UpdateCouponDto';
import type { UpdateCouponStatusDto } from '../models/UpdateCouponStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class CouponsService {

  /**
   * Get list of coupon
   * @param limit
   * @param offset
   * @param q
   * @param title
   * @param redeemMethod
   * @param status
   * @param createdFrom
   * @param createdTo
   * @param startDateValidFrom
   * @param endDateValidFrom
   * @param startDateValidTo
   * @param endDateValidTo
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static couponControllerGetList(
    limit: number = 10,
    offset: number,
    q?: string | null,
    title?: string | null,
    redeemMethod?: string | null,
    status?: string | null,
    createdFrom?: string | null,
    createdTo?: string | null,
    startDateValidFrom?: string | null,
    endDateValidFrom?: string | null,
    startDateValidTo?: string | null,
    endDateValidTo?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'title': title,
        'redeemMethod': redeemMethod,
        'status': status,
        'createdFrom': createdFrom,
        'createdTo': createdTo,
        'startDateValidFrom': startDateValidFrom,
        'endDateValidFrom': endDateValidFrom,
        'startDateValidTo': startDateValidTo,
        'endDateValidTo': endDateValidTo,
        'order': order,
      },
    });
  }

  /**
   * Create a coupon
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static couponControllerCreateOne(
    requestBody: CreateCouponDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/coupons',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of coupon store
   * @returns any
   * @throws ApiError
   */
  public static couponControllerGetCouponStoreList(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/store',
    });
  }

  /**
   * Check a coupon is existed by code
   * @param code
   * @returns any
   * @throws ApiError
   */
  public static couponControllerCheckCoupon(
    code: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/check',
      query: {
        'code': code,
      },
    });
  }

  /**
   * Update a coupon
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static couponControllerUpdateCoupon(
    id: string,
    requestBody: UpdateCouponDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/coupons/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a coupon
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static couponControllerDeleteCoupon(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/coupons/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status of coupon
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static couponControllerUpdateStatusOfCoupon(
    id: string,
    requestBody: UpdateCouponStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/coupons/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get coupon detail
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static couponControllerGetCouponDetail(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/{id}/detail',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get coupon statistics
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static couponControllerGetCouponStatistic(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/{id}/statistics',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get coupon logs histories
   * @param id
   * @param limit
   * @param offset
   * @param fromDate
   * @param toDate
   * @param redeemFrom
   * @param redeemTo
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static couponControllerGetCouponLogHistories(
    id: string,
    limit: number = 10,
    offset: number,
    fromDate?: string,
    toDate?: string,
    redeemFrom?: string,
    redeemTo?: string,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/{id}/log-histories',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'fromDate': fromDate,
        'toDate': toDate,
        'redeemFrom': redeemFrom,
        'redeemTo': redeemTo,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Get coupon logs activities
   * @param id
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static couponControllerGetCouponLogActivities(
    id: string,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/{id}/log-activities',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

}
