/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateAvailableCarDto } from '../models/UpdateAvailableCarDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class AvailableCarsService {

  /**
   * Get list available car
   * @returns any
   * @throws ApiError
   */
  public static availableCarControllerGetListAvailableCar(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/available-cars',
    });
  }

  /**
   * Get available car by id
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static availableCarControllerGetAvailableCarById(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/available-cars/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update available car
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static availableCarControllerUpdateAvailableCar(
    id: string,
    requestBody: UpdateAvailableCarDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/available-cars/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
