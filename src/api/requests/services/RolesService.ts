/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateRoleDto } from '../models/CreateRoleDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateRoleDto } from '../models/UpdateRoleDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class RolesService {

  /**
   * Get list of roles of a business
   * @param businessId
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static roleControllerGetRolesOfBusiness(
    businessId: string,
    limit: number = 10,
    offset: number,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/roles',
      path: {
        'businessId': businessId,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Create a role of business
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static roleControllerCreateRoleOfBusiness(
    businessId: string,
    requestBody: CreateRoleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/businesses/{businessId}/roles',
      path: {
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a role of business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static roleControllerGetRoleOfBusiness(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/businesses/{businessId}/roles/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Update a role of a business
   * @param id
   * @param businessId
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static roleControllerUpdateRoleOfBusiness(
    id: string,
    businessId: string,
    requestBody: UpdateRoleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/businesses/{businessId}/roles/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a role of business
   * @param id
   * @param businessId
   * @returns any
   * @throws ApiError
   */
  public static roleControllerDeleteRoleOfBusiness(
    id: string,
    businessId: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/businesses/{businessId}/roles/{id}',
      path: {
        'id': id,
        'businessId': businessId,
      },
    });
  }

  /**
   * Get list of roles of system
   * @param limit
   * @param offset
   * @param q
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static roleControllerGetRoles(
    limit: number = 10,
    offset: number,
    q?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/roles',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'order': order,
      },
    });
  }

  /**
   * Create a role of system
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static roleControllerCreateRole(
    requestBody: CreateRoleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/roles',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a role of system
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static roleControllerGetRole(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/roles/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a role of a system
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static roleControllerUpdateRole(
    id: string,
    requestBody: UpdateRoleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/roles/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a role of system
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static roleControllerDeleteRole(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/roles/{id}',
      path: {
        'id': id,
      },
    });
  }

}
