/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateUserStatusDto } from '../models/UpdateUserStatusDto';
import type { UpsertUserDto } from '../models/UpsertUserDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class AdminService {

  /**
   * Get list of admins
   * @param limit
   * @param offset
   * @param q
   * @param roleId
   * @param isActive
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static adminControllerGetAdmins(
    limit: number = 10,
    offset: number,
    q?: string | null,
    roleId?: string | null,
    isActive?: boolean | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/admin',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'roleId': roleId,
        'isActive': isActive,
        'order': order,
      },
    });
  }

  /**
   * Create an admin account
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static adminControllerCreateAdmin(
    requestBody: UpsertUserDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/admin',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get a admin
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static adminControllerGetUser(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/admin/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update an admin account
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static adminControllerUpdateAdminOfBusiness(
    id: string,
    requestBody: UpsertUserDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/admin/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete an admin
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static adminControllerDeleteAdminOfBusiness(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/admin/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status of a admin
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static adminControllerUpdateAdminStatus(
    id: string,
    requestBody: UpdateUserStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/admin/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
