/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BannerImageType } from './BannerImageType';
import type { CreatedByUserEntity } from './CreatedByUserEntity';

export type BannerEntity = {
  id: string;
  campaign: string;
  isDisplay: boolean;
  bannerImages: BannerImageType;
  navigationType: BannerEntity.navigationType;
  target: string;
  isActive: boolean;
  createdBy: CreatedByUserEntity;
  createdDate: string;
  pos?: number | null;
  audience: BannerEntity.audience;
  createdByUser: CreatedByUserEntity;
  specificUserIds?: Array<any[]> | null;
  specificUserInfos?: Array<any[]> | null;
};

export namespace BannerEntity {

  export enum navigationType {
    URL = 'URL',
    IN_APP = 'IN_APP',
  }

  export enum audience {
    ALL = 'ALL',
    OLD_USER = 'OLD_USER',
    NEW_USER = 'NEW_USER',
    SPECIFIC_USER = 'SPECIFIC_USER',
  }


}

