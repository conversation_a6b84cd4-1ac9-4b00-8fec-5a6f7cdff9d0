/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { AreaDto } from './AreaDto';

export type CreateServiceFeeDto = {
  name: string;
  nameFr: string;
  amount: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  perType: CreateServiceFeeDto.perType;
  applyFromType: CreateServiceFeeDto.applyFromType;
  applyFrom: string;
  status: CreateServiceFeeDto.status;
  specificPickUpAreas: Array<AreaDto>;
  specificDropOffAreas: Array<AreaDto>;
};

export namespace CreateServiceFeeDto {

  export enum perType {
    RIDE = 'RIDE',
    KILOMETER = 'KILOMETER',
  }

  export enum applyFromType {
    ALL = 'ALL',
    SPECIFIC_DATE = 'SPECIFIC_DATE',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

