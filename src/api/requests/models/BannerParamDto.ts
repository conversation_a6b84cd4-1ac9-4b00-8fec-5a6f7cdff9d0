/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BannerImageType } from './BannerImageType';

export type BannerParamDto = {
  campaign: string;
  bannerImages: BannerImageType;
  navigationType: BannerParamDto.navigationType;
  /**
   * Can use URL if navigation type is URL
   */
  target?: BannerParamDto.target | null;
  audience: BannerParamDto.audience;
  specificUserIds?: Array<any[]>;
};

export namespace BannerParamDto {

  export enum navigationType {
    URL = 'URL',
    IN_APP = 'IN_APP',
  }

  /**
   * Can use URL if navigation type is URL
   */
  export enum target {
    HOMEPAGE = 'HOMEPAGE',
    BOOK_A_CAR = 'BOOK_A_CAR',
    SCHEDULE_A_CAR = 'SCHEDULE_A_CAR',
    ACTIVITIES_RECENT = 'ACTIVITIES_RECENT',
    ACTIVITIES_HISTORY = 'ACTIVITIES_HISTORY',
    NOTIFICATION_GENERAL = 'NOTIFICATION_GENERAL',
    NOTIFICATION_PROMOTION = 'NOTIFICATION_PROMOTION',
    SETTINGS_PROFILE = 'SETTINGS_PROFILE',
    SETTINGS_PAYMENT = 'SETTINGS_PAYMENT',
    SETTINGS_SAVED_LOCATIONS = 'SETTINGS_SAVED_LOCATIONS',
    SETTINGS_LOYAL_HUB = 'SETTINGS_LOYAL_HUB',
    SETTINGS_LOYAL_ONE = 'SETTINGS_LOYAL_ONE',
    SETTINGS_COUPON_LIST = 'SETTINGS_COUPON_LIST',
    SETTINGS_COUPON_STORE = 'SETTINGS_COUPON_STORE',
    SETTINGS_LOYAL_POINTS = 'SETTINGS_LOYAL_POINTS',
    SETTINGS_LOYAL_POINTS_HISTORY = 'SETTINGS_LOYAL_POINTS_HISTORY',
    SETTINGS_CHANGE_PASSWORD = 'SETTINGS_CHANGE_PASSWORD',
    SETTINGS_ACCOUNT_DELETION = 'SETTINGS_ACCOUNT_DELETION',
    SETTINGS_LANGUAGE = 'SETTINGS_LANGUAGE',
    SETTINGS_ABOUT = 'SETTINGS_ABOUT',
    SETTINGS_REFER_FRIENDS = 'SETTINGS_REFER_FRIENDS',
  }

  export enum audience {
    ALL = 'ALL',
    OLD_USER = 'OLD_USER',
    NEW_USER = 'NEW_USER',
    SPECIFIC_USER = 'SPECIFIC_USER',
  }


}

