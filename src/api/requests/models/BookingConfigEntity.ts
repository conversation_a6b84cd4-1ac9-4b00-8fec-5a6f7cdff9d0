/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type BookingConfigEntity = {
  tipPosition: BookingConfigEntity.tipPosition;
  anyCar: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
};

export namespace BookingConfigEntity {

  export enum tipPosition {
    BEFORE = 'BEFORE',
    AFTER = 'AFTER',
  }


}

