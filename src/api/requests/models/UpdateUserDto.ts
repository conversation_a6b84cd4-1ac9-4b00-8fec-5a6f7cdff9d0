/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type UpdateUserDto = {
  fullName?: string | null;
  avatarUrl?: string | null;
  phoneNumber?: string | null;
  gender?: UpdateUserDto.gender | null;
  birthday?: string | null;
};

export namespace UpdateUserDto {

  export enum gender {
    MALE = 'MALE',
    FEMALE = 'FEMALE',
    OTHER = 'OTHER',
  }


}

