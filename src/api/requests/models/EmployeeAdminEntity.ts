/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { DepartmentBaseEntity } from './DepartmentBaseEntity';
import type { RoleEntity } from './RoleEntity';

export type EmployeeAdminEntity = {
  id: string;
  email: string;
  phoneNumber: string;
  isActive: boolean;
  fullName?: string | null;
  avatarUrl?: string | null;
  role?: RoleEntity | null;
  departments?: Array<DepartmentBaseEntity> | null;
};

