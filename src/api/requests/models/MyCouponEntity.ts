/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type MyCouponEntity = {
  id: string;
  title: string;
  type: MyCouponEntity.type;
  value: number;
  description?: string;
  validTo?: string;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  maxDiscountLimit?: number;
  paymentMethods?: Array<'ALL' | 'CARD' | 'CASH'>;
  isValid?: boolean;
  discountAmount?: number;
  userCouponType?: MyCouponEntity.userCouponType;
  quantity?: number;
};

export namespace MyCouponEntity {

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
    DROP_RATE = 'DROP_RATE',
  }

  export enum userCouponType {
    COUPON_STORE = 'COUPON_STORE',
    COUPON_SUBSCRIPTION = 'COUPON_SUBSCRIPTION',
  }


}

