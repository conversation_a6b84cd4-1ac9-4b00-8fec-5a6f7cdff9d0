/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateCodeDto = {
  code: string;
  PIN: string;
  departmentId?: string | null;
  employeeId?: string | null;
  maxSpendingLimit?: number | null;
  maxNumberOfUsage?: number | null;
  distanceLimit?: number | null;
  expiredDate?: string | null;
  repeatType: CreateCodeDto.repeatType;
};

export namespace CreateCodeDto {

  export enum repeatType {
    NO_REPEAT = 'NO_REPEAT',
    WEEKLY = 'WEEKLY',
    MONTHLY = 'MONTHLY',
  }


}

