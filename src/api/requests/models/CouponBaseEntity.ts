/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CouponBaseEntity = {
  id: string;
  title: string;
  titleFr: string;
  code?: string;
  status: CouponBaseEntity.status;
  type: CouponBaseEntity.type;
  value: number;
  validTo?: string;
  description?: string;
  descriptionFr?: string;
  maxDiscountLimit?: number;
  topUpPercent?: number;
};

export namespace CouponBaseEntity {

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    EXPIRED = 'EXPIRED',
  }

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
    DROP_RATE = 'DROP_RATE',
  }


}

