/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { EstimateAmountEntityInput } from './EstimateAmountEntityInput';

export type EstimateAmountEntity = {
  vehicleType: EstimateAmountEntity.vehicleType;
  amount: number;
  fixedFare: EstimateAmountEntityInput;
  isScheduled: boolean;
};

export namespace EstimateAmountEntity {

  export enum vehicleType {
    ALL = 'ALL',
    TYPICAL_CAR = 'TYPICAL_CAR',
    ELECTRIC_CAR = 'ELECTRIC_CAR',
    VAN = 'VAN',
    ACCESSIBLE_VAN = 'ACCESSIBLE_VAN',
    ANY_CAR = 'ANY_CAR',
  }


}

