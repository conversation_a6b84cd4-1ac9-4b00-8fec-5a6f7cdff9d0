/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { AreaDto } from './AreaDto';
import type { SpecificTimeType } from './SpecificTimeType';

export type CreateRateFeeDto = {
  name: string;
  nameFr: string;
  kmRate: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  applyDateType: CreateRateFeeDto.applyDateType;
  applyRepeatDay: Array<number>;
  startApplyDate: string;
  endApplyDate: string;
  status: CreateRateFeeDto.status;
  startTime: SpecificTimeType;
  endTime: SpecificTimeType;
  specificPickUpAreas: Array<AreaDto>;
  specificDropOffAreas: Array<AreaDto>;
};

export namespace CreateRateFeeDto {

  export enum applyDateType {
    ALL = 'ALL',
    SPECIFIC_DATE = 'SPECIFIC_DATE',
    DAY = 'DAY',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

