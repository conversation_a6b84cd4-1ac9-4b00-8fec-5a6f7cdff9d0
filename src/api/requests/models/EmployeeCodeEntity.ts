/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BusinessBaseEntity } from './BusinessBaseEntity';
import type { CodeBaseEntity } from './CodeBaseEntity';
import type { EmployeeBaseEntity } from './EmployeeBaseEntity';

export type EmployeeCodeEntity = {
  employee: EmployeeBaseEntity;
  code: CodeBaseEntity;
  business: BusinessBaseEntity;
  isAvailable: boolean;
  remainingSpending?: number | null;
  remainingNumberOfUsage?: number | null;
  remainingDistance?: number | null;
  source?: EmployeeCodeEntity.source | null;
};

export namespace EmployeeCodeEntity {

  export enum source {
    MOBILE_APP = 'MOBILE_APP',
    BUSINESS_ADMIN = 'BUSINESS_ADMIN',
  }


}

