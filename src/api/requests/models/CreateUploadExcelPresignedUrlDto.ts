/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateUploadExcelPresignedUrlDto = {
  fileName: string;
  fileType: CreateUploadExcelPresignedUrlDto.fileType;
  folderPrefix: CreateUploadExcelPresignedUrlDto.folderPrefix;
};

export namespace CreateUploadExcelPresignedUrlDto {

  export enum fileType {
    APPLICATION_VND_MS_EXCEL = 'application/vnd.ms-excel',
    APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_SPREADSHEETML_SHEET = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  }

  export enum folderPrefix {
    AVATARS = 'avatars',
    EXCELS = 'excels',
    BANNERS = 'banners',
  }


}

