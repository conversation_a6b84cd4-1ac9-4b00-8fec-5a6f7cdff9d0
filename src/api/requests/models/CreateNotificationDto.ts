/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateNotificationDto = {
  userIds: Array<string>;
  targetNotificationUserType: CreateNotificationDto.targetNotificationUserType;
  campaign: string;
  title: string;
  titleFr: string;
  sortMessage: string;
  sortMessageFr: string;
  message: string;
  messageFr: string;
  sendTime?: string;
  type: CreateNotificationDto.type;
};

export namespace CreateNotificationDto {

  export enum targetNotificationUserType {
    ALL = 'ALL',
    PAID_USER = 'PAID_USER',
    NEW_USER = 'NEW_USER',
    SPECIFIC_USER = 'SPECIFIC_USER',
  }

  export enum type {
    GENERAL = 'GENERAL',
    PROMOTION = 'PROMOTION',
  }


}

