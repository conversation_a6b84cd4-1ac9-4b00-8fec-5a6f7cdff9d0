/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CardEntity } from './CardEntity';
import type { SubscriptionTransactionDataType } from './SubscriptionTransactionDataType';
import type { UserBaseEntity } from './UserBaseEntity';

export type SubscriptionTransactionEntity = {
  id: string;
  processedDate: string;
  action: SubscriptionTransactionEntity.action;
  status: SubscriptionTransactionEntity.status;
  stripePaymentIntentId: string;
  amount: number;
  subscriptionData: SubscriptionTransactionDataType;
  card?: CardEntity;
  cancelReason?: string;
  user?: UserBaseEntity;
  recurringType?: string;
};

export namespace SubscriptionTransactionEntity {

  export enum action {
    RENEW = 'RENEW',
    NEW = 'NEW',
    CANCEL = 'CANCEL',
  }

  export enum status {
    PURCHASED = 'PURCHASED',
    DECLINED = 'DECLINED',
  }


}

