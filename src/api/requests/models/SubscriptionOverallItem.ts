/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type SubscriptionOverallItem = {
  name: string;
  value: number;
  recurringType: SubscriptionOverallItem.recurringType;
};

export namespace SubscriptionOverallItem {

  export enum recurringType {
    MONTHLY_1 = 'MONTHLY_1',
    YEARLY_1 = 'YEARLY_1',
  }


}

