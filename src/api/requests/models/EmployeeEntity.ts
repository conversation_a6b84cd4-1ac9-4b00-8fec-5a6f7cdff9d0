/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CodeBaseEntity } from './CodeBaseEntity';
import type { DepartmentBaseEntity } from './DepartmentBaseEntity';
import type { DepartmentGroupEntity } from './DepartmentGroupEntity';
import type { DepartmentRoleEntity } from './DepartmentRoleEntity';

export type EmployeeEntity = {
  id: string;
  employeeCode: number;
  fullName?: string | null;
  email: string;
  phoneNumber?: string | null;
  avatarUrl?: string | null;
  departmentGroup?: DepartmentGroupEntity | null;
  departmentRole?: DepartmentRoleEntity | null;
  isActive: boolean;
  address?: string | null;
  department?: DepartmentBaseEntity | null;
  codes?: Array<CodeBaseEntity> | null;
  lastMonthRideCompleted?: number;
  lastMonthSpending?: number;
};

