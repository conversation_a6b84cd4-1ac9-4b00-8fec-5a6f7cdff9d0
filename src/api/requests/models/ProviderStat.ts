/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { StatChange } from './StatChange';

export type ProviderStat = {
  provider: ProviderStat.provider;
  value: number;
  changes: StatChange;
};

export namespace ProviderStat {

  export enum provider {
    TAXILOYAL_APP = 'Taxiloyal-app',
    FRAXION = 'Fraxion',
    STO = 'STO',
    MOCTEL = 'moctel',
    RRF_LOYAL = 'RRF_LOYAL',
    TAXI_BUTLER = 'TaxiButler',
    NOMADE = 'Nomade',
    CITYWAY = 'Cityway',
    MEGA_TAXI = 'MegaTaxi',
  }


}

