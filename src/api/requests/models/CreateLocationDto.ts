/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CreateSubLocationDto } from './CreateSubLocationDto';

export type CreateLocationDto = {
  title: string;
  placeId: string;
  status: CreateLocationDto.status;
  address: string;
  subLocations: Array<CreateSubLocationDto>;
};

export namespace CreateLocationDto {

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

