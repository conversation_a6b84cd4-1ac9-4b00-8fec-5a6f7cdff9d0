/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { IPermissionRoleParam } from './IPermissionRoleParam';

export type IPermissionRole = {
  permissions: Array<IPermissionRoleParam>;
  scope: IPermissionRole.scope;
};

export namespace IPermissionRole {

  export enum scope {
    ISOLATED = 'ISOLATED',
    DEPARTMENT = 'DEPARTMENT',
    ALL = 'ALL',
    SUPER_ADMIN = 'SUPER_ADMIN',
  }


}

