/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type BusinessEntity = {
  id: string;
  name: string;
  avatarUrl?: string;
  isActive: boolean;
  businessCode: number;
  address?: string | null;
  phoneNumber?: string | null;
  faxNumber?: string | null;
  website?: string | null;
  email?: string | null;
  lastMonthRideCompleted?: number;
  lastMonthSpending?: number;
  deactivateNote?: string;
};

