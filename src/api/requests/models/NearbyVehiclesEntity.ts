/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type NearbyVehiclesEntity = {
  id: number;
  latitude: number;
  longitude: number;
  vehicleType: NearbyVehiclesEntity.vehicleType;
};

export namespace NearbyVehiclesEntity {

  export enum vehicleType {
    ALL = 'ALL',
    TYPICAL_CAR = 'TYPICAL_CAR',
    ELECTRIC_CAR = 'ELECTRIC_CAR',
    VAN = 'VAN',
    ACCESSIBLE_VAN = 'ACCESSIBLE_VAN',
    ANY_CAR = 'ANY_CAR',
  }


}

