/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { IOverallDashBoardBookingRevenue } from './IOverallDashBoardBookingRevenue';
import type { IOverallDashBoardDetail } from './IOverallDashBoardDetail';

export type IOverallDashBoard = {
  bookingCompleted: IOverallDashBoardDetail;
  bookingCancelled: IOverallDashBoardDetail;
  bookingRevenue: IOverallDashBoardBookingRevenue;
  loyalOneRevenue: IOverallDashBoardDetail;
  newCustomer: IOverallDashBoardDetail;
  currentBooking: IOverallDashBoardDetail;
};

