/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type LoginInputDto = {
  email: string;
  password: string;
  userType?: LoginInputDto.userType | null;
};

export namespace LoginInputDto {

  export enum userType {
    CUSTOMER = 'CUSTOMER',
    BUSINESS_ADMIN = 'BUSINESS_ADMIN',
    SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  }


}

