/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SpecificTimeType } from './SpecificTimeType';

export type UpdateFixedFareDto = {
  fromZones?: Array<any[]>;
  toZones?: Array<any[]>;
  isRoundTrip?: boolean;
  isAllowCoupon?: boolean;
  amount?: number;
  vehicleTypes?: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  applyDateType?: UpdateFixedFareDto.applyDateType;
  applyRepeatDay?: Array<number>;
  startApplyDate?: string;
  endApplyDate?: string;
  status?: UpdateFixedFareDto.status;
  startTime?: SpecificTimeType;
  endTime?: SpecificTimeType;
};

export namespace UpdateFixedFareDto {

  export enum applyDateType {
    ALL = 'ALL',
    SPECIFIC_DATE = 'SPECIFIC_DATE',
    DAY = 'DAY',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

