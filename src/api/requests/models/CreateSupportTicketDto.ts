/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateSupportTicketDto = {
  bookingId: string;
  issueType: CreateSupportTicketDto.issueType;
  supportNote: string;
};

export namespace CreateSupportTicketDto {

  export enum issueType {
    PAYMENT_ISSUE = 'PAYMENT_ISSUE',
    LOST_ITEM = 'LOST_ITEM',
    OTHER = 'OTHER',
  }


}

