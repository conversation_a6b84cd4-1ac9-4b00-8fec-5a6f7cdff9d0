/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CreateSubLocationDto } from './CreateSubLocationDto';

export type UpdateLocationDto = {
  title?: string;
  placeId?: string;
  status?: UpdateLocationDto.status;
  address?: string;
  subLocations?: Array<CreateSubLocationDto>;
};

export namespace UpdateLocationDto {

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

