/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SpecificTimeType } from './SpecificTimeType';

export type UpdateSubscriptionCouponDto = {
  title: string;
  titleFr: string;
  description?: string;
  descriptionFr?: string;
  type: UpdateSubscriptionCouponDto.type;
  value: number;
  maxDiscountLimit?: number;
  quantity: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  paymentMethods?: Array<'ALL' | 'CARD' | 'CASH'>;
  minSpendForAdoption?: number;
  applyForFromSpecificTime?: SpecificTimeType;
  applyForToSpecificTime?: SpecificTimeType;
  applyForRepeatTime?: Array<number> | null;
  applyForPickUpAreaCoordinates?: Array<number> | null;
  applyForPickUpAreaRadius?: number | null;
  applyForDropOffAreaCoordinates?: Array<number> | null;
  applyForDropOffAreaRadius?: number | null;
  applyForBirthDay?: boolean | null;
  applyForBirthMonth?: boolean | null;
  topUpPercent?: number;
};

export namespace UpdateSubscriptionCouponDto {

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
    DROP_RATE = 'DROP_RATE',
  }


}

