/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { IPermissionRole } from './IPermissionRole';

export type IContextUser = {
  id: string;
  fullName: string;
  email: string;
  isBlocked?: boolean | null;
  userProfile: string;
  userType: string;
  permissions?: Array<string> | null;
  permissionRole?: IPermissionRole | null;
  businessId?: string | null;
  isActive: boolean;
};

