/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type ForgotPasswordInputDto = {
  email: string;
  userType?: ForgotPasswordInputDto.userType | null;
};

export namespace ForgotPasswordInputDto {

  export enum userType {
    CUSTOMER = 'CUSTOMER',
    BUSINESS_ADMIN = 'BUSINESS_ADMIN',
    SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  }


}

