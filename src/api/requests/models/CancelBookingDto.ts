/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CancelBookingDto = {
  reason: CancelBookingDto.reason;
  note?: string | null;
};

export namespace CancelBookingDto {

  export enum reason {
    DRIVER_ARRIVED_EARLY = 'DRIVER_ARRIVED_EARLY',
    DRIVER_ASKED_CANCEL = 'DRIVER_ASKED_CANCEL',
    DRIVER_NOT_GETTING_CLOSER = 'DRIVER_NOT_GETTING_CLOSER',
    COULD_NOT_FIND_DRIVER = 'COULD_NOT_FIND_DRIVER',
    WAIT_TIME_TOO_LONG = 'WAIT_TIME_TOO_LONG',
    DRIVER_DELAYED = 'DRIVER_DELAYED',
    DRIVER_NOT_MOVING = 'DRIVER_NOT_MOVING',
    INCORRECT_ADDRESS = 'INCORRECT_ADDRESS',
    CANCELLED_BY_CENTER = 'CANCELLED_BY_CENTER',
    AUTO_CANCELLED = 'AUTO_CANCELLED',
    OTHER = 'OTHER',
  }


}

