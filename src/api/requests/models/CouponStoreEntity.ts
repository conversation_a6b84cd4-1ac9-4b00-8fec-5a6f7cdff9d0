/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { PickUpAreaLocationType } from './PickUpAreaLocationType';
import type { SpecificTimeType } from './SpecificTimeType';
import type { TargetSpecificUser } from './TargetSpecificUser';

export type CouponStoreEntity = {
  id: string;
  title: string;
  titleFr: string;
  code?: string;
  status: CouponStoreEntity.status;
  type: CouponStoreEntity.type;
  value: number;
  validTo?: string;
  description?: string;
  descriptionFr?: string;
  maxDiscountLimit?: number;
  topUpPercent?: number;
  totalUsageLimit: number;
  limitPerUser: number;
  validFrom: string;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  redeemMethod: CouponStoreEntity.redeemMethod;
  paymentMethods?: Array<'ALL' | 'CARD' | 'CASH'>;
  minSpendForAdoption?: number;
  redeemPoint?: number;
  targetUserType: CouponStoreEntity.targetUserType;
  targetSpecificUserIds?: Array<string>;
  targetUserProfile: CouponStoreEntity.targetUserProfile;
  applyForFromSpecificTime?: SpecificTimeType;
  applyForToSpecificTime?: SpecificTimeType;
  applyForRepeatTime?: Array<number>;
  applyForPickUpAreaCoordinates?: Array<number>;
  applyForPickUpAreaRadius?: number;
  applyForDropOffAreaCoordinates?: Array<number>;
  applyForDropOffAreaRadius?: number;
  appliedCount: number;
  createdAt: string;
  targetSpecificUsers?: Array<TargetSpecificUser>;
  applyForPickUpArea?: Array<PickUpAreaLocationType>;
  applyForDropOffArea?: Array<PickUpAreaLocationType>;
  isValid?: boolean;
};

export namespace CouponStoreEntity {

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    EXPIRED = 'EXPIRED',
  }

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
    DROP_RATE = 'DROP_RATE',
  }

  export enum redeemMethod {
    FREE = 'FREE',
    MANUAL_INPUT = 'MANUAL_INPUT',
    REDEEM_BY_POINT = 'REDEEM_BY_POINT',
  }

  export enum targetUserType {
    ALL = 'ALL',
    PAID_USER = 'PAID_USER',
    NEW_USER = 'NEW_USER',
    SPECIFIC_USER = 'SPECIFIC_USER',
  }

  export enum targetUserProfile {
    ALL = 'ALL',
    BUSINESS = 'BUSINESS',
    INDIVIDUAL = 'INDIVIDUAL',
  }


}

