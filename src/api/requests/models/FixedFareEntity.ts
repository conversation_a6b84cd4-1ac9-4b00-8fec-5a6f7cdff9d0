/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SimpleZoneEntity } from './SimpleZoneEntity';
import type { SpecificTimeType } from './SpecificTimeType';
import type { UserBaseEntity } from './UserBaseEntity';

export type FixedFareEntity = {
  id: string;
  fromZones: Array<SimpleZoneEntity>;
  toZones: Array<SimpleZoneEntity>;
  isRoundTrip: boolean;
  isAllowCoupon: boolean;
  amount: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  applyDateType: FixedFareEntity.applyDateType;
  startApplyDate?: string;
  endApplyDate?: string;
  applyRepeatDay?: Array<any[]>;
  status: FixedFareEntity.status;
  startTime?: SpecificTimeType;
  endTime?: SpecificTimeType;
  createdBy: UserBaseEntity;
};

export namespace FixedFareEntity {

  export enum applyDateType {
    ALL = 'ALL',
    SPECIFIC_DATE = 'SPECIFIC_DATE',
    DAY = 'DAY',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

