/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SubLocationType } from './SubLocationType';

export type LocationEntity = {
  id: string;
  title: string;
  placeId: string;
  status: LocationEntity.status;
  address: string;
  subLocations: Array<SubLocationType>;
  type: LocationEntity.type;
  totalSubLocation: number;
};

export namespace LocationEntity {

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }

  export enum type {
    CHILD = 'CHILD',
    PARENT = 'PARENT',
  }


}

