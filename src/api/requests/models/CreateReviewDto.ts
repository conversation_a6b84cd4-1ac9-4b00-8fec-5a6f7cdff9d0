/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateReviewDto = {
  rating?: number;
  note?: string | null;
  tipAmount?: number;
  reviewStatus?: CreateReviewDto.reviewStatus;
};

export namespace CreateReviewDto {

  export enum reviewStatus {
    REVIEWED = 'REVIEWED',
    NOT_REVIEWED = 'NOT_REVIEWED',
    PENDING = 'PENDING',
  }


}

