/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type ResetPasswordInputDto = {
  verifyCode: string;
  email: string;
  password: string;
  userType?: ResetPasswordInputDto.userType | null;
};

export namespace ResetPasswordInputDto {

  export enum userType {
    CUSTOMER = 'CUSTOMER',
    BUSINESS_ADMIN = 'BUSINESS_ADMIN',
    SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  }


}

