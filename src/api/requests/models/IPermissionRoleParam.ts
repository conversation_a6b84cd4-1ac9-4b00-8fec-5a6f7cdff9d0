/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type IPermissionRoleParam = {
  entity: IPermissionRoleParam.entity;
  actions: Array<'CREATE' | 'VIEW' | 'VIEW_REVENUE' | 'VIEW_ASSIGNED' | 'VIEW_DEPARTMENT' | 'UPDATE' | 'DELETE' | 'REFUND'>;
};

export namespace IPermissionRoleParam {

  export enum entity {
    BOOKING_PERSONAL = 'BOOKING_PERSONAL',
    BOOKING_BUSINESS = 'BOOKING_BUSINESS',
    DEPARTMENT = 'DEPARTMENT',
    PERMISSION = 'PERMISSION',
    COUPON = 'COUPON',
    LOCATION = 'LOCATION',
    LOYAL_POINT = 'LOYAL_POINT',
    LOYAL_ONE = 'LOYAL_ONE',
    CONFIG_FEE = 'CONFIG_FEE',
    CONFIG_RATE = 'CONFIG_RATE',
    NOTIFICATION = 'NOTIFICATION',
    BANNER = 'BANNER',
    EMPLOYEE = 'EMPLOYEE',
    CUSTOMER_PERSONAL = 'CUSTOMER_PERSONAL',
    CUSTOMER_BUSINESS = 'CUSTOMER_BUSINESS',
    SUPPORT_TICKET = 'SUPPORT_TICKET',
    DRIVER = 'DRIVER',
    CUSTOMER_REVIEW = 'CUSTOMER_REVIEW',
    AVERAGE_RATING = 'AVERAGE_RATING',
    ZONE = 'ZONE',
    CONFIG_TASK = 'CONFIG_TASK',
    TASK = 'TASK',
    HEAT_MAP = 'HEAT_MAP',
    BOOKING_SOURCE = 'BOOKING_SOURCE',
    FLEET = 'FLEET',
    CONFIG_TIP = 'CONFIG_TIP',
    CONFIG_ARTICLE = 'CONFIG_ARTICLE',
  }


}

