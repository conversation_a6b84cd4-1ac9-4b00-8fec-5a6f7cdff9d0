/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { UserBaseEntity } from './UserBaseEntity';

export type TipPositionLogEntity = {
  actionBy: UserBaseEntity;
  createdAt: string;
  tipPosition: TipPositionLogEntity.tipPosition;
};

export namespace TipPositionLogEntity {

  export enum tipPosition {
    BEFORE = 'BEFORE',
    AFTER = 'AFTER',
  }


}

