/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BookingAddressType } from './BookingAddressType';
import type { DriverEntity } from './DriverEntity';
import type { EmployeeBaseEntity } from './EmployeeBaseEntity';
import type { UserBaseEntity } from './UserBaseEntity';
import type { VehicleEntity } from './VehicleEntity';

export type BookingSharingEntity = {
  bookingCode: number;
  user: UserBaseEntity;
  employee?: EmployeeBaseEntity;
  vehicleType: BookingSharingEntity.vehicleType;
  originLocation: Array<number>;
  originAddress: BookingAddressType;
  destinationLocation: Array<number>;
  destinationAddress: BookingAddressType;
  currentLocation?: Array<number> | null;
  driver?: DriverEntity | null;
  driverId?: number;
  vehicle?: VehicleEntity | null;
  status: BookingSharingEntity.status;
  vehicleId?: number;
};

export namespace BookingSharingEntity {

  export enum vehicleType {
    ALL = 'ALL',
    TYPICAL_CAR = 'TYPICAL_CAR',
    ELECTRIC_CAR = 'ELECTRIC_CAR',
    VAN = 'VAN',
    ACCESSIBLE_VAN = 'ACCESSIBLE_VAN',
    ANY_CAR = 'ANY_CAR',
  }

  export enum status {
    DRAFT = 'DRAFT',
    SCHEDULED = 'SCHEDULED',
    DEMAND_CREATION = 'DEMAND_CREATION',
    TO_BE_DISTRIBUTED = 'TO_BE_DISTRIBUTED',
    CONFIRMED_ADDRESS = 'CONFIRMED_ADDRESS',
    AWAITING_CONFIRMED_VEHICLE = 'AWAITING_CONFIRMED_VEHICLE',
    VEHICLE_CONFIRMED = 'VEHICLE_CONFIRMED',
    ARRIVAL_AT_CLIENT = 'ARRIVAL_AT_CLIENT',
    CLIENT_ON_BOARD = 'CLIENT_ON_BOARD',
    AMOUNT_MONEY_RECEIVED = 'AMOUNT_MONEY_RECEIVED',
    COMPLETED = 'COMPLETED',
    NO_LOAD = 'NO_LOAD',
    CANCELLATION_REQUEST = 'CANCELLATION_REQUEST',
    CANCELLED = 'CANCELLED',
  }


}

