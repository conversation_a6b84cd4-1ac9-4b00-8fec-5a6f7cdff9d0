/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type DiscountEntity = {
  id: string;
  type: DiscountEntity.type;
  value: number;
  validFrom: string;
  validTo: string;
  status: DiscountEntity.status;
  recurringType: DiscountEntity.recurringType;
  stripeCouponCode: string;
  createdById: string;
};

export namespace DiscountEntity {

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    EXPIRED = 'EXPIRED',
  }

  export enum recurringType {
    MONTHLY_1 = 'MONTHLY_1',
    YEARLY_1 = 'YEARLY_1',
  }


}

