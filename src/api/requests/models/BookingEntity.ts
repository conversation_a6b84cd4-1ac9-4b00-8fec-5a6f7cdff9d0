/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BookingAddressType } from './BookingAddressType';
import type { BookingDriverEntity } from './BookingDriverEntity';
import type { BookingRateFeeEntity } from './BookingRateFeeEntity';
import type { BookingReviewType } from './BookingReviewType';
import type { BusinessBaseEntity } from './BusinessBaseEntity';
import type { CancelReason } from './CancelReason';
import type { CardEntity } from './CardEntity';
import type { CodeBaseEntity } from './CodeBaseEntity';
import type { CouponBaseEntity } from './CouponBaseEntity';
import type { DepartmentBaseEntity } from './DepartmentBaseEntity';
import type { EmployeeBaseEntity } from './EmployeeBaseEntity';
import type { ServiceFeeType } from './ServiceFeeType';
import type { UserBaseEntity } from './UserBaseEntity';
import type { VehicleEntity } from './VehicleEntity';

export type BookingEntity = {
  bookingCode: number;
  id: string;
  distance: number;
  amount: number;
  originAddress: BookingAddressType;
  destinationAddress: BookingAddressType;
  vehicle?: VehicleEntity | null;
  paymentMethodType?: BookingEntity.paymentMethodType;
  user: UserBaseEntity;
  employee?: EmployeeBaseEntity;
  code?: CodeBaseEntity;
  type: string;
  date: string;
  endDate?: string;
  scheduledDate?: string;
  duration: number;
  vehicleType: BookingEntity.vehicleType;
  originLocation: Array<number>;
  destinationLocation: Array<number>;
  currentLocation?: Array<number> | null;
  driver?: BookingDriverEntity | null;
  driverId?: number;
  status: BookingEntity.status;
  business?: BusinessBaseEntity;
  department?: DepartmentBaseEntity;
  coupon?: CouponBaseEntity;
  createdAt: string;
  paymentData: Record<string, any>;
  detailPayment: Record<string, any>;
  price?: number;
  tps: number;
  tvq: number;
  cancelReason?: CancelReason;
  minAmount?: number;
  isCancelAfterChargeTime?: boolean;
  awardedPoint: number;
  awardedFirstRide?: number;
  paymentDate?: string;
  review?: BookingReviewType;
  stripeFees: number;
  amountDriver?: number;
  note?: string;
  tipAmount?: number;
  tipStripeFees?: number;
  tipPaymentId?: string;
  vehicleId?: number;
  reviewStatus: BookingEntity.reviewStatus;
  destinationsChanged?: Array<BookingAddressType>;
  couponValue?: number;
  refundTag?: BookingEntity.refundTag;
  refundAmount?: number;
  encryptedId?: string;
  serviceFees: Array<ServiceFeeType>;
  tripFare: BookingRateFeeEntity;
  tripFareFee: number;
  couponAmount: number;
  topUpAmount?: number;
  zones: Array<number>;
  card?: CardEntity;
  tipCard?: CardEntity;
};

export namespace BookingEntity {

  export enum paymentMethodType {
    ALL = 'ALL',
    CARD = 'CARD',
    CASH = 'CASH',
  }

  export enum vehicleType {
    ALL = 'ALL',
    TYPICAL_CAR = 'TYPICAL_CAR',
    ELECTRIC_CAR = 'ELECTRIC_CAR',
    VAN = 'VAN',
    ACCESSIBLE_VAN = 'ACCESSIBLE_VAN',
    ANY_CAR = 'ANY_CAR',
  }

  export enum status {
    DRAFT = 'DRAFT',
    SCHEDULED = 'SCHEDULED',
    DEMAND_CREATION = 'DEMAND_CREATION',
    TO_BE_DISTRIBUTED = 'TO_BE_DISTRIBUTED',
    CONFIRMED_ADDRESS = 'CONFIRMED_ADDRESS',
    AWAITING_CONFIRMED_VEHICLE = 'AWAITING_CONFIRMED_VEHICLE',
    VEHICLE_CONFIRMED = 'VEHICLE_CONFIRMED',
    ARRIVAL_AT_CLIENT = 'ARRIVAL_AT_CLIENT',
    CLIENT_ON_BOARD = 'CLIENT_ON_BOARD',
    AMOUNT_MONEY_RECEIVED = 'AMOUNT_MONEY_RECEIVED',
    COMPLETED = 'COMPLETED',
    NO_LOAD = 'NO_LOAD',
    CANCELLATION_REQUEST = 'CANCELLATION_REQUEST',
    CANCELLED = 'CANCELLED',
  }

  export enum reviewStatus {
    REVIEWED = 'REVIEWED',
    NOT_REVIEWED = 'NOT_REVIEWED',
    PENDING = 'PENDING',
  }

  export enum refundTag {
    REFUNDED = 'REFUNDED',
    PARTIAL_REFUND = 'PARTIAL_REFUND',
  }


}

