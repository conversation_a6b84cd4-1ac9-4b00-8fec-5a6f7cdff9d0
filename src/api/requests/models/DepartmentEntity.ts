/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CodeBaseEntity } from './CodeBaseEntity';

export type DepartmentEntity = {
  id: string;
  name: string;
  isActive: boolean;
  departmentCode: number;
  description: string;
  businessId: string;
  codes: Array<CodeBaseEntity>;
  totalEmployee: number;
  lastMonthAmount: number;
};

