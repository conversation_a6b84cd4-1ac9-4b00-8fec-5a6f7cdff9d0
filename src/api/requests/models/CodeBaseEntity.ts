/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CodeBaseEntity = {
  id: string;
  code: string;
  PIN: string;
  maxSpendingLimit?: number | null;
  maxNumberOfUsage?: number | null;
  distanceLimit?: number | null;
  expiredDate?: string | null;
  repeatType: CodeBaseEntity.repeatType;
  isActive: boolean;
  departmentId: string;
};

export namespace CodeBaseEntity {

  export enum repeatType {
    NO_REPEAT = 'NO_REPEAT',
    WEEKLY = 'WEEKLY',
    MONTHLY = 'MONTHLY',
  }


}

