/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { LocationDto } from './LocationDto';

export type CreateBookingWithPaymentDto = {
  originLocation: LocationDto;
  destinationLocation: LocationDto;
  amount: number;
  vehicleType: CreateBookingWithPaymentDto.vehicleType;
  date?: string | null;
  paymentMethodType?: CreateBookingWithPaymentDto.paymentMethodType;
  couponId?: string | null;
  note?: string | null;
  tipAmount?: number | null;
};

export namespace CreateBookingWithPaymentDto {

  export enum vehicleType {
    ALL = 'ALL',
    TYPICAL_CAR = 'TYPICAL_CAR',
    ELECTRIC_CAR = 'ELECTRIC_CAR',
    VAN = 'VAN',
    ACCESSIBLE_VAN = 'ACCESSIBLE_VAN',
    ANY_CAR = 'ANY_CAR',
  }

  export enum paymentMethodType {
    ALL = 'ALL',
    CARD = 'CARD',
    CASH = 'CASH',
  }


}

