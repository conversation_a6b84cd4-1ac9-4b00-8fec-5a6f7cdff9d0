/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { EmployeeBaseEntity } from './EmployeeBaseEntity';

export type EmployeeCodeAdminEntity = {
  employee: EmployeeBaseEntity;
  source?: EmployeeCodeAdminEntity.source | null;
};

export namespace EmployeeCodeAdminEntity {

  export enum source {
    MOBILE_APP = 'MOBILE_APP',
    BUSINESS_ADMIN = 'BUSINESS_ADMIN',
  }


}

