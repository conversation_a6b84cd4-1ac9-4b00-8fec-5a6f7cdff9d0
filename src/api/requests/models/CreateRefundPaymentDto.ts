/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateRefundPaymentDto = {
  amount: number;
  reasonType: CreateRefundPaymentDto.reasonType;
  reasonNote: string;
};

export namespace CreateRefundPaymentDto {

  export enum reasonType {
    DUPLICATE = 'DUPLICATE',
    FRAUDULENT = 'FRAUDULENT',
    REQUESTED_BY_CUSTOMER = 'REQUESTED_BY_CUSTOMER',
    OTHER = 'OTHER',
  }


}

