/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CreatedByEntity } from './CreatedByEntity';
import type { TargetSpecificUser } from './TargetSpecificUser';

export type AdminNotificationEntity = {
  id: string;
  userIds: Array<string>;
  businessId: string | null;
  title: string;
  titleFr: string;
  message: string;
  messageFr: string;
  sortMessage: string;
  sortMessageFr: string;
  isSeen: boolean;
  notificationType: AdminNotificationEntity.notificationType;
  createdAt: string;
  sendTime: string;
  createdType: string;
  type: string;
  status: string;
  createdBy: CreatedByEntity;
  campaign: string;
  targetSpecificUsers?: Array<TargetSpecificUser>;
  targetNotificationUserType?: AdminNotificationEntity.targetNotificationUserType;
  totalSelectedUsers: number;
};

export namespace AdminNotificationEntity {

  export enum notificationType {
    NEWS = 'NEWS',
    BOOKING_CONFIRMED = 'BOOKING_CONFIRMED',
    BOOKING_DRIVER_ON_THE_WAY = 'BOOKING_DRIVER_ON_THE_WAY',
    BOOKING_DRIVER_ARRIVED = 'BOOKING_DRIVER_ARRIVED',
    BOOKING_CANCELLED = 'BOOKING_CANCELLED',
    BOOKING_AUTO_CANCELLED = 'BOOKING_AUTO_CANCELLED',
    BOOKING_COMPLETED = 'BOOKING_COMPLETED',
    BOOKING_SCHEDULE = 'BOOKING_SCHEDULE',
    BOOKING_PAYMENT_SUCCESS = 'BOOKING_PAYMENT_SUCCESS',
    BOOKING_PAYMENT_FAILURE = 'BOOKING_PAYMENT_FAILURE',
    BOOKING_RIDE_TOMORROW = 'BOOKING_RIDE_TOMORROW',
    BOOKING_RIDE_IN_HOUR = 'BOOKING_RIDE_IN_HOUR',
    BOOKING_WARNING_CANCEL = 'BOOKING_WARNING_CANCEL',
    USER_COUPON_REMINDER = 'USER_COUPON_REMINDER',
    FIRST_RIDE = 'FIRST_RIDE',
    COMPLETED_RIDE = 'COMPLETED_RIDE',
    COMPLETED_RIDE_REMINDER = 'COMPLETED_RIDE_REMINDER',
    COUPON_REMINDER = 'COUPON_REMINDER',
    BOOKING_REMINDER = 'BOOKING_REMINDER',
    SUCCESSFUL_REFERRAL = 'SUCCESSFUL_REFERRAL',
    REFERRAL_BONUS_UNLOCKED = 'REFERRAL_BONUS_UNLOCKED',
    SUBSCRIPTION_RENEWAL_FAILED = 'SUBSCRIPTION_RENEWAL_FAILED',
    SUBSCRIPTION_RENEWAL_SUCCESSFULLY = 'SUBSCRIPTION_RENEWAL_SUCCESSFULLY',
    SUBSCRIPTION_CANCELLED = 'SUBSCRIPTION_CANCELLED',
    SUBSCRIPTION_RESUBSCRIBE = 'SUBSCRIPTION_RESUBSCRIBE',
    SUBSCRIPTION_EXPIRED = 'SUBSCRIPTION_EXPIRED',
    SUBSCRIPTION_REMINDER = 'SUBSCRIPTION_REMINDER',
    BOOKING_REVIEW_REMINDER = 'BOOKING_REVIEW_REMINDER',
  }

  export enum targetNotificationUserType {
    ALL = 'ALL',
    PAID_USER = 'PAID_USER',
    NEW_USER = 'NEW_USER',
    SPECIFIC_USER = 'SPECIFIC_USER',
  }


}

