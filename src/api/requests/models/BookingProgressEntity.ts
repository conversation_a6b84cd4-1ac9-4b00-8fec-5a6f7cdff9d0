/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CancelReason } from './CancelReason';

export type BookingProgressEntity = {
  status: BookingProgressEntity.status;
  date: string;
  cancelReason?: CancelReason;
};

export namespace BookingProgressEntity {

  export enum status {
    DEMAND_CREATION = 'DEMAND_CREATION',
    TO_BE_DISTRIBUTED = 'TO_BE_DISTRIBUTED',
    CONFIRMED_ADDRESS = 'CONFIRMED_ADDRESS',
    AWAITING_CONFIRMED_VEHICLE = 'AWAITING_CONFIRMED_VEHICLE',
    VEHICLE_CONFIRMED = 'VEHICLE_CONFIRMED',
    ARRIVAL_AT_CLIENT = 'ARRIVAL_AT_CLIENT',
    CLIENT_ON_BOARD = 'CLIENT_ON_BOARD',
    AMOUNT_MONEY_RECEIVED = 'AMOUNT_MONEY_RECEIVED',
    COMPLETED = 'COMPLETED',
    NO_LOAD = 'NO_LOAD',
    CANCELLATION_REQUEST = 'CANCELLATION_REQUEST',
    CANCELLED = 'CANCELLED',
  }


}

