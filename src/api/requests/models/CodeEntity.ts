/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { DepartmentBaseEntity } from './DepartmentBaseEntity';
import type { EmployeeBaseEntity } from './EmployeeBaseEntity';

export type CodeEntity = {
  id: string;
  code: string;
  PIN: string;
  maxSpendingLimit?: number | null;
  maxNumberOfUsage?: number | null;
  distanceLimit?: number | null;
  expiredDate?: string | null;
  repeatType: CodeEntity.repeatType;
  isActive: boolean;
  departmentId: string;
  department?: DepartmentBaseEntity | null;
  employee?: EmployeeBaseEntity | null;
  lastUsedDate?: string | null;
  lastChangedDate?: string | null;
  updatedAt: string;
};

export namespace CodeEntity {

  export enum repeatType {
    NO_REPEAT = 'NO_REPEAT',
    WEEKLY = 'WEEKLY',
    MONTHLY = 'MONTHLY',
  }


}

