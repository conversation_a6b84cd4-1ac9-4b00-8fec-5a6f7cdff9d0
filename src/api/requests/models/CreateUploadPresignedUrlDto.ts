/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type CreateUploadPresignedUrlDto = {
  fileName: string;
  fileType: string;
  folderPrefix: CreateUploadPresignedUrlDto.folderPrefix;
};

export namespace CreateUploadPresignedUrlDto {

  export enum folderPrefix {
    AVATARS = 'avatars',
    EXCELS = 'excels',
    BANNERS = 'banners',
  }


}

