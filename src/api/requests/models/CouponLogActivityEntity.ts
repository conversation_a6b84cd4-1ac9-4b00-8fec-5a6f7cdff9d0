/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { UserBaseEntity } from './UserBaseEntity';

export type CouponLogActivityEntity = {
  actionBy: UserBaseEntity;
  activity: CouponLogActivityEntity.activity;
  atTime: string;
};

export namespace CouponLogActivityEntity {

  export enum activity {
    CREATE = 'CREATE',
    UPDATE = 'UPDATE',
    ACTIVE = 'ACTIVE',
    DEACTIVATE = 'DEACTIVATE',
  }


}

