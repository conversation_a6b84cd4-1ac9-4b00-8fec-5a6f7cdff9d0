/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BookingAddressType } from './BookingAddressType';
import type { VehicleEntity } from './VehicleEntity';

export type BaseBookingEntity = {
  bookingCode: number;
  id: string;
  distance: number;
  amount: number;
  originAddress: BookingAddressType;
  destinationAddress: BookingAddressType;
  vehicle?: VehicleEntity | null;
  paymentMethodType?: BaseBookingEntity.paymentMethodType;
};

export namespace BaseBookingEntity {

  export enum paymentMethodType {
    ALL = 'ALL',
    CARD = 'CARD',
    CASH = 'CASH',
  }


}

