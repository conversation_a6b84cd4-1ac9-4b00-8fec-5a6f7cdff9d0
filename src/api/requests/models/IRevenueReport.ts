/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type IRevenueReport = {
  date: string;
  count: number;
  type: IRevenueReport.type;
  bookingType?: IRevenueReport.bookingType | null;
  paymentMethodType: string;
  zone?: string | null;
};

export namespace IRevenueReport {

  export enum type {
    BOOKING = 'BOOKING',
    LOYAL_ONE = 'LOYAL_ONE',
  }

  export enum bookingType {
    PERSONAL = 'PERSONAL',
    BUSINESS = 'BUSINESS',
  }


}

