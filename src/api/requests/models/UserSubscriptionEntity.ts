/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { StripeSubscriptionPaymentDataType } from './StripeSubscriptionPaymentDataType';
import type { SubscriptionEntity } from './SubscriptionEntity';
import type { SubscriptionPlanEntity } from './SubscriptionPlanEntity';

export type UserSubscriptionEntity = {
  id: string;
  subscriptionId: string;
  plan: SubscriptionPlanEntity;
  nextPlan: SubscriptionPlanEntity;
  status: UserSubscriptionEntity.status;
  paymentData: StripeSubscriptionPaymentDataType;
  renewDate?: string;
  endDate?: string;
  subscription: SubscriptionEntity;
  isChangePlan: boolean;
  appliedDiscounts: Array<string>;
};

export namespace UserSubscriptionEntity {

  export enum status {
    PENDING = 'PENDING',
    SUBSCRIBED = 'SUBSCRIBED',
    CANCELED = 'CANCELED',
  }


}

