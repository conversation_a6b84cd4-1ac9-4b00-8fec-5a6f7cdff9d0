/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SubscriptionPlanEntity } from './SubscriptionPlanEntity';

export type AppSubscriptionEntity = {
  id: string;
  name: string;
  description: string;
  usageInstruction: string;
  termsOfUse: string;
  plans: Array<SubscriptionPlanEntity>;
  status: AppSubscriptionEntity.status;
  createdAt: string;
};

export namespace AppSubscriptionEntity {

  export enum status {
    NOT_STARTED = 'NOT_STARTED',
    IN_PROGRESS = 'IN_PROGRESS',
    CANCELLED = 'CANCELLED',
  }


}

