/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { LocationDto } from './LocationDto';

export type CreateBookingWithCodeDto = {
  originLocation: LocationDto;
  destinationLocation: LocationDto;
  amount: number;
  codeId: string;
  vehicleType: CreateBookingWithCodeDto.vehicleType;
  date?: string | null;
  couponId?: string | null;
  note?: string | null;
};

export namespace CreateBookingWithCodeDto {

  export enum vehicleType {
    ALL = 'ALL',
    TYPICAL_CAR = 'TYPICAL_CAR',
    ELECTRIC_CAR = 'ELECTRIC_CAR',
    VAN = 'VAN',
    ACCESSIBLE_VAN = 'ACCESSIBLE_VAN',
    ANY_CAR = 'ANY_CAR',
  }


}

