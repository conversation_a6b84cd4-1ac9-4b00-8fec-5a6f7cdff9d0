/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SpecificTimeType } from './SpecificTimeType';

export type SubscriptionCouponEntity = {
  id: string;
  title: string;
  description?: string;
  quantity?: number;
  status: SubscriptionCouponEntity.status;
  type: SubscriptionCouponEntity.type;
  value: number;
  maxDiscountLimit?: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  paymentMethods?: Array<'ALL' | 'CARD' | 'CASH'>;
  minSpendForAdoption?: number;
  applyForFromSpecificTime?: SpecificTimeType;
  applyForToSpecificTime?: SpecificTimeType;
  applyForRepeatTime?: Array<any[]>;
  applyForPickUpAreaCoordinates?: Array<any[]>;
  applyForPickUpAreaRadius?: number;
  applyForPickUpArea?: Array<any[]>;
  applyForDropOffArea?: Array<any[]>;
  applyForDropOffAreaCoordinates?: Array<any[]>;
  applyForDropOffAreaRadius?: number;
  applyForBirthDay?: boolean;
  applyForBirthMonth?: boolean;
  pos: number;
  createdAt: string;
  titleFr: string;
  descriptionFr: string;
  topUpPercent?: number;
};

export namespace SubscriptionCouponEntity {

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    EXPIRED = 'EXPIRED',
  }

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
    DROP_RATE = 'DROP_RATE',
  }


}

