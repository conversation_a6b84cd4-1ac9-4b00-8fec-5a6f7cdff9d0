/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SubscriptionBaseEntity } from './SubscriptionBaseEntity';

export type UserEntity = {
  id: string;
  userCode: number;
  email: string;
  avatarUrl: string;
  fullName: string;
  phoneNumber?: string;
  address: string;
  locale: string;
  isActive: boolean;
  permissions: Array<string>;
  departmentIds: Array<string>;
  gender?: UserEntity.gender | null;
  birthday?: string | null;
  lastMonthRideCompleted?: number;
  lastMonthSpending?: number;
  currentPoint: number;
  userType: UserEntity.userType;
  referralCode: string;
  subscription?: SubscriptionBaseEntity | null;
  deactivateNote?: string | null;
  createdAt: string;
};

export namespace UserEntity {

  export enum gender {
    MALE = 'MALE',
    FEMALE = 'FEMALE',
    OTHER = 'OTHER',
  }

  export enum userType {
    PAID_USER = 'PAID_USER',
    NEW_USER = 'NEW_USER',
  }


}

