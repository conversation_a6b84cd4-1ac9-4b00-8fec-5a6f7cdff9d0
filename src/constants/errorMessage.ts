import i18n from 'i18next';

export enum ServerErrorCode {
  InternalServerError = 500,
  BadGateway = 502,
  ServiceUnavailable = 503,
  GatewayTimeout = 504,
}

export interface ErrorInfo {
  code: ServerErrorCode;
  message: string;
  description: string;
}

const errorMessages: ErrorInfo[] = [
  {
    code: ServerErrorCode.InternalServerError,
    description: i18n.t('errorMessage.500.description'),
    message: i18n.t('errorMessage.500.message'),
  },
  {
    code: ServerErrorCode.BadGateway,
    description: i18n.t('errorMessage.502.description'),
    message: i18n.t('errorMessage.502.message'),
  },
  {
    code: ServerErrorCode.ServiceUnavailable,
    description: i18n.t('errorMessage.503.description'),
    message: i18n.t('errorMessage.503.message'),
  },
  {
    code: ServerErrorCode.GatewayTimeout,
    description: i18n.t('errorMessage.504.description'),
    message: i18n.t('errorMessage.504.message'),
  },
];
export function getErrorMessage(errorCode: number): {
  message: string;
  description: string;
} {
  const error = errorMessages.find(e => e.code === errorCode);
  if (error) {
    return {description: error.description, message: error.message};
  }
  return {description: '', message: ''};
}
