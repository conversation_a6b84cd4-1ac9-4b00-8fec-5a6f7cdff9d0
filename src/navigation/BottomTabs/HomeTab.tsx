import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Home from '@screens/Home';

const Stack = createNativeStackNavigator();

function HomeTab() {
  return (
    <Stack.Navigator initialRouteName={'Home'}>
      <Stack.Screen
        component={Home}
        name={'Home'}
        options={({route}: {route: any}) => ({
          headerShown: false,
          title: route?.params?.headerTitle,
        })}
      />
    </Stack.Navigator>
  );
}

export default HomeTab;
