/* eslint-disable no-nested-ternary */
/* eslint-disable no-restricted-imports */
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import {StyleSheet, Image} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import LottieView from 'lottie-react-native';
import Notifications from '../../screens/Notifications';
import Activities from '../../screens/Activities';
import HomeTab from './HomeTab';
import {WText, WView} from '@components';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors, Images} from '@themes';
import Settings from '@screens/Settings';
import {
  useNotificationsServiceNotificationControllerHasUnseenNotifications,
  useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
} from '@queries';
import {useSpecialDay} from '@utils/hooks/useSpecialDay';
import {LOTTIE} from '@themes/Lottie';
import {useBirthdayEvent} from '@utils/hooks/useBirthdayEvent';

const BottomTab = createBottomTabNavigator();

function BottomTabs() {
  const {t} = useTypeSafeTranslation();
  const {dataSpecial} = useSpecialDay();
  const {isHappening} = useBirthdayEvent();

  const {data: dataHasUnSeen} =
    useNotificationsServiceNotificationControllerHasUnseenNotifications([
      useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
    ]);

  const defaultHeaderProps = {
    headerStyle: {
      borderBottomColor: Colors.grey3,
      borderBottomWidth: 0.4,
    },
    headerTitleAlign: 'center',
  };

  return (
    <BottomTab.Navigator
      sceneContainerStyle={{backgroundColor: Colors.white}}
      screenOptions={
        {
          // tabBarHideOnKeyboard: Platform.OS === 'android',
        }
      }>
      <BottomTab.Screen
        component={HomeTab}
        name="HomeTab"
        options={() => ({
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <WView>
              {isHappening && focused ? (
                <LottieView
                  autoPlay
                  resizeMode="cover"
                  source={LOTTIE.birthday40}
                  speed={1}
                  style={styles.lottieBirthday}
                />
              ) : (
                <>
                  {dataSpecial?.bottomHome && focused ? (
                    <LottieView
                      autoPlay
                      resizeMode="cover"
                      source={dataSpecial?.bottomHome}
                      speed={0.5}
                      style={styles.lottieView}
                    />
                  ) : (
                    <Image
                      resizeMode="contain"
                      source={Images.simpleLogo}
                      style={[
                        styles.imgHome,
                        {
                          tintColor: focused
                            ? Colors.primary
                            : Colors.neutral500,
                        },
                      ]}
                    />
                  )}
                </>
              )}
            </WView>
          ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <WText
              color={focused ? Colors.primary : Colors.neutral700}
              type="medium12">
              {t('bottomTab.home')}
            </WText>
          ),
        })}
      />

      <BottomTab.Screen
        component={Activities}
        name="ActivitiesTab"
        options={() => ({
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Icon
              color={focused ? Colors.primary : Colors.neutral500}
              name="Outline-Notepad"
              size={20}
              style={styles.mTop}
            />
          ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <WText
              color={focused ? Colors.primary : Colors.neutral700}
              type="medium12">
              {t('bottomTab.activities')}
            </WText>
          ),
        })}
      />
      <BottomTab.Screen
        component={Notifications}
        name="Notification"
        options={() => ({
          headerShown: false,
          headerStyle: {
            backgroundColor: Colors.neutral900,
          },
          tabBarIcon: ({focused}) => (
            <WView>
              <Icon
                color={focused ? Colors.primary : Colors.neutral500}
                name="Outline-BellRinging"
                size={20}
                style={styles.mTop}
              />
              {dataHasUnSeen?.data && (
                <WView
                  aLeft={16}
                  aTop={2}
                  absolute
                  borderRadius={8}
                  color={Colors.primary}
                  h={8}
                  w={8}
                />
              )}
            </WView>
          ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <WText
              color={focused ? Colors.primary : Colors.neutral700}
              type="medium12">
              {t('bottomTab.notification')}
            </WText>
          ),
        })}
      />

      <BottomTab.Screen
        component={Settings}
        name="SettingTab"
        options={() =>
          ({
            ...defaultHeaderProps,
            headerShown: false,
            headerStyle: {
              backgroundColor: Colors.white,
            },
            tabBarIcon: ({focused}: {focused: boolean}) => (
              <Icon
                color={focused ? Colors.primary : Colors.neutral500}
                name={focused ? 'Outline-Gear' : 'Outline-Gear'}
                size={20}
                style={styles.mTop}
              />
            ),
            // eslint-disable-next-line react/no-unused-prop-types
            tabBarLabel: ({focused}: {focused: boolean}) => (
              <WText
                color={focused ? Colors.primary : Colors.neutral700}
                type="medium12">
                {t('bottomTab.setting')}
              </WText>
            ),
          } as any)
        }
      />
    </BottomTab.Navigator>
  );
}

export default BottomTabs;

const styles = StyleSheet.create({
  imgHome: {
    height: 16,
    marginTop: 4,
    width: 16,
  },
  lottieBirthday: {height: 35, marginTop: 4, width: 35},
  lottieView: {
    height: 40,
    marginTop: 12,
    width: 50,
  },
  mTop: {marginTop: 8},
});
