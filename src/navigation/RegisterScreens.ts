/*
 * import {createNativeStackNavigator} from '@react-navigation/native-stack';
 * import Login from '../screens/Auth/Login';
 */

/*
 * const SCREENS: any = {
 *   Login,
 * };
 */

// const Stack = createNativeStackNavigator();

/*
 * export const renderListChild = () => (
 *   <>
 *     {Object.keys(SCREENS).map(item => (
 *       <Stack.Screen
 *         component={SCREENS[item]}
 *         key={item}
 *         name={item}
 *         options={({route, navigation}: {route: any; navigation: any}) => ({
 *           contentStyle: {backgroundColor: Colors.white},
 *           headerShadowVisible: !route?.params?.noBorder,
 *           headerShown: route?.params?.isTopBarEnable,
 *           headerTitleAlign: 'center',
 *           title: route?.params?.headerTitle,
 *           ...(SCREEN_HAS_BACK_HOME_RIGHT_BUTTONS.includes(item) && {
 *             headerRight: () => (
 *               <TouchableOpacity
 *                 onPress={() => {
 *                   navigation.navigate('HomeTab');
 *                 }}>
 *                 <Icon color={Colors.grey1} name="ic-home-3" size={20} />
 *               </TouchableOpacity>
 *             ),
 *           }),
 *         })}
 *       />
 *     ))}
 *   </>
 * );
 */
