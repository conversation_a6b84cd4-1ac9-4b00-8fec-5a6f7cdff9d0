import {
  CommonActions,
  createNavigationContainerRef,
  NavigationProp,
  StackActions,
} from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

export function navigate<P = undefined>(name: string, params?: P) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  }
}

export function goBack() {
  if (navigationRef.isReady()) {
    navigationRef.goBack();
  }
}

export const PopTo = (number = 1) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.pop(number));
  }
};

export const getCurrentRoute = () => {
  if (navigationRef.isReady()) {
    return navigationRef.getCurrentRoute();
  }
};

export const reset = params => {
  if (navigationRef.isReady()) {
    return navigationRef.reset(params);
  }
};
