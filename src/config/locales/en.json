{"common": {"optional": "(optional)"}, "validate": {"email": {"invalid": "<PERSON><PERSON> is invalid", "required": "Email is required"}, "password": {"invalid": "Password is invalid", "required": "Password is required", "min": "Be at least 8 characters.", "shouldContain": "Should contain:", "lowerCase": "Lower case letters (a-z).", "upperCase": "Upper case letters (A-Z).", "number": "Number (0-9).", "specialCharacters": "Special characters."}, "fullName": {"invalid": "Full name is invalid", "required": "Full name is required"}, "phoneNumber": {"invalid": "Phone number is invalid", "required": "Phone number is required"}, "code": {"required": "Code is required"}, "pin": {"required": "PIN is required", "numbersOnly": "Please enter numbers only"}, "referralCode": {"invalid": "Referral Code is invalid"}, "reason": {"required": "Reason is required"}, "yourIssue": {"required": "The issue is required"}}, "button": {"confirm": "Confirm", "resendCode": "Resend code", "confirmCancelBooking": "Yes, cancel", "bookCar": "Book This Car", "cancel": "Cancel", "discard": "Discard", "addLocation": "Add New Location", "apply": "Apply", "cancelSchedule": "Cancel Schedule", "saveChange": "Save Change", "edit": "Edit", "addMethod": "Add method", "addCard": "Add Credit Card/Debit Card", "unlink": "Unlink", "addCodePin": "Add CODE & PIN", "ok": "OK", "login": "Log in", "help": "Need Help", "confirmDestination": "Confirm Destination", "confirmPickUp": "Confirm This Pick-Up", "addNewBusiness": "Add New Business", "submit": "Submit", "later": "Later", "save": "Save", "addPromoCode": "Enter your promo code", "cancelBooking": "Cancel Booking", "saveLocation": "Save This Location", "stillWait": "Still Wait", "removeCoupon": "Remove This Coupon", "applyCoupon": "Apply This Coupon", "remove": "Remove", "changePaymentMethod": "Change Payment Method", "addBusiness": "Add Business", "bookACar": "Book A Car", "updateNow": "Update Now", "getStarted": "Get Started", "addPaymentMethod": "Add Payment Method", "logout": "Logout", "add": "Add", "skipAndCancel": "Skip and Cancel", "keepMyRide": "Keep My Ride", "confirmDelete": "Confirm to delete", "delete": "Delete", "bookingNow": "Booking Now", "subscribe": "Subscribe", "resubscribe": "Resubscribe", "subscribeNow": "Subscribe Now", "change": "Change", "cancelPlan": "Cancel Package", "proceedCancel": "Proceed to Unsubscribe", "gotIt": "Got it", "skip": "<PERSON><PERSON>", "shareLink": "Share link", "view": "View", "useNow": "Use Now", "useLater": "Use Later", "use": "Use", "useLater2Line": "Use\nLater", "seeOtherCoupon": "See other coupon", "continueWithoutCoupon": "Continue without coupon", "viewLoyalPoints": "View Loyal Points", "dismiss": "<PERSON><PERSON><PERSON>", "redeemCoupon": "Redeem Coupon", "redeemCouponPoint": "Redeem Coupon ({{count}} Points)", "keepPlan": "Keep Package", "viewDetail": "View Detail", "continue": "Continue", "redeem": "Redeem", "redeemAndUse": "Redeem and Use", "next": "Next", "selectPickupTime": "Select Pickup Time", "reschedule": "Reschedule", "bookNow": "Book Now", "reserveBooking": "Reserve Booking", "pickUpNow": "Pick Up Now", "backToHomepage": "Back to Homepage", "scheduleBooking": "Schedule Booking", "back": "Back", "verify": "Verify", "resendCodeIn": "Resend code in", "verifyNow": "Verify now", "verified": "Verified", "applyChange": "Apply Change", "done": "Done", "resend": "Resend", "finish": "Finish"}, "login": {"title": "<PERSON><PERSON>", "emailTitle": "Your email", "passwordTitle": "Password", "troubleLogin": "Trouble logging in?", "signUpWith": "Or sign up with", "loginWith": "Or log in with", "loginSession": "Your login session has expired, please log in again", "signUpNow": "Sign up now", "doNotHaveAccount": "Don’t have account? ", "or": "Or", "getStarted": "Get started with TaxiLoyal", "createAccount": "Create an account to unlock the experience.", "continuePhoneNumber": "Continue with phone number", "loginInWithEmail": "Login with email", "toCreateANew": "To create a new account or sign back in, let us know your mobile number.", "enterPhoneNumber": "Enter phone number", "otpVerification": "OTP Verification", "enterOtp": "Enter the verification code you received via SMS message ", "didNotReceiveCode": "Didn’t receive the code?", "updateAccount": "One last step to set up account", "enterRealName": "Enter your real name to receive accurate billing info.", "yourName": "Your name", "yourEmail": "Your email", "referralCode": "Referral code"}, "signUp": {"title": "Sign up", "nameTitle": "Your name", "phoneNumberTitle": "Phone number", "addressTitle": "Address", "referralCode": "Enter referral code"}, "verifyCode": {"title": "Verify your email", "content": "The verification code has been sent to your email, please check and fill in the code below", "expiredCode": "The code is incorrect or has expired."}, "forgotPassword": {"title": "Forgot password", "newPassword": "New password", "content": "The verification code has been sent to your email, please check and fill in the code below"}, "changePassword": {"title": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password"}, "booking": {"cancelRide": "Cancel Your Booking", "driverComing": "Your driver is already on the way.", "confirmCancelNoCharge": "You will not be charged for canceling this booking", "confirmCancelChargeLate": "You will be charged ${{count}} for exceeding the cancellation period", "bookingConfirmation": "Booking Confirmation", "distance": "Distance", "estimatedTime": "Estimated Time", "personal": "Personal", "business": "Business", "paymentMethod": "Payment Method", "coupon": "Coupon", "estArrivedTime": "Est. Arrive Time", "waitingTime": "Waiting Time", "price": "Price", "year": "{{count}} year", "year_plural": "{{count}} years", "month": "{{count}} month", "month_plural": "{{count}} months", "day": "{{count}} day", "day_plural": "{{count}} days", "hour": "{{count}} hour", "hour_plural": "{{count}} hours", "minute": "{{count}} minute", "minute_plural": "{{count}} minutes", "second": "{{count}} second", "second_plural": "{{count}} seconds", "connectDrivers": "Looking for a driver", "distanceTime": "Travel time", "noCarTitle": "We don't have a car right now, if you like to wait for another 10 minutes, we can give you a discount 10%.", "scheduleConfirmation": "Schedule Confirmation", "yourLocation": "Your Location", "type": "Type", "arrivedTime": "Arrived Time", "totalIncludingTax": "Total Price (Including Tax)", "confirmToSchedule": "Confirm To Schedule", "noDriversNearby": "No Drivers Nearby", "noDriversNearbyContent": "There are currently no drivers near your location. Would you like to continue waiting or try again later?", "noDriversNearbyContent2": "Sorry for this inconvenience. Please try again later!", "noDriversNearbyInAppNotification": "There are currently no drivers near your location. We’re still looking!", "noDriversNearbyInAppNotification2": "Your booking has been automatically canceled due to no drivers nearby. Please try again later!", "couponNotApplied": "Coupon cannot be applied", "couponNotAppliedContent": "You need to change to a different payment method in order to apply promo code.", "businessAdditionRequired": "Business addition required", "businessAdditionRequiredContent": "A business need to be added, then you can apply business option for booking.", "paymentType": "Payment Type", "transportationTime": "Transportation Time", "estArrivalTime": "Est Arrival Time", "cardPaymentShouldUsed": "Card payment should be used", "cardPaymentShouldUsedContent": "Please use card as payment method, you can apply coupons and other offers.", "typeOfCar": "Type Of Car", "anyCar": "Any Car", "typicalCar": "Typical Car-4", "electricCar": "Electric Car", "van": "<PERSON>", "accessibleVan": "Accessible Van", "4seaters": "4-seaters", "46seaters": "4-6 seaters", "6seaters": "6-seaters", "bookingInProcessing": "Your booking is in processing", "bookingOnGoing": "Booking On-going", "status": {"completed": "You’ve arrived at your destination", "arrivalAtClient": "The driver just arrived", "clientOnBoard": "You’re on the road", "vehicleConfirm": "The driver is on the way"}, "bookingID": "Booking ID:", "updatePhoneTitle": "Update your phone number", "phonePlaceholder": "Enter your phone number", "couponCannotUsed": "Coupon cannot be used", "couponCannotUsedContent": "Your selected coupon can’t be used. You can continue to book without the coupon.", "updatePhone": "Update your phone number", "updatePhoneContent": "To make it easier for the driver to find you", "updatePhonePlaceholder": "Enter your phone number", "timeExceeded": "Time Exceeded", "timeExceededMessage": "Your scheduled time has passed. Please reschedule to continue, or book this car now!", "chooseATime": "Choose a time", "pickUpAt": "Pick up at", "selectLocation": "Select Location", "placeholderSelect": "Select", "viewAll": "View all ({{count}})", "priceUpdateTitle": "The price has been updated", "priceUpdateDesc": "The price has changed since you last viewed it", "priceUpdated": "The price has been updated", "couponFixedFare": "Coupon not applicable to trips with fixed fare", "rideUpdated": "Ride information has been updated"}, "selectDestination": {"title": "Select Destination", "schedule": "Schedule", "chooseOnMap": "Choose on the map", "whereTo": "Where to?"}, "bottomTab": {"home": "Taxi Loyal", "activities": "Activities", "notification": "Notification", "setting": "Setting"}, "savedLocation": {"title": {"default": "Saved Locations", "home": "Setup Home", "work": "Setup Work", "add": "Add to saved locations", "editLocation": "Edit locations"}, "home": "Home", "work": "Work", "recent": "Recent search locations", "noResult": "No Result", "enterLocation": "Enter a location"}, "business": {"available": "Available", "unavailable": "Unavailable", "codeAndPin": "CODE", "pin": "PIN", "maxSpendingLimit": "<PERSON> Spending Limit", "maxNumberUsage": "Max number of usage", "distanceLimit": "Distance limit", "expiredDate": "Exp", "newBusiness": "New Business", "taxiLoyalBusinessHub": "TaxiLoyal Business Hub", "businessConfiguration": "Business Configuration", "viewDetailInformation": "View Detail Information", "removeBusiness": "Remove Business", "selectYourBusiness": "Select Your Business", "yourBusiness": "Your Business", "detailInformation": "Detail Information", "removeBusinessContent": "Are you sure to remove this business?", "yourCompanyName": "Your Company Name", "yourDepartment": "Your Department", "yourGroup": "Your Group", "yourRole": "Your Role", "status": "Status", "repeat": "Repeat", "noRepeat": "No repeat", "monthly": "Monthly", "weekly": "Weekly", "yourBusinessEmail": "Your Business Email", "mainInfoTitle": "Elevate your TaxiLoyal experience with our Business Hub, designed for business travelers. Discover if your organization issues  a unique CODE & PIN for exclusive business option.", "infoTitle1": "Exclusive Corporate Rides", "infoContent1": "Enjoy priority pickups, top-rated drivers, and extra comfort features with TaxiLoyal.", "infoTitle2": "Effortless Expense Management", "infoContent2": "Streamline expense tracking with automatic receipt uploads through integrated systems.", "infoTitle3": "Business-Personal Segmentation", "infoContent3": "Easily switch between business and personal profiles, ensuring accurate expense allocation and payment methods."}, "cancelBooking": {"title": "Cancel Ride?", "whyCancel": "Why do you want to cancel?", "optionalChoice": "*Optional Choice", "reasonBooking": "Reason for canceling booking", "enterReason": "Enter other reason", "reason1": "Driver arrived early", "reason2": "Driver asked me to cancel", "reason3": "Driver not getting closer", "reason4": "Could not find driver", "reason5": "Wait time was too long", "reason6": "The driver is not moving", "reason7": "Delay of the driver", "reason8": "Incorrect pickup or drop-off address", "reason9": "Other", "autoCancellation": "(Auto cancellation)", "cancelledByCenter": "Cancel by Dispatch Center"}, "rating": {"title": "Rating Driver", "content": "Rate your trip", "enterNote": "Give a compliment", "earnPoint": "Rate & get total +{{count}} Loyal Points for this trip!", "earnPointFirstRide": "You earned +{{count}} Loyal Points for completing your first ride!", "earnPointFirstRideTitle": "+{{count}} <PERSON><PERSON> Points for your first ride", "addTip": "Add a tip?", "tripPrice": "Your trip price is {{count}}", "enterCustomAmount": "Enter custom amount", "payment": "Payment", "noTip": "No tip", "insufficientBalance": "Insufficient balance", "tipYourDriver": "Tip your driver", "titleFinish": "Cheers to the Ride!", "contentFinish": "Thanks for riding with us! Hope your trip was as smooth as butter. Come back for more easy adventures! 😊", "tipAmount": "<PERSON><PERSON> Amount", "tipTheDriver": "Tip the driver", "noteTip": "Your driver receives 100% of your tip. It's based on the original trip price of {{value}} before discounts.", "noteTip2": "Please note that the tip amount cannot be changed after you book the trip.", "extraTip": "+ extra {{value}}", "noCard": "Add a card to unlock app perks and easily tip your driver to say thanks for the great service!", "inclTip": "Incl. {{value}} tip"}, "activities": {"index": "Activities", "history": "History", "cancelTitle": "Your booking has been cancelled", "cancelContent": "You will not be charged for this booking.", "away": "away", "cancelledReason": "Cancelled Reason:", "allActivities": "All", "completed": "Completed", "cancelled": "Cancelled", "onGoing": "On-Going", "scheduled": "Scheduled", "currentBooking": "Current", "rebook": "Rebook", "rateOrTip": "Rate/Tip", "rebookThisRide": "Rebook This Ride", "rateOrTipDriver": "Rate/Tip Driver", "rideConfirmed": "Ride confirmed!", "rideConfirmedDesc": "We’ll share your driver details here closer to your trip", "recent": "Recent", "route": "{{origin}} to {{destination}}", "viewHistory": "View History", "exportInvoice": "Export Invoice", "pdfOption": "PDF Options", "chooseOption": "Choose an option to proceed", "downloadPdf": "Download PDF", "previewPdf": "Preview PDF", "sharePdf": "Share PDF", "downLoadSuccess": "Download PDF successfully", "tipPaymentType": "Tip payment method", "fullyRefund": "Fully refund", "partialRefund": "Partial refund", "refundTitleFull": "This booking has been fully refunded", "refundTitlePartial": "This booking has been partial refunded", "refundDesc": "You have been refunded {{value}} CAD. It typically takes 3–5 business days for the amount to appear on your payment method."}, "home": {"welcome": "Loyal Point", "search": "Where do you want to go?", "setupPayment": "The payment method is required for booking, set up a payment method right now.", "hello": "Hello", "there": "there", "readMore": "Read more", "happyHoliday": "Happy Holiday,", "tapHere": "Tap here to view and redeem Loyal Points", "days": "Days", "hours": "Hours", "mins": "<PERSON>s", "secs": "Secs"}, "scheduleTime": {"title": "Schedule Time", "time": "Time"}, "empty": {"title": "Empty", "notification": "You have no notifications right now", "coupon": "No coupon code", "business": "No business added, please enter a CODE & PIN", "booking": "No any activity, book a car now", "pointHistory": "No history", "couponManual": "Sorry, the code you entered has either been fully redeemed or does not exist.", "subscription": "No package available"}, "deleteAccount": {"title": "Account Deletion", "reason": "Reason", "description": "Description", "content1": "We are really sad to see you go. Our goal has always been to serve you better.", "content2": "Please note that deleting your account will have these permanent changes:", "content3": "Your existing Loyal Points and other related benefits will be lost. If you join us again in the future, your previously unused Loyal Points or others can’t be reinstated.", "content4": "Your TaxiLoyal account will be inaccessible, unrecoverable, and permanently closed.", "content5": "If you still would like to delete your TaxiLoyal account", "content6": "Please submit your request via the form below and our team will get back to you if clarifications are needed.", "confirm1": "I understand and consent to the deactivation of my account.", "confirm2": "I confirm that I have removed my payment cards from my TaxiLoyal account.", "enterReason": "Enter the reason"}, "profile": {"avatar": "Avatar", "generalInformation": "Personal Information", "editPersonalInformation": "Edit Personal Information", "name": "Name", "email": "Email", "phoneNumber": "Phone Number", "gender": "Gender", "dob": "Date of Birth", "selectDob": "Select date of birth", "selectGender": "Select gender", "male": "Male", "female": "Female", "other": "Other"}, "payment": {"title": "Payment Methods", "setDefault": "Set as the default", "cardNumber": "Card Number", "unlinkMethod": "Unlink this payment method", "default": "<PERSON><PERSON><PERSON>", "unlinkPayment": "Unlink Payment Method", "unlinkPaymentContent": "Are you sure to remove this payment method?", "cash": "Pay in car", "cashWaring": "The promo code cannot be applied if using pay in car", "promoCodeApplied": "The promo code can be applied", "date": "Date", "applePay": "Apple Pay", "googlePay": "Google Pay", "clickToSetUp": "Click to set up", "payInCard": "Pay in card"}, "locationPicker": {"title": "Choose on the map", "pickUpConfirmation": "Pick-Up Confirmation", "soFarAway": "Your pickup location seems far away"}, "help": {"index": "Help", "title": "Need Help", "callPolice": "Call the police", "callDispatchCenter": "Call the dispatch center", "shareLocation": "Share the location to your friend", "contactSupport": "I need to contact support", "supportDescription": "If you’ve having issues with a trip or your account and wish to speak to our Support team, please use the form below to let us know how we can help.", "paymentIssue": "Payment issue", "lostItems": "Lost items", "other": "Other", "selectReason": "Select reason", "shareYourIssue": "Share your issue", "reasonTitle": "Share details about your issue", "thankYou": "Thank You!", "submitSuccess": "Your ticket has been submitted. We’ll get back to you shortly!"}, "tooltip": {"distanceAndTime": "This is the distance and time from your location to your destination", "estimatedDestination": "This is only an estimated time to the destination, not an exact time to arrive."}, "picker": {"openImages": "Select photo from gallery", "openCamera": "Take a photo"}, "about": {"rental": "Rental", "contactUs": "Contact us", "businessHours": "Business hours", "taxiLoyalPolicies": "TaxiLoyal policies", "privacyPolicy": "Privacy policy", "protectionPolicy": "Personal information protection policy", "workingTime": "24 hours a day, 7 days a week"}, "birthday40Year": {"title": "🎉 TAXI LOYAL TURNS 40! 🎉", "date": "Tuesday, 20 May, 2025", "time": "00:00", "mainTitle": "40 Years Together. 40 Days of Thanks.", "mainDescription": "We can't believe it's been 40 years - and it's all thanks to you for keeping us going. So this time, you're getting the best treats! 🎁\nFrom May 20 to June 28, enjoy our 40 Days of Thanks, and trust us, you'll love what's inside.", "discountTitle": "🚗 Get 40% OFF with code LOYAL40TH", "discountDescription": "Enjoy 40% off your next 4 rides (up to $40 total) - now you can save more on the go!", "bookNowButton": "<< Click to BOOK NOW >>", "subscriptionTitle": "🚗 Subscribe to Loyal One - now only $71.93/year", "subscriptionOriginalPrice": "(down from $119.88)", "subscriptionDescription": "Big perks. Bigger savings. All year long:", "subscriptionBenefit1": "• 💸 10% off every ride", "subscriptionBenefit2": "• 💸 20% off one ride each month", "subscriptionBenefit3": "• 💸 A $30 birthday credit", "subscriptionBenefit4": "• 💸 Priority Pickup", "tryNowButton": "<< Click to TRY NOW >>", "referralTitle": "🚗 Referring friends? We've got something for that too.", "referralDescription": "For every successful referral, you'll get", "referralDescription2": " 400 Loyal Points", "referralDescription3": " — and so will your friend. That's 8x the usual reward! Collect points and redeem them for exclusive discounts. \nDon't miss out!", "earnPointsButton": "<< Click to earn points >>", "hurryMessage": "📲 Hurry, grab your offers before they're gone!"}, "notification": {"title": "Notifications", "general": "General", "promotion": "Promotion", "readPromotion": "You have read all promotion notifications.", "readGeneral": "You have read all general notifications."}, "coupon": {"title": "Coupon", "promoCode": "Promo code", "loyalPoint": "Loyal point", "comingSoon": "Coming soon", "loyalPointContent": "We're working on a cool new feature – coming soon for you to try out!", "searchCoupon": "Enter your Coupon code here", "discountPercent": "{{count}}% off", "discountAmount": "{{count}} off", "upTo": "(up to {{count}})", "redeemCoupon": "Redeem and use this coupon?", "redeemThisCoupon": "Redeem this coupon?", "unavailable": "The coupon can’t be used for this service. Please review the coupon terms for more details.", "myCoupon": "My coupon", "couponStore": "Coupon store", "redeemCouponSuccess": "Redeem Coupon successfully!"}, "updateAppVersion": {"title": "New version available", "content": "We have launched a new version. Update for better performance and use all the latest features."}, "intro": {"introTitle1": "Go Everywhere, Any Time", "introContent1": "Unlock the city at your fingertips - our app connects you with a ride anywhere, anytime you are.", "introTitle2": "Schedule Pickup", "introContent2": "Unlock the city at your fingertips - our app connects you with a ride wherever you are.", "introTitle3": "Accepted Wide Range Payment", "introContent3": "Pay securely using your Visa, MasterCard, Apple Pay, Google Pay.", "introTitle4": "Loyal Points", "introContent4": "Use your Loyal Points to unlock coupons, freebies, and more exciting benefits!"}, "language": {"title": "Language", "changeLanguage": "Change Language"}, "setting": {"general": "General", "profileDetails": "Profile Details", "privacyCentre": "Privacy Centre", "about": "About", "others": "Others", "version": "version", "aboutTaxiLoyal": "About TaxiLoyal", "referFriends": "Refer Friends"}, "error": {"inAnotherBooking": "You are currently in another booking", "invalidLink": "The link is invalid", "codeSend": "A new code has been send to your email", "closeDestination": "The destination is quite close, please choose another destination.", "500": "Oops! Something went wrong while processing your request. Please try again later.", "notSupportedLocation": "The location is not supported"}, "permissions": {"requestPhoto": "Allow access to Photo?", "requestCamera": "Allow access to Camera?", "titleAlertLimited": "You are in restricted mode, please change your settings.", "selectMore": "Select more photos", "selectAll": "Allow access to all photos", "keepPhotos": "Keep current selection", "saveFile": "Your permission is required to save Files to your device", "fileDownload": "File Download Permission"}, "subscription": {"description": "Become our member and enjoy exclusive benefits with the {{count}} package!", "paidMonthly": "Paid Monthly", "subscribed": "Subscribed", "canceled": "Canceled", "endOn": "Ends on ", "renewOn": "Renews on ", "unlimitedBenefits": "Unlimited benefits", "history": "History", "title": "Loyal One", "benefits": "Benefits", "managePlan": "Manage package", "paymentMethod": "Payment method: • ", "termOfUse": "Terms of use", "usageInstruction": "Usage instruction", "renewTitle": "Keep using your benefits", "renewContent": "Keep your membership active after {{count}} when you resubscribe", "monthlyCharge": "Monthly charge", "billingStarts": "Billing starts: {{count}}", "billing": "Billing", "planPrice": "Package price", "totalPrice": "Total price", "includingTax": "(including tax)", "billNote": "Billing will renew automatically every {{value}}. Payments won’t be refunded for partial billing periods. Cancel anytime in Settings.", "purchased": "Purchased", "planHistory": "Package History", "transactionDetails": "Transaction Details", "servicePeriod": "Service period", "paymentID": "Payment ID", "cancelTitle": "Before you go, let us know why you’re unsubscribing, to help us improve", "cancelPlan": "Cancel Package", "cancelSuccessContent": "You can continue use all PACKAGE Coupon and extra benefits until {{count}}.", "cancelSuccessTitle": "Your package has been canceled", "welcome": "Welcome to {{count}}", "welcomeContent": "Enjoy your benefits and savings starting today.", "cancelConfirm": "Are you sure you want to cancel ?", "cancelConfirmContent": "By canceling your package, you won’t be able to enjoy Package benefit once it end.", "detail": "Details", "chooseAPlan": "Choose a plan", "currentPlan": "Current plan", "changePlan": "Change plan", "unsubscribe": "Unsubscribe", "noteUnsubscribe": "If you unsubscribe now, you can still access your package until {{date}}", "unsubscribeModal": {"confirm": {"title": "Confirm Unsubscription", "content": "By unsubscribing from your package, you will lose access to its benefits once the current period ends"}, "success": {"title": "Unsubscription Successful", "description": "Your PACKAGE package has been cancelled. It will remain active until {{value}}, and you won’t be charged again."}}, "endsIn": "Ends in: ", "subscribedTo": "Subscribed to {{value}} Plan", "renewalOn": "<PERSON>wal on ", "cancelledPackage": "Cancelled package", "expireOn": "Expire on ", "from": "From", "month": "month", "year": "year", "noteApplyChange": "Your new plan will begin when your current plan expires on {{date}}", "yearly": "Yearly", "monthly": "Monthly", "equivalentToOnly": "Equivalent to only", "off": "off", "yearlyCharge": "Yearly charge", "noteChangePlan": "Your new plan will apply starting on {{date}}", "saveValue": "Save {{value}} ", "getDiscount": "Get {{value}} Discount ", "onPlan": "on {{value}} plan", "cancelPackage": "Cancelled {{value}} plan", "resubscribe": "Resubscribe", "keepActive": "Keep your {{plan}} package active after {{value}} when you resubscribe", "yourAllSet": "You’re all set", "changePlanDescription": "You have switched to a {{plan}} plan.\nYour new plan will start once your current plan ends on {{value}}.", "priorityPickup": "Priority pickup", "unsubscribePackage": "Unsubscribe Package", "success": "Success", "failed": "Failed", "plan": "Plan", "planEnds": "{{package}} plan ends on ", "planStarts": "{{package}} plan charges & starts on ", "switchPlan": "You have switched back to a {{value}} plan.", "processedDate": "Processed date", "chooseYourPlan": "Choose your plan", "chooseYourPlanDesc": "Auto renews. Cancel anytime.", "changePlanNote": "You haven’t been charged yet. Your new plan will begin and be billed when your current plan ends on {{date}}."}, "referral": {"title": "<PERSON><PERSON> & <PERSON><PERSON>n <PERSON>", "content": "With each friend signing up using your code, you'll earn {{count}} Loyal <PERSON>", "successfulReferral": "Successful Referral", "totalPointsEarned": "Total Points Earned", "myReferralCode": "My Referral Code", "invitedList": "Invited List", "points": "points", "copy": "<PERSON>pied", "socialContent": "You will earn {{count}} bonus Loyal Points with referral code. Skip if you don’t have one.", "enterReferralCode": "Enter referral code", "placeHolder": "Enter code", "referralBonus": "Referral bonus", "referralBonusUnlocked": "Referral Bonus Unlocked!", "referralBonusUnlockedContent": "You’ve earned {{count}} Loyal Points for using referral code!"}, "loyalPoints": {"title": "Loyal Points", "totalLoyalPoints": "Total Loyal Points", "howGetPoints": "How to get Loyal Points", "howUsePoints": "How to use Loyal Points", "redeemYourPoints": "Redeem your points for exclusive rewards.", "redeem": "Redeem", "yourPoints": "Your Points"}, "historyPoint": {"title": "Loyal Points History", "completedRide": "Completed ride", "completedFirstRide": "Completed first ride"}, "accessibleVan": {"content": "This option needs to be reserved at least 24 hours before your trip", "contentModal": "This is a special ride, and you need to book at least 24 hours in advance."}, "note": {"noteToDriver": "Note to driver", "title": "Add pickup details (e.g packages, with pets)", "placeHolderNote": "Enter your request (maximum 100 character)", "travelWithCat": "Travel with Cat", "travelWithDog": "Travel with Dog", "babySeat": "<PERSON>", "heavyPackage": "Heavy Package", "addedPickupDetails": "Added pickup details"}, "deactivated": {"title": "Account Deactivated", "description": "Your account has been deactivated. For assistance, please contact", "cannotOpenURL": "Provided URL can not be handled"}, "modalRide": {"now": "Now", "schedule": "Schedule", "nowDesc": "Just tap, sit back, and let the adventure begin", "scheduleDesc": "Make reservation at least 2 hours 30 minutes in advance and your plans are set", "title": "When do you need a ride?"}, "shareRoute": {"lookingForDriver": "is looking for a driver", "waitingForPickup": "is waiting for pickup", "driverArrived": "The driver just arrived", "onTheWay": "is on the way", "arrived": "arrived", "linkShare": "Hey, check out my route’s progress and join me on the journey! ", "titleSuccess": "This shared ride has wrapped up", "titleFailed": "Booking Missing", "descriptionSuccess": "Thank you for checking in! Let’s guide you back to explore more options", "descriptionFailed": "The shared booking might have ended or no longer exists. Thanks for sticking around!", "bookingCancel": "This booking has been canceled."}, "verifyPhone": {"title": "Verify your phone number", "descriptionPhone": "To make it easier for the driver to find you.", "descriptionOtp": "The verification code has been sent to your phone, please check and fill in the code below", "otpInCorrect": "The code is incorrect or has expired.", "titleSuccess": "Verify successful", "descriptionSuccess": "Great! Your phone number has been successfully verified.", "policy": "By entering my mobile number, I agree to receive recurring automated promotional and personalized marketing text messages from Taxi Loyal at the number provided during sign-up. Message and data rates may apply. Reply HELP for assistance or STOP to cancel. View our privacy policy", "here": " here"}, "errorMessage": {"500": {"message": "Internal Server Error", "description": "An issue has occurred with the server. Please try again later."}, "502": {"message": "Bad Gateway", "description": "The server is not responding correctly. Please try again later."}, "503": {"message": "Service Unavailable", "description": "The service is currently unavailable or overloaded. Please try again later."}, "504": {"message": "Gateway Timeout", "description": "Unable to connect to the server. Please try again later."}, "noInternet": {"message": "Network Error", "description": "A network issue has occurred. Please check your connection and try again."}}}