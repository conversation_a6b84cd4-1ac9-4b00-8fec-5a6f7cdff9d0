import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import {NativeModules, Platform} from 'react-native';
import {queryClient} from '../react-query/queryClient';
import locales from './locales';
import {GLOBAL_STATE_KEY_PREFIX, LANGUAGE} from '@themes/Constants';

// const LANGUAGE_KEY = 'language';
const DEFAULT_LANGUAGE = 'en'; // you can define it here

const getDeviceLanguage = () => {
  const appLanguage =
    Platform.OS === 'ios'
      ? NativeModules.SettingsManager.settings.AppleLocale ||
        NativeModules.SettingsManager.settings.AppleLanguages[0]
      : NativeModules.I18nManager.localeIdentifier;

  const deviceLanguage =
    appLanguage.search(/-|_/g) !== -1 ? appLanguage.slice(0, 2) : appLanguage;
  if (['fr', 'en'].includes(deviceLanguage)) {
    return deviceLanguage;
  }
  return undefined;
};

const languageDetector = {
  async: true,
  cacheUserLanguage: () => {
    //TODO
  },
  // flags below detection to be async
  detect: async (callback: any) => {
    // const userLang = await AsyncStorage.getItem(LANGUAGE_KEY);
    const cacheData = await AsyncStorage.getItem('REACT_QUERY_OFFLINE_CACHE');
    const asyncStoreLanguage = JSON.parse(
      cacheData as string,
    )?.clientState?.queries.find((ele: any) =>
      ele?.queryHash?.includes('language'),
    )?.state?.data;
    const stateKey = [GLOBAL_STATE_KEY_PREFIX, 'language'];
    const userLang = queryClient.getQueryData(stateKey);

    let newLanguage = undefined;

    if (userLang) {
      newLanguage = userLang;
    }

    const deviceLang =
      newLanguage ||
      asyncStoreLanguage ||
      getDeviceLanguage() ||
      DEFAULT_LANGUAGE;

    if (!newLanguage) {
      // AsyncStorage.setItem(LANGUAGE_KEY, JSON.stringify(deviceLang));
    }

    // languageVar(deviceLang);
    queryClient.setQueryData(stateKey, deviceLang);
    callback(deviceLang);
  },

  init: () => {
    //TODO
  },

  type: 'languageDetector',
};

export const defaultNS = 'translation';
export const initI18n = () => {
  i18n
    .use(languageDetector as any)
    .use(initReactI18next)
    .init({
      compatibilityJSON: 'v3',
      debug: false,
      defaultNS,
      fallbackLng: LANGUAGE.EN,
      interpolation: {
        escapeValue: false,
      },
      ns: [defaultNS],
      react: {useSuspense: false},
      resources: locales,
    });
};

export default i18n;
