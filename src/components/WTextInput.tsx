import React, {forwardRef, ReactNode, useEffect, useState} from 'react';
import {
  NativeSyntheticEvent,
  ReturnKeyTypeOptions,
  StyleProp,
  StyleSheet,
  TextInput,
  TextInputFocusEventData,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {isEmpty} from 'lodash-es';
import {Colors, Fonts} from '../themes';
import {GlobalStyle} from '../themes/GlobalStyle';
import WView from './WView';
import WText from './WText';

interface WTextInputProps extends TextInputProps {
  title?: string;
  errorMessage?: string;
  required?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  style?: StyleProp<ViewStyle>;
  isSecure?: boolean;
  onChangeText?: (text: string) => void;
  onSubmitEditing?: () => void;
  returnKeyType?: ReturnKeyTypeOptions;
  editable?: boolean;
  touched?: boolean;
  onBlur?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  onFocus?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  styleInput?: StyleProp<TextStyle>;
  renderLeft?: () => ReactNode;
}

const WTextInput = forwardRef<TextInput, WTextInputProps>(
  (
    {
      title,
      errorMessage,
      required,
      containerStyle,
      isSecure,
      returnKeyType,
      onChangeText,
      onSubmitEditing,
      style,
      editable = true,
      touched,
      onBlur,
      onFocus,
      renderLeft,
      styleInput,
      ...more
    }: WTextInputProps,
    ref,
  ) => {
    const [isFocus, setIsFocus] = useState(false);
    const [isError, setIsError] = useState(false);
    const [isSecureState, setIsSecureState] = useState(isSecure);

    const onChangeSecureState = () => {
      setIsSecureState(!isSecureState);
    };

    const _onChangeText = (text: string) => {
      onChangeText?.(text);
    };

    const _onSubmitEditing = () => {
      onSubmitEditing?.();
    };

    useEffect(() => {
      setIsError(!isEmpty(errorMessage));
    }, [errorMessage]);

    const asterisk = '*';
    return (
      <WView style={containerStyle}>
        {!!title && (
          <WView row>
            <WText marginBottom={6} type="regular14">
              {title}
            </WText>
            {required && (
              <WText
                color={Colors.primary900}
                marginBottom={6}
                marginLeft={2}
                type="regular14">
                {asterisk}
              </WText>
            )}
          </WView>
        )}
        <WView
          alignCenter
          style={[
            styles.inputContainer,
            isFocus && styles.focus,
            isError && touched && styles.error,
            !editable && {
              borderColor: Colors.neutral300,
            },
            style,
          ]}>
          {renderLeft?.()}
          <TextInput
            editable={editable}
            {...more}
            allowFontScaling={false}
            onBlur={e => {
              onBlur?.(e);
              setIsFocus(false);
            }}
            onChangeText={_onChangeText}
            onFocus={e => {
              onFocus?.(e);
              setIsFocus(true);
            }}
            onSubmitEditing={_onSubmitEditing}
            ref={ref}
            returnKeyType={returnKeyType || 'default'}
            secureTextEntry={isSecureState}
            style={[
              GlobalStyle.flex1,
              !editable && {color: Colors.neutral600},
              {
                fontFamily: Fonts.type.regular,
                fontSize: Fonts.size.S14,
              },
              styleInput,
            ]}
          />
          {isSecure && (
            <TouchableOpacity onPress={onChangeSecureState}>
              <Icon
                color={Colors.grey1}
                name={isSecureState ? 'Outline-EyeSlash' : 'Outline-Eye'}
                size={18}
                style={styles.secure}
              />
            </TouchableOpacity>
          )}
        </WView>
        {isError && touched && (
          <WView mTop={4} row>
            <Icon
              color={Colors.primary900}
              name="Outline-WarningCircle"
              size={16}
            />
            <WText color={Colors.primary900} marginLeft={4} type="regular12">
              {errorMessage}
            </WText>
          </WView>
        )}
      </WView>
    );
  },
);

export default WTextInput;

const styles = StyleSheet.create({
  error: {
    borderColor: Colors.primary900,
    borderWidth: 2,
  },
  focus: {
    borderColor: Colors.grey1,
    borderWidth: 3,
  },
  inputContainer: {
    // alignItems: 'center',
    backgroundColor: Colors.white,
    borderColor: Colors.neutral600,
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: 'row',
    /*
     * maxHeight: 48,
     */
    height: 47,
    paddingHorizontal: 16,
    // paddingVertical: Platform.select({android: 0, ios: 12}),
  },
  secure: {
    alignSelf: 'center',
    marginLeft: 12,
  },
});
