/* eslint-disable react/function-component-definition */
import React from 'react';
import {ActivityIndicator, FlatList, ListRenderItemInfo} from 'react-native';
import {SCREEN_HEIGHT} from '../themes/Constants';
import useTypeSafeTranslation from '../utils/hooks/useTypeSafeTranslation';
import WText from './WText';
import WView from './WView';
import ItemSearchLocation from './ItemSearchLocation';
import {Colors} from '@themes';
import {ILocationItem, ISearchAddressResultProps} from '@global';

const SearchAddressResult: React.FC<ISearchAddressResultProps> = React.memo(
  ({
    data,
    onPressItem,
    onTouch,
    hasSearchText,
    style,
    loading,
    currentSearchType,
    dataSubLocation,
  }) => {
    const {t} = useTypeSafeTranslation();

    const renderItem = ({item, index}: ListRenderItemInfo<ILocationItem>) => (
      <ItemSearchLocation
        currentSearchType={currentSearchType}
        data={dataSubLocation?.[index]}
        hasSearchText={hasSearchText}
        item={item}
        onPressItem={onPressItem}
      />
    );

    const renderHeader = () => {
      if (hasSearchText) {
        return null;
      }
      return (
        <WView
          borderBottomColor={Colors.neutral200}
          borderBottomWidth={1}
          mHoz={16}
          mTop={16}
          pBottom={12}>
          <WText color={Colors.neutral600} type="medium14">
            {t('savedLocation.recent')}
          </WText>
        </WView>
      );
    };

    const keyExtractor = React.useCallback(
      (_: ILocationItem, index: number) => index.toString(),
      [],
    );

    return (
      <FlatList
        ListEmptyComponent={() => (
          <WView alignCenter fill h={SCREEN_HEIGHT * 0.4} justifyCenter>
            {loading ? (
              <ActivityIndicator color={Colors.primary} size={'small'} />
            ) : (
              <WText color={Colors.neutral600} type="regular14">
                {t('savedLocation.noResult')}
              </WText>
            )}
          </WView>
        )}
        ListHeaderComponent={renderHeader}
        data={data}
        keyExtractor={keyExtractor}
        keyboardShouldPersistTaps={'always'}
        nestedScrollEnabled
        onTouchStart={() => {
          onTouch?.();
        }}
        renderItem={renderItem}
        style={style}
      />
    );
  },
);

export default SearchAddressResult;
