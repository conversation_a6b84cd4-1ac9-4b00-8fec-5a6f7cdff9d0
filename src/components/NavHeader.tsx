import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleSheet, TouchableWithoutFeedback} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '../themes';
import {WText, WView} from './index';

interface NavHeaderProps {
  title?: string;
  renderRightButton?: () => React.ReactNode;
}

function NavHeader({title, renderRightButton}: NavHeaderProps) {
  const navigation = useNavigation();

  const onBack = () => {
    navigation.goBack();
  };
  return (
    <WView alignCenter gap={6} justifyBetween mTop={12} pHoz={16} row>
      <TouchableWithoutFeedback onPress={onBack}>
        <WView alignCenter fill row>
          <WView style={styles.buttonBack}>
            <Icon
              color={Colors.neutral900}
              name="Outline-CaretLeft"
              size={20}
            />
          </WView>
          <WText fill numberOfLine={1} type="medium16">
            {title || 'Back'}
          </WText>
        </WView>
      </TouchableWithoutFeedback>
      <WView mLeft={'auto'}>{renderRightButton?.()}</WView>
    </WView>
  );
}

export default NavHeader;

const styles = StyleSheet.create({
  buttonBack: {
    marginRight: 10,
  },
});
