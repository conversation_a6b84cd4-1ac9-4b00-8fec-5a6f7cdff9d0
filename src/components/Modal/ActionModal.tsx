import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useRef, useState, useEffect} from 'react';
import {
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {useFormik} from 'formik';
import * as Yup from 'yup'; // Import Yup for validation schema
import <PERSON><PERSON><PERSON>ie<PERSON> from 'lottie-react-native';
import WText from '../WText';
import WView from '../WView';
import WButton from '../WButton';
import WTextInput from '../WTextInput';
import ModalWrapper from './ModalWrapper';
import {ActionModalRouteProps} from '@global';
import {GlobalStyle} from '@themes/GlobalStyle';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  PHONE_REGEX,
  REFERRAL_CODE_REGEX,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
} from '@themes/Constants';
import {Colors} from '@themes';

interface ActionModalProps {
  route: RouteProp<ActionModalRouteProps, 'ActionModal'>;
}
const THEME = {
  default: {
    primary: Colors.blue700,
    secondary: Colors.blue200,
    soft: Colors.blue100,
  },
  error: {
    primary: Colors.danger700,
    secondary: Colors.danger200,
    soft: Colors.danger50,
  },
  success: {
    primary: Colors.success,
    secondary: Colors.success500,
    soft: Colors.success100,
  },
  warning: {
    primary: Colors.warning700,
    secondary: Colors.warning600,
    soft: Colors.warning100,
  },
};

let count = 0;

const DURATION = 10;

interface ErrorTypeProps {
  [key: string]: any;
}

function ActionModal({route}: ActionModalProps) {
  const {
    onApply,
    title,
    content,
    onCancel,
    cancelLabel,
    confirmLabel,
    cancelBtnStyle,
    confirmBtnStyle,
    enableCloseOnMask = true,
    type = 'error',
    icon,
    image,
    hasInput,
    placeholder,
    defaultInputValue,
    keyboardType,
    hasCancelBtn = true,
    autoConfirm,
    autoConfirmDuration = DURATION,
    iconSize = 20,
    widthIcon,
    heightIcon,
    buttonAlign = 'horizontal',
    validateType,
    fallbackContent,
    lottieJson,
  } = route?.params || {};
  const inputRef = useRef<TextInput>(null);
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  const [currentCount, setCurrentCount] = useState(count);

  const errorType: ErrorTypeProps = {
    ['phone']: Yup.string()
      .matches(PHONE_REGEX, t('validate.phoneNumber.invalid'))
      .required(t('validate.phoneNumber.required')),
    ['referralCode']: Yup.string()
      .trim()
      .required(t('validate.referralCode.invalid'))
      .matches(REFERRAL_CODE_REGEX, t('validate.referralCode.invalid')),
  };

  const validationSchema = Yup.object({
    textInputData: errorType?.[validateType as string] || {},
  });

  const _onCancel = () => {
    if (onCancel) {
      onCancel?.();
    } else {
      navigation.goBack();
    }
  };

  const formik = useFormik({
    initialValues: {
      textInputData: defaultInputValue,
    },
    onSubmit: values => {
      onApply?.(values?.textInputData);
    },
    validationSchema,
  });

  const _onApply = () => {
    onApply?.(formik?.values?.textInputData);
  };

  useEffect(() => {
    if (autoConfirm) {
      const interval = setInterval(() => {
        count = count + 1;
        setCurrentCount(count);
        if (count === autoConfirmDuration) {
          _onApply();
          clearInterval(interval);
        }
      }, 1000);

      // Cleanup function to clear the interval when the component unmounts
      return () => {
        count = 0;
        clearInterval(interval);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const confirmText =
    `${confirmLabel || t('button.ok')}` +
    ` ${autoConfirm ? `(${autoConfirmDuration - currentCount}s)` : ''}`;

  const renderHeaderIcon = () => {
    if (hasInput) {
      return null;
    }
    if (lottieJson) {
      return (
        <LottieView
          autoPlay
          loop
          resizeMode="cover"
          source={lottieJson}
          style={{
            height: heightIcon ?? iconSize,
            width: widthIcon ?? iconSize,
          }}
        />
      );
    }
    if (image) {
      return (
        <Image
          source={image}
          style={{
            height: heightIcon ?? iconSize,
            width: widthIcon ?? iconSize,
          }}
        />
      );
    }
    return (
      <WView
        alignCenter
        borderRadius={30}
        color={THEME?.[type].soft}
        h={54}
        justifyCenter
        w={54}>
        <WView
          alignCenter
          borderRadius={20}
          color={THEME?.[type].secondary}
          h={36}
          justifyCenter
          w={36}>
          <Icon
            color={THEME?.[type].primary}
            name={icon || 'Outline-WarningCircle'}
            size={iconSize}
          />
        </WView>
      </WView>
    );
  };

  return (
    <ModalWrapper
      enableCloseOnMask={enableCloseOnMask}
      style={styles.modelWrapper}>
      <SafeAreaView style={styles.alignCenter}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
          keyboardVerticalOffset={SCREEN_HEIGHT * 0.4}>
          <TouchableWithoutFeedback
            onPress={() => {
              Keyboard.dismiss();
            }}>
            <WView
              alignCenter
              borderRadius={16}
              color={Colors.white}
              mBottom={12}
              maxHeight={(SCREEN_HEIGHT / 3) * 2}
              padding={16}
              w={SCREEN_WIDTH - 32}>
              {renderHeaderIcon()}
              <WText marginTop={12} type="medium18">
                {title}
              </WText>
              {fallbackContent ? (
                fallbackContent
              ) : (
                <WText center lineHeight={22} marginTop={8} type="regular14">
                  {content}
                </WText>
              )}

              {hasInput && (
                <WTextInput
                  autoCapitalize={
                    validateType === 'referralCode' ? 'characters' : 'none'
                  }
                  errorMessage={formik.errors?.textInputData}
                  keyboardType={keyboardType}
                  onChangeText={text => {
                    if (validateType === 'referralCode') {
                      formik.handleChange('textInputData')(text?.toUpperCase());
                    } else {
                      formik.handleChange('textInputData')(text);
                    }
                  }}
                  placeholder={placeholder}
                  ref={inputRef}
                  style={styles.input}
                  touched={formik.touched.textInputData}
                  value={formik.values.textInputData}
                />
              )}
              {buttonAlign === 'horizontal' ? (
                <WView alignCenter mTop={24} row w={SCREEN_WIDTH - 64}>
                  {hasCancelBtn && (
                    <WButton
                      label={cancelLabel || t('button.cancel')}
                      onPress={_onCancel}
                      outline
                      style={[styles.cancelBtn, cancelBtnStyle]}
                    />
                  )}
                  <WButton
                    label={confirmText || t('button.ok')}
                    onPress={!validateType ? _onApply : formik.handleSubmit}
                    style={[GlobalStyle.flex1, confirmBtnStyle]}
                  />
                </WView>
              ) : (
                <WView mTop={24} w={SCREEN_WIDTH - 64}>
                  <WButton
                    label={confirmText || t('button.ok')}
                    onPress={!validateType ? _onApply : formik.handleSubmit}
                    style={[styles.confirmVerBtn, confirmBtnStyle]}
                  />
                  {hasCancelBtn && (
                    <WButton
                      label={cancelLabel || t('button.cancel')}
                      onPress={_onCancel}
                      outline
                      style={[styles.cancelVerBtn, cancelBtnStyle]}
                    />
                  )}
                </WView>
              )}
            </WView>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ModalWrapper>
  );
}

export default ActionModal;

const styles = StyleSheet.create({
  alignCenter: {alignItems: 'center'},
  cancelBtn: {flex: 1, marginRight: 12},
  cancelVerBtn: {height: 44, marginTop: 10, width: '100%'},
  confirmVerBtn: {height: 44, width: '100%'},
  input: {marginTop: 16, width: '100%'},
  modelWrapper: {alignItems: 'center', justifyContent: 'center'},
});
