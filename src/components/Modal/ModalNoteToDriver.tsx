/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {FlatList, StyleSheet, TextInput, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/icomoon';
import WButton from '@components/WButton';
import WText from '@components/WText';
import WView from '@components/WView';
import {Colors} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {SCREEN_WIDTH} from '@themes/Constants';

interface IDataNote {
  id: number;
  label: string;
  icon: string;
}

interface ModalNoteToDriverProps {
  note: string;
  onChangText: (text: string) => void;
  onSelectItem: (item: IDataNote) => void;
  onClosed: () => void;
  data: IDataNote[];
  onSave: () => void;
  visible: boolean;
}

function ModalNoteToDriver({
  note,
  onChangText,
  onClosed,
  data,
  onSelectItem,
  onSave,
  visible,
}: ModalNoteToDriverProps) {
  const {t} = useTypeSafeTranslation();

  const renderItem = ({item, index}: {item: IDataNote; index: number}) => {
    const isSelected = note?.includes(item?.label);
    return (
      <TouchableOpacity
        disabled={isSelected}
        key={index}
        onPress={() => onSelectItem(item)}
        style={[styles.item, {borderWidth: isSelected ? 1 : 0}]}>
        <Icon color={Colors.neutral500} name={item?.icon} size={24} />
        <WText marginLeft={4} type="regular14">
          {item?.label?.replace(',', '')}
        </WText>
      </TouchableOpacity>
    );
  };
  return (
    <Modal
      animationInTiming={500}
      avoidKeyboard={true}
      isVisible={visible}
      onBackButtonPress={onClosed}
      onBackdropPress={onClosed}
      style={styles.modal}>
      <WView
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        color={Colors.white}
        pHoz={16}
        pVer={16}>
        <WView alignCenter justifyBetween row>
          <WText type="semiBold18">{t('note.noteToDriver')}</WText>
          <TouchableOpacity onPress={onClosed}>
            <Icon color={Colors.neutral500} name="Fill-XCircle" size={20} />
          </TouchableOpacity>
        </WView>
        <TextInput
          allowFontScaling={false}
          maxLength={100}
          multiline
          onChangeText={onChangText}
          placeholder={t('note.placeHolderNote')}
          placeholderTextColor={Colors.neutral600}
          style={styles.textInput}
          value={note || ''}
        />
        <FlatList
          data={data}
          keyExtractor={item => item?.id?.toString()}
          numColumns={2}
          renderItem={renderItem}
        />
        <WButton
          label={t('button.save')}
          onPress={onSave}
          style={styles.bookBtn}
        />
      </WView>
    </Modal>
  );
}

export default ModalNoteToDriver;

const styles = StyleSheet.create({
  bookBtn: {
    marginBottom: 16,
    marginTop: 16,
  },
  item: {
    alignItems: 'center',
    backgroundColor: Colors.defaultSecondaryBg,
    borderColor: Colors.neutral400,
    borderRadius: 8,
    flexDirection: 'row',
    marginBottom: 8,
    marginRight: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    width: SCREEN_WIDTH,
  },
  textInput: {
    alignItems: 'flex-start',
    borderColor: Colors.neutral500,
    borderRadius: 8,
    borderWidth: 1,
    color: Colors.neutral600,
    marginBottom: 16,
    marginTop: 16,
    minHeight: 63,
    paddingHorizontal: 12,
    paddingTop: 8,
  },
});
