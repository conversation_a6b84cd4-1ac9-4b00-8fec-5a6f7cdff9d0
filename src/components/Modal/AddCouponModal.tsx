import {RouteProp, useNavigation} from '@react-navigation/native';
import {debounce, isEmpty} from 'lodash-es';
import React, {Ref, useEffect, useMemo, useRef, useState} from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import WText from '../WText';
import WTextInput from '../WTextInput';
import WView from '../WView';
import ModalWrapper from './ModalWrapper';
import {AddCouponModalRouteProps, SearchCouponParamsProps} from '@global';
import CouponItem from '@screens/Coupon/CouponItem';
import {useGlobalState} from '@react-query/clientStateManage';
import {CouponEntity, MyCouponEntity} from '@requests';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors} from '@themes';
import {
  useUsersServiceUserCouponControllerSearchCouponOnList,
  useUsersServiceUserCouponControllerSearchManualCoupon,
} from '@queries';
import {IS_IOS} from '@themes/Constants';

interface AddCouponModalProps {
  route: RouteProp<AddCouponModalRouteProps, 'AddCouponModal'>;
}

function AddCouponModal({route}: AddCouponModalProps) {
  const {isFromBooking, onApply, onPress, searchParams, selectedCoupon} =
    route?.params || {};

  const [manualCoupons, setManualCoupons] = useGlobalState('manualCoupons');
  const {t} = useTypeSafeTranslation();

  const navigation = useNavigation();

  const [searchValue, setSearchValue] = useState('');
  const [value, setValue] = useState('');

  const onSearch = debounce(async (input: string) => {
    setSearchValue(input);
    setValue(input);

    if (value !== input) {
      setValue(input);
    }
  }, 500);

  const {data: publicData} =
    useUsersServiceUserCouponControllerSearchCouponOnList(
      {
        code: value,
      },
      undefined,
      {
        enabled: !!value && !isFromBooking,
      },
    );

  const {data} = useUsersServiceUserCouponControllerSearchManualCoupon(
    {
      ...(searchParams as SearchCouponParamsProps),
      code: value,
    },
    undefined,
    {
      enabled: !!value && !isEmpty(searchParams) && isFromBooking,
    },
  );
  const couponData: MyCouponEntity = isFromBooking
    ? (data?.data as MyCouponEntity)
    : (publicData?.data as MyCouponEntity);

  const onAddCoupon = () => {
    const existsCoupon = manualCoupons?.some(
      item => item.id === couponData?.id,
    );
    if (!existsCoupon && isFromBooking) {
      setManualCoupons([couponData as CouponEntity, ...(manualCoupons || [])]);
    }
    onApply?.(couponData as CouponEntity);
  };

  const textInputRef = useRef<TextInput>();

  useEffect(() => {
    const unsubscribe = setTimeout(() => {
      textInputRef?.current?.focus();
    }, 200);

    return () => {
      clearTimeout(unsubscribe);
    };
  }, []);

  const isCheckApply = useMemo(
    () =>
      couponData?.id === selectedCoupon?.id && selectedCoupon?.id !== undefined,
    [couponData, selectedCoupon],
  );

  return (
    <KeyboardAvoidingView
      behavior={IS_IOS ? 'padding' : undefined}
      style={styles.container}>
      <ModalWrapper
        color={Colors.transparent}
        enableDetectKeyboard={false}
        enableSafeArea={false}
        style={styles.modalWrapper}>
        <TouchableWithoutFeedback
          onPress={() => {
            Keyboard.dismiss();
          }}>
          <SafeAreaView style={styles.content}>
            <WView borderRadius={16} color={Colors.white} padding={16}>
              <WText marginBottom={24} type="semiBold18">
                {t('button.addPromoCode')}
              </WText>
              <WTextInput
                onChangeText={onSearch}
                placeholder={t('coupon.searchCoupon')}
                ref={textInputRef as Ref<TextInput>}
              />
              <WView>
                {!couponData && !!searchValue && (
                  <WView style={styles.emptyContainer}>
                    <WText center color={Colors.neutral600} type="regular12">
                      {t('empty.couponManual')}
                    </WText>
                  </WView>
                )}

                <CouponItem
                  allowForceViewDetail={isFromBooking && !!couponData}
                  applyLabel={isFromBooking ? undefined : t('button.view')}
                  coupon={couponData as CouponEntity}
                  disable={
                    isFromBooking ? !couponData?.isValid : isEmpty(couponData)
                  }
                  isApply={isCheckApply}
                  onApply={onAddCoupon}
                  onPress={() => {
                    navigation.goBack();
                    onPress?.(couponData as CouponEntity);
                  }}
                  style={[
                    styles.coupon,
                    isEmpty(couponData) && styles.opacity0,
                  ]}
                />
              </WView>
            </WView>
          </SafeAreaView>
        </TouchableWithoutFeedback>
      </ModalWrapper>
    </KeyboardAvoidingView>
  );
}

export default AddCouponModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.fadeBackground3,
    flex: 1,
  },
  content: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  coupon: {
    marginBottom: 12,
    marginHorizontal: 0,
  },
  emptyContainer: {
    left: 24,
    position: 'absolute',
    right: 24,
    top: 16,
  },
  modalWrapper: {justifyContent: 'flex-end'},
  opacity0: {
    opacity: 0,
  },
});
