import {StyleSheet} from 'react-native';
import React from 'react';
import {RouteProp, useNavigation} from '@react-navigation/native';
import WView from '../WView';
import WText from '../WText';
import WButton from '../WButton';
import ModalWrapper from './ModalWrapper';
import {queryClient} from '@react-query/queryClient';
import {
  useCouponsServiceCouponControllerGetCouponStoreListKey,
  useUsersServiceUserControllerGetUserKey,
  useUsersServiceUserCouponControllerRedeemCouponsStore,
} from '@queries';
import {CouponEntity} from '@requests';
import {RedeemCouponModalRouteProps} from '@global';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors} from '@themes';
import CouponItem from '@screens/Coupon/CouponItem';
import MixPanelSdk from '@utils/mixPanelSdk';
import {useGlobalState} from '@react-query/clientStateManage';

interface RedeemCouponModalProps {
  route: RouteProp<RedeemCouponModalRouteProps, 'RedeemCouponModal'>;
}

function RedeemCouponModal({route}: RedeemCouponModalProps) {
  const [userData] = useGlobalState('userData');
  const {coupon, applyBtnLabel, onApply, titleModal} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {mutate: redeemCoupon, isPending} =
    useUsersServiceUserCouponControllerRedeemCouponsStore();

  const onRedeemCoupon = () => {
    redeemCoupon(
      {
        requestBody: {
          couponId: coupon?.id as string,
        },
      },
      {
        onSuccess: () => {
          onApply?.(coupon as CouponEntity);
          queryClient.refetchQueries({
            queryKey: [useUsersServiceUserControllerGetUserKey],
            type: 'all',
          });
          queryClient.refetchQueries({
            queryKey: [useCouponsServiceCouponControllerGetCouponStoreListKey],
            type: 'all',
          });
          MixPanelSdk.logEvent({
            eventName: 'coupon_redeem',
            eventValues: {
              ...(coupon?.title && {couponName: coupon?.title}),
              ...(coupon?.value && {
                couponValue: Number(coupon?.value) / 100,
              }),
              email: userData?.user?.email,
              phoneNumber: userData?.user?.phoneNumber,
              userName: userData?.user?.fullName,
            },
          });
          navigation.goBack();
        },
      },
    );
  };

  return (
    <ModalWrapper style={styles.container}>
      <WView borderRadius={12} color={Colors.white} mHoz={16} padding={16}>
        <WText center type="semiBold18">
          {titleModal ? titleModal : t('coupon.redeemCoupon')}
        </WText>
        <CouponItem
          coupon={coupon as CouponEntity}
          couponPointCost={coupon?.redeemPoint}
          mode="redeem"
          style={styles.coupon}
        />
        <WView alignCenter mTop={24} row>
          <WButton
            label={t('button.dismiss')}
            onPress={() => {
              navigation.goBack();
            }}
            outline
            style={styles.dismissBtn}
          />
          <WButton
            label={applyBtnLabel || t('button.redeem')}
            loading={isPending}
            onPress={onRedeemCoupon}
            style={GlobalStyle.flex1}
          />
        </WView>
      </WView>
    </ModalWrapper>
  );
}

export default RedeemCouponModal;

const styles = StyleSheet.create({
  container: {justifyContent: 'center'},
  coupon: {marginHorizontal: 0, marginTop: 20},
  dismissBtn: {flex: 1, marginRight: 12},
});
