import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import * as Yup from 'yup';
import {WButton, WTextInput, WView} from '..';
import WText from '../WText';
import ModalWrapper from './ModalWrapper';
import {queryClient} from '@react-query/queryClient';
import {GlobalStyle} from '@themes/GlobalStyle';
import {
  useUsersServiceEmployeeCodeControllerGetCodesOfUser,
  useUsersServiceEmployeeCodeControllerGetCodesOfUserKey,
  useUsersServiceUserControllerJoinBusiness,
} from '@queries';
import {Colors} from '@themes';
import {NUMBER_ONLY_REGEX, SCREEN_HEIGHT} from '@themes/Constants';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import MixPanelSdk from '@utils/mixPanelSdk';
import {useGlobalState} from '@react-query/clientStateManage';

interface JoinBusinessParams {
  code: string;
  email: string;
  pin: string;
}

const INITIAL_FORMIK = {
  code: '',
  email: '',
  pin: '',
};

function AddBusinessCodeModal() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const [userData] = useGlobalState('userData');

  const {data, refetch} = useUsersServiceEmployeeCodeControllerGetCodesOfUser({
    limit: 50,
    offset: 0,
    order: 'createdAt:desc',
  });

  const {mutate: joinBusiness} = useUsersServiceUserControllerJoinBusiness({
    onSuccess: () => {
      navigation.goBack();
      queryClient.refetchQueries({
        queryKey: [useUsersServiceEmployeeCodeControllerGetCodesOfUserKey],
        type: 'active',
      });
      refetch();
      setTimeout(() => {
        MixPanelSdk.logEvent({
          eventName: 'business_register',
          eventValues: {
            businessName: data?.data?.[0]?.business?.name || '',
            email: userData?.user?.email,
            phoneNumber: userData?.user?.phoneNumber,
            userName: userData?.user?.fullName,
          },
        });
      }, 1000);
    },
  });

  const onJoinBusiness = (data: JoinBusinessParams) => {
    joinBusiness({
      requestBody: {
        PIN: data?.pin,
        code: data?.code,
        email: data?.email,
      },
    });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={-SCREEN_HEIGHT / 7}
      style={styles.container}>
      <ModalWrapper color={Colors.transparent} style={styles.modalWrapper}>
        <Formik
          initialValues={INITIAL_FORMIK}
          onSubmit={values => onJoinBusiness(values)}
          validateOnBlur={false}
          validateOnChange={false}
          validationSchema={Yup.object({
            code: Yup.string().required(t('validate.code.required')),
            email: Yup.string()
              .email(t('validate.email.invalid'))
              .required(t('validate.email.required')),
            pin: Yup.string()
              .matches(NUMBER_ONLY_REGEX, t('validate.pin.numbersOnly'))
              .required(t('validate.pin.required')),
          })}>
          {({handleChange, handleSubmit, errors, handleBlur, touched}) => (
            <TouchableWithoutFeedback
              onPress={() => {
                Keyboard.dismiss();
              }}>
              <WView
                borderRadius={12}
                color={Colors.white}
                mBottom={12}
                mHoz={16}
                padding={16}>
                <WText center marginBottom={24} type="semiBold18">
                  {t('button.addCodePin')}
                </WText>
                <WTextInput
                  containerStyle={styles.mb16}
                  errorMessage={errors?.code}
                  onBlur={handleBlur('code')}
                  onChangeText={handleChange('code')}
                  title={t('business.codeAndPin')}
                  touched={touched?.code}
                />
                <WTextInput
                  containerStyle={styles.mb16}
                  errorMessage={errors?.pin}
                  onBlur={handleBlur('pin')}
                  onChangeText={handleChange('pin')}
                  title={t('business.pin')}
                  touched={touched?.pin}
                />
                <WTextInput
                  errorMessage={errors?.email}
                  onBlur={handleBlur('email')}
                  onChangeText={handleChange('email')}
                  title={t('business.yourBusinessEmail')}
                  touched={touched?.email}
                />

                {/* {!!error && (
                  <WView
                    alignCenter
                    borderColor={Colors.danger600}
                    borderRadius={12}
                    borderWidth={1}
                    color={Colors.danger100}
                    mTop={16}
                    padding={12}
                    row>
                    <Icon
                      color={Colors.danger600}
                      name="Outline-WarningCircle"
                      size={24}
                    />
                    <WText fill marginLeft={8} type="regular12">
                      Invalid entry. Please check your CODE, PIN or business
                      email, then try again.
                    </WText>
                  </WView>
                )} */}

                <WView mTop={24} row>
                  <WButton
                    label={t('button.discard')}
                    onPress={() => {
                      navigation.goBack();
                    }}
                    outline
                    style={styles.discardBtn}
                  />
                  <WButton
                    label={t('button.add')}
                    onPress={handleSubmit}
                    style={GlobalStyle.flex1}
                  />
                </WView>
              </WView>
            </TouchableWithoutFeedback>
          )}
        </Formik>
      </ModalWrapper>
    </KeyboardAvoidingView>
  );
}

export default AddBusinessCodeModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.fadeBackground3,
    flex: 1,
  },
  discardBtn: {flex: 1, marginRight: 12},
  mb16: {marginBottom: 16},
  modalWrapper: {justifyContent: 'center'},
});
