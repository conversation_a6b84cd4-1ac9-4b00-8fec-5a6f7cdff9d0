import {Animated, PanResponder, StyleSheet} from 'react-native';
import React, {ReactNode, useRef, useState} from 'react';
import {WView} from 'src/components/index';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors} from '@themes/index';
import {SCREEN_WIDTH} from '@themes/Constants';

interface Props {
  maxHeight: number;
  minHeight: number;
  children: ReactNode;
  bgTop?: string;
}

function ModalBottomDrop({
  maxHeight,
  minHeight,
  children,
  bgTop = Colors.white,
}: Props) {
  const [containerHeight, setContainerHeight] = useState(minHeight);

  const containerHeightRef = useRef(containerHeight);

  const panResponder = useRef(
    PanResponder.create({
      onPanResponderMove: (_, gestureState) => {
        if (containerHeightRef?.current) {
          const newHeight = Math.max(
            minHeight,
            Math.min(maxHeight, containerHeightRef?.current - gestureState?.dy),
          );
          setContainerHeight(newHeight);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (containerHeightRef?.current) {
          const newHeight = Math.max(
            minHeight,
            Math.min(maxHeight, containerHeightRef?.current - gestureState?.dy),
          );
          setContainerHeight(newHeight);

          containerHeightRef.current = newHeight;
        }
      },
      onPanResponderTerminationRequest: () => true,
      onStartShouldSetPanResponder: () => true,
    }),
  )?.current;

  return (
    <Animated.View
      style={[styles.viewDrop, {height: containerHeight}]}
      {...panResponder?.panHandlers}>
      <WView
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
        color={Colors.white}
        fill
        mTop={-10}
        style={GlobalStyle.shadow}
        w={SCREEN_WIDTH}>
        <WView borderTopLeftRadius={16} borderTopRightRadius={16} color={bgTop}>
          <WView
            borderRadius={5}
            color={Colors.neutral400}
            h={5}
            mVer={12}
            selfCenter
            w={36}
          />
        </WView>
        {children}
      </WView>
    </Animated.View>
  );
}

export default ModalBottomDrop;

const styles = StyleSheet.create({
  viewDrop: {
    bottom: 0,
    position: 'absolute',
  },
});
