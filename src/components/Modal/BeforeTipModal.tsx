import {
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  TouchableWithoutFeedback,
  TextInput,
} from 'react-native';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {WB<PERSON>on, WText, WView} from '..';
import ModalWrapper from './ModalWrapper';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import {Colors, Fonts} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {BeforeTipModalRouteProps} from '@global';
import {formatNumberPrice} from '@utils/Tools';

interface BeforeTipModalProps {
  route: RouteProp<BeforeTipModalRouteProps, 'BeforeTipModal'>;
}

function BeforeTipModal({route}: BeforeTipModalProps) {
  const {t} = useTypeSafeTranslation();
  const {amount, onApply, valueDefault} = route?.params || {};

  const navigation = useNavigation();
  const [tipAmount, setTipAmount] = useState<number | string>('');
  const [isInputTipVisible, setIsInputTipVisible] = useState(false);
  const tipInputRef = useRef<TextInput | null>(null);

  const dataTip = useMemo(
    () => [
      {
        isHighlight: true,
        label: t('rating.noTip'),
        value: 0,
      },
      {
        isHighlight: Number(amount) !== 0,
        label: '10%',
        value: ((amount / 100) * 0.1).toFixed(2),
      },
      {
        isHighlight: Number(amount) !== 0,
        label: '15%',
        value: ((amount / 100) * 0.15).toFixed(2),
      },
      {
        isHighlight: Number(amount) !== 0,
        label: '20%',
        value: ((amount / 100) * 0.2).toFixed(2),
      },
    ],
    [amount, t],
  );

  const isDisabledSubmit = tipAmount === null;

  useEffect(() => {
    if (valueDefault || valueDefault === 0) {
      setTipAmount((valueDefault / 100).toFixed(2));
      setIsInputTipVisible(true);
    }
  }, [valueDefault]);

  const handleOpenKeyBoard = () => {
    if (tipInputRef?.current) {
      tipInputRef?.current?.focus();
    }
  };

  const handleCustomAmount = () => {
    setIsInputTipVisible(true);
    setTimeout(() => {
      handleOpenKeyBoard();
    }, 300);
  };

  const handleChangeAmountTip = (value: string) => {
    let formattedValue = value?.replace(/[^0-9.,]/g, '');

    formattedValue = formattedValue?.replace(',', '.');
    const parts = formattedValue?.split('.');

    if (Number(parts?.length) > 2) {
      formattedValue = `${parts?.[0]}.${parts
        ?.slice(1)
        ?.join('')
        ?.slice(0, 2)}`;
    } else if (Number(parts?.length) === 2) {
      formattedValue = `${parts?.[0]}.${parts[1]?.slice(0, 2)}`;
    }
    setTipAmount(formattedValue);
  };

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <ModalWrapper enableCloseOnMask={false} style={styles.modelWrapper}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
        keyboardVerticalOffset={SCREEN_HEIGHT * 0.4}>
        <WView
          borderRadius={16}
          color={Colors.white}
          padding={16}
          w={SCREEN_WIDTH - 32}>
          <WView gap={12}>
            <WText center type="semiBold18">
              {t('rating.addTip')}
            </WText>
            <WText center color={Colors.neutral700} type="regular14">
              {t('rating.noteTip', {value: formatNumberPrice(amount)})}
            </WText>
          </WView>

          <WView alignCenter justifyBetween mTop={12} row w={'100%'}>
            {dataTip?.map((item, index) => {
              const isSelected =
                tipAmount == item?.value &&
                isInputTipVisible &&
                item?.isHighlight;

              return (
                <TouchableOpacity
                  disabled={!item?.isHighlight}
                  key={index}
                  onPress={() => {
                    setTipAmount(item.value);
                    setIsInputTipVisible(true);
                  }}
                  style={[
                    styles.btnItemTip,
                    isSelected && styles.btnItemTipSelected,
                    !item?.isHighlight && styles.btnDisabled,
                  ]}>
                  <WText
                    color={isSelected ? Colors.white : Colors.neutral900}
                    type="regular14">
                    {item.label}
                  </WText>
                </TouchableOpacity>
              );
            })}
          </WView>

          <WView mTop={16}>
            {isInputTipVisible ? (
              <TouchableWithoutFeedback onPress={handleOpenKeyBoard}>
                <WView alignCenter justifyCenter row>
                  <WText style={styles.txtDollar} type="regular24">
                    {`$`}
                  </WText>
                  <TextInput
                    keyboardType="numeric"
                    onChangeText={handleChangeAmountTip}
                    ref={tipInputRef}
                    style={styles.inputAmount}
                    value={String(tipAmount)}
                    allowFontScaling={false}
                  />
                </WView>
              </TouchableWithoutFeedback>
            ) : (
              <TouchableOpacity onPress={handleCustomAmount}>
                <WText
                  color={Colors.blue700}
                  textAlign="center"
                  type="regular14">
                  {t('rating.enterCustomAmount')}
                </WText>
              </TouchableOpacity>
            )}
          </WView>
          <WView
            w={'100%'}
            h={1}
            color={Colors.neutral150}
            mTop={12}
            mBottom={16}
          />
          <WText color={Colors.neutral600} type="regular12" center>
            {t('rating.noteTip2')}
          </WText>

          <WView alignCenter mTop={24} row gap={12}>
            <WButton label={t('button.later')} fill onPress={goBack} outline />
            <WButton
              label={t('button.done')}
              onPress={() => {
                onApply((tipAmount as number) * 100);
              }}
              disabled={isDisabledSubmit}
              fill
            />
          </WView>
        </WView>
      </KeyboardAvoidingView>
    </ModalWrapper>
  );
}

export default BeforeTipModal;
const styles = StyleSheet.create({
  btnDisabled: {
    backgroundColor: Colors.grey6,
  },
  btnItemTip: {
    alignItems: 'center',
    borderColor: Colors.grey6,
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: 'center',
    paddingHorizontal: 13,
    paddingVertical: 8,
    width: '22%',
  },
  btnItemTipSelected: {
    backgroundColor: Colors.primary,
    borderWidth: 0,
  },
  modelWrapper: {alignItems: 'center', justifyContent: 'center'},
  txtDollar: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S34,
  },
  inputAmount: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S34,
    textAlign: 'center',
  },
});
