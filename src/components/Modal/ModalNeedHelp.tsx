/* eslint-disable react-hooks/exhaustive-deps */
import {
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useMemo, useState} from 'react';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import ModalWrapper from '@components/Modal/ModalWrapper';
import {IS_IOS, SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import {WButton, WText, WTextInput, WView} from '@components/index';
import {Colors, Icons} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import Dropdown from '@components/Dropdown';
import {CreateSupportTicketDto} from '@requests';
import {useSupportTicketServiceSupportTicketControllerCreateSupportTicket} from '@queries';
import {ModalNeedHelpProps} from '@global';

function ModalNeedHelp() {
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {bookingId, isRemovePayment = false} = (useRoute()?.params ??
    {}) as ModalNeedHelpProps;

  const [isLoading, setIsLoading] = useState(false);

  const {mutate: mutateCreateSupport} =
    useSupportTicketServiceSupportTicketControllerCreateSupportTicket();

  const {
    errors,
    handleBlur,
    handleChange,
    handleSubmit,
    values,
    setFieldValue,
    touched,
  } = useFormik({
    initialValues: {
      issueType: '',
      supportNote: '',
    },
    onSubmit: () => handleSubmitHelp(),
    validationSchema: Yup.object({
      issueType: Yup.string().required(t('validate.reason.required')),
      supportNote: Yup.string().required(t('validate.yourIssue.required')),
    }),
  });

  const handleSubmitHelp = () => {
    setIsLoading(true);
    const {issueType, supportNote} = values ?? {};
    mutateCreateSupport(
      {
        requestBody: {
          bookingId,
          issueType: issueType as CreateSupportTicketDto.issueType,
          supportNote,
        },
      },
      {
        onSuccess: () => {
          navigation.goBack();
          setTimeout(() => {
            navigation.navigate('ActionModal', {
              confirmLabel: t('button.gotIt'),
              content: t('help.submitSuccess'),
              hasCancelBtn: false,
              iconSize: 54,
              image: Icons.icSuccess,
              onApply: () => {
                navigation.goBack();
              },
              title: t('help.thankYou'),
            });
          }, 300);
        },
      },
    );
    setIsLoading(false);
  };

  const dataReason = useMemo(
    () => [
      ...(!isRemovePayment
        ? [
            {
              label: t('help.paymentIssue'),
              value: CreateSupportTicketDto.issueType.PAYMENT_ISSUE,
            },
          ]
        : []),
      {
        label: t('help.lostItems'),
        value: CreateSupportTicketDto.issueType.LOST_ITEM,
      },
      {
        label: t('help.other'),
        value: CreateSupportTicketDto.issueType.OTHER,
      },
    ],
    [t, isRemovePayment],
  );

  return (
    <ModalWrapper enableCloseOnMask={false} style={styles.modelWrapper}>
      <SafeAreaView style={styles.alignCenter}>
        <KeyboardAvoidingView
          behavior={IS_IOS ? 'padding' : 'height'}
          keyboardVerticalOffset={SCREEN_HEIGHT * (IS_IOS ? 0.4 : 1)}>
          <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
            <WView
              borderRadius={16}
              color={Colors.white}
              padding={16}
              w={SCREEN_WIDTH - 32}>
              <WView alignCenter justifyBetween row>
                <WText type="semiBold18">{t('help.index')}</WText>
                <TouchableWithoutFeedback onPress={navigation.goBack}>
                  <WView
                    borderRadius={20}
                    center
                    color={Colors.neutral500}
                    h={20}
                    w={20}>
                    <IonIcon
                      color={Colors.white}
                      name="close-outline"
                      size={16}
                    />
                  </WView>
                </TouchableWithoutFeedback>
              </WView>
              <WView gap={16} mTop={16}>
                <WText color={Colors.neutral875} type="medium14">
                  {t('help.contactSupport')}
                </WText>
                <WText color={Colors.neutral875} type="medium14">
                  {t('help.supportDescription')}
                </WText>
                <WView gap={16}>
                  <Dropdown
                    data={dataReason}
                    errorMessage={errors?.issueType}
                    label={t('help.reasonTitle')}
                    onChange={item => setFieldValue('issueType', item)}
                    placeholder={t('help.selectReason')}
                    touched={touched.issueType}
                    value={values.issueType}
                  />
                  {values?.issueType && (
                    <WTextInput
                      errorMessage={errors?.supportNote}
                      multiline
                      onBlur={handleBlur('supportNote')}
                      onChangeText={handleChange('supportNote')}
                      placeholder={t('help.shareYourIssue')}
                      style={styles.yourIssueContent}
                      styleInput={styles.yourIssueInput}
                      touched={touched?.supportNote}
                    />
                  )}
                </WView>
                <WButton
                  label={t('button.submit')}
                  loading={isLoading}
                  onPress={handleSubmit}
                />
              </WView>
            </WView>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ModalWrapper>
  );
}

export default ModalNeedHelp;

const styles = StyleSheet.create({
  alignCenter: {alignItems: 'center'},
  modelWrapper: {alignItems: 'center', justifyContent: 'center'},
  yourIssueContent: {
    height: 70,
    paddingVertical: 6,
  },
  yourIssueInput: {
    height: '100%',
  },
});
