import {RouteProp, useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleSheet, TouchableWithoutFeedback} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import WText from '../WText';
import WView from '../WView';
import ModalWrapper from './ModalWrapper';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {GENDER_DATA} from '@themes/Constants';
import {Colors} from '@themes';
import {GenderModalRouteProps} from '@global';
import {TranslationKeys} from '@generated/translationKeys';

interface GenderModalProps {
  route: RouteProp<GenderModalRouteProps, 'GenderModal'>;
}

function GenderModal({route}: GenderModalProps) {
  const {onApply, defaultGender} = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={16}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <WView alignCenter justifyBetween mBottom={16} row>
            <WText type="bold18">{t('profile.selectGender')}</WText>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral500}
                h={20}
                w={20}>
                <IonIcon color={Colors.white} name="close-outline" size={16} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>
          {GENDER_DATA.map(item => {
            const isSelected = item.id === defaultGender?.id;
            return (
              <TouchableWithoutFeedback
                key={item?.id}
                onPress={() => {
                  onApply?.(item);
                }}>
                <WView
                  alignCenter
                  justifyBetween
                  mVer={4}
                  pHoz={4}
                  pVer={12}
                  row
                  style={isSelected && styles.active}>
                  <WText type="medium14">
                    {t(item?.title as TranslationKeys)}
                  </WText>
                </WView>
              </TouchableWithoutFeedback>
            );
          })}
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default GenderModal;

const styles = StyleSheet.create({
  active: {
    backgroundColor: Colors.neutral150,
    borderRadius: 4,
  },
});
