import {Image, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import Icon from 'react-native-vector-icons/icomoon';
import IonIcon from 'react-native-vector-icons/Ionicons';

import Toast from 'react-native-toast-message';
import WView from '../WView';
import WText from '../WText';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors, Images} from '@themes';
import {ACTIVE_OPACITY, SCREEN_WIDTH} from '@themes/Constants';

export interface CustomToastProps {
  props: {
    content?: string;
    icon?: string;
    iconColor?: string;
    image?: string;
    title?: string;
    showCloseIcon?: boolean;
  };
}
function CustomToast({props}: CustomToastProps) {
  const {
    title,
    content,
    iconColor,
    icon,
    image,
    showCloseIcon = true,
  } = props || {};

  return (
    <SafeAreaView>
      <WView
        borderColor={Colors.neutral200}
        borderRadius={8}
        borderWidth={1}
        color={Colors.white}
        mHoz={16}
        padding={16}
        w={SCREEN_WIDTH - 32}>
        <WView alignCenter row>
          {image ? (
            <Image
              resizeMode="contain"
              source={image || Images.simpleLogo}
              style={styles.toastImg}
            />
          ) : (
            <Icon
              color={iconColor || Colors.success500}
              name={icon || 'Outline-CheckCircle'}
              size={20}
            />
          )}
          <WText
            color={Colors.neutral850}
            mWidth={'85%'}
            marginLeft={8}
            type="medium14">
            {title || 'Success'}
          </WText>
          {showCloseIcon && (
            <TouchableOpacity
              activeOpacity={ACTIVE_OPACITY}
              hitSlop={GlobalStyle.hitSlop}
              onPress={() => {
                Toast.hide();
              }}
              style={styles.closeIcon}>
              <IonIcon
                color={Colors.neutral600}
                name="close-outline"
                size={24}
              />
            </TouchableOpacity>
          )}
        </WView>

        {!!content && (
          <WText
            color={Colors.neutral600}
            lineHeight={20}
            marginTop={8}
            type="medium14">
            {content}
          </WText>
        )}
      </WView>
    </SafeAreaView>
  );
}

export default CustomToast;

const styles = StyleSheet.create({
  closeIcon: {marginLeft: 'auto'},
  toastImg: {height: 18, width: 18},
});
