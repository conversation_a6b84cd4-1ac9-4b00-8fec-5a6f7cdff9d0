import {RouteProp, useNavigation} from '@react-navigation/native';
import React from 'react';
import {TouchableWithoutFeedback} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {WView} from '..';
import ModalHeader from '../ModalHeader';
import WText from '../WText';
import ModalWrapper from './ModalWrapper';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors} from '@themes';
import {queryClient} from '@react-query/queryClient';
import {
  useUsersServiceEmployeeCodeControllerDeleteEmployeeCode,
  useUsersServiceEmployeeCodeControllerGetCodesOfUserKey,
} from '@queries';
import {BusinessConfigRouteProps} from '@global';

interface BusinessConfigModalProps {
  route: RouteProp<BusinessConfigRouteProps, 'BusinessConfig'>;
}

function BusinessConfigModal({route}: BusinessConfigModalProps) {
  const {business} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const {mutate: deleteBusinessCode} =
    useUsersServiceEmployeeCodeControllerDeleteEmployeeCode({
      onSuccess: () => {
        queryClient.refetchQueries({
          queryKey: [useUsersServiceEmployeeCodeControllerGetCodesOfUserKey],
          type: 'all',
        });
      },
    });

  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={12}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <ModalHeader title={t('business.businessConfiguration')} />

          <TouchableWithoutFeedback
            onPress={() => {
              navigation.goBack();
              navigation.navigate('BusinessDetail', {business});
            }}>
            <WView alignCenter mBottom={8} pVer={12} row>
              <WView
                alignCenter
                borderRadius={16}
                color={Colors.neutral200}
                h={32}
                justifyCenter
                w={32}>
                <Icon name="Outline-Eye" size={20} />
              </WView>
              <WText marginLeft={16} type="medium14">
                {t('business.viewDetailInformation')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback
            onPress={() => {
              navigation.goBack();
              navigation.navigate('ActionModal', {
                content: t('business.removeBusinessContent'),
                onApply: () => {
                  navigation.goBack();
                  deleteBusinessCode({
                    id: business?.code?.id,
                  });
                },
                title: t('business.removeBusiness'),
              });
            }}>
            <WView alignCenter pVer={12} row>
              <WView
                alignCenter
                borderRadius={16}
                color={Colors.neutral200}
                h={32}
                justifyCenter
                w={32}>
                <Icon name="Outline-Trash" size={20} />
              </WView>
              <WText marginLeft={16} type="medium14">
                {t('business.removeBusiness')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default BusinessConfigModal;
