/* eslint-disable react-hooks/exhaustive-deps */
import React, {useMemo} from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import {WView} from '..';
import WText from '../WText';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {BookingEntity} from '@requests';
import {getCarData} from '@utils/Tools';
import {GlobalStyle} from '@themes/GlobalStyle';
import {Colors} from '@themes';

interface CurrentBookingModalProps {
  onGoToBooking?: () => void;
  booking?: BookingEntity;
  onClose?: () => void;
}

function CurrentBookingModal({
  onGoToBooking,
  booking,
  onClose,
}: CurrentBookingModalProps) {
  const {t} = useTypeSafeTranslation();
  const _onGoToBooking = () => {
    onGoToBooking?.();
  };

  const _onClose = () => {
    onClose?.();
  };

  const car = getCarData(booking?.vehicleType as string);

  const titleStatus = useMemo(() => {
    if (!booking?.status) return '';

    const statusMapping: {
      DEMAND_CREATION: string;
      VEHICLE_CONFIRMED: string;
      COMPLETED: string;
      ARRIVAL_AT_CLIENT: string;
      CLIENT_ON_BOARD: string;
      AMOUNT_MONEY_RECEIVED: string;
      AWAITING_CONFIRMED_VEHICLE: string;
    } = {
      [BookingEntity.status.DEMAND_CREATION]: t('booking.connectDrivers'),
      [BookingEntity.status.VEHICLE_CONFIRMED]: t(
        'booking.status.vehicleConfirm',
      ),
      [BookingEntity.status.COMPLETED]: t('booking.status.completed'),
      [BookingEntity.status.ARRIVAL_AT_CLIENT]: t(
        'booking.status.arrivalAtClient',
      ),
      [BookingEntity.status.CLIENT_ON_BOARD]: t('booking.status.clientOnBoard'),
      [BookingEntity.status.AMOUNT_MONEY_RECEIVED]: t(
        'booking.status.completed',
      ),
      [BookingEntity.status.AWAITING_CONFIRMED_VEHICLE]: t(
        'booking.connectDrivers',
      ),
    };

    return statusMapping[booking.status as keyof typeof statusMapping] || '';
  }, [booking, t]);

  return (
    <SafeAreaView style={[styles.container, GlobalStyle.shadowSearchInput]}>
      <TouchableWithoutFeedback onPress={_onGoToBooking}>
        <WView padding={12}>
          <WView alignCenter row>
            <WView
              alignCenter
              borderRadius={8}
              color={Colors.neutral100}
              h={52}
              justifyCenter
              w={52}>
              <Image
                resizeMode="contain"
                source={car?.image}
                style={styles.carImg}
              />
            </WView>
            <WView fill mLeft={8} mRight={'auto'}>
              <WText type="medium14">{titleStatus}</WText>
              <WText color={Colors.neutral600} marginTop={4} type="regular14">
                {t('booking.bookingInProcessing')}
              </WText>
            </WView>
            <TouchableOpacity hitSlop={GlobalStyle.hitSlop} onPress={_onClose}>
              <IonIcon
                color={Colors.neutral500}
                name="close-outline"
                size={28}
              />
            </TouchableOpacity>
          </WView>
        </WView>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
}

export default CurrentBookingModal;

const styles = StyleSheet.create({
  carImg: {height: 20, width: 38},
  container: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral150,
    borderRadius: 12,
    borderWidth: 1,
    bottom: 16,
    left: 16,
    position: 'absolute',
    right: 16,
    zIndex: 10,
  },
});
