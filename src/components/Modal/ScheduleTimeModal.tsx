import {RouteProp, useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useRef, useState} from 'react';
import {
  LayoutAnimation,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import Icon from 'react-native-vector-icons/icomoon';
import DatePicker from 'react-native-date-picker';
import dayjs from 'dayjs';
import {WButton, WText, WView} from '..';
import ModalHeader from '../ModalHeader';
import ModalWrapper from './ModalWrapper';
import {CreateBookingWithCodeDto} from '@requests';
import {ScheduleTimeModalRouteProps} from '@global';
import {useGlobalState} from '@react-query/clientStateManage';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  DEFAULT_FORMAT_DATE,
  IS_ANDROID,
  SCREEN_WIDTH,
  SEARCH_TYPE,
  STR_TIME_FORMAT,
  TIME_NOT_24H_FORMAT,
} from '@themes/Constants';
import {Colors, Fonts} from '@themes';

const CURRENT_DATE = moment().format(DEFAULT_FORMAT_DATE);
const CALENDAR_THEME = {
  dayTextColor: Colors.neutral900,
  monthTextColor: Colors.neutral900,
  selectedDotColor: Colors.danger700,
  textDisabledColor: Colors.neutral400,
  textMonthFontFamily: Fonts.type.medium,
  textMonthFontWeight: '500',
  textSectionTitleColor: Colors.neutral900,
  todayTextColor: Colors.primary,
};

const TIME_PICKER_MODE = {
  DATE: 'DATE',
  TIME: 'TIME',
};

const MIN_TIME = 150;

interface ScheduleTimeModalProps {
  route: RouteProp<ScheduleTimeModalRouteProps, 'ScheduleTimeModal'>;
}

const DEFAULT_DATE = new Date();

function ScheduleTimeModal({route}: ScheduleTimeModalProps) {
  const {
    defaultDate,
    onApply,
    isMinDate = false,
    isRide,
    onPickupNow,
  } = route?.params || {};
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const [selectedDate, setSelectedDate] = useState(
    moment(defaultDate).format(DEFAULT_FORMAT_DATE) || CURRENT_DATE,
  );
  const [bookingWithCodeParams, setBookingWithCodeParams] = useGlobalState(
    'bookingWithCodeParams',
  );
  const [mode, setMode] = useState(TIME_PICKER_MODE.DATE);
  const [selectedTime, setSelectedTime] = useState(
    moment(defaultDate || CURRENT_DATE).toDate() || DEFAULT_DATE,
  );

  const formatCalenderDate = (date: string) => ({
    [date]: {
      marked: true,
      selected: true,
      selectedColor: Colors.neutral900,
    },
  });
  const scrollRef = useRef<ScrollView>(null);

  const getMinimumDate = () => {
    if (
      isMinDate &&
      selectedDate === dayjs().add(1, 'day').format(DEFAULT_FORMAT_DATE)
    ) {
      return dayjs().add(1, 'day').toDate();
    }
    if (selectedDate === moment().format(DEFAULT_FORMAT_DATE)) {
      return moment().add(MIN_TIME, 'minutes').toDate();
    }
    return undefined;
  };

  const onSelectTime = () => {
    if (mode === TIME_PICKER_MODE.DATE) {
      setBookingWithCodeParams({
        ...bookingWithCodeParams,
        date: `${moment(selectedDate).format(DEFAULT_FORMAT_DATE)} ${moment(
          selectedTime,
        ).format(STR_TIME_FORMAT)}`,
      } as CreateBookingWithCodeDto);

      if (
        !!bookingWithCodeParams?.originLocation?.address?.address &&
        !!bookingWithCodeParams?.destinationLocation?.address?.address
      ) {
        if (onApply) {
          return onApply(
            `${moment(selectedDate).format(DEFAULT_FORMAT_DATE)} ${moment(
              selectedTime,
            ).format(STR_TIME_FORMAT)}`,
          );
        }
        navigation.goBack();
        navigation.navigate('LocationMarkerSelector', {
          currentSearchType: SEARCH_TYPE.PICKUP,
          isConfirmPickupLocation: true,
          isTopBarEnable: false,
          location: bookingWithCodeParams?.originLocation,
        });
      } else {
        navigation.goBack();
      }
    } else {
      LayoutAnimation.easeInEaseOut();
      setMode(TIME_PICKER_MODE.DATE);
      scrollRef.current?.scrollTo?.({
        animated: true,
        x: -SCREEN_WIDTH - 32,
      });
    }
  };

  const openTimeModal = () => {
    scrollRef.current?.scrollTo?.({
      animated: true,
      x: SCREEN_WIDTH - 32,
    });
    setMode(TIME_PICKER_MODE.TIME);
    LayoutAnimation.easeInEaseOut();
  };

  const onDayPress = (day: any) => {
    setSelectedDate(day?.dateString);
    if (
      moment(
        `${day?.dateString} ${moment(selectedTime).format(STR_TIME_FORMAT)}`,
      ).isBefore(moment())
    ) {
      setSelectedTime(moment().toDate());
    }

    const changeDateMoment = moment(
      `${day?.dateString} ${moment(selectedTime).format(STR_TIME_FORMAT)}`,
    ).toDate();
    const minDate = isMinDate
      ? moment().add(1, 'day').toDate()
      : moment().add(MIN_TIME, 'minutes').toDate();

    const selectedMoment = moment(changeDateMoment).second(0);
    const minimumMoment = moment(minDate).second(0);
    if (selectedMoment.isBefore(minimumMoment)) {
      setSelectedTime(minimumMoment.toDate());
    }
    setTimeout(() => {
      openTimeModal();
    }, 300);
  };

  const handlePickupNow = () => {
    setBookingWithCodeParams({
      ...bookingWithCodeParams,
      date: null,
    } as CreateBookingWithCodeDto);
    onPickupNow?.();
    navigation.goBack();
  };

  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={16}
          color={Colors.white}
          h={mode === TIME_PICKER_MODE.TIME ? 380 : undefined}
          mBottom={12}
          mHoz={16}
          pVer={16}>
          <ModalHeader
            onClose={() => {
              if (mode === TIME_PICKER_MODE.TIME) {
                LayoutAnimation.easeInEaseOut();
                setMode(TIME_PICKER_MODE.DATE);
                scrollRef.current?.scrollTo?.({
                  animated: true,
                  x: -SCREEN_WIDTH - 32,
                });
              } else {
                navigation.goBack();
              }
            }}
            style={styles.mHoz16}
            title={t('booking.chooseATime')}
          />

          <ScrollView
            horizontal
            ref={scrollRef}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}>
            <WView w={SCREEN_WIDTH - 32}>
              <Calendar
                current={CURRENT_DATE}
                markedDates={formatCalenderDate(selectedDate)}
                maxDate={moment().add(6, 'day').format(DEFAULT_FORMAT_DATE)}
                minDate={
                  isMinDate ? dayjs().add(1, 'day').toDate() : CURRENT_DATE
                }
                onDayPress={onDayPress}
                renderArrow={(direction: string) => (
                  <Icon
                    name={
                      direction === 'left'
                        ? 'Outline-CaretLeft'
                        : 'Outline-CaretRight'
                    }
                    size={18}
                  />
                )}
                style={styles.calendar}
                theme={CALENDAR_THEME}
              />

              <WView alignCenter justifyBetween mTop={20} pHoz={16} row>
                <WText type="medium14">{t('booking.pickUpAt')}</WText>
                <TouchableOpacity onPress={openTimeModal}>
                  <WView
                    borderRadius={8}
                    color={Colors.neutral150}
                    pHoz={16}
                    pVer={8}>
                    <WText type="regular14">
                      {moment(selectedTime).format(TIME_NOT_24H_FORMAT)}
                    </WText>
                  </WView>
                </TouchableOpacity>
              </WView>
            </WView>
            <WView alignCenter justifyCenter w={SCREEN_WIDTH - 32}>
              {IS_ANDROID ? (
                <DatePicker
                  date={selectedTime}
                  is24hourSource="locale"
                  locale="en"
                  minimumDate={getMinimumDate()}
                  mode="time"
                  onConfirm={setSelectedTime}
                  onDateChange={setSelectedTime}
                  theme="light"
                />
              ) : (
                <DatePicker
                  date={selectedTime}
                  is24hourSource="locale"
                  locale="en"
                  minimumDate={getMinimumDate()}
                  mode="time"
                  onConfirm={setSelectedTime}
                  onDateChange={setSelectedTime}
                  theme="light"
                />
              )}
            </WView>
          </ScrollView>

          <WView gap={16} mHoz={16} mTop={16} row>
            {isRide && (
              <WButton
                bgColor={Colors.neutral150}
                fill
                label={t('button.pickUpNow')}
                labelColor={Colors.neutral900}
                onPress={handlePickupNow}
              />
            )}
            <WButton
              fill
              label={
                mode === TIME_PICKER_MODE.DATE
                  ? t('button.scheduleBooking')
                  : t('button.ok')
              }
              onPress={onSelectTime}
            />
          </WView>
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default ScheduleTimeModal;

const styles = StyleSheet.create({
  calendar: {
    height: 350,
  },
  mHoz16: {marginHorizontal: 16},
});
