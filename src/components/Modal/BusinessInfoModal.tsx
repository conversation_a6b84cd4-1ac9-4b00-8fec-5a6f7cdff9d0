import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleSheet, TouchableWithoutFeedback} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {WButton, WView} from '..';
import WText from '../WText';
import ModalWrapper from './ModalWrapper';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {TranslationKeys} from '@generated/translationKeys';
import {Colors} from '@themes';

const CONTENTS = [
  {
    content: 'business.infoContent1',
    icon: 'Outline-Briefcase',
    title: 'business.infoTitle1',
  },
  {
    content: 'business.infoContent2',
    icon: 'Outline-Newspaper',
    title: 'business.infoTitle2',
  },
  {
    content: 'business.infoContent3',
    icon: 'Outline-CreditCard',
    title: 'business.infoTitle3',
  },
];

function BusinessInfoModal() {
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();

  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={12}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <WText type="regular14">{t('business.mainInfoTitle')}</WText>
          <WView mTop={12}>
            {CONTENTS.map((item, index) => (
              <WView key={`${index}`} mBottom={12} row>
                <WView
                  alignCenter
                  borderRadius={18}
                  color={Colors.neutral150}
                  h={36}
                  justifyCenter
                  mRight={12}
                  w={36}>
                  <Icon color={Colors.neutral700} name={item.icon} size={20} />
                </WView>
                <WView fill>
                  <WText type="medium16">
                    {t(item?.title as TranslationKeys)}
                  </WText>
                  <WText type="regular14">
                    {t(item?.content as TranslationKeys)}
                  </WText>
                </WView>
              </WView>
            ))}
          </WView>
          <WButton
            label={t('button.getStarted')}
            onPress={() => {
              navigation.goBack();
              navigation.navigate('AddBusinessCodeModal');
            }}
            style={styles.mTop8}
          />
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default BusinessInfoModal;

const styles = StyleSheet.create({
  mTop8: {marginTop: 8},
});
