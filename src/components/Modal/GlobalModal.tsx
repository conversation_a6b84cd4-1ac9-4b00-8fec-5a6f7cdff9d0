import React, {forwardRef, useImperativeHandle, useState} from 'react';
import Modal from 'react-native-modal';
import {WButton, WImage, WText, WView} from '..';
import {Colors, Icons} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {useModal} from '@utils/hooks/useModal';

export interface GlobalModalRef {
  show: (message: string, description: string) => void;
  hide: () => void;
}

export const globalModalRef = React.createRef<GlobalModalRef>();

export const GlobalModalService = {
  hide: () => {
    globalModalRef.current?.hide();
  },
  show: (message: string, description: string) => {
    globalModalRef.current?.show(message, description);
  },
};

export const GlobalModal = forwardRef<GlobalModalRef>((_, ref) => {
  const {t} = useTypeSafeTranslation();
  const {isVisible, openModal, closeModal} = useModal();
  const [message, setMessage] = useState('');
  const [description, setDescription] = useState('');

  useImperativeHandle(ref, () => ({
    hide: () => {
      closeModal();
    },
    show: (msg: string, desc: string) => {
      setMessage(msg);
      setDescription(desc);
      openModal();
    },
  }));

  return (
    <Modal
      animationIn={'zoomIn'}
      animationOut={'zoomOut'}
      isVisible={isVisible}
      onBackdropPress={closeModal}>
      <WView borderRadius={16} color={Colors.white} gap={8} padding={16}>
        <WView center>
          <WImage size={48} source={Icons.icAlert} />
        </WView>
        <WText center color={Colors.neutral875} type="semiBold18">
          {message}
        </WText>
        <WText center color={Colors.neutral875} type="regular14">
          {description}
        </WText>

        <WView mTop={16}>
          <WButton label={t('button.gotIt')} onPress={closeModal} />
        </WView>
      </WView>
    </Modal>
  );
});
