import {ActivityIndicator} from 'react-native';
import React from 'react';
import WView from '../WView';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import {Colors} from '@themes';

function LoadingToast() {
  return (
    <WView
      alignCenter
      color={Colors.fadeBackground2}
      h={SCREEN_HEIGHT}
      justifyCenter
      w={SCREEN_WIDTH}>
      <WView
        alignCenter
        borderRadius={8}
        color={Colors.neutral200}
        h={50}
        justifyCenter
        w={50}>
        <ActivityIndicator color={Colors.neutral700} size={'small'} />
      </WView>
    </WView>
  );
}

export default LoadingToast;
