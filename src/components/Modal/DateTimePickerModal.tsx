import {RouteProp, useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {StyleSheet, TouchableWithoutFeedback} from 'react-native';
import DatePicker from 'react-native-date-picker';
import IonIcon from 'react-native-vector-icons/Ionicons';
import WButton from '../WButton';
import WText from '../WText';
import WView from '../WView';
import ModalWrapper from './ModalWrapper';
import {DateTimePickerModalRouteProps} from '@global';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors} from '@themes';

const DEFAULT_DATE = new Date();
interface DateTimePickerModalProps {
  route: RouteProp<DateTimePickerModalRouteProps, 'DateTimePickerModal'>;
}

function DateTimePickerModal({route}: DateTimePickerModalProps) {
  const {onApply, defaultDate} = route?.params || {};
  const [date, setDate] = useState(defaultDate || DEFAULT_DATE);
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={16}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <WView alignCenter justifyBetween mBottom={16} row>
            <WText type="bold18">{t('profile.dob')}</WText>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral500}
                h={20}
                w={20}>
                <IonIcon color={Colors.white} name="close-outline" size={16} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>
          <DatePicker
            date={date}
            is24hourSource="locale"
            locale="en"
            mode="date"
            onDateChange={setDate}
            style={styles.datePicker}
            theme="light"
          />
          <WButton
            label={t('button.confirm')}
            onPress={() => {
              onApply?.(date);
            }}
            style={styles.confirmBtn}
          />
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default DateTimePickerModal;

const styles = StyleSheet.create({
  confirmBtn: {marginHorizontal: 16},
  datePicker: {alignSelf: 'center', marginVertical: 16},
});
