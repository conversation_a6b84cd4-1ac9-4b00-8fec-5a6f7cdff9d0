import {useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import {WButton, WText, WView} from '..';
import ModalWrapper from './ModalWrapper';
import {CancelBookingDto} from '@requests';
import {useGlobalState} from '@react-query/clientStateManage';
import {
  useBookingsServiceBookingControllerCancelBooking,
  useBookingsServiceBookingControllerGetBookingOfMeKey,
  useBookingsServiceBookingControllerGetCurrentBookingKey,
} from '@queries';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {GlobalStyle} from '@themes/GlobalStyle';
import {SCREEN_HEIGHT, SCREEN_WIDTH} from '@themes/Constants';
import {Colors} from '@themes';
import {queryClient} from '@react-query/queryClient';

function InputCancelReasonModal() {
  const [currentBooking, setCurrentBooking] = useGlobalState('currentBooking');
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();

  const [note, setNote] = useState('');

  const onDiscard = () => {
    navigation.goBack();
  };

  const {mutate: cancelBooking} =
    useBookingsServiceBookingControllerCancelBooking({
      onSuccess: () => {
        setCurrentBooking({id: undefined});
      },
    });
  const onCancelBooking = () => {
    if (currentBooking?.id) {
      cancelBooking(
        {
          id: currentBooking?.id,
          requestBody: {
            note: note?.trim() || '',
            reason: CancelBookingDto.reason.OTHER,
          },
        },
        {
          onSuccess: () => {
            queryClient.refetchQueries({
              queryKey: [useBookingsServiceBookingControllerGetBookingOfMeKey],
              type: 'all',
            });
            queryClient.refetchQueries({
              queryKey: [
                useBookingsServiceBookingControllerGetCurrentBookingKey,
              ],
              type: 'all',
            });
            navigation.navigate('Main');
          },
        },
      );
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={-SCREEN_HEIGHT / 8}
      style={styles.container}>
      <ModalWrapper style={styles.modalWrapper}>
        <TouchableWithoutFeedback>
          <WView
            borderRadius={16}
            color={Colors.white}
            mBottom={12}
            mHoz={16}
            padding={16}>
            <WText center marginBottom={24} type="semiBold18">
              {t('cancelBooking.reasonBooking')}
            </WText>
            <TextInput
              allowFontScaling={false}
              multiline
              onChangeText={text => {
                setNote(text);
              }}
              placeholder={t('cancelBooking.enterReason')}
              style={styles.input}
              textAlignVertical={'top'}
              value={note}
            />
            <WView alignCenter row>
              <WButton
                label={t('button.discard')}
                onPress={onDiscard}
                outline
                style={styles.discardBtn}
              />
              <WButton
                label={t('button.confirm')}
                onPress={onCancelBooking}
                style={GlobalStyle.flex1}
              />
            </WView>
          </WView>
        </TouchableWithoutFeedback>
      </ModalWrapper>
    </KeyboardAvoidingView>
  );
}

export default InputCancelReasonModal;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  discardBtn: {flex: 1, marginRight: 12},
  input: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral400,
    borderRadius: 8,
    borderWidth: 1,
    height: SCREEN_HEIGHT / 8,
    marginBottom: 24,
    marginTop: 'auto',
    padding: 15,
    paddingTop: 15,
    width: SCREEN_WIDTH - 64,
  },
  modalWrapper: {justifyContent: 'center'},
});
