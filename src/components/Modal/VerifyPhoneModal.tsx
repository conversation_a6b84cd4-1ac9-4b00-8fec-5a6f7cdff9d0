/* eslint-disable react-hooks/exhaustive-deps */
import {KeyboardAvoidingView, Linking, StyleSheet} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {useNavigation, useRoute} from '@react-navigation/native';
import * as Yup from 'yup';
import ModalWrapper from './ModalWrapper';
import {
  CANADA_PHONE_CODE,
  EStepVerify,
  PHONE_REGEX,
  POLICY_VERIFY_PHONE,
  REGION,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
} from '@themes/Constants';
import {
  CodeInputForm,
  WButton,
  WImage,
  WText,
  WTextInput,
  WTouchable,
  WView,
} from '@components/index';
import {Colors, Icons} from '@themes/index';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {
  formatTimeMinuteSecond,
  numericText,
  showErrorAlert,
} from '@utils/Tools';
import {
  useUsersServiceUserControllerGetUserKey,
  useUsersServiceUserControllerResendVerificationCode,
  useUsersServiceUserControllerSendVerificationCode,
  useUsersServiceUserControllerUpdateMe,
  useUsersServiceUserControllerVerifyCode,
} from '@queries';
import {queryClient} from '@react-query/queryClient';
import {VerifyPhoneModalProps} from '@global';
import {ConfirmOtpDto} from '@requests';
import {useTranslation} from 'react-i18next';

function VerifyPhoneModal() {
  const {onApply, defaultPhoneNumber} = (useRoute().params ??
    {}) as VerifyPhoneModalProps;
  const navigation = useNavigation();
  const {t} = useTypeSafeTranslation();
  const {i18n} = useTranslation();

  const [step, setStep] = useState<EStepVerify>(EStepVerify.PhoneNumber);
  const [messageError, setMessageError] = useState('');
  const [isCounting, setIsCounting] = useState(false);
  const [timer, setTimer] = useState(60);
  const [isLoading, setIsLoading] = useState(false);

  const {mutateAsync: updateMe} = useUsersServiceUserControllerUpdateMe();
  const {mutateAsync: sendOtp} =
    useUsersServiceUserControllerSendVerificationCode();
  const {mutate: resendOtp} =
    useUsersServiceUserControllerResendVerificationCode();
  const {mutate: verifyOtp} = useUsersServiceUserControllerVerifyCode();

  const formik = useFormik({
    initialValues: {
      otp: '',
      phoneNumber: '',
    },
    onSubmit: () => handleUpdatePhone(),
    validationSchema: Yup.object({
      phoneNumber: Yup.string()
        .matches(PHONE_REGEX, t('validate.phoneNumber.invalid'))
        .required(t('validate.phoneNumber.required')),
    }),
  });

  const description = useMemo(
    () =>
      step === EStepVerify.VerificationCode
        ? t('verifyPhone.descriptionOtp')
        : t('verifyPhone.descriptionPhone'),
    [step],
  );

  const goBack = () => navigation.goBack();

  const handleResendCode = () => {
    const {phoneNumber} = formik.values ?? {};
    resendOtp(
      {
        requestBody: {
          phoneNumber,
          region: ConfirmOtpDto.region.CA,
        },
      },
      {
        onSuccess: () => {
          setIsCounting(true);
          setTimer(60);
        },
      },
    );
  };

  const handleUpdatePhone = async () => {
    const {phoneNumber} = formik.values ?? 0;
    setIsLoading(true);
    try {
      await updateMe({
        requestBody: {
          phoneNumber,
        },
      });

      await queryClient.refetchQueries({
        queryKey: [useUsersServiceUserControllerGetUserKey],
        type: 'all',
      });

      await sendOtp({
        requestBody: {phoneNumber, region: ConfirmOtpDto.region.CA},
      });

      setStep(EStepVerify.VerificationCode);
      setIsCounting(true);
    } catch (error) {
      // error
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyPhone = (code: string) => {
    const {phoneNumber} = formik.values ?? {};

    verifyOtp(
      {
        requestBody: {
          code,
          phoneNumber,
          region: ConfirmOtpDto.region.CA,
        },
      },
      {
        onSuccess: response => {
          if (response?.data) {
            navigation.goBack();
            setTimeout(() => {
              navigation.navigate('ActionModal', {
                enableCloseOnMask: false,
                image: Icons.icSuccess,
                iconSize: 54,
                confirmLabel: t('button.confirm'),
                hasCancelBtn: false,
                title: t('verifyPhone.titleSuccess'),
                content: t('verifyPhone.descriptionSuccess'),
                onApply: () => {
                  navigation.goBack();
                  setTimeout(() => {
                    onApply?.();
                  }, 300);
                },
              });
            }, 300);
          } else {
            setMessageError(t('verifyPhone.otpInCorrect'));
          }
        },
      },
    );
  };

  const onSetDefaultPhoneNumber = () => {
    if (defaultPhoneNumber) {
      formik.setFieldValue('phoneNumber', defaultPhoneNumber);
    }
  };

  useEffect(() => {
    onSetDefaultPhoneNumber();
  }, [defaultPhoneNumber]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isCounting) {
      interval = setInterval(() => {
        setTimer(prevTimer => {
          if (prevTimer <= 1) {
            clearInterval(interval as any);
            setIsCounting(false);
            return 60;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval as any);
  }, [isCounting]);

  const renderInput = () => {
    const handleChangePhoneNumber = (text: string) => {
      formik.setFieldValue('phoneNumber', numericText(text));
    };

    const handleOpenPolicy = () => {
      const language = i18n.language as 'en' | 'fr';
      const policyUrl = POLICY_VERIFY_PHONE[language];
      Linking.canOpenURL(policyUrl).then(supported => {
        if (supported) {
          Linking.openURL(policyUrl);
        } else {
          showErrorAlert({
            message: t('error.invalidLink'),
          });
        }
      });
    };

    const renderPhone = () => (
      <WView
        color={Colors.neutral150}
        pHoz={10}
        alignCenter
        pVer={4}
        gap={4}
        row
        borderRadius={32}
        mRight={16}>
        <WImage source={Icons.icCanada} size={24} />
        <WText type="medium14" color={Colors.neutral900}>
          {CANADA_PHONE_CODE}
        </WText>
      </WView>
    );

    if (step === EStepVerify.VerificationCode) {
      return (
        <CodeInputForm
          autoFocusOnLoad={true}
          isDefaultDash
          messageError={messageError}
          onChange={verifyCode => {
            formik.setFieldValue('otp', verifyCode);
          }}
          onSubmit={handleVerifyPhone}
          style={styles.codeInputContainer}
          styleInput={styles.inputOtp}
        />
      );
    }
    return (
      <WView gap={10}>
        <WTextInput
          errorMessage={formik.errors?.phoneNumber}
          onBlur={formik.handleBlur('phoneNumber')}
          placeholder={t('booking.phonePlaceholder')}
          style={styles.inputPhone}
          touched={formik.touched?.phoneNumber}
          renderLeft={renderPhone}
          value={formik.values.phoneNumber}
          keyboardType="phone-pad"
          onChangeText={handleChangePhoneNumber}
        />
        <WText type="medium12" lineHeight={16} color={Colors.neutral700}>
          {t('verifyPhone.policy')}
          <WText
            lineHeight={16}
            onPress={handleOpenPolicy}
            type="semiBold12"
            color={Colors.primary}>
            {t('verifyPhone.here')}
          </WText>
          .
        </WText>
      </WView>
    );
  };

  const renderButton = () => {
    if (step === EStepVerify.VerificationCode) {
      return (
        <WView gap={16} mTop={24}>
          <WTouchable center disabled={isCounting} onPress={handleResendCode}>
            <WText
              type="medium14"
              color={isCounting ? Colors.neutral500 : Colors.neutral875}>
              {isCounting ? t('button.resendCodeIn') : t('button.resendCode')}
              {` `}
              {isCounting && (
                <WText type="medium14">{formatTimeMinuteSecond(timer)}</WText>
              )}
            </WText>
          </WTouchable>
          <WButton label={t('button.back')} onPress={goBack} outline />
        </WView>
      );
    }
    return (
      <WView gap={12} mTop={24} row>
        <WButton fill label={t('button.cancel')} onPress={goBack} outline />
        <WButton
          fill
          loading={isLoading}
          label={t('button.verify')}
          onPress={formik.handleSubmit}
        />
      </WView>
    );
  };
  return (
    <ModalWrapper enableCloseOnMask={false} style={styles.modelWrapper}>
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={SCREEN_HEIGHT * 0.4}>
        <WView
          borderRadius={16}
          color={Colors.white}
          padding={16}
          w={SCREEN_WIDTH - 32}>
          <WView gap={8}>
            <WText center type="semiBold18">
              {t('verifyPhone.title')}
            </WText>
            <WText center color={Colors.neutral700} type="regular14">
              {description}
            </WText>
          </WView>
          {renderInput()}
          {renderButton()}
        </WView>
      </KeyboardAvoidingView>
    </ModalWrapper>
  );
}

export default VerifyPhoneModal;

const styles = StyleSheet.create({
  codeInputContainer: {
    marginTop: 16,
  },
  inputOtp: {
    height: 52,
    width: 45,
  },
  inputPhone: {
    marginTop: 10,
  },
  modelWrapper: {alignItems: 'center', justifyContent: 'center'},
});
