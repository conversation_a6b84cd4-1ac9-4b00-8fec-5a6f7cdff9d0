import {useNavigation} from '@react-navigation/native';
import React, {useMemo} from 'react';
import {
  Linking,
  Share,
  TouchableWithoutFeedback,
  StyleSheet,
} from 'react-native';
import IonIcon from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/icomoon';
import WText from '../WText';
import WView from '../WView';
import ModalWrapper from './ModalWrapper';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors} from '@themes';
import {useGlobalState} from '@react-query/clientStateManage';

interface ActionItemProps {
  icon?: string;
  label?: string;
  onPress: () => void;
}

function ActionItem({icon, label, onPress}: ActionItemProps) {
  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <WView alignCenter pVer={12} row>
        <WView
          alignCenter
          borderRadius={16}
          color={Colors.neutral200}
          h={32}
          justifyCenter
          w={32}>
          <Icon name={icon || 'Outline-ShieldStar'} size={20} />
        </WView>
        <WText marginLeft={16} type="medium14">
          {label || 'label'}
        </WText>
      </WView>
    </TouchableWithoutFeedback>
  );
}

function HelpsModal() {
  const [bookingWithCodeParams] = useGlobalState('bookingWithCodeParams');
  const [configData] = useGlobalState('configData');
  const {t} = useTypeSafeTranslation();

  const navigation = useNavigation();
  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `https://www.google.com/maps/search/?api=1&query=${bookingWithCodeParams?.originLocation?.geo?.latitude},${bookingWithCodeParams?.originLocation?.geo?.longitude}`,
        title: 'Share',
        url: `https://www.google.com/maps/search/?api=1&query=${bookingWithCodeParams?.originLocation?.geo?.latitude},${bookingWithCodeParams?.originLocation?.geo?.longitude}`,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      // return showErrorAlert({ message: error.message });
    }
  };

  const HELP_ACTIONS = useMemo(
    () => [
      {
        icon: 'Outline-ShieldStar',
        id: '1',
        label: t('help.callPolice'),
        onPress: () => {
          Linking.openURL(`tel:${configData?.policePhoneNumber}`);
        },
      },
      {
        icon: 'Outline-UserGear',
        id: '2',
        label: t('help.callDispatchCenter'),
        onPress: () => {
          Linking.openURL(`tel:${configData?.dispatchPhoneNumber}`);
        },
      },
      {
        icon: 'Outline-MapPinLine',
        id: '3',
        label: t('help.shareLocation'),
        onPress: () => {
          onShare();
        },
      },
    ],
    [configData?.dispatchPhoneNumber, configData?.policePhoneNumber, t],
  );

  return (
    <ModalWrapper style={styles.container}>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={16}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <WView alignCenter justifyBetween mBottom={16} row>
            <WText type="bold18">{t('help.title')}</WText>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.goBack();
              }}>
              <WView
                borderRadius={20}
                center
                color={Colors.neutral500}
                h={20}
                w={20}>
                <IonIcon color={Colors.white} name="close-outline" size={16} />
              </WView>
            </TouchableWithoutFeedback>
          </WView>
          <WView>
            {HELP_ACTIONS.map(item => (
              <ActionItem
                icon={item.icon}
                key={item.id}
                label={item.label}
                onPress={item.onPress}
              />
            ))}
          </WView>
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default HelpsModal;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
  },
});
