import {
  Keyboard,
  SafeAreaView,
  StyleProp,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import WView from '../WView';
import {useKeyboardVisible} from '@utils/hooks/useKeyBoard';
import {Colors} from '@themes';

interface ModalWrapperProps {
  children?: React.ReactNode;
  color?: string;
  style?: StyleProp<ViewStyle>;
  enableCloseOnMask?: boolean;
  enableSafeArea?: boolean;
  enableDetectKeyboard?: boolean;
}

function ModalWrapper({
  children,
  color,
  enableCloseOnMask = true,
  enableSafeArea = true,
  enableDetectKeyboard = true,
  style,
}: ModalWrapperProps) {
  const navigation = useNavigation();
  const isKeyboardVisible = useKeyboardVisible();

  return (
    <TouchableWithoutFeedback
      disabled={!enableCloseOnMask}
      onPress={() => {
        if (isKeyboardVisible && enableDetectKeyboard) {
          Keyboard.dismiss();
        } else {
          navigation.goBack();
        }
      }}>
      <WView
        color={color || Colors.fadeBackground2}
        fill
        justifyContent="flex-end"
        style={style}>
        {enableSafeArea ? (
          <SafeAreaView>{children}</SafeAreaView>
        ) : (
          <View>{children}</View>
        )}
      </WView>
    </TouchableWithoutFeedback>
  );
}

export default ModalWrapper;
