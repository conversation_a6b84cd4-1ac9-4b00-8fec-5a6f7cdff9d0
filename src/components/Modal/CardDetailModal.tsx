import React from 'react';
import {TouchableWithoutFeedback} from 'react-native';
import {PaymentIcon} from 'react-native-payment-icons';
import Icon from 'react-native-vector-icons/icomoon';
import {RouteProp, useNavigation} from '@react-navigation/native';
import WText from '../WText';
import WView from '../WView';
import ModalHeader from '../ModalHeader';
import ModalWrapper from './ModalWrapper';
import {CardViewType} from '@themes/Constants';
import {CardDetailModalRouteProps} from '@global';
import {
  useUsersServiceUserControllerDeleteUserCreditCard,
  useUsersServiceUserControllerListCardsKey,
  useUsersServiceUserControllerSetDefaultCard,
} from '@queries';
import {queryClient} from '@react-query/queryClient';
import {PopTo} from '@navigation/utils';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {Colors} from '@themes';

const asterisk = '**** **** ****';

interface CardDetailModalProps {
  route: RouteProp<CardDetailModalRouteProps, 'CardDetailModal'>;
}

function CardDetailModal({route}: CardDetailModalProps) {
  const {card} = route?.params || {};
  const {t} = useTypeSafeTranslation();
  const navigation = useNavigation();
  const {mutate: deleteUserCreditCard} =
    useUsersServiceUserControllerDeleteUserCreditCard({
      onSuccess: () => {
        PopTo(2);
        setTimeout(() => {
          queryClient.refetchQueries({
            queryKey: [useUsersServiceUserControllerListCardsKey],
            type: 'all',
          });
        }, 1000);
      },
    });

  const {mutate: setDefaultCard} = useUsersServiceUserControllerSetDefaultCard({
    onSuccess: () => {
      navigation.goBack();
      queryClient.refetchQueries({
        queryKey: [useUsersServiceUserControllerListCardsKey],
        type: 'all',
      });
    },
  });

  const onUnlinkPayment = () => {
    navigation.navigate('ActionModal', {
      confirmLabel: t('button.unlink'),
      content: t('payment.unlinkPaymentContent'),
      onApply: () => {
        deleteUserCreditCard({
          id: card?.id as string,
        });
      },
      title: t('payment.unlinkPayment'),
    });
  };

  const cardDate = `${card?.expMonth}/${card?.expYear}`;

  return (
    <ModalWrapper>
      <TouchableWithoutFeedback>
        <WView
          borderRadius={16}
          color={Colors.white}
          mBottom={12}
          mHoz={16}
          padding={16}>
          <ModalHeader
            title={`${card?.brand?.toLocaleUpperCase()} - ${card?.last4}`}
          />

          <WView borderRadius={16} color={Colors.neutral150} padding={24}>
            <WView justifyBetween row>
              <WView>
                <WText color={Colors.neutral600} type="regular14">
                  {t('payment.cardNumber')}
                </WText>
                <WView alignCenter mTop={4} row>
                  <WText color={Colors.neutral900} type="medium20">
                    {asterisk}
                  </WText>
                  <WText
                    color={Colors.neutral900}
                    marginBottom={8}
                    marginLeft={6}
                    type="medium14">
                    {card?.last4}
                  </WText>
                </WView>
                <WView mTop={24}>
                  <WText color={Colors.neutral600} type="regular14">
                    {t('payment.date')}
                  </WText>
                  <WText
                    color={Colors.neutral900}
                    marginTop={4}
                    type="medium14">
                    {cardDate}
                  </WText>
                </WView>
              </WView>
              <PaymentIcon
                height={40}
                type={card?.brand as CardViewType}
                width={40}
              />
            </WView>
          </WView>
          <TouchableWithoutFeedback
            onPress={() => {
              if (card?.id) {
                setDefaultCard({
                  id: card?.id,
                });
              }
            }}>
            <WView alignCenter mTop={14} pVer={12} row>
              <WView
                alignCenter
                borderRadius={16}
                color={Colors.neutral200}
                h={32}
                justifyCenter
                w={32}>
                <Icon name="Outline-Flag" size={18} />
              </WView>
              <WText marginLeft={16} type="medium14">
                {t('payment.setDefault')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>
          <TouchableWithoutFeedback onPress={onUnlinkPayment}>
            <WView alignCenter pVer={12} row>
              <WView
                alignCenter
                borderRadius={16}
                color={Colors.neutral200}
                h={32}
                justifyCenter
                w={32}>
                <Icon
                  color={Colors.danger700}
                  name="Outline-LinkBreak"
                  size={18}
                />
              </WView>
              <WText marginLeft={16} type="medium14">
                {t('payment.unlinkMethod')}
              </WText>
            </WView>
          </TouchableWithoutFeedback>
        </WView>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
}

export default CardDetailModal;
