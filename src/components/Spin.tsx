import {ActivityIndicator} from 'react-native';
import React, {ReactNode} from 'react';
import WView from './WView';
import {Colors} from '@themes/index';

interface Props {
  loading: boolean;
  children: ReactNode;
}

const Spin: React.FC<Props> = ({children, loading}) => {
  if (loading) {
    return (
      <WView center h={'100%'}>
        <ActivityIndicator color={Colors.primary} size={'large'} />
      </WView>
    );
  }
  return <>{children}</>;
};

export default Spin;
