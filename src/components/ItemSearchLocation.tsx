import {StyleSheet, View} from 'react-native';
import React, {useMemo, useState} from 'react';
import Icon from 'react-native-vector-icons/icomoon';
import WTouchable from './WTouchable';
import {ItemLocationProps, ILocationItem} from '@global';
import useTypeSafeTranslation from '@utils/hooks/useTypeSafeTranslation';
import {formatLocationData} from '@utils/Tools';
import {
  GOOGLE_MAPS_API_KEY,
  ITEM_SHOW_SUB,
  SEARCH_TYPE,
} from '@themes/Constants';
import {Colors} from '@themes/index';
import {WText, WView} from '@components/index';
import {useGlobalState} from '@react-query/clientStateManage';

function ItemSearchLocation({
  currentSearchType,
  data,
  item,
  hasSearchText,
  onPressItem,
}: ItemLocationProps) {
  const {t} = useTypeSafeTranslation();

  const [showAllChildren, setShowAllChildren] = useState(false);

  const [pickUpConfirm, setPickUpConfirm] = useGlobalState('pickUpConfirm');

  const searchPlaceDetail = async (placeId: string) => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${GOOGLE_MAPS_API_KEY}`,
    );
    const result = formatLocationData((await response.json())?.result);

    onPressItem?.(result);
  };

  const handlePressItem = (item: ILocationItem, isChildren = false) => {
    const placeId = isChildren ? data?.placeId : item?.placeId;

    if (placeId && currentSearchType === SEARCH_TYPE.PICKUP) {
      setPickUpConfirm({...pickUpConfirm, placeId});
    }

    if (placeId) {
      return onPressItem?.({
        ...formatLocationData(item),
        placeId,
      });
    }

    if (item?.name) {
      return onPressItem?.(formatLocationData(item));
    }

    searchPlaceDetail(item?.place_id ?? '');
  };

  const renderItemLocation = (item: ILocationItem, isChildren = false) => (
    <WTouchable
      alignItems="flex-start"
      color={Colors.white}
      key={item?.place_id || item?.name}
      mHoz={16}
      mTop={16}
      onPress={() => handlePressItem(item, isChildren)}
      row>
      <WView
        alignCenter
        borderRadius={14}
        color={Colors.neutral200}
        h={28}
        justifyCenter
        w={28}>
        <Icon
          color={Colors.neutral500}
          name={
            hasSearchText ? 'Outline-MapPin' : 'Outline-ClockCounterClockwise'
          }
          size={16}
        />
      </WView>

      <View style={styles.address}>
        <WText color={Colors.neutral875} numberOfLines={1} type="medium14">
          {item?.name ?? item?.structured_formatting?.main_text}
        </WText>
        <WView alignCenter justifyContent="flex-start" row>
          <WText
            color={Colors.neutral600}
            lineHeight={20}
            style={styles.textAddress}
            type="regular12">
            {item?.address || item?.formatted_address || item?.description}
          </WText>
        </WView>
      </View>
    </WTouchable>
  );

  const formatSubLocation = useMemo(() => {
    if ((data?.subLocations || [])?.length === 0) return [];
    return (data?.subLocations || [])?.map(item => ({
      address: item?.address || '',
      coordinates: {
        latitude: item?.coordinates?.[1] || 0,
        longitude: item?.coordinates?.[0] || 0,
      },
      name: item?.name || '',
    }));
  }, [data?.subLocations]);

  const itemsToShow =
    formatSubLocation && showAllChildren
      ? formatSubLocation
      : formatSubLocation?.slice(0, 2);
  const countViewAll = formatSubLocation ? formatSubLocation?.length - 2 : 0;

  return (
    <WView>
      {renderItemLocation(item)}
      {formatSubLocation?.length > 0 && hasSearchText && (
        <WView mLeft={40}>
          {itemsToShow?.map(childItem => renderItemLocation(childItem, true))}
          {!showAllChildren &&
            Number(formatSubLocation?.length) > ITEM_SHOW_SUB && (
              <WTouchable
                mLeft={20}
                mTop={16}
                onPress={() => setShowAllChildren(true)}>
                <WText color={Colors.blue700} type="medium14">
                  {t('booking.viewAll', {count: countViewAll})}
                </WText>
              </WTouchable>
            )}
        </WView>
      )}
    </WView>
  );
}

export default ItemSearchLocation;

const styles = StyleSheet.create({
  address: {
    flex: 1,
    marginLeft: 12,
  },
  textAddress: {
    marginTop: 5,
  },
});
