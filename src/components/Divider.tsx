import React from 'react';
import {WText, WView} from '@components/index';
import {Colors} from '@themes/index';

interface Props {
  label?: string;
}

function Divider({label}: Props) {
  return (
    <WView alignCenter gap={8} row>
      <WView color={Colors.neutral300} fill h={1} mVer={12} />
      {label && (
        <WText color={Colors.neutral300} type="medium14">
          {label}
        </WText>
      )}
      <WView color={Colors.neutral300} fill h={1} mVer={12} />
    </WView>
  );
}

export default Divider;
