import {StyleSheet, View} from 'react-native';
import React from 'react';
import {SCREEN_WIDTH} from '../themes/Constants';
import {Colors} from '../themes';
import WText from './WText';

interface CodeItemProps {
  value?: string;
  isSelected?: boolean;
}

function CodeItem({value, isSelected}: CodeItemProps) {
  return (
    <View
      style={[
        styles.textInput,
        {
          borderColor: isSelected ? Colors.grey1 : Colors.neutral400,
        },
      ]}>
      <WText color={Colors.grey1} type="bold24">
        {value || null}
      </WText>
    </View>
  );
}

export default CodeItem;

const styles = StyleSheet.create({
  textInput: {
    alignItems: 'center',
    backgroundColor: Colors.neutral50,
    borderRadius: 8,
    borderWidth: 1,
    height: (SCREEN_WIDTH - 32) / 6,
    justifyContent: 'center',
    marginRight: 5,
    width: (SCREEN_WIDTH - 72) / 6,
  },
});
