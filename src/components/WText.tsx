/* eslint-disable react-native/no-unused-styles */
import React from 'react';
import {
  FlexStyle,
  StyleProp,
  StyleSheet,
  Text as RNText,
  TextProps,
  TextStyle,
} from 'react-native';
import {Colors, Fonts} from '../themes';

interface RNTextProps extends TextProps {
  type:
    | 'bold10'
    | 'bold12'
    | 'bold13'
    | 'bold14'
    | 'bold16'
    | 'bold18'
    | 'bold20'
    | 'bold24'
    | 'bold36'
    | 'light10'
    | 'light12'
    | 'light14'
    | 'light16'
    | 'light18'
    | 'light20'
    | 'medium10'
    | 'medium11'
    | 'medium12'
    | 'medium14'
    | 'medium16'
    | 'medium18'
    | 'medium20'
    | 'medium24'
    | 'medium30'
    | 'medium34'
    | 'regular10'
    | 'regular12'
    | 'regular14'
    | 'regular16'
    | 'regular18'
    | 'regular20'
    | 'regular24'
    | 'semiBold10'
    | 'semiBold12'
    | 'semiBold14'
    | 'semiBold16'
    | 'semiBold18'
    | 'semiBold20'
    | 'semiBold22'
    | 'semiBold24'
    | 'semiBold30'
    | 'semiBold34'
    | 'semiBold6'
    | 'montserratBold24'
    | 'montserratRegular14'
    | 'montserratRegular10'
    | 'montserratMedium14'
    | 'montserratBold10'
    | 'montserratBold14'
    | 'txtUnderline';
  center?: boolean;
  style?: StyleProp<TextStyle>;
  children?: React.ReactNode;
  fill?: boolean;
  textAlign?: 'auto' | 'center' | 'justify' | 'left' | 'right';
  color?: TextStyle['color'];
  underLine?: boolean;
  marginTop?: FlexStyle['marginTop'];
  marginBottom?: FlexStyle['marginBottom'];
  marginLeft?: FlexStyle['marginLeft'];
  marginRight?: FlexStyle['marginRight'];
  marginHorizontal?: FlexStyle['marginHorizontal'];
  marginVertical?: FlexStyle['marginVertical'];
  numberOfLine?: number;
  lineHeight?: number;
  mWidth?: string | number;
  capitalize?: boolean;
  minHeight?: number;
  textDecorationLine?: TextStyle['textDecorationLine'];
}

function WText(props: RNTextProps) {
  const {
    type,
    color = Colors.neutral900,
    center,
    underLine,
    style,
    children,
    marginTop,
    marginBottom,
    marginLeft,
    marginRight,
    marginHorizontal,
    marginVertical,
    fill,
    numberOfLine,
    textAlign,
    lineHeight,
    mWidth,
    capitalize,
    minHeight,
    textDecorationLine,
  } = props;
  return (
    <RNText
      {...props}
      allowFontScaling={false}
      numberOfLines={numberOfLine}
      style={[
        styles[type],
        color && {color},
        center && styles.center,
        fill && styles.fill,
        underLine && styles.txtUnderline,
        marginTop && {marginTop},
        marginBottom && {marginBottom},
        marginLeft && {marginLeft},
        marginRight && {marginRight},
        marginHorizontal && {marginHorizontal},
        marginVertical && {marginVertical},
        textAlign && {textAlign},
        // eslint-disable-next-line react-native/no-inline-styles
        capitalize && {textTransform: 'capitalize'},
        style,
        lineHeight && {
          lineHeight,
        },
        mWidth && {maxWidth: mWidth},
        minHeight && {minHeight},
        textDecorationLine && {textDecorationLine},
      ]}>
      {children}
    </RNText>
  );
}
export const styles = StyleSheet.create({
  bold10: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S10,
  },
  bold12: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S12,
  },
  bold13: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S13,
  },
  bold14: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S14,
  },
  bold16: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S16,
  },
  bold18: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S18,
  },
  bold20: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S20,
  },
  bold24: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S24,
  },
  bold36: {
    fontFamily: Fonts.type.bold,
    fontSize: Fonts.size.S36,
  },
  center: {
    textAlign: 'center',
  },
  fill: {
    flex: 1,
  },
  light10: {
    fontFamily: Fonts.type.light,
    fontSize: Fonts.size.S10,
  },
  light12: {
    fontFamily: Fonts.type.light,
    fontSize: Fonts.size.S12,
  },

  light14: {
    fontFamily: Fonts.type.light,
    fontSize: Fonts.size.S14,
  },
  light16: {
    fontFamily: Fonts.type.light,
    fontSize: Fonts.size.S16,
  },
  light18: {
    fontFamily: Fonts.type.light,
    fontSize: Fonts.size.S18,
  },
  light20: {
    fontFamily: Fonts.type.light,
    fontSize: Fonts.size.S20,
  },
  medium10: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S10,
  },
  medium11: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S11,
  },
  medium12: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S12,
  },
  medium14: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S14,
  },
  medium16: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S16,
  },
  medium18: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S18,
  },
  medium20: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S20,
  },
  medium24: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S24,
  },
  medium30: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S30,
  },
  medium34: {
    fontFamily: Fonts.type.medium,
    fontSize: Fonts.size.S34,
  },
  montserratBold10: {
    fontFamily: Fonts.type.montserratBold,
    fontSize: 10,
  },
  montserratBold14: {
    fontFamily: Fonts.type.montserratBold,
    fontSize: 14,
  },
  montserratBold24: {
    fontFamily: Fonts.type.montserratBold,
    fontSize: 24,
  },
  montserratMedium14: {
    fontFamily: Fonts.type.montserratMedium,
    fontSize: 14,
  },
  montserratRegular10: {
    fontFamily: Fonts.type.montserratRegular,
    fontSize: 10,
  },
  montserratRegular14: {
    fontFamily: Fonts.type.montserratRegular,
    fontSize: 14,
  },
  regular10: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S10,
  },
  regular12: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S12,
  },
  regular14: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S14,
  },
  regular16: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S16,
  },
  regular18: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S18,
  },
  regular20: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S20,
  },
  regular24: {
    fontFamily: Fonts.type.regular,
    fontSize: Fonts.size.S24,
  },
  semiBold10: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S10,
  },
  semiBold12: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S12,
  },
  semiBold14: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S14,
  },
  semiBold16: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S16,
  },
  semiBold18: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S18,
  },
  semiBold20: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S20,
  },
  semiBold22: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S22,
  },
  semiBold24: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S24,
  },
  semiBold30: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S30,
  },
  semiBold34: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S34,
  },
  semiBold6: {
    fontFamily: Fonts.type.semiBold,
    fontSize: Fonts.size.S6,
  },
  txtUnderline: {
    textDecorationLine: 'underline',
  },
});

export default WText;
