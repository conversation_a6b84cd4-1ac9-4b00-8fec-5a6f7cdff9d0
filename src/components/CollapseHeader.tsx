import React from 'react';
import {LayoutAnimation, TouchableWithoutFeedback} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {WText, WView} from '@components';

interface CollapseHeaderProps {
  title?: string;
  isOpen?: boolean;
  onPress?: () => void;
}

function CollapseHeader({title, onPress, isOpen}: CollapseHeaderProps) {
  const _onPress = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    onPress?.();
  };
  return (
    <TouchableWithoutFeedback onPress={_onPress}>
      <WView alignCenter mBottom={4} mHoz={16} mTop={24} row>
        <WText marginRight={'auto'} type="semiBold18">
          {title}
        </WText>
        <Icon
          name={isOpen ? 'Outline-CaretUp' : 'Outline-CaretDown'}
          size={24}
        />
      </WView>
    </TouchableWithoutFeedback>
  );
}

export default CollapseHeader;
