import React, {useState, useEffect, useRef} from 'react';
import {WView, WText, WTouchable} from '@components/index';
import {Colors} from '@themes/index';
import {
  FlatList,
  ListRenderItem,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {useModal} from '@utils/hooks/useModal';
import {IData} from '@global';
import {GlobalStyle} from '@themes/GlobalStyle';
import {isEmpty} from 'lodash-es';

interface Props {
  data: IData[];
  onChange: (item: string) => void;
  label?: string;
  required?: boolean;
  placeholder?: string;
  value?: string;
  errorMessage?: string;
  touched?: boolean;
}

function Dropdown({
  data,
  onChange,
  label,
  required,
  placeholder,
  value,
  errorMessage,
  touched,
}: Props) {
  const {isVisible, closeModal, toggleModal} = useModal();
  const [isError, setIsError] = useState(false);

  const dropdownHeight = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      Animated.timing(dropdownHeight, {
        toValue: 200,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: false,
      })?.start();
    } else {
      Animated.timing(dropdownHeight, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: false,
      })?.start();
    }
  }, [isVisible]);

  useEffect(() => {
    setIsError(!isEmpty(errorMessage));
  }, [errorMessage]);

  const nameValue = data?.find(item => item?.value === value)?.label ?? '';

  const renderItem: ListRenderItem<IData> = ({item}) => {
    const onChangeItem = () => {
      onChange(String(item?.value));
      closeModal();
    };

    const isSelected = value === item?.value;
    return (
      <WTouchable onPress={onChangeItem}>
        <WText
          type="regular14"
          color={isSelected ? Colors.blue700 : Colors.neutral875}>
          {item?.label ?? ''}
        </WText>
      </WTouchable>
    );
  };

  return (
    <WView>
      {label && (
        <WText marginBottom={9} color={Colors.neutral875} type="medium14">
          {label}
          {required && (
            <WText color={Colors.error} type="medium14">
              {` *`}
            </WText>
          )}
        </WText>
      )}

      <WTouchable
        pHoz={16}
        onPress={toggleModal}
        pVer={12}
        borderWidth={1}
        borderRadius={8}
        row
        alignCenter
        justifyBetween
        borderColor={Colors.neutral600}
        style={[isError && touched && styles.error]}>
        {value ? (
          <WText type="regular14" color={Colors.neutral875}>
            {nameValue}
          </WText>
        ) : placeholder ? (
          <WText type="regular14" color={Colors.neutral500}>
            {placeholder}
          </WText>
        ) : null}
        <Icon
          name={isVisible ? 'Outline-CaretUp' : 'Outline-CaretDown'}
          size={20}
        />
      </WTouchable>

      {isError && touched && (
        <WView mTop={4} row>
          <Icon
            color={Colors.primary900}
            name="Outline-WarningCircle"
            size={16}
          />
          <WText color={Colors.primary900} marginLeft={4} type="regular12">
            {errorMessage}
          </WText>
        </WView>
      )}

      <WView style={GlobalStyle.shadow}>
        <Animated.View
          style={[styles.dropdownContainer, {maxHeight: dropdownHeight}]}>
          <WView
            color={Colors.white}
            padding={12}
            borderRadius={8}
            mBottom={16}>
            <FlatList
              data={data}
              keyExtractor={(_, index) => `${index}`}
              renderItem={renderItem}
              ItemSeparatorComponent={() => (
                <WView h={1} color={Colors.neutral200} mVer={12} />
              )}
            />
          </WView>
        </Animated.View>
      </WView>
    </WView>
  );
}

export default Dropdown;

const styles = StyleSheet.create({
  dropdownContainer: {
    overflow: 'hidden',
    borderRadius: 8,
    marginTop: 8,
  },
  error: {
    borderColor: Colors.primary900,
    borderWidth: 2,
  },
});
