import {Animated, PanResponder, StyleSheet, Text, View} from 'react-native';
import React, {useRef} from 'react';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '../themes';
import WView from './WView';
import WText from './WText';

const MIN_WIDTH = 0;
const MAX_WIDTH = 200;

function SwipeableRow({item}) {
  const widthAnimatedValue = useRef(new Animated.Value(MIN_WIDTH)).current;
  const lastGestureDy = useRef(0);

  const panResponder = useRef(
    PanResponder.create({
      onPanResponderGrant: () => {
        widthAnimatedValue.setOffset(lastGestureDy.current);
      },
      onPanResponderMove: (e, gesture) => {
        widthAnimatedValue.setValue(gesture.dy);
      },
      onPanResponderRelease: (e, gesture) => {
        scrollRef.current?.scrollTo?.({animated: true, y: 0});
        widthAnimatedValue.flattenOffset();

        lastGestureDy.current += gesture.dy;

        if (gesture.dy > 0) {
          // dragging down
          if (gesture.dy <= DRAG_THRESHOLD) {
            springAnimation('up');
          } else {
            springAnimation('down');
          }
        } else {
          // dragging up
          // eslint-disable-next-line no-lonely-if
          if (gesture.dy >= -DRAG_THRESHOLD) {
            springAnimation('down');
          } else {
            springAnimation('up');
          }
        }
      },
      onStartShouldSetPanResponder: () => true,
    }),
  ).current;

  return (
    <WView color={Colors.white} pHoz={16} pVer={12} row>
      <WView
        alignCenter
        borderRadius={20}
        color={Colors.neutral900}
        h={38}
        justifyCenter
        w={38}>
        <Icon color={Colors.white} name="Outline-BellSimpleRinging" size={20} />
      </WView>
      <WView fill mLeft={8}>
        <WText type="medium14">{item?.title}</WText>
        <WText fill marginTop={4} numberOfLine={2} type="regular14">
          {item?.content}
        </WText>
      </WView>
    </WView>
  );
}

export default SwipeableRow;

const styles = StyleSheet.create({});
