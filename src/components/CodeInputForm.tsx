/* eslint-disable no-nested-ternary */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  StyleProp,
  ViewStyle,
} from 'react-native';
import {Colors} from '../themes';
import {SCREEN_WIDTH} from '../themes/Constants';
import {GlobalStyle} from '../themes/GlobalStyle';
import WText from './WText';

interface CodeInputFormProps {
  autoFocusOnLoad?: boolean;
  onSubmit?: (code: string) => void;
  style?: StyleProp<ViewStyle>;
  onChange?: (code: string | undefined) => void;
  styleInput?: StyleProp<ViewStyle>;
  isDefaultDash?: boolean;
  messageError?: string;
}

function CodeInputForm({
  autoFocusOnLoad,
  onSubmit,
  onChange,
  style,
  styleInput,
  isDefaultDash = false,
  messageError,
  ...props
}: CodeInputFormProps) {
  const codeInput = useRef<TextInput>(null);
  const [code, setCode] = useState('');
  const [isBlur, setIsBlur] = useState(!autoFocusOnLoad);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const onFocusTextInput = () => {
    setIsBlur(false);
    codeInput.current?.focus();
  };

  useEffect(() => {
    if (autoFocusOnLoad) {
      onFocusTextInput();
    }
  }, []);

  const onChangeText = (inputCode: string) => {
    setCode(inputCode);

    if (inputCode.length === 6 && !isSubmitted) {
      setIsSubmitted(true);
      onSubmit?.(inputCode);
      Keyboard.dismiss();
    } else {
      setIsSubmitted(false);
    }

    onChange?.(inputCode);
  };

  const onBlurInput = () => {
    setIsBlur(true);
  };

  const renderInput = (position: number) => (
    <View
      style={[
        styles.textInput,
        {
          borderColor: messageError
            ? Colors.primary900
            : position === code.length && !isBlur
            ? Colors.grey1
            : Colors.neutral400,
        },
        styleInput,
      ]}>
      <WText color={Colors.grey1} type="bold24">
        {code[position] || (isDefaultDash ? '-' : '')}
      </WText>
    </View>
  );
  return (
    <View style={[styles.container, style]}>
      <TextInput
        {...props}
        allowFontScaling={false}
        autoFocus={true}
        keyboardType={Platform.OS === 'android' ? 'numeric' : 'number-pad'}
        maxLength={6}
        onBlur={onBlurInput}
        onChangeText={onChangeText}
        ref={codeInput}
        selectionColor={'white'}
        style={styles.marginTop}
        textContentType="oneTimeCode"
        value={code}
      />
      <View style={styles.topView}>
        <TouchableWithoutFeedback
          hitSlop={GlobalStyle.hitSlop}
          onPress={onFocusTextInput}
          style={styles.inputCollectionTouch}>
          <View style={styles.inputCollection}>
            {renderInput(0)}
            {renderInput(1)}
            {renderInput(2)}
            {renderInput(3)}
            {renderInput(4)}
            {renderInput(5)}
          </View>
        </TouchableWithoutFeedback>
        {messageError && (
          <WText color={Colors.primary900} marginTop={8} type="regular14">
            {messageError}
          </WText>
        )}
      </View>
    </View>
  );
}

export default CodeInputForm;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    alignSelf: 'center',
    height: (SCREEN_WIDTH - 32) / 6,
    justifyContent: 'center',
    width: '100%',
  },
  inputCollection: {
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputCollectionTouch: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  marginTop: {
    color: Colors.white,
    marginTop: 5,
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: Colors.neutral50,
    borderRadius: 8,
    borderWidth: 1,
    height: (SCREEN_WIDTH - 32) / 6,
    justifyContent: 'center',
    marginRight: 5,
    width: (SCREEN_WIDTH - 72) / 6,
  },
  topView: {
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
});
