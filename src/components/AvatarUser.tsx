/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {
  Image,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors, Fonts} from '../themes';
import {ACTIVE_OPACITY} from '../themes/Constants';
import Skeleton from './Skeleton';
import {WText, WView} from './index';

interface AvatarUserProps {
  avatarUrl?: string;
  fullName?: string;
  size?: number;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
}

function AvatarUser({
  avatarUrl,
  fullName,
  size = 40,
  style,
  onPress,
}: AvatarUserProps) {
  const [isLoading, setIsLoading] = useState<boolean>(!!avatarUrl);

  const _onPress = () => {
    onPress?.();
  };

  const renderAvatar = () => {
    if (!avatarUrl && !fullName) {
      return (
        <Icon color={Colors.primary} name="Outline-User" size={size / 2} />
      );
    }
    return (
      <>
        {avatarUrl ? (
          <View style={{position: 'relative'}}>
            {isLoading && (
              <Skeleton
                borderRadius={size / 2}
                height={size - 2}
                style={StyleSheet.absoluteFillObject}
                width={size - 2}
              />
            )}
            <Image
              onError={() => setIsLoading(false)}
              onLoad={() => setIsLoading(false)}
              source={{uri: avatarUrl}}
              style={{
                borderRadius: size / 2,
                height: size - 2,
                opacity: isLoading ? 0 : 1,
                width: size - 2,
              }}
            />
          </View>
        ) : (
          <WText
            color={Colors.primary}
            style={{
              fontFamily: Fonts.type.semiBold,
              fontSize: size * 0.4,
            }}
            type="regular14">
            {fullName?.charAt(0)?.toUpperCase()}
          </WText>
        )}
      </>
    );
  };
  return (
    <TouchableOpacity
      activeOpacity={!onPress ? 1 : ACTIVE_OPACITY}
      onPress={_onPress}>
      <WView
        alignCenter
        borderColor={avatarUrl ? Colors.neutral200 : Colors.primary}
        borderRadius={size / 2}
        borderWidth={1}
        center
        color={avatarUrl ? Colors.white : Colors.primary100}
        h={size}
        justifyCenter
        style={style}
        w={size}>
        {renderAvatar()}
      </WView>
    </TouchableOpacity>
  );
}

export default AvatarUser;

const styles = StyleSheet.create({});
