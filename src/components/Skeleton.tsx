// Skeleton.tsx
import React, {useEffect, useRef} from 'react';
import {View, StyleSheet, Animated, Easing, ViewStyle} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {Colors} from '../themes';

interface SkeletonProps {
  width: number | string;
  height: number | string;
  borderRadius?: number;
  style?: ViewStyle;
  shape?: 'rectangle' | 'circle';
}

function Skeleton({
  width,
  height,
  borderRadius = 4,
  style,
  shape = 'rectangle',
}: SkeletonProps) {
  const shimmerValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.timing(shimmerValue, {
        duration: 1000,
        easing: Easing.linear,
        toValue: 1,
        useNativeDriver: true,
      }),
    );
    animation.start();

    return () => animation.stop();
  }, [shimmerValue]);

  const translateX = shimmerValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-(width || 100), width || 100],
  });

  return (
    <View
      style={[
        styles.container,
        style,
        {
          borderRadius:
            shape === 'circle'
              ? typeof width === 'number'
                ? width / 2
                : 0
              : borderRadius,
          height,
          width,
        },
      ]}>
      <Animated.View
        style={[StyleSheet.absoluteFill, {transform: [{translateX}]}]}>
        <LinearGradient
          colors={[Colors.grey6, Colors.grey5, Colors.grey6]}
          end={{x: 1, y: 0.5}}
          start={{x: 0, y: 0.5}}
          style={styles.gradient}
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.grey6,
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
  },
});

export default Skeleton;
