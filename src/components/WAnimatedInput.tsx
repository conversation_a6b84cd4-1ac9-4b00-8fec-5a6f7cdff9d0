import React, {useRef} from 'react';
import {
  View,
  TextInput,
  Animated,
  TouchableWithoutFeedback,
  StyleSheet,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {Colors, Fonts} from '@themes/index';

interface WAnimatedInputProps extends TextInputProps {
  label?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  style?: ViewStyle;
  required?: boolean;
}

function WAnimatedInput({
  label = '',
  value = '',
  onChangeText,
  style = {},
  required = false,
  ...props
}: WAnimatedInputProps) {
  const {t} = useTranslation();
  const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;
  const inputRef = useRef<TextInput>(null);

  const handleFocus = (): void => {
    animateLabel(1);
  };

  const handleBlur = (): void => {
    if (!value) {
      animateLabel(0);
    }
  };

  const animateLabel = (toValue: number): void => {
    Animated.timing(animatedValue, {
      duration: 200,
      toValue,
      useNativeDriver: false,
    }).start();
  };

  const labelStyle = {
    color: Colors.neutral600,
    fontFamily: Fonts.type.regular,
    fontSize: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [16, 12],
    }),
    left: 16,
    position: 'absolute' as const,
    top: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [20, 8],
    }),
  };

  const getLabelText = (): React.ReactNode => {
    if (required) {
      return (
        <>
          {label}
          <Animated.Text style={[labelStyle, {color: Colors.error}]}>
            {' '}
            *
          </Animated.Text>
        </>
      );
    }
    return (
      <>
        {label}
        <Animated.Text style={labelStyle}>
          {' '}
          {t('common.optional')}
        </Animated.Text>
      </>
    );
  };

  return (
    <TouchableWithoutFeedback onPress={() => inputRef.current?.focus()}>
      <View style={[style, styles.containerStyle]}>
        <Animated.Text style={labelStyle}>{getLabelText()}</Animated.Text>
        <TextInput
          onBlur={handleBlur}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          ref={inputRef as any}
          style={styles.textInput}
          value={value}
          {...props}
        />
      </View>
    </TouchableWithoutFeedback>
  );
}

export default WAnimatedInput;

const styles = StyleSheet.create({
  containerStyle: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral200,
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    paddingBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  textInput: {
    color: Colors.neutral500,
    fontFamily: Fonts.type.regular,
    fontSize: 16,
    paddingBottom: 0,
    paddingTop: 8,
  },
});
