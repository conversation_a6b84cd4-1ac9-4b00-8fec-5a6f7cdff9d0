import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleProp, TouchableOpacity, ViewStyle} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '../themes';
import {GlobalStyle} from '../themes/GlobalStyle';
import WText from './WText';
import WView from './WView';

interface ModalHeaderProps {
  title?: string;
  onClose?: () => void;
  style?: StyleProp<ViewStyle>;
}

function ModalHeader({title, style, onClose}: ModalHeaderProps) {
  const navigation = useNavigation();
  const _onPress = () => {
    if (onClose) {
      onClose();
    } else {
      navigation.goBack();
    }
  };
  return (
    <WView justifyBetween mBottom={16} row style={style}>
      <WText fill type="semiBold18">
        {title}
      </WText>
      <TouchableOpacity
        activeOpacity={1}
        hitSlop={GlobalStyle.hitSlop}
        onPress={_onPress}>
        <Icon color={Colors.neutral500} name="Fill-XCircle" size={24} />
      </TouchableOpacity>
    </WView>
  );
}

export default ModalHeader;
