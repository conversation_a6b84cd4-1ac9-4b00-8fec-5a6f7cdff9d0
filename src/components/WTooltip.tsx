import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {
  StyleProp,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/icomoon';
import {Colors} from '../themes';
import {GlobalStyle} from '../themes/GlobalStyle';
import WView from './WView';
import WText from './WText';

interface TooltipProps {
  label: string;
  direction: 'left' | 'right';
  width?: number;
  style?: StyleProp<ViewStyle>;
  showIcon?: boolean;
  icon?: string;
  iconColor?: string;
}

const WTooltip = forwardRef(
  (
    {
      label,
      direction,
      width,
      style,
      showIcon = true,
      icon,
      iconColor,
    }: TooltipProps,
    ref,
  ) => {
    const [showTooltip, setShowTooltip] = useState(false);
    const onClose = () => {
      setShowTooltip(false);
    };
    const onOpen = () => {
      setShowTooltip(true);
    };
    const onToggle = () => {
      setShowTooltip(!showTooltip);
    };
    useImperativeHandle(
      ref,
      () => ({
        onClose,
        onOpen,
        onToggle,
      }),
      [showTooltip],
    );

    return (
      <View style={style}>
        <TouchableWithoutFeedback
          hitSlop={GlobalStyle.hitSlop}
          onPress={() => {
            setShowTooltip(!showTooltip);
          }}>
          <WView h={20} w={20}>
            {showTooltip && (
              <WView color={Colors.warning100} style={styles.tooltipContainer}>
                <WView
                  color={Colors.neutral800}
                  style={[
                    styles.tooltip,
                    GlobalStyle.shadow,
                    direction === 'left'
                      ? styles.leftDirection
                      : styles.rightDirection,
                  ]}
                  w={width || 150}>
                  <WText color={Colors.white} fill type="regular12">
                    {label}
                  </WText>
                  <WView
                    style={[
                      styles.square,
                      GlobalStyle.shadow,
                      direction === 'left'
                        ? styles.leftDirection1
                        : styles.rightDirection1,
                    ]}
                  />
                </WView>
              </WView>
            )}
            {showIcon && (
              <Icon
                color={iconColor ? iconColor : Colors.neutral500}
                name={icon ? icon : 'Outline-Question'}
                size={20}
              />
            )}
          </WView>
        </TouchableWithoutFeedback>
      </View>
    );
  },
);

export default WTooltip;

const styles = StyleSheet.create({
  leftDirection: {
    right: 0,
  },
  leftDirection1: {
    right: 14,
  },
  rightDirection: {
    left: 0,
  },
  rightDirection1: {
    left: 14,
  },
  square: {
    backgroundColor: Colors.neutral800,
    bottom: -5,
    height: 10,
    position: 'absolute',
    transform: [{rotate: '45deg'}],
    width: 10,
    zIndex: 999,
  },
  tooltip: {
    borderRadius: 8,
    padding: 12,
    position: 'absolute',
    zIndex: 999,
  },
  tooltipContainer: {
    height: 40,
    justifyContent: 'flex-end',
    position: 'absolute',
    right: -10,
    top: -50,
    width: 40,
    zIndex: 999,
  },
});
