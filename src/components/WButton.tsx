import React from 'react';
import {
  ActivityIndicator,
  ColorValue,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import {Colors} from '../themes';
import WText from './WText';
import {GlobalStyle} from '@themes/GlobalStyle';

interface WButtonProps {
  label?: string;
  disabled?: boolean;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  labelColor?: string;
  outline?: boolean;
  labelType?: string;
  loading?: boolean;
  fill?: boolean;
  bgColor?: ColorValue;
}

function WButton({
  label,
  labelColor,
  disabled,
  outline,
  onPress,
  style,
  labelType,
  loading,
  fill,
  bgColor,
}: WButtonProps) {
  const _onPress = () => {
    onPress?.();
  };
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      disabled={disabled || loading}
      onPress={_onPress}
      style={
        [
          styles.container,
          (disabled || loading) && styles.disable,
          outline && styles.outline,
          fill && GlobalStyle.flex1,
          bgColor && {backgroundColor: bgColor},
          style,
        ] as StyleProp<ViewStyle>
      }>
      {loading ? (
        <ActivityIndicator color={Colors.neutral850} />
      ) : (
        <WText
          color={labelColor || Colors.white}
          style={[
            disabled && {color: Colors.neutral900},
            outline && {color: Colors.neutral900},
          ]}
          type={(labelType as any) || 'semiBold14'}>
          {label || 'OK'}
        </WText>
      )}
    </TouchableOpacity>
  );
}

export default WButton;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: Colors.neutral875,
    borderRadius: 8,
    height: 46,
    justifyContent: 'center',
    minWidth: 80,
  },
  disable: {
    backgroundColor: Colors.neutral100,
    borderColor: Colors.neutral200,
    borderWidth: 1,
  },
  outline: {
    backgroundColor: Colors.white,
    borderColor: Colors.neutral500,
    borderWidth: 1,
  },
});
