import {useQuery, useQueryClient} from '@tanstack/react-query';
import {GLOBAL_STATE_KEY_PREFIX} from '../themes/Constants';
import {
  AppConfigEntity,
  BookingEntity,
  CardEntity,
  CouponEntity,
  CreateBookingWithCodeDto,
  ILoginResponse,
  LocationDto,
  LoyalPointConfigEntity,
  NotificationTokenEntity,
  UserEntity,
} from '@requests';
import {PickUpConfirmProps} from '@global';

interface SetGlobalState<T> {
  (newState: T): void;
}

type StateKeys =
  | 'count'
  | 'isShareRoute'
  | 'userData'
  | 'savedLocationType'
  | 'bookingWithCodeParams'
  | 'bookingStatus'
  | 'currentBooking'
  | 'configData'
  | 'loyalPointConfig'
  | 'currentWaitingTime'
  | 'maxWaitingTime'
  | 'anyCarPrice'
  | 'isInBookingFlow'
  | 'isSkipIntro'
  | 'currentPayment'
  | 'currentScreen'
  | 'currentListScreen'
  | 'manualCoupons'
  | 'language'
  | 'referralCode'
  | 'fcmData'
  | 'pickUpConfirm'
  | 'searchType'
  | 'navigateTab';

export interface UserDataProps extends Omit<ILoginResponse, 'user'> {
  location?: LocationDto;
  user: UserEntity;
}

export interface AnyCarPriceProps {
  maxAmount?: number;
  minAmount?: number;
}

interface GlobalStateTypes {
  isShareRoute: boolean;
  userData: UserDataProps;
  count: number;
  savedLocationType: string;
  bookingWithCodeParams: CreateBookingWithCodeDto | undefined;
  bookingStatus: BookingEntity.status;
  currentBooking: BookingEntity | undefined;
  fcmData: NotificationTokenEntity;
  configData: AppConfigEntity;
  currentWaitingTime: number;
  maxWaitingTime: number;
  anyCarPrice: AnyCarPriceProps;
  isInBookingFlow: boolean;
  isSkipIntro: boolean;
  currentPayment: CardEntity;
  language: string;
  manualCoupons: CouponEntity[];
  loyalPointConfig: LoyalPointConfigEntity;
  currentScreen: string;
  currentListScreen: string[];
  referralCode: string;
  pickUpConfirm?: PickUpConfirmProps;
  searchType: string;
  navigateTab: string;
}

type UseGlobalStateResult<T extends StateKeys> = [
  GlobalStateTypes[T] | undefined,
  SetGlobalState<GlobalStateTypes[T]>,
];

export function useGlobalState<T extends StateKeys>(
  key: T,
): UseGlobalStateResult<T>;

export function useGlobalState<T extends StateKeys>(
  key: T,
  initialState: GlobalStateTypes[T],
): UseGlobalStateResult<T>;

export function useGlobalState<T extends StateKeys>(
  key: T,
  initialData?: GlobalStateTypes[T],
): UseGlobalStateResult<T> {
  const queryClient = useQueryClient();

  const stateKey = [GLOBAL_STATE_KEY_PREFIX, key];
  const {data} = useQuery({
    initialData,
    queryFn: () => (initialData ? initialData : null),
    queryKey: stateKey,
    staleTime: Infinity,
  });

  const setData = (newState: GlobalStateTypes[T]) => {
    queryClient.setQueryData(stateKey, newState);
  };

  return [data as GlobalStateTypes[T], setData];
}
