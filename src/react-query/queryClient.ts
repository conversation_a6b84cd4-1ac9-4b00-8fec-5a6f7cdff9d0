import AsyncStorage from '@react-native-async-storage/async-storage';
import {createAsyncStoragePersister} from '@tanstack/query-async-storage-persister';
import {QueryClient} from '@tanstack/react-query';
import axios from 'axios';
import Config from 'react-native-config';
import i18next from 'i18next';
import {OpenAPI} from '../api/requests';
import {reset} from '../navigation/utils';
import {showErrorAlert} from '../utils/Tools';
import {UserDataProps} from './clientStateManage';

const GLOBAL_STATE_KEY_PREFIX = 'globalState';
let showLogoutAlert = true;
let isRefreshingToken = false;

const getNewToken = () => {
  if (isRefreshingToken) {
    return;
  }

  isRefreshingToken = true;

  AsyncStorage.getItem('REACT_QUERY_OFFLINE_CACHE')
    .then(data => {
      if (!data) {
        isRefreshingToken = false;
        return;
      }

      const clientState = JSON.parse(data)?.clientState;
      if (!clientState?.queries) {
        isRefreshingToken = false;
        return;
      }

      const userDataQuery = clientState.queries.find((ele: any) =>
        ele?.queryHash?.includes('userData'),
      );

      const userData: UserDataProps = userDataQuery?.state?.data;
      const refreshToken = userData?.refreshToken;
      const stateKey = [GLOBAL_STATE_KEY_PREFIX, 'userData'];

      if (!refreshToken) {
        isRefreshingToken = false;
        return;
      }

      axios
        .post(`${Config.BASE_URL}/v1/auth/refresh-token`, {
          refreshToken,
        })
        .then(res => {
          // Using type assertion for response
          const responseData = res.data as {
            data: {accessToken: string; refreshToken?: string};
          };
          const token = responseData.data?.accessToken;
          const newRefreshToken = responseData.data?.refreshToken;

          if (token) {
            OpenAPI.TOKEN = token;
            axios
              .get(`${Config.BASE_URL}/v1/users/me`, {
                headers: {
                  Authorization: token,
                },
              })
              .then(userRes => {
                queryClient.setQueryData(stateKey, {
                  ...userData,
                  accessToken: token,
                  ...(newRefreshToken && {refreshToken: newRefreshToken}),
                  user: userRes?.data,
                });
                isRefreshingToken = false;
              })
              .catch(error => {
                console.log(error);
                isRefreshingToken = false;
              });
          } else {
            isRefreshingToken = false;
          }
        })
        .catch(() => {
          isRefreshingToken = false;
          if (showLogoutAlert) {
            showLogoutAlert = false;
            showErrorAlert({
              buttons: [
                {
                  onPress: () => {
                    showLogoutAlert = true;
                    AsyncStorage.clear();
                    queryClient.clear();
                    reset({routes: [{name: 'Login'}]});
                  },
                  text: 'Ok',
                },
              ],
              message: i18next.t('login.loginSession'),
            });

            /*
             * Alert.alert('Taxi Loyal', i18next.t('login.loginSession'), [
             *   {
             *     onPress: () => {
             *       showLogoutAlert = true;
             *       AsyncStorage.clear();
             *       queryClient.clear();
             *       reset({routes: [{name: 'Login'}]});
             *     },
             *     text: 'Ok',
             *   },
             * ]);
             */
          }
        });
    })
    .catch(error => {
      console.log('Error reading from AsyncStorage:', error);
      isRefreshingToken = false;
    });
};

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: Infinity,
      retry: 3,
      retryDelay: (_, error) => {
        if (error?.message === 'Unauthorized') {
          getNewToken();
          return 3000; // Directly return number instead of wrapping in Promise
        }
        // Retry others just once
        return 0;
      },
      staleTime: 0,
    },
  },
});

export const persister = createAsyncStoragePersister({
  storage: AsyncStorage,
});
