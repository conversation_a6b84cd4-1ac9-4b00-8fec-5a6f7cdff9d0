/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/consistent-type-definitions */
import {NavigationProp} from '@react-navigation/native';
import {ImageSourcePropType, KeyboardTypeOptions} from 'react-native';
import {ReactNode} from 'react';
import {AnimationObject} from 'lottie-react-native';
import {
  AppSubscriptionEntity,
  BookingEntity,
  CardEntity,
  CouponEntity,
  LocationDto,
  LocationEntity,
  SavedLocationEntity,
  SubscriptionPlanEntity,
  SubscriptionTransactionEntity,
  UserEntity,
  UserSubscriptionEntity,
} from './src/api/requests';
import {ERide} from '@themes/Constants';
import {ISubscription} from '@interface/subscription';

interface ScreenOptions {
  isTopBarEnable?: boolean;
  noBorder?: boolean;
  headerTitle?: string;
}
export interface SearchCouponParamsProps {
  bookingType: 'PERSONAL' | 'BUSINESS';
  vehicleType:
    | 'ALL'
    | 'TYPICAL_CAR'
    | 'ELECTRIC_CAR'
    | 'VAN'
    | 'ACCESSIBLE_VAN';
  amount: number;
  date: string;
  originLatitude: number;
  originLongitude: number;
  destinationLatitude: number;
  destinationLongitude: number;
  code: string;
  paymentMethod?: 'ALL' | 'CARD' | 'CASH' | null;
}
interface VerifyCodeProps {
  email?: string;
  referralCode?: string;
}

interface ListBusinessProps {
  isFromBooking?: boolean;
  onApplyCode?: (business?: EmployeeCodeEntity) => void;
  selectedBusiness?: EmployeeCodeEntity;
}

interface CancelReasonListProps {
  bookingId?: string;
  booking?: BookingEntity;
}

interface InputCancelReasonModalProps {
  bookingId?: string;
}

interface BookingCancelProps {
  bookingId?: string;
  date?: string;
}
interface ScheduleTimeModalProps {
  defaultDate?: string;
  onApply?: (val?: string | null) => void;
  isMinDate?: boolean;
  isRide?: boolean;
  onPickupNow?: () => void;
}

interface RatingDriverProps {
  bookingDetail: BookingEntity;
  onClose?: () => void;
  isActivitiesFlow?: boolean;
}

interface ListPaymentProps {
  defaultPayment?: CardEntity;
  isFromBooking?: boolean;
  hideHeader?: boolean;
  hideCash?: boolean;
  onApply?: (item: CardEntity) => void;
  hidePayment?: boolean;
}

interface ActivitiesDetailProps {
  item?: BookingEntity;
  isBooked: boolean;
}

interface ActionModalProps {
  onApply?: (data?: string) => void;
  onCancel?: () => void;
  title: string;
  content: string;
  cancelLabel?: string;
  confirmLabel?: string;
  enableCloseOnMask?: boolean;
  cancelBtnStyle?: StyleProp<ViewStyle>;
  confirmBtnStyle?: StyleProp<ViewStyle>;
  type?: 'error' | 'success' | 'default' | 'warning';
  icon?: string;
  image?: ImageSourcePropType;
  hasInput?: boolean;
  placeholder?: string;
  defaultInputValue?: string;
  keyboardType?: KeyboardTypeOptions;
  hasCancelBtn?: boolean;
  autoConfirm?: boolean;
  autoConfirmDuration?: number;
  buttonAlign?: 'vertical' | 'horizontal';
  iconSize?: number;
  validateType?: 'phone' | 'referralCode';
  fallbackContent?: ReactNode;
  heightIcon?: number;
  widthIcon?: number;
  lottieJson?: string | AnimationObject | {uri: string};
}

export interface CouponProps {
  isDisabled?: boolean;
  tabIndex?: number;
  coupon?: CouponEntity;
  isFromBooking?: boolean;
  onApply?: (item?: CouponEntity) => void;
  bookingParams?: {
    bookingType: 'PERSONAL' | 'BUSINESS';
    vehicleType:
      | 'ALL'
      | 'TYPICAL_CAR'
      | 'ELECTRIC_CAR'
      | 'VAN'
      | 'ACCESSIBLE_VAN';
    paymentMethod: 'ALL' | 'CARD' | 'CASH';
    amount: number;
    date: string;
    originLatitude: number;
    originLongitude: number;
    destinationLatitude: number;
    destinationLongitude: number;
  };
}
interface CouponDetailProps {
  isApply?: boolean;
  isUnavailable?: boolean;
  onApply?: () => void;
  coupon?: CouponEntity;
  isFromBooking?: boolean;
  isFromCouponStore?: boolean;
}

export interface GenderProps {
  id: UpdateUserDto.gender;
  title: string;
}
interface GenderModalProps {
  onApply?: (gender: GenderProps) => void;
  defaultGender?: GenderProps;
}

export interface GeoLocationProps {
  geolocation?: {
    latitude?: number;
    longitude?: number;
    name?: string;
  };
}
interface LocationMarkerSelectorProps extends GeoLocationProps {
  currentSearchType?: string;
  onConfirm?: (item?: LocationDto) => void;
  location?: LocationDto;
  isConfirmPickupLocation?: boolean;
}

interface BookingMapsProps {
  bookingStatus?: string;
  defaultBookingDetail?: BookingEntity;
}

interface AddSavedLocationProps {
  location?: SavedLocationEntity;
  isEdit?: boolean;
  isFromBooking?: boolean;
}

interface DateTimePickerModalProps {
  onApply?: (date: Date) => void;
  defaultDate?: Date;
}

interface EditProfileProps {
  user: UserEntity;
  isVerifyPhone?: boolean;
}

interface ListSavedLocationProps {
  isFromBooking?: boolean;
  onApply?: (item: SavedLocationEntity) => void;
}

interface SearchSavedLocationProps {
  isEdit?: boolean;
  location?: SavedLocationEntity;
  onApply?: (item: SavedLocationEntity) => void;
}

interface AddCouponModalProps {
  isFromBooking?: boolean;
  onApply?: (coupon: CouponEntity) => void;
  onPress?: (coupon: CouponEntity) => void;
  searchParams?: SearchCouponParamsProps;
  selectedCoupon?: CouponEntity;
}

interface RedeemCouponModalProps {
  coupon?: CouponEntity;
  applyBtnLabel?: string;
  onApply?: (coupon: CouponEntity) => void;
  titleModal?: string;
}

interface CancelPlanProps {
  subscription?: ISubscription;
}

interface CardDetailModalProps {
  card?: CardEntity;
}

interface SubscriptionDetailsProps {
  subscription?: AppSubscriptionEntity;
  isFirstTimeBuySubscription?: boolean;
  isSubscribe?: boolean;
  defaultPaymentMethod?: CardEntity;
  hidePayment?: boolean;
  subscriptionId?: string;
}

interface ChangePlanProps {
  subscription?: ISubscription;
}

interface SubscribePlanProps {
  subscription?: AppSubscriptionEntity;
  selectedPlan?: SubscriptionPlanEntity | null;
  endDate?: string;
}

interface SubscriptionHistoryDetailProps {
  subscriptionHistoryItem?: SubscriptionTransactionEntity;
}

interface CancelSubscriptionModalProps {
  subscription?: AppSubscriptionEntity;
}

interface BusinessConfigProps {
  business?: EmployeeCodeEntity;
}

interface BusinessDetailProps {
  business?: EmployeeCodeEntity;
}

interface SubscriptionProps {
  hidePayment?: boolean;
}

export interface ReasonListProps {
  icon: string;
  id: string;
  label: string;
}

export interface ILocationSelect extends LocationDto {
  placeId?: string;
}
export interface ILocationItem {
  address: string;
  coordinates?: CoordinatesDto;
  name?: string;
  place_id?: string;
  structured_formatting?: {
    main_text: string;
  };
  formatted_address?: string;
  description?: string;
  placeId?: string;
}

export interface ISearchAddressResultProps {
  data?: ILocationItem[];
  onPressItem?: (item?: LocationDto | ILocationItem) => void;
  onTouch?: () => void;
  style?: StyleProp<ViewStyle>;
  hasSearchText?: boolean;
  loading?: boolean;
  currentSearchType: string;
  dataSubLocation: LocationEntity[];
}

export interface ItemLocationProps {
  data: LocationEntity;
  item: ILocationItem;
  onPressItem?: (item?: LocationDto | ILocationItem) => void;
  hasSearchText?: boolean;
  currentSearchType: string;
}

export interface PickUpConfirmProps {
  placeId?: string;
}

export interface IRide {
  description: string;
  id: ERide;
  title: string;
}

export interface IShortcutItem {
  icon: string;
  titleEn: string;
  titleFr: string;
  title?: string;
  type: string;
  userInfo: {
    url: string;
  };
}

/// route params type

export type SubscriptionRouteProps = {
  Subscription?: SubscriptionProps;
};

export type LocationMarkerSelectorRouteProps = {
  LocationMarkerSelector: LocationMarkerSelectorProps;
};

export type VerifyCodeRouteProps = {
  VerifyCode: VerifyCodeProps;
};

export type BookingMapsRouteProps = {
  BookingMaps: BookingMapsProps;
};

export type ListBusinessRouteProps = {
  ListBusiness: ScreenOptions & ListBusinessProps;
};

export type CancelReasonListRouteProps = {
  CancelReasonList: ScreenOptions & CancelReasonListProps;
};

export type InputCancelReasonModalRouteProps = {
  InputCancelReasonModal: ScreenOptions & InputCancelReasonModalProps;
};

export type BookingCancelRouteProps = {
  BookingCancel: ScreenOptions & BookingCancelProps;
};

export type RatingDriverRouteProps = {
  RatingDriver: ScreenOptions & RatingDriverProps;
};

export type ListPaymentRouteProps = {
  ListPaymentProps: ScreenOptions & ListPaymentProps;
};

export type ScheduleTimeModalRouteProps = {
  ScheduleTimeModal: ScreenOptions & ScheduleTimeModalProps;
};

export type ActivitiesDetailRouteProps = {
  ActivitiesDetail: ScreenOptions & ActivitiesDetailProps;
};

export type DateTimePickerModalRouteProps = {
  DateTimePickerModal: ScreenOptions & DateTimePickerModalProps;
};

export type GenderModalRouteProps = {
  GenderModal: ScreenOptions & GenderModalProps;
};

export type EditProfileRouteProps = {
  EditProfile: ScreenOptions & EditProfileProps;
};

export type AddSavedLocationRouteProps = {
  AddSavedLocation: ScreenOptions & AddSavedLocationProps;
};

export type SearchSavedLocationRouteProps = {
  SearchSavedLocation: ScreenOptions & SearchSavedLocationProps;
};

export type ListSavedLocationRouteProps = {
  ListSavedLocation: ScreenOptions & ListSavedLocationProps;
};

export type CardDetailModalRouteProps = {
  CardDetailModal: ScreenOptions & CardDetailModalProps;
};

export type SubscriptionDetailsRouteProps = {
  SubscriptionDetails: ScreenOptions & SubscriptionDetailsProps;
};

export type ChangePlanRouteProps = {
  ChangePlan: ScreenOptions & ChangePlanProps;
};

export type SubscribePlanRouteProps = {
  SubscribePlan: ScreenOptions & SubscribePlanProps;
};

export type SubscriptionHistoryDetailRouteProps = {
  SubscriptionHistoryDetail: ScreenOptions & SubscriptionHistoryDetailProps;
};

export type CancelSubscriptionModalRouteProps = {
  CancelSubscriptionModal: ScreenOptions & CancelSubscriptionModalProps;
};

export type SuccessSubscribeModalRouteProps = {
  SuccessSubscribeModal: ScreenOptions;
};

export type AddCouponModalRouteProps = {
  AddCouponModal: ScreenOptions & AddCouponModalProps;
};

export type RedeemCouponModalRouteProps = {
  RedeemCouponModal: ScreenOptions & RedeemCouponModalProps;
};

export type CancelPlanRouteProps = {
  CancelPlan: ScreenOptions & CancelPlanProps;
};

export type ActionModalRouteProps = {
  ActionModal: ScreenOptions & ActionModalProps;
};

export type CouponRouteProps = {
  Coupon: ScreenOptions & CouponProps;
};

export type CouponDetailRouteProps = {
  CouponDetail: ScreenOptions & CouponDetailProps;
};

export type BusinessConfigRouteProps = {
  BusinessConfig: ScreenOptions & BusinessConfigProps;
};

export type BusinessDetailRouteProps = {
  BusinessDetail: BusinessDetailProps;
};

export type BeforeTipModalRouteProps = {
  BeforeTipModal: BeforeTipModalProps;
};

export type NotificationDetailRouteProps = {
  NotificationDetail: ScreenOptions & NotificationDetailProps;
};

export type NotificationDetailProps = {
  id: string;
};

export type SelectDestinationProps = {
  isSchedule?: boolean;
};

export type ShareRouteProps = {
  bookingId?: string;
};

export interface IData {
  label: string;
  value: string;
}

export interface ModalNeedHelpProps {
  bookingId: string;
  isRemovePayment: boolean;
}

export type VerifyPhoneModalProps = {
  onApply?: () => void;
  defaultPhoneNumber?: string;
};

export interface WebViewScreenProps {
  url: string;
  title?: string;
}

export type BeforeTipModalProps = {
  amount: number;
  onApply: (amount: number) => void;
  valueDefault?: number;
};

export interface IRealTime {
  status: BookingEntity.status;
  isWarningCancel: boolean;
  waitingTime: number;
}

declare global {
  namespace ReactNavigation {
    interface RootParamList {
      Main?: ScreenOptions;
      Login?: ScreenOptions;
      AddSavedLocation?: ScreenOptions & AddSavedLocationProps;
      BookingMaps?: ScreenOptions & BookingMapsProps;
      BookingProgress?: ScreenOptions;
      ForgotPassword?: ScreenOptions;
      ListSavedLocation?: ScreenOptions & ListSavedLocationProps;
      LocationMarkerSelector?: ScreenOptions & LocationMarkerSelectorProps;
      SearchSavedLocation?: ScreenOptions & SearchSavedLocationProps;
      SelectDestination?: ScreenOptions & SelectDestinationProps;
      VerifyCode?: ScreenOptions & VerifyCodeProps;
      ListBusiness?: ScreenOptions & ListBusinessProps;
      AddCreditCard?: ScreenOptions;
      ChangePassword?: ScreenOptions;
      DeleteAccount?: ScreenOptions;
      About?: ScreenOptions;
      NetworkMonitor?: ScreenOptions;
      Intro?: ScreenOptions;
      ChangeLanguage?: ScreenOptions;
      ReferralCode?: ScreenOptions;
      PickupSubLocation?: ScreenOptions;
      ShareRoute?: ScreenOptions & ShareRouteProps;
      BirthDay40Year?: ScreenOptions;

      Notification?: ScreenOptions;
      ActivitiesTab?: ScreenOptions;
      ActivitiesHistory?: ScreenOptions;
      ChangePlan?: ScreenOptions & ChangePlanProps;
      LoginV2?: ScreenOptions;
      EnterPhoneNumber?: ScreenOptions;
      OtpVerificationV2?: ScreenOptions;
      UpdateInformation?: ScreenOptions;
      //transparent modal
      ActionModal?: ScreenOptions & ActionModalProps;
      ActivitiesDetail?: ScreenOptions & ActivitiesDetailProps;
      RatingDriver?: ScreenOptions & RatingDriverProps;
      Profile?: ScreenOptions;
      EditProfile?: ScreenOptions & EditProfileProps;
      GenderModal?: ScreenOptions & GenderModalProps;
      CancelReasonList?: ScreenOptions & CancelReasonListProps;
      Coupon?: ScreenOptions & CouponProps;
      CouponDetail?: ScreenOptions & CouponDetailProps;
      ListPayment?: ScreenOptions & ListPaymentProps;
      BusinessInfoModal?: ScreenOptions;
      AddBusinessCodeModal?: ScreenOptions;
      BusinessConfigModal?: ScreenOptions & BusinessConfigProps;
      BusinessDetail?: ScreenOptions & BusinessDetailProps;
      InputCancelReasonModal?: ScreenOptions & InputCancelReasonModalProps;
      BookingCancel?: ScreenOptions & BookingCancelProps;
      ScheduleTimeModal?: ScreenOptions & ScheduleTimeModalProps;
      DateTimePickerModal?: ScreenOptions & DateTimePickerModalProps;
      CardDetailModal?: ScreenOptions & CardDetailModalProps;
      HelpsModal?: ScreenOptions;
      Subscription?: ScreenOptions & SubscriptionProps;
      SubscriptionDetails?: ScreenOptions & SubscriptionDetailsProps;
      SubscribePlan?: ScreenOptions & SubscribePlanProps;
      SuccessSubscribeModal?: ScreenOptions;
      SubscriptionHistory?: ScreenOptions;
      SubscriptionHistoryDetail?: ScreenOptions &
        SubscriptionHistoryDetailProps;
      CancelSubscriptionModal?: ScreenOptions & CancelSubscriptionModalProps;
      RedeemCouponModal?: ScreenOptions & RedeemCouponModalProps;
      CancelPlan?: ScreenOptions & CancelPlanProps;
      LoyalPoint?: ScreenOptions;
      HistoryLoyalPoint?: ScreenOptions;
      AddCouponModal?: ScreenOptions & AddCouponModalProps;
      ReferFriends?: ScreenOptions;
      NotificationDetail?: ScreenOptions & NotificationDetailProps;
      ModalNeedRide?: ScreenOptions;
      ModalNeedHelp?: ScreenOptions & ModalNeedHelpProps;
      VerifyPhoneModal?: ScreenOptions & VerifyPhoneModalProps;
      WebViewScreen?: ScreenOptions & WebViewScreenProps;
      BeforeTipModal?: ScreenOptions & BeforeTipModalProps;
    }
  }

  const navigation: NavigationProp<ReactNavigation.RootParamList>;
}
