/* eslint-disable max-lines */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-empty-function */
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  NavigationContainer,
  useNavigationState,
} from '@react-navigation/native';
import {
  NativeStackNavigationOptions,
  createNativeStackNavigator,
} from '@react-navigation/native-stack';
import {StripeProvider} from '@stripe/stripe-react-native';
import {QueryClient} from '@tanstack/react-query';
import {PersistQueryClientProvider} from '@tanstack/react-query-persist-client';
import * as React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Config from 'react-native-config';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {enableLatestRenderer} from 'react-native-maps';
import SplashScreen from 'react-native-splash-screen';
import Toast from 'react-native-toast-message';
import {
  getTrackingStatus,
  requestTrackingPermission,
} from 'react-native-tracking-transparency';
import Icon from 'react-native-vector-icons/icomoon';
import {Settings} from 'react-native-fbsdk-next';
import appsFlyer, {
  OnAppOpenAttributionData,
  UnifiedDeepLinkData,
} from 'react-native-appsflyer';
import {ConfigsService, LoyalPointService, OpenAPI} from './src/api/requests';
import ActionModal from './src/components/Modal/ActionModal';
import AddBusinessCodeModal from './src/components/Modal/AddBusinessCodeModal';
import AddCouponModal from './src/components/Modal/AddCouponModal';
import BusinessConfigModal from './src/components/Modal/BusinessConfigModal';
import BusinessInfoModal from './src/components/Modal/BusinessInfoModal';
import CardDetailModal from './src/components/Modal/CardDetailModal';
import CustomToast from './src/components/Modal/CustomToast';
import DateTimePickerModal from './src/components/Modal/DateTimePickerModal';
import GenderModal from './src/components/Modal/GenderModal';
import HelpsModal from './src/components/Modal/HelpsModal';
import InputCancelReasonModal from './src/components/Modal/InputCancelReasonModal';
import LoadingToast from './src/components/Modal/LoadingToast';
import RedeemCouponModal from './src/components/Modal/RedeemCouponModal';
import ScheduleTimeModal from './src/components/Modal/ScheduleTimeModal';
import BottomTabs from './src/navigation/BottomTabs';
import {navigationRef} from './src/navigation/utils';
import {persister, queryClient} from './src/react-query/queryClient';
import ActivitiesDetail from './src/screens/Activities/ActivitiesDetail';
import ChangePassword from './src/screens/Auth/ChangePassword';
import DeleteAccount from './src/screens/Auth/DeleteAccount';
import ForgotPassword from './src/screens/Auth/ForgotPassword';
import Login from './src/screens/Auth/Login';
import VerifyCode from './src/screens/Auth/VerifyCode';
import BookingCancel from './src/screens/Booking/BookingCancel';
import BookingProgress from './src/screens/Booking/BookingProgress';
import CancelReasonList from './src/screens/Booking/CancelReasonList';
import RatingDriver from './src/screens/Booking/RatingDriver';
import SelectDestination from './src/screens/Booking/SelectDestination';
import BusinessDetail from './src/screens/Business/BusinessDetail';
import ListBusiness from './src/screens/Business/ListBusiness';
import Coupon from './src/screens/Coupon';
import CouponDetail from './src/screens/Coupon/CouponDetail';
import Intro from './src/screens/Intro';
import LoyalPoint from './src/screens/LoyalPoint';
import HistoryLoyalPoint from './src/screens/LoyalPoint/HistoryLoyalPoint';
import BookingMaps from './src/screens/Maps/BookingMaps';
import LocationMarkerSelector from './src/screens/Maps/LocationMarkerSelector';
import AddCreditCard from './src/screens/Payment/AddCreditCard';
import ListPayment from './src/screens/Payment/ListPayment';
import Profile from './src/screens/Profile';
import EditProfile from './src/screens/Profile/EditProfile';
import NetworkMonitor from './src/screens/Profile/NetworkMonitor';
import AddSavedLocation from './src/screens/SavedLocation/AddSavedLocation';
import ListSavedLocation from './src/screens/SavedLocation/ListSavedLocation';
import SearchSavedLocation from './src/screens/SavedLocation/SearchSavedLocation';
import About from './src/screens/Settings/About';
import ChangeLanguage from './src/screens/Settings/ChangeLanguage';
import Subscription from './src/screens/Subscription';
import CancelPlan from './src/screens/Subscription/CancelPlan';
import SubscribePlan from './src/screens/Subscription/SubscribePlan';
import SubscriptionDetails from './src/screens/Subscription/SubscriptionDetails';
import SubscriptionHistory from './src/screens/Subscription/SubscriptionHistory';
import SubscriptionHistoryDetail from './src/screens/Subscription/SubscriptionHistoryDetail';
import CancelSubscriptionModal from './src/screens/Subscription/components/CancelSubscriptionModal';
import SuccessSubscribeModal from './src/screens/Subscription/components/SuccessSubscribeModal';
import {Colors} from './src/themes';
import {GlobalStyle} from './src/themes/GlobalStyle';
import {getRoutesName, setCalendarLanguage} from './src/utils/Tools';
import WebViewScreen from './src/screens/WebView';
import ReferFriends from '@screens/ReferFriends';
import NotificationDetail from '@screens/Notifications/NotificationDetail';
import {RootStackParamList} from '@interface/root';
import ModalNeedRide from '@screens/Home/components/ModalNeedRide';
import ReferralCode from '@screens/Auth/ReferralCode';
import PickupSubLocation from '@screens/Maps/PickupSubLocation';
import ShareRoute from '@screens/Maps/ShareRoute';
import ModalNeedHelp from '@components/Modal/ModalNeedHelp';
import {useQuickActions} from '@utils/hooks/useQuickActions';
import DeepLinkHandler from '@utils/deeplinkHandler';
import VerifyPhoneModal from '@components/Modal/VerifyPhoneModal';
import ActivitiesHistory from '@screens/Activities/ActivitiesHistory';
import {useNotificationBadge} from '@utils/hooks/useNotificationBadge';
import {BookingProvider} from 'src/contexts/BookingProvider';
import {GlobalModal, globalModalRef} from '@components/Modal/GlobalModal';
import ChangePlan from '@screens/Subscription/ChangePlan';
import BeforeTipModal from '@components/Modal/BeforeTipModal';
import BirthDay40Year from '@screens/BirthDay40Year';
import LoginV2 from '@screens/Auth/LoginV2';
import EnterPhoneNumber from '@screens/Auth/EnterPhoneNumber';
import OtpVerificationV2 from '@screens/Auth/OtpVerificationV2';
import UpdateInformation from '@screens/Auth/UpdateInformation';

function App() {
  const [loading, setLoading] = React.useState(true);

  const [initialRouteName, setInitialRouteName] = React.useState('Intro');

  const getConfig = async (queryClientData: QueryClient) => {
    try {
      const res = await ConfigsService.appConfigControllerGetAppConfigs();
      const stateKey = ['globalState', 'configData'];
      queryClientData.setQueryData(stateKey, res?.data);
      const pointConfigRes =
        await LoyalPointService.loyalPointPublicControllerGetAppLoyalPointConfig();
      const pointKey = ['globalState', 'loyalPointConfig'];
      queryClientData.setQueryData(pointKey, pointConfigRes?.data);
    } catch (error) {
      console.log('💩: getConfig -> error', error);
    }
  };

  const getInitialRouteName = async () => {
    try {
      const data = await AsyncStorage.getItem('REACT_QUERY_OFFLINE_CACHE');
      const token = JSON.parse(data)?.clientState?.queries.find(ele =>
        ele?.queryHash?.includes('userData'),
      )?.state?.data?.accessToken;
      const isSkipIntro = JSON.parse(data)?.clientState?.queries.find(ele =>
        ele?.queryHash?.includes('isSkipIntro'),
      )?.state?.data;
      if (token) {
        setInitialRouteName('Main');
        return;
      }
      if (isSkipIntro) {
        setInitialRouteName('Login');
      }
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const handleTracking = async () => {
    const trackingStatus = await getTrackingStatus();
    if (trackingStatus === 'authorized' || trackingStatus === 'unavailable') {
      // enable tracking features
    }
    if (trackingStatus === 'not-determined') {
      requestTrackingPermission();
    }
  };

  const handleDeepLink = (
    event: UnifiedDeepLinkData | OnAppOpenAttributionData,
  ) => {
    DeepLinkHandler.processDeepLink({
      code: event?.data?.code,
      deep_link_value: event?.data?.deep_link_value,
      scr: event?.data?.scr,
    });
  };

  React.useEffect(() => {
    getInitialRouteName();
    handleTracking();
    Settings.initializeSDK();
  }, []);

  const Stack = createNativeStackNavigator();

  const SCREENS: any = {
    About,
    ActivitiesDetail,
    ActivitiesHistory,
    AddCreditCard,
    AddSavedLocation,
    BirthDay40Year,
    BookingMaps,
    BookingProgress,
    CancelPlan,
    CancelReasonList,
    ChangeLanguage,
    ChangePassword,
    ChangePlan,
    Coupon,
    CouponDetail,
    DeleteAccount,
    EditProfile,
    EnterPhoneNumber,
    ForgotPassword,
    HistoryLoyalPoint,
    ListBusiness,
    ListPayment,

    ListSavedLocation,

    LocationMarkerSelector,

    LoginV2,

    LoyalPoint,

    NetworkMonitor,

    NotificationDetail,

    OtpVerificationV2,

    PickupSubLocation,

    Profile,

    ReferFriends,

    ReferralCode,

    // Login,
    SearchSavedLocation,

    SelectDestination,

    ShareRoute,

    SubscribePlan,

    Subscription,

    SubscriptionDetails,

    SubscriptionHistory,

    SubscriptionHistoryDetail,
    UpdateInformation,
    VerifyCode,
    WebViewScreen,
  };

  const TRANSPARENT_MODAL: any = {
    ActionModal,
    AddBusinessCodeModal,
    AddCouponModal,
    BeforeTipModal,
    BookingCancel,
    BusinessConfigModal,
    BusinessDetail,
    BusinessInfoModal,
    CancelSubscriptionModal,
    CardDetailModal,
    DateTimePickerModal,
    GenderModal,
    HelpsModal,
    InputCancelReasonModal,
    ModalNeedHelp,
    ModalNeedRide,
    RatingDriver,
    RedeemCouponModal,
    ScheduleTimeModal,
    SuccessSubscribeModal,
    VerifyPhoneModal,
  };

  const SCREEN_HAS_BACK_HOME_RIGHT_BUTTONS = [''];

  const renderListChild = () => (
    <>
      {Object.keys(SCREENS).map(item => (
        <Stack.Screen
          component={SCREENS[item]}
          key={item}
          name={item}
          options={({route, navigation}: {route: any; navigation: any}) => ({
            contentStyle: {backgroundColor: Colors.white},
            headerShadowVisible: !route?.params?.noBorder,
            headerShown: route?.params?.isTopBarEnable,
            headerTitleAlign: 'center',
            title: route?.params?.headerTitle,
            ...(SCREEN_HAS_BACK_HOME_RIGHT_BUTTONS.includes(item) && {
              headerRight: () => (
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('HomeTab');
                  }}>
                  <Icon color={Colors.grey1} name="ic-home-3" size={20} />
                </TouchableOpacity>
              ),
            }),
          })}
        />
      ))}
    </>
  );

  const renderListModal = () => (
    <>
      {Object.keys(TRANSPARENT_MODAL).map(item => (
        <Stack.Screen
          component={TRANSPARENT_MODAL[item]}
          key={item}
          name={item as keyof RootStackParamList}
          options={{headerShown: false}}
        />
      ))}
    </>
  );

  const defaultScreenOptions = ({navigation}: {navigation: any}) =>
    ({
      animation: 'fade_from_bottom',
      // animation: 'slide_from_right',
      animationDuration: 100,
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}>
          <Icon name="Outline-CaretLeft" size={20} />
        </TouchableOpacity>
      ),
      keyboardHandlingEnabled: true,
    } as NativeStackNavigationOptions);

  function AppStackScreen() {
    SplashScreen.hide();
    enableLatestRenderer();
    if (!__DEV__) {
      useQuickActions();
    }
    useNotificationBadge();

    /*
     * const routeName = useNavigationState(state => {
     *   const route = state?.routes[state.index];
     *   return route?.name;
     * });
     */
    const routes = useNavigationState(state => state?.routes);
    const currentListScreenKey = ['globalState', 'currentListScreen'];
    // const currentScreenKey = ['globalState', 'currentScreen'];

    queryClient.setQueryData(currentListScreenKey, getRoutesName(routes));

    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={Colors.primary} size={'large'} />
        </View>
      );
    }

    appsFlyer.onDeepLink(handleDeepLink);
    appsFlyer.onAppOpenAttribution(handleDeepLink);

    return (
      <Stack.Navigator
        initialRouteName={initialRouteName}
        screenOptions={defaultScreenOptions as NativeStackNavigationOptions}>
        <Stack.Screen
          component={Login}
          name="Login"
          options={{
            contentStyle: {backgroundColor: Colors.white},
            headerShown: false,
          }}
        />
        <Stack.Screen
          component={Intro}
          name="Intro"
          options={{headerShown: false}}
        />
        <Stack.Screen
          component={BottomTabs}
          name="Main"
          options={{headerShown: false}}
        />

        <Stack.Group
          screenOptions={{
            animation: 'fade',
            animationDuration: 500,
            presentation: 'transparentModal',
          }}>
          {renderListModal()}
        </Stack.Group>

        {renderListChild()}
      </Stack.Navigator>
    );
  }

  const {STRIPE_KEY, MERCHANT_ID} = Config;

  //white list save in async store
  const WHITE_LIST = [
    'userData',
    'currentBooking',
    'bookingWithCodeParams',
    'currentWaitingTime',
    'maxWaitingTime',
    'anyCarPrice',
    'configData',
    'isSkipIntro',
    'currentPayment',
    'language',
    'loyalPointConfig',
  ];

  const toastConfig = {
    error: (props: any) => (
      <CustomToast
        props={{
          ...props?.props,
          icon: 'Outline-XCircle',
          iconColor: Colors.danger500,
        }}
      />
    ),
    loading: () => <LoadingToast />,
    success: (props: any) => (
      <CustomToast
        props={{
          ...props?.props,
          icon: 'Outline-CheckCircle',
          iconColor: Colors.success500,
        }}
      />
    ),
  };

  return (
    <GestureHandlerRootView style={GlobalStyle.flex1}>
      <PersistQueryClientProvider
        client={queryClient}
        onSuccess={async () => {
          const data = await AsyncStorage.getItem('REACT_QUERY_OFFLINE_CACHE');
          const token = JSON.parse(data)?.clientState?.queries.find(ele =>
            ele?.queryHash?.includes('userData'),
          )?.state?.data?.accessToken;
          const language = JSON.parse(data)?.clientState?.queries.find(ele =>
            ele?.queryHash?.includes('language'),
          )?.state?.data;

          OpenAPI.TOKEN = token;
          OpenAPI.HEADERS = await {['Accept-Language']: language};
          getConfig(queryClient);
          setCalendarLanguage(language);
        }}
        persistOptions={{
          dehydrateOptions: {
            shouldDehydrateQuery: query =>
              WHITE_LIST.some(str => query?.queryHash?.includes(str)),
          },
          maxAge: Infinity,
          persister,
        }}>
        <StripeProvider
          merchantIdentifier={MERCHANT_ID} // required for Apple Pay
          publishableKey={STRIPE_KEY as string}
          urlScheme="your-url-scheme" // required for 3D Secure and bank redirects
        >
          <NavigationContainer ref={navigationRef}>
            <BookingProvider>
              <AppStackScreen />
            </BookingProvider>
            <Toast config={toastConfig} />
            <GlobalModal ref={globalModalRef} />
          </NavigationContainer>
        </StripeProvider>
      </PersistQueryClientProvider>
    </GestureHandlerRootView>
  );
}

export default App;

const styles = StyleSheet.create({
  loadingContainer: {
    alignItems: 'center',
    backgroundColor: Colors.white,
    flex: 1,
    justifyContent: 'center',
  },
});
